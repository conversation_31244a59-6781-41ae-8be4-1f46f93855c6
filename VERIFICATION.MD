# Verification System Documentation

## Overview

The verification system is a multi-step process that handles user identity, occupation, and PAN verification. It supports different verification cases and provides a flexible, status-based workflow.

## Types and Enums

### Verification Status

```typescript
enum VERIFICATION_STATUS {
  Verified = "verified",
  Received = "received",
  Requested = "requested",
}
```

### Verification Cases

```typescript
enum VERIFICATION_CASE {
  IdentityOnly = "identity_only",
  IdentityAndChoice = "identity_and_choice",
  AllRequired = "all_required",
}
```

### User Verification Interface

```typescript
interface USER_VERIFICATION {
  id: number
  created_at: number
  user_id: number
  identity_verified_type: "otp" | "document"
  occupation_verified_type: "gst" | "document"
  credit_verified_type: "fetched" | "requested"
  identity_status: VERIFICATION_STATUS
  occupation_status: VERIFICATION_STATUS
  credit_status: VERIFICATION_STATUS
  occupation_value: string
  occupation_type: OCCUPATION_TYPE
  verification_case: VERIFICATION_CASE
}
```

### Verification Statuses Interface

```typescript
interface VERIFICATION_STATUSES {
  isIdentityVerified: boolean
  isOccupationVerified: boolean
  isPanVerified: boolean
}
```

## Components

### VerificationSteps

Main component that orchestrates the verification flow.

**Props:**

```typescript
interface VerificationStepsProps {
  userVerification: USER_VERIFICATION
  faqs: FaqType[]
  isFaqsLoading: boolean
}
```

### StepWrapper

Base wrapper component for all verification steps.

**Props:**

```typescript
interface StepWrapperProps {
  icon: ReactNode
  title: string
  description: string
  status: VERIFICATION_STATUS
  onAction: () => void
  isExpanded: boolean
  isOptional?: boolean
  children: ReactNode
}
```

### Individual Step Components

- `IdentityStep`: Handles identity verification
- `OccupationStep`: Handles occupation verification
- `PanStep`: Handles PAN verification
- `ChoiceStep`: Handles choice-based verification flow

## Utility Functions

### getHeaderContent

Determines the header content based on verification status.

```typescript
function getHeaderContent(
  verificationStatuses: VERIFICATION_STATUSES,
  userVerification: USER_VERIFICATION,
  showChoice: boolean,
): HeaderContent
```

**Returns:**

- Title and description based on verification state
- Special messages for review state and completion

### renderSteps

Renders verification steps based on current state.

```typescript
function renderSteps({
  userVerification,
  verificationStatuses,
  activeStep,
  handleVerify,
  unableToVerify,
  showChoice,
}: RenderStepsProps): JSX.Element
```

**Features:**

- Handles step ordering
- Manages requested vs non-requested steps
- Conditional PAN step visibility
- Choice-based flow support

### getVerificationStatus

Converts verification status to UI elements.

```typescript
function getVerificationStatus(stage: VERIFICATION_STATUS): {
  status: string
  icon: ReactNode
  actionText: string
}
```

## Verification Flow

The verification system supports three states:

- Requested: Initial state when verification is needed
- Received: Documents/information received and under review
- Verified: Verification successfully completed

1. **Initial State**

   - System determines verification case
   - Renders appropriate steps
   - Shows identity verification by default

2. **Identity Verification**

   - Always required
   - Must be completed first
   - Supports OTP or document verification

3. **Choice-based Flow** (if applicable)

   - Shown when verification_case is IdentityAndChoice
   - User can choose between occupation and PAN verification
   - Only visible after identity verification

4. **Occupation Verification**

   - Required for AllRequired case
   - Optional for other cases
   - Supports GST or document verification

5. **PAN Verification**
   - Only visible when credit_verified_type is "requested"
   - Required for AllRequired case
   - Optional for other cases

## Status Management

- **Verified**: Verification complete
- **Received**: Under review
- **Requested**: Action required

## UI Components

### VerificationCard

Displays verification information in card format.

```typescript
interface VerificationCardProps {
  title: string
  description: React.ReactNode
  icon: LucideIcon
  iconBgColor: string
  iconColor: string
  gradientColor: string
  children: React.ReactNode
}
```

### FeatureItem

Displays individual verification features.

```typescript
interface FeatureItemProps {
  icon: LucideIcon
  title: string
  description: string
  iconBgColor?: string
  iconColor?: string
}
```

## Best Practices

1. **Status Checks**

   - Always verify identity first
   - Check verification case before rendering optional steps
   - Handle all verification statuses

2. **Error Handling**

   - Provide "Unable to Verify" option
   - Include WhatsApp support integration
   - Show clear error messages

3. **UI/UX**

   - Use consistent icons and colors
   - Show clear progress indicators
   - Provide FAQ access
   - Maintain responsive design

4. **Performance**
   - Memoize verification statuses
   - Optimize step rendering
   - Handle loading states

## Integration Guide

1. Import required components and types
2. Setup user verification state
3. Configure verification case
4. Implement verification handlers
5. Add FAQ support
6. Style according to theme
7. Test all verification paths

```typescript
import { VerificationSteps } from '@/components/verification'

function YourComponent() {
  return (
    <VerificationSteps
      userVerification={userVerification}
      faqs={faqs}
      isFaqsLoading={loading}
    />
  )
}
```
