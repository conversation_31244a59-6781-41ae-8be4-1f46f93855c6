import { Coupon } from "@/types/coupon"
import { customFetch } from "@/utils/customFetch"
import { handleFetchError } from "."

export const getOfferCoupon = async (): Promise<Coupon> => {
  try {
    const result = await customFetch(
      "https://api.sharepal.in/api:EhQQ9F-L/offer-coupon",
    )
    return result
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Offer Coupon",
      error instanceof Response ? error : undefined,
    )
  }
}
