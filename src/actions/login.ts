"use client"
import { encryptData } from "@/utils/encryption"
// import { encryptWithEnvKey } from "@/utils/encryption"
import { toast } from "sonner"

// Reusable function to handle AES encryption for phone-based operations
const encryptDataWithPhone = async (dataToSend: {
  country_code: string
  calling_phone_number: string
  entered_otp?: string
}) => {
  // Use fixed AES key and IV from environment variables
  const encryptedData = await encryptData(JSON.stringify(dataToSend))

  return {
    encryptedData,
  }
}

export const sendOTP = async (
  country_code: string,
  calling_phone_number: string,
) => {
  const dataToSend = {
    country_code,
    calling_phone_number,
  }

  const { encryptedData } = await encryptDataWithPhone(dataToSend)

  try {
    const response = await fetch(
      `https://api.sharepal.in/api:nqphhSAW/user/otp/send/v1`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Internal-App": "Sharepal",
        },
        body: JSON.stringify({ data: encryptedData }),
      },
    )

    if (!response.ok) {
      throw new Error("Failed to send OTP !")
    }

    return response.json()
  } catch (error) {
    toast.error(error instanceof Error ? error.message : "Failed to send OTP.")
    console.error(error)
    return error
  }
}

export const verifyOTP = async (
  country_code: string,
  calling_phone_number: string,
  entered_otp: string,
) => {
  const dataToSend = {
    country_code,
    calling_phone_number,
    entered_otp,
  }

  const { encryptedData } = await encryptDataWithPhone(dataToSend)

  try {
    const response = await fetch(
      `https://api.sharepal.in/api:nqphhSAW/user/otp/verify/v1`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Internal-App": "Sharepal",
        },
        body: JSON.stringify({ data: encryptedData }),
      },
    )
    return response.json()
  } catch (error) {
    console.error(error)
  }
}
