import { CheckoutParams, ProcessOrderParams } from "@/types/checkout"
import { customFetch } from "@/utils/customFetch"
import { encryptData } from "@/utils/encryption"
import { fetchWithAuth } from "@/utils/fetchWithAuth"
import { handleFetchError } from "."
export const fetchPaymentOptions = async (backend_order?: string) => {
  try {
    const response = await customFetch(
      "https://api.sharepal.in/api:AIoqxnqr/checkout/payment-options",
      {
        method: "POST",
        body: JSON.stringify({
          backend_order: <PERSON><PERSON><PERSON>(backend_order),
        }),
      },
    )
    return response
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Payment Options",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchDiscountCoupons = async () => {
  try {
    const response = await customFetch(
      "https://api.sharepal.in/api:EhQQ9F-L/coupons/type-all",
      {
        method: "POST",
        body: JSON.stringify({
          coupon_type: "rent",
        }),
      },
    )
    return response
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch discount Coupons",
      error instanceof Response ? error : undefined,
    )
  }
}

export const verifyCoupon = async ({
  applied_coupon,
  coupon_type,
  delivery_date,
  total_days,
}: {
  applied_coupon: string
  delivery_date: Date
  coupon_type: string
  total_days: number
}) => {
  try {
    const data = await customFetch(
      "https://api.sharepal.in/api:EhQQ9F-L/coupon/verify",
      {
        method: "POST",
        body: JSON.stringify({
          dicount_coupons_name: applied_coupon,
          delivery_date: delivery_date,
          total_days: total_days,
          coupon_type: coupon_type,
        }),
      },
    )
    return data
  } catch (error) {
    console.error(error)
    throw handleFetchError(
      "Failed to verify Coupon",
      error instanceof Response ? error : undefined,
    )
  }
}

export const processOrder = async (order: ProcessOrderParams) => {
  try {
    const data = await encryptData(JSON.stringify(order))
    const response = await fetchWithAuth(
      "https://api.sharepal.in/api:AIoqxnqr/checkout/process-order",
      {
        method: "POST",
        body: JSON.stringify({
          data: data,
        }),
      },
    )
    return response
  } catch (error) {
    throw handleFetchError(
      "Failed to process Order",
      error instanceof Response ? error : undefined,
    )
  }
}

export const createOrder = async (order: CheckoutParams) => {
  try {
    const data = await encryptData(JSON.stringify(order))
    const response = await fetchWithAuth(
      "https://api.sharepal.in/api:AIoqxnqr/checkout/create-order",
      {
        method: "POST",
        body: JSON.stringify({
          data: data,
        }),
      },
    )
    return response
  } catch (error) {
    throw handleFetchError(
      "Failed to create Order",
      error instanceof Response ? error : undefined,
    )
  }
}
