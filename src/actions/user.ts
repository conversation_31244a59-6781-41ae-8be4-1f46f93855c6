import { getCookie } from "@/functions/cookies"
import { capitalizeFirstLetter } from "@/functions/small-functions"
import { ContactDetails } from "@/types/checkout"
import { User, Wallet } from "@/types/user"
import { decryptData } from "@/utils/decryption"
import { encryptData } from "@/utils/encryption"
import { fetchWithAuth } from "@/utils/fetchWithAuth"
import { addDays } from "date-fns"
import { toast } from "sonner"

interface ProfileFormData {
  first_name: string
  last_name: string
  email: string
}

export const getLoggedInUser = async (city: string) => {
  if (typeof window === "undefined") return
  const uid = getCookie("uid")

  try {
    const encryptedData = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/me`,
    )

    if (!encryptedData) {
      return null
    }

    const decryptedData = await decryptData(encryptedData)

    // Parse the decrypted data into a User object
    const user = JSON.parse(decryptedData) as User

    // console.log("Decrypted User Data:", user)

    if (uid && user?.user_uid !== uid) {
      await updateUserUid()
      await updateUserCity(capitalizeFirstLetter(city))
    }

    return user
  } catch (error) {
    console.error(error)
    // throw new Error('Error fetching user data')
    throw error
  }
}

export const updateUserUid = async () => {
  try {
    await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/update-user-uid`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_uid: getCookie("uid"),
        }),
      },
    )
  } catch (error) {
    console.error("Error updating user uid:", error)
  }
}

// const { data } = await api.put("/update_user_new", formValues);

export const updateUser = async (
  userDetails: ContactDetails | ProfileFormData,
) => {
  try {
    const encryptedData = await encryptData(JSON.stringify(userDetails))

    const result = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/update-user-new`,
      {
        method: "PUT",
        body: JSON.stringify({ data: encryptedData }),
      },
    )

    const decryptedData = await decryptData(result)

    // Parse the decrypted data into a User object
    const user = JSON.parse(decryptedData) as User

    return user
  } catch (error) {
    console.error(error)
  }
}

export const updateUserCity = async (user_city: string) => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/update-user-city`,
      {
        method: "PATCH",
        body: JSON.stringify({ city: user_city }),
      },
    )
    return data
  } catch (error) {
    console.error(error)
  }
}

//get user saved addresses
export const getUserAddresses = async () => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/all-address`,
    )
    return data
  } catch (error) {
    console.error(error)
  }
}

export const getUserWallet = async (): Promise<Wallet> => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/wallet`,
    )

    return data
  } catch (error) {
    console.error("Error fetching user wallet:", error)
    throw error // Rethrow the error for handling by the caller
  }
}

export const gerUserWishlist = async () => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/wishlist`,
    )
    return data
  } catch (error) {
    console.error(error)
  }
}

export const updateUserDates = async (data: {
  delivery_date: Date
  return_date: Date
  number_of_days: number
}) => {
  if (
    !data ||
    !data.delivery_date ||
    !data.return_date ||
    !data.number_of_days
  ) {
    // throw new Error("Invalid data provided for updating user dates")
    return
  }
  const payload = {
    delivery_date: addDays(data.delivery_date, 1),
    return_date: addDays(data.return_date, 1),
    number_of_days: data.number_of_days,
  }
  // const encryptedPayload = await encryptData(JSON.stringify(payload))

  await fetchWithAuth(
    "https://api.sharepal.in/api:AIoqxnqr/user/update-user-date",
    {
      method: "PATCH",
      body: JSON.stringify({ ...payload }),
    },
  )
}

// Single function to handle everything
export const handleFavourite = async (
  itemId: number,
  user: User, // User object with optional favourite_items
) => {
  const favourite_items = user?.favourite_items || [] // Get current favourite items or default to empty array

  try {
    let updatedFavourites: number[]
    let action: "added" | "removed" | null = null // Track the action for personalized messages

    // Step 1: Remove "0" if it exists (no message for this)
    if (favourite_items.includes(0)) {
      updatedFavourites = favourite_items.filter((id) => id !== 0)
    } else {
      updatedFavourites = [...favourite_items] // Copy the array if "0" doesn't exist
    }

    // Step 2: Add or remove the itemId
    if (favourite_items.includes(itemId)) {
      // If itemId is already present, remove it
      updatedFavourites = updatedFavourites.filter((id) => id !== itemId)
      action = "removed" // Set action to "removed"
    } else {
      // If itemId is not present, add it to the front of the array
      updatedFavourites = [itemId, ...updatedFavourites]
      action = "added" // Set action to "added"
    }

    // Step 3: Update the wishlist on the server
    const data = await fetchWithAuth(
      "https://api.sharepal.in/api:AIoqxnqr/user/update-favourite-items",
      {
        method: "PATCH",
        body: JSON.stringify({ favourite_item: updatedFavourites }),
      },
    )

    if (data) {
      // Step 4: Show personalized success message based on the action
      if (action === "added") {
        toast.success(`Item  added to favourites!`)
      } else if (action === "removed") {
        toast.success(`Item  removed from favourites!`)
      }

      // Step 5: Return the updated data
      return data
    }
  } catch (error) {
    // Handle errors
    toast.error("Something Went Wrong!")
    console.error(error)
  }
}

export const backendLogin = async (uniqueid: string, token: string) => {
  try {
    const encryptedData = await encryptData(JSON.stringify(uniqueid))
    const res = await fetch(
      "https://api.sharepal.in/api:AIoqxnqr/admin/backend/login",
      {
        method: "POST",
        body: JSON.stringify({
          data: encryptedData,
        }),
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    return res.json()
  } catch (error) {
    console.error(error)
  }
}

export const getUserVerification = async () => {
  try {
    const result = await fetchWithAuth(
      "https://api.sharepal.in/api:qsuyzexA/user_verification",
    )
    return result
  } catch (error) {
    console.error(error)
    return null
  }
}
