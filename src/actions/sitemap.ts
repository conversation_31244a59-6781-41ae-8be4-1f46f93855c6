import { cache } from "react"

// For POST request
const API_URL = "https://api.sharepal.in/api:EhQQ9F-L/sitemap/sitemap-xml"
const TYPES = ["city"]

export interface SiteUrl {
  id: string
  url: string
  title: string
  city: string
  type: string
  frequency?:
    | "always"
    | "hourly"
    | "daily"
    | "weekly"
    | "monthly"
    | "yearly"
    | "never"
  priority?: number
}

export const fetchSiteMap = cache(async (): Promise<SiteUrl[]> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/sitemap/site-urls`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        next: { revalidate: 86400 }, // Cache for 1 days
      },
    )

    if (!response.ok) {
      throw new Error(
        `Failed to fetch site map: ${response.status} ${response.statusText}`,
      )
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Error fetching site map:", error)
    return []
  }
})

export const fetchCitySiteMap = cache(async (): Promise<SiteUrl[]> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/sitemap/city-urls`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        next: { revalidate: 86400 }, // Cache for 1 days
      },
    )

    if (!response.ok) {
      throw new Error(
        `Failed to fetch site map: ${response.status} ${response.statusText}`,
      )
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Error fetching site map:", error)
    return []
  }
})

export const fetchSiteUrls = cache(async (): Promise<SiteUrl[]> => {
  try {
    let allUrls: SiteUrl[] = []
    for (const type of TYPES) {
      const response = await fetch(API_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ type }),
        next: { revalidate: 864000 }, // Cache for 10 days
      })

      if (!response.ok) {
        throw new Error(
          `Failed to fetch site URLs for type ${type}: ${response.status} ${response.statusText}`,
        )
      }

      const data = await response.json()
      allUrls = allUrls.concat(data)
    }

    return allUrls
  } catch (error) {
    console.error("Error fetching site URLs:", error)
    return []
  }
})

export function generateSitemapIndex(urls: SiteUrl[]): string {
  let sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
`

  const cities = Array.from(new Set(urls.map((url) => url.city)))
  // sitemapIndex += `    <loc>https://sharepal.in/sitemap/${urlObj.city.toLowerCase()}</loc>\n`;

  cities.forEach((city) => {
    sitemapIndex += `  <sitemap>
    <loc>${process.env.NEXT_PUBLIC_APP_URL ?? "https://sharepal.in"}/sitemap/${city.toLowerCase()}</loc>
  </sitemap>
`
  })

  sitemapIndex += `</sitemapindex>`

  return sitemapIndex
}

export function generateCitySitemap(urls: SiteUrl[]): string {
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>\n`
  sitemap += `<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd" >\n `

  urls.forEach((urlObj) => {
    const escapedUrl = urlObj.url.replace(/[<>&]/g, (char) => {
      switch (char) {
        case "<":
          return "&lt;"
        case ">":
          return "&gt;"
        case "&":
          return "&amp;"
        default:
          return char
      }
    })
    sitemap += `  <url>
    <loc>${escapedUrl}</loc>
    <changefreq>${urlObj.frequency || "weekly"}</changefreq>
    <priority>${urlObj.priority || "0.5"}</priority>
  </url>\n
`
  })

  sitemap += `</urlset>`

  return sitemap
}
