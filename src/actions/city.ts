import { capitalize<PERSON>irstLetter } from "@/functions/small-functions"
import { City } from "@/types/address"
import { customFetch } from "@/utils/customFetch"
import { handleFetchError } from "."

export type CityDescription = {
  id: number
  city: string
  desc: string
}
export interface CityHeader {
  id: number
  created_at: number
  city: string
  language: string
  line_0: string
  line_1: string
  line_2: string
}

export const fetchCities = async (): Promise<City[]> => {
  const url = `https://api.sharepal.in/api:EhQQ9F-L/cities`
  try {
    const result = await customFetch(url)
    if (!result) throw new Error("Failed to fetch Cities")

    // const data: City[] = await response.json()
    return result
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Cities",
      error instanceof Response ? error : undefined,
    )
  }
}

export const getCityMinTh = async (city: string, catSnameList: string[]) => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/city/subcat-minth`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          city: city,
          subcat_sname: catSnameList,
        }),
      },
    )

    if (!response.ok) throw new Error("Failed to fetch City Min Th")
    const data = await response.json() // Correctly typing the response data
    return data
  } catch (error) {
    console.error(error)
  }
}

export const fetchCityHeader = async (city: string) => {
  try {
    const result = await customFetch(
      "https://api.sharepal.in/api:EhQQ9F-L/city/header?city=" +
        capitalizeFirstLetter(city),
    )
    return result
  } catch (error) {
    console.error(error)
  }
}
