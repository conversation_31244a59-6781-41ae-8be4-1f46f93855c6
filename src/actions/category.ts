// Import BASE_URL from constants and utility functions
import { capitalizeFirstLetter } from "@/functions/small-functions"
import { Category, RentalItem, RentalItemVarient, SubCategory } from "@/types"
import { SeoDetails } from "@/types/super-category"
import { customFetch } from "@/utils/customFetch"
import { handleFetchError } from "."

// Type for FetchCategoryProductsResponse
// type FetchCategoryProductsResponse = {
//   products: RentalItem[]
//   total: number
// }

// Type for FetchCategoryProductsResponse
type FetchCategoryProductsAllResponse = {
  items: RentalItem[]
  itemsReceived: number
  curPage: number
  nextPage: number
  prevPage: number
  offset: number
  perPage: number
  itemsTotal: number
  pageTotal: number
}

// Fetch all categories
export const fetchCategories = async (): Promise<Category[]> => {
  try {
    const result = await customFetch(
      `https://api.sharepal.in/api:EhQQ9F-L/category/fetch/all`,
    )
    if (!result) {
      throw handleFetchError("Failed to fetch categories", result)
    }
    return result
  } catch (error) {
    throw handleFetchError(
      "An error occurred while fetching categories",
      error instanceof Response ? error : undefined,
    )
  }
}

//fetch category seo

export const fetchCategorySeo = async (
  cat: string,
): Promise<SeoDetails | null> => {
  try {
    const data = await customFetch(
      `https://api.sharepal.in/api:EhQQ9F-L/category/fetch/seodescription?category_url=${cat}`,
    )
    if (data) {
      return data
    } else {
      return null
    }
  } catch (error) {
    console.error("An error occurred while fetching category seo", error)
    return null
  }
}

// Fetch subcategories for a specific category
export const fetchSubCategories = async (
  cat: string,
): Promise<SubCategory[]> => {
  try {
    const data = await customFetch(
      `https://api.sharepal.in/api:EhQQ9F-L/subcategory/fetch/category?category_url=${cat}`,
      {
        cache: "force-cache",
        next: {
          revalidate: 3600,
        },
      },
    )
    if (data) {
      return data
    } else {
      return []
    }
  } catch (error) {
    console.error("An error occurred while fetching subcategories", error)
    return []
  }
}

export const fetchFooterLinks = async () => {
  try {
    const data = await customFetch(
      `https://api.sharepal.in/api:qsuyzexA/footer-links`,
      {
        next: {
          revalidate: 86400,
        },
      },
    )
    if (data) {
      return data
    } else {
      return []
    }
  } catch (error) {
    console.error("An error occurred while fetching footer links", error)
    return []
  }
}

export const fetchSubCategoriesWithSC = async (
  cat: string,
): Promise<SubCategory[]> => {
  try {
    const data = await customFetch(
      `https://api.sharepal.in/api:EhQQ9F-L/subcategory/fetch/super_category?super_category_url=${cat}`,
    )
    if (data) {
      return data
    } else {
      return []
    }
  } catch (error) {
    console.error("An error occurred while fetching subcategories", error)
    return []
  }
}

// Fetch products for a specific subcategory in a city
export const fetchSubCategoryProducts = async (
  city: string,
  subcat: string,
): Promise<RentalItem[]> => {
  const url = `https://api.sharepal.in/api:EhQQ9F-L/rentalitem/fetch?city=${capitalizeFirstLetter(
    city,
  )}&sub_category_url=${subcat}`
  try {
    const response = await fetch(url, {
      cache: "force-cache",
    })
    if (!response.ok) {
      throw handleFetchError("Failed to fetch subcategory products", response)
    }
    return response.json()
  } catch (error) {
    throw handleFetchError(
      "An error occurred while fetching subcategory products",
      error instanceof Response ? error : undefined,
    )
  }
}

// Fetch products for a specific category with pagination
export const fetchCategoriesProducts = async (
  city: string,
  categoryId: number,
  pageNumber: number = 1,
): Promise<FetchCategoryProductsAllResponse> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/rentalitem/pagination`,
      {
        cache: "force-cache",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          city: capitalizeFirstLetter(city),
          category_id: categoryId,
          paging: { page: pageNumber, per_page: 10 },
        }),
      },
    )
    if (!response.ok) {
      throw handleFetchError("Failed to fetch trending items", response)
    }
    return response.json()
  } catch (error) {
    throw handleFetchError(
      "An error occurred while fetching trending items",
      error instanceof Response ? error : undefined,
    )
  }
}

// Fetch products for a specific category with pagination
export const fetchAllCategoriesProducts = async (
  city: string,
  category_name: string,
  pageNumber: number = 1,
): Promise<FetchCategoryProductsAllResponse> => {
  try {
    const response = await fetch(
      `$https://api.sharepal.in/api:EhQQ9F-L/rentalitem/category/all`,
      {
        cache: "force-cache",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          city: city,
          category_name,
          paging: { page: pageNumber, per_page: 12 },
        }),
      },
    )
    if (!response.ok) {
      throw handleFetchError("Failed to fetch trending items", response)
    }
    return response.json()
  } catch (error) {
    throw handleFetchError(
      "An error occurred while fetching trending items",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchAllCategoriesProductsSC = async (
  city: string,
  super_category_url: string,
  pageNumber: number = 1,
  admin_only: boolean = false,
): Promise<FetchCategoryProductsAllResponse> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/rentalitem/supercategory/all`,
      {
        cache: "force-cache",
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          city: city,
          super_category_url,
          page: pageNumber,
          per_page: 28,
          admin_only,
          paging: { page: pageNumber, per_page: 28 },
        }),
      },
    )
    if (!response.ok) {
      throw handleFetchError("Failed to fetch trending items", response)
    }
    return response.json()
  } catch (error) {
    throw handleFetchError(
      "An error occurred while fetching trending items",
      error instanceof Response ? error : undefined,
    )
  }
}

// // Fetch themes for a specific category
// export const fetchCategoryThemes = async (
//   category_url: string,
// ): Promise<RentalItem[]> => {
//   try {
//     const response = await fetch(`${BASE_URL}/category/theme`, {
//       cache: "force-cache",
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify({ category_url }),
//     })
//     if (!response.ok) {
//       throw handleFetchError("Failed to fetch category themes", response)
//     }
//     return response.json()
//   } catch (error) {
//     throw handleFetchError(
//       "An error occurred while fetching category themes",
//       error instanceof Response ? error : undefined,
//     )
//   }
// }

export const fetchRentalVarients = async (
  p_short_name: string,
): Promise<RentalItemVarient[]> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/rentalitemvariants/fetch?p_short_name=${p_short_name}`,
      {
        cache: "force-cache",
      },
    )
    if (!response.ok) {
      throw handleFetchError("Failed to fetch categories", response)
    }
    const data: RentalItemVarient[] = await response.json()
    return data
  } catch (error) {
    throw handleFetchError(
      "An error occurred while fetching categories",
      error instanceof Response ? error : undefined,
    )
  }
}
