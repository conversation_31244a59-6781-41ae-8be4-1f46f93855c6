import { Partner, ProductSurge } from "@/types"
import { City } from "@/types/address"
import { handleFetchError } from "."

interface SurgeArgs {
  surge_date: string
  surge_factor: number
}

export const fetchSurgeFactor = async ({
  delivery_date,
}: {
  delivery_date: Date
}): Promise<SurgeArgs | undefined> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/surge/delivery`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          delivery_date,
        }),
      },
    )
    const data: SurgeArgs = await response.json()
    return data
  } catch (error) {
    console.error(error)
  }
}

export const fetchSameDaySurge = async (city: string): Promise<City> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/surge/city`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          city,
        }),
      },
    )
    const data: City = await response.json()
    return data
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Video Size",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchPartner = async (partner_name: string): Promise<Partner> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/partner-campaign`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          partner_name,
        }),
      },
    )
    const data: Partner = await response.json()
    return data
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Video Size",
      error instanceof Response ? error : undefined,
    )
  }
}

interface FetchProductSurgeArgs {
  city: string
  delivery_date: Date
  super_category: string
  sub_category: string
}

export const fetchProductSurgeByCategory = async (
  args: FetchProductSurgeArgs,
): Promise<ProductSurge[]> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/surge/product`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(args),
      },
    )
    const data: ProductSurge[] = await response.json()
    return data
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Video Size",
      error instanceof Response ? error : undefined,
    )
  }
}

interface FetchProductSurgeByRiNameArgs {
  city: string
  delivery_date: Date
  ri_name: string[]
}

export const fetchProductSurgeByRINames = async (
  args: FetchProductSurgeByRiNameArgs,
): Promise<ProductSurge[]> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/surge/with-rinames`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(args),
      },
    )
    const data: ProductSurge[] = await response.json()
    return data
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Video Size",
      error instanceof Response ? error : undefined,
    )
  }
}
