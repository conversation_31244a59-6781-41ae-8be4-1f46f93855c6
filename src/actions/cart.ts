import { get<PERSON><PERSON>ie } from "@/functions/cookies"
import { CartItem } from "@/types"
import { customFetchPost } from "@/utils/customFetch"

// Fetch Cart Items

// Interface for AddToCart arguments
interface AddToCartArgs {
  user_id: number
  id: number
  user_uid: string
  num_days: number
  size: string
  cart_type: "rent"
  surge_factor: number
  city_surge: number
  same_day_surge: number
}

// Interface for updating cart item quantity arguments
interface UpdateCartItemQuantityArgs {
  user_uid: string
  cart_item_id: number
  quantity: number
}

interface AddToCartArgs {
  user_id: number
  id: number
  user_uid: string
  num_days: number
  size: string
  cart_type: "rent"
  surge_factor: number
  city_surge: number
  same_day_surge: number
  final_surge: number
}

// Add To Cart
export const addCartItem = async (
  props: AddToCartArgs,
): Promise<CartItem | undefined> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/cart/add`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(props),
      },
    )
    const data: CartItem = await response.json()
    return data
  } catch (error) {
    console.error("Error adding cart item:", error)
  }
}

// Delete From Cart
export const deleteCartItem = async (props: {
  cart_item_id: number
  user_uid: string
}): Promise<CartItem | undefined> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/cart/delete`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(props),
      },
    )
    return response.json()
  } catch (error) {
    console.error("Error deleting cart item:", error)
  }
}

// Update Quantity
export const updateCartItemQuantity = async (
  props: UpdateCartItemQuantityArgs,
): Promise<CartItem | undefined> => {
  try {
    const response = await customFetchPost<CartItem>(
      `https://api.sharepal.in/api:EhQQ9F-L/cart/quantity`,
      props,
    )
    return response
  } catch (error) {
    console.error("Error updating cart item quantity:", error)
  }
}

// Fetch All Cart Items
export const fetchAllCartItems = async ({
  user_uid,
  num_days,
}: {
  user_uid: string
  num_days: number
}): Promise<CartItem[] | undefined> => {
  try {
    const response = await customFetchPost<CartItem[]>(
      `https://api.sharepal.in/api:EhQQ9F-L/cart/fetch`,
      {
        user_uid,
        num_days,
      },
    )
    return response
  } catch (error) {
    console.error("Error fetching all cart items:", error)
    throw error
  }
}

export const clearCart = async () => {
  const uid = getCookie("uid")
  if (!uid || typeof window === "undefined") return
  try {
    const response = await customFetchPost(
      `https://api.sharepal.in/api:EhQQ9F-L/cart/clear`,
      {
        user_uid: uid,
      },
      {
        next: {
          revalidate: 0,
        },
      },
    )
    return response
  } catch (error) {
    console.error("Error clearing cart:", error)
    throw error
  }
}

export const handleNotifyMe = (
  e: React.MouseEvent<HTMLButtonElement>,
  ri_short_name: string,
) => {
  e.preventDefault()
  e.nativeEvent.stopImmediatePropagation()
  // handle notify me here
  window.open(
    `https://api.whatsapp.com/send?phone=+************&text=Hi, I am interested in ${ri_short_name}.`,
    "_blank",
  )
}
