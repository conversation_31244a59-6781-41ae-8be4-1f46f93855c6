import { AdditionalOrderSummary, ChecklistItem } from "@/types/return-order"
import { fetchWithAuth, fetchWithAuthPost } from "@/utils/fetchWithAuth"

export const fetchProductsAndPackagingItems = async (order_id: string) => {
  try {
    const data = await fetchWithAuth(
      "https://api.sharepal.in/api:AIoqxnqr/return/fetch-products?order_id=" +
        order_id,
    )
    return data
  } catch (error) {
    console.error("Error fetching products and packaging items:", error)
    return null
  }
}

// fetch return items
export const fetchReturnItems = async (order_id: string) => {
  try {
    const data = await fetchWithAuth(
      "https://api.sharepal.in/api:AIoqxnqr/return/fetch-return-items?order_id=" +
        order_id,
    )
    return data as ChecklistItem[]
  } catch (error) {
    console.error("Error Fetch return Items", error)
    return []
  }
}

export const fetchAdditionalOrderSummary = async (order_id: string) => {
  try {
    const data = await fetchWithAuthPost(
      "https://api.sharepal.in/api:AIoqxnqr/orders/additional-details",
      {
        order_id: order_id,
      },
    )
    return data as AdditionalOrderSummary
  } catch (error) {
    console.error("Error Fetch Additional Order Summary", error)
    return null
  }
}

export const checkScheduleShipment = async (
  order_id: string,
): Promise<boolean> => {
  try {
    const data = await fetchWithAuthPost(
      "https://api.sharepal.in/api:AIoqxnqr/shipment/schedule-check",
      {
        order_id: order_id,
      },
    )
    return data as boolean
  } catch (error) {
    console.error("Error checking schedule shipment", error)
    return false
  }
}
