import { DiscountCoupons } from "@/types"
import { customFetch } from "@/utils/customFetch"

export const fetchAllCoupons = async (): Promise<
  DiscountCoupons[] | undefined
> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/coupons/type-all`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          coupon_type: "rent",
        }),
      },
    )
    const data: DiscountCoupons[] = await response.json()
    return data
  } catch (error) {
    console.error(error)
  }
}

export const fetchAllFaqs = async () => {
  try {
    const response = await customFetch(
      `https://api.sharepal.in/api:KlLQg1sH/faqs`,
    )
    // const data = await response.json()
    return response
  } catch (error) {
    console.error(error)
  }
}
