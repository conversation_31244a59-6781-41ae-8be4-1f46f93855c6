import { capitalizeFirstLetter, formatUrl } from "@/functions/small-functions"
import { FaqType } from "@/types"
import { customFetch } from "@/utils/customFetch"
import { handleFetchError } from "."

// Define a common FetchError type
interface FetchError extends Error {
  response?: Response
}

// Utility function for handling errors
const createFetchError = (message: string, response?: Response): FetchError => {
  const error: FetchError = new Error(message)
  if (response) error.response = response
  return error
}

export const fetchProduct = async (
  city: string,
  product: string,
  subcat: string,
) => {
  try {
    const url = `https://api.sharepal.in/api:EhQQ9F-L/product/details?city=${capitalizeFirstLetter(
      city,
    )}&ri_name=${formatUrl(product)}&sc_name=${formatUrl(subcat)}`
    const data = await customFetch(url)
    return data
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch SubCategory",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchInclusions = async (shortName: string) => {
  try {
    const url = `https://api.sharepal.in/api:EhQQ9F-L/product/inclusions/1?p_short_name=${shortName}`
    const result = await customFetch(url, {
      cache: "force-cache",
      next: {
        revalidate: 3600,
      },
    })

    return result
  } catch {
    // console.error("Failed to fetch Inclusions:", error)
    return []
    // throw handleFetchError(
    //   "Failed to fetch Inclusions",
    //   error instanceof Response ? error : undefined,
    // )
  }
}

export const fetchProductVideos = async (shortName: string) => {
  try {
    const result = await customFetch(
      `https://api.sharepal.in/api:EhQQ9F-L/product/yt-short-videos?ri_name=${shortName}`,
    )
    return result
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Product Videos",
      error instanceof Response ? error : undefined,
    )
  }
}

// export const fetchFaqs = async (
//   type?: string,
//   city?: string,
//   category?: string,
// ): Promise<FaqType[]> => {
//   try {
//     const url = `${BASE_URL}/faqs?type=${type?.trim() ?? "rent"}&city=${city?.trim() ?? "bangalore"}&category=${category?.trim() ?? ""}`
//     const response = await fetch(url, {
//       cache: "force-cache",
//       next: {
//         revalidate: 3600,
//       },
//     })
//     if (!response.ok) throw createFetchError("Failed to fetch FAQs", response)
//     const data: FaqType[] = await response.json()
//     return data
//   } catch (error) {
//     throw handleFetchError(
//       "Failed to fetch FAQs",
//       error instanceof Response ? error : undefined,
//     )
//   }
// }

export const fetchFaqs = async (
  type?: string,
  city?: string,
  category?: string,
): Promise<FaqType[]> => {
  try {
    const url = `https://api.sharepal.in/api:EhQQ9F-L/faqs/all`
    const body = JSON.stringify({
      type: type?.trim(),
      city: city?.trim(),
      category: category?.trim(),
    })

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body,
      cache: "force-cache",
      next: {
        revalidate: 3600,
      },
    })

    if (!response.ok) throw createFetchError("Failed to fetch FAQs", response)
    const data: FaqType[] = await response.json()
    return data
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch FAQs",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchGoogleReviews = async (category: string) => {
  try {
    const url = `https://api.sharepal.in/api:EhQQ9F-L/product/reviews`
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ category_name: category.replace(/-/g, " ") }),

      cache: "force-cache",
      next: {
        revalidate: 3600,
      },
    })
    if (!response.ok)
      throw createFetchError("Failed to fetch Google Reviews", response)
    return response.json()
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Google Reviews",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchProductDescription = async (shortName: string) => {
  try {
    const url = `https://api.sharepal.in/api:EhQQ9F-L/product/feature-description`
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ product_short_name: shortName }),

      cache: "force-cache",
      next: {
        revalidate: 3600,
      },
    })
    if (!response.ok)
      throw createFetchError("Failed to fetch Product Description", response)
    return response.json()
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Product Description",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchAddOns = async ({
  city,
  shortName,
  pageNumber = 1,
}: {
  city: string
  shortName: string
  pageNumber: number
}) => {
  try {
    const url = `https://api.sharepal.in/api:EhQQ9F-L/product/customizables`
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },

      body: JSON.stringify({
        ri_short_name: shortName,
        city: capitalizeFirstLetter(city),
        paging: { page: pageNumber, per_page: 10 },
      }),
    })
    if (!response.ok) throw createFetchError("Failed to fetch AddOns", response)
    return response.json()
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch AddOns",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchComboProducts = async (
  riName: string,
  city: string,
  pageNumber: number = 1,
) => {
  try {
    const url = `https://api.sharepal.in/api:EhQQ9F-L/product/combos`
    const result = await customFetch(url, {
      method: "POST",
      body: JSON.stringify({
        ri_name: riName,
        city: capitalizeFirstLetter(city),
        paging: { page: pageNumber, per_page: 10 },
      }),
    })
    return result
  } catch (error) {
    console.error("Failed to fetch combo products:", error)
    return []
  }
}
