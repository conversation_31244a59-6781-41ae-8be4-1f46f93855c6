import { capitalizeFirstLetter } from "@/functions/small-functions"
import { customFetch } from "@/utils/customFetch"
import { handleFetchError } from "."

interface CityDescription {
  id: number
  city: string
  desc: string
}

export const fetchCitySeoDescription = async (
  city: string,
): Promise<CityDescription | null> => {
  const url = `https://api.sharepal.in/api:EhQQ9F-L/seo-desc/home/<USER>
  try {
    const data = await customFetch(url)
    if (data) {
      return data
    } else {
      return null
    }
    // if (!response.ok) throw new Error('Failed to fetch City Seo Description')

    // const data: CityDescription = await response.json() // Correctly typing the response data
    // return data
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch City Seo Description",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchGeneralSeoContent = async (
  city: string,
  name: string,
): Promise<{ content: string }> => {
  const url = `https://api.sharepal.in/api:EhQQ9F-L/seo/get-seo-content?city=${city}&name${name}`
  try {
    const data = await customFetch(url)
    if (data) {
      return data
    } else {
      return { content: "" }
    }
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch General Seo Content",
      error instanceof Response ? error : undefined,
    )
  }
}
