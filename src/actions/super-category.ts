import { SuperCategory } from "@/types/super-category"
import { customFetch } from "@/utils/customFetch"
import { handleFetchError } from "."

export const fetchSuperCategories = async (): Promise<SuperCategory[]> => {
  try {
    const data = await customFetch(
      "https://api.sharepal.in/api:EhQQ9F-L/super-category",
    )
    return data
  } catch (error) {
    throw handleFetchError(
      "An error occurred while fetching super categories",
      error instanceof Response ? error : undefined,
    )
  }
}
