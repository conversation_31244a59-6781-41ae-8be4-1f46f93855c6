import { loadScript } from "@/functions/loadScript"

export const initializeSEON = async () => {
  if (typeof window === "undefined") return
  const SEON_SCRIPT_URL = "https://cdn.seondf.com/js/v6/agent.umd.js"

  try {
    // Load the SEON script
    await loadScript(SEON_SCRIPT_URL)

    // Check if SEON is available on the window
    if (window.seon) {
      await window.seon.init()

      const config = {
        geolocation: {
          canPrompt: false,
        },
        networkTimeoutMs: 2000,
        fieldTimeoutMs: 2000,
        region: "eu",
        silentMode: true,
      }

      // Get the session details
      const session = await window.seon.getSession(config)
      return session // Return the session object
    } else {
      throw new Error("SEON SDK is not available")
    }
  } catch (error) {
    console.error("Error initializing SEON:", error)
    throw error
  }
}
