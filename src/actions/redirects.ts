import { customFetchPost } from "@/utils/customFetch"

export interface RedirectCategory {
  category_id: number
  category_name: string
  category_short_name: string
  super_category_url: string
}

//redirect cateogry
export const redirectCategory = async (
  categoryUrl: string,
): Promise<RedirectCategory | null> => {
  try {
    const result = await customFetchPost(
      "https://api.sharepal.in/api:EhQQ9F-L/redirect/category-to-supercategory",
      {
        category_url: categoryUrl,
      },
    )
    return result as RedirectCategory
  } catch (error) {
    console.error(error)
    return null
  }
}
