// await api.post('/spcreators/add', userData)

import { CreatorFormData } from "@/lib/validations/creator.schema"

export const handleCreatorForm = async (data: CreatorFormData) => {
  try {
    const response = await fetch(
      "https://api.sharepal.in/api:EhQQ9F-L/spcreators/add",
      {
        method: "POST",
        body: JSON.stringify(data),
      },
    )
    return response
  } catch (error) {
    console.log(error)
  }
}
