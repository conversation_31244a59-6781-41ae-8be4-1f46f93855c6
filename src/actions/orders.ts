import { CartI<PERSON>, RentOrder } from "@/types"
import {
  APIOrderResponse,
  CancellationReason,
  CancellationReasons,
  FilterPayload,
  FilterValue,
  OrderItemsResponse,
  OrderStatusResponse,
  OrderSummary,
  OrderTimelineResponse,
} from "@/types/order"
import {
  fetchWithAuth,
  fetchWithAuthGet,
  fetchWithAuthPost,
} from "@/utils/fetchWithAuth"

export const fetchOrderDetails = async (
  orderId: string,
): Promise<OrderSummary | null> => {
  try {
    const data = await fetchWithAuth(
      "https://api.sharepal.in/api:AIoqxnqr/order/order-summary",
      {
        method: "POST",
        body: JSON.stringify({ order_id: orderId }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
  //   const data = await fetchWithAuth(`/order/${orderId}`)
  //   return data
}
export const fetchOrder = async (
  orderId: string,
): Promise<{ order: RentOrder; cart_items: CartItem[] } | null> => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/order/order-details`,
      {
        method: "POST",
        body: JSON.stringify({ order_id: orderId }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
}

export const fetchOrderWithoutCart = async (
  orderId: string,
): Promise<{ order: RentOrder; cart_items: CartItem[] } | null> => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/order/fetch-without-cart`,
      {
        method: "POST",
        body: JSON.stringify({ order_id: orderId }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
}

export const fetchOrderAgain = async (
  orderId: string,
  delivery_date: Date,
): Promise<{ order: RentOrder; cart_items: CartItem[] } | null> => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/order/again`,
      {
        method: "POST",
        body: JSON.stringify({
          order_id: orderId,
          delivery_date: delivery_date,
        }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
}

export const fetchOrderStatus = async (
  orderId: string,
): Promise<OrderStatusResponse | null> => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/order/order-status`,
      {
        method: "POST",
        body: JSON.stringify({ order_id: orderId }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
}

export const fetchOrderTimeline = async (
  orderId: string,
): Promise<OrderTimelineResponse | null> => {
  try {
    //old
    // const data = await fetchWithAuth(`/order_status_update`, {
    //   method: 'POST',
    //   body: JSON.stringify({ order_id: orderId }),
    // })

    //new
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:qsuyzexA/order_timeline`,
      {
        method: "POST",
        body: JSON.stringify({ order_id: orderId }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
}

export const fetchOrderItems = async (
  orderId: string,
  page?: number,
): Promise<OrderItemsResponse | null> => {
  try {
    const url = "https://api.sharepal.in/api:AIoqxnqr/order/order-items"
    const response = await fetchWithAuth(url, {
      method: "POST",
      body: JSON.stringify({
        order_id: orderId,
        paging: { page: page ?? 1, per_page: 10 },
      }),
    })
    return response as OrderItemsResponse
  } catch (error) {
    console.error("Error fetching order items", error)
    return null
  }
}

export const fetchCancellationReasons = async (): Promise<
  CancellationReason[]
> => {
  const data = await fetchWithAuthGet<CancellationReasons[]>(
    "https://api.sharepal.in/api:AIoqxnqr/website/cancellation-reasons",
  )
  return data
}

export const fetchOrders = async (
  page: number,
  perPage: number = 5,
  filters: FilterValue[],
): Promise<APIOrderResponse | null> => {
  const payload: FilterPayload = {
    order_type: "rent",
    paging: {
      page,
      per_page: perPage,
    },
    filters,
  }

  const response = await fetchWithAuthPost(
    "https://api.sharepal.in/api:AIoqxnqr/order/all",
    payload,
  )
  try {
    return response as APIOrderResponse
  } catch (error) {
    console.error("Error fetching orders:", error)
    return null
  }
}
