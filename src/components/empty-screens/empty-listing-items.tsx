import SpImage from "@/shared/SpImage/sp-image"
import { Typography } from "../ui/typography"

const EmptyListingItems = () => (
  <div className='mt-3 flex h-full w-full flex-col items-center justify-center gap-3 rounded-2xl bg-gray-100 px-4 py-10 md:mt-6 md:px-6 md:py-12'>
    <SpImage
      src='https://images.sharepal.in/misc/hard-coded/sharepal/listing-empty.svg'
      alt='empty-listing-img'
      width={350}
      height={350}
      className='h-full w-full'
      containerClassName='md:w-[150px] md:h-[150px] w-[100px] h-[100px]'
    />
    <Typography as={"h6"} className='text-h4 text-neutral-900 md:text-h1'>
      Exciting Things are on the Way! 🚀
    </Typography>
    <Typography
      as={"p"}
      className='text-center text-b4 text-neutral-400 md:text-sh1'
    >
      We&apos;re gearing up with new products—stay tuned for surprises! In the
      meantime, explore our other great finds.
    </Typography>
  </div>
)

export default EmptyListingItems
