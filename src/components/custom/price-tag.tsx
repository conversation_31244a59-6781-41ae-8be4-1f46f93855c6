import { Typography } from "../ui/typography"

interface PriceTagProps {
  amount: number
  className?: string
}

export function PriceTag({ amount, className }: PriceTagProps) {
  const formattedAmount = new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    maximumFractionDigits: 0,
  }).format(amount)

  return (
    <Typography
      as={"p"}
      className={`text-h4 text-gray-900 sm:text-h3 md:text-h2 lg:text-h1 ${className}`}
    >
      {formattedAmount}
    </Typography>
  )
}
