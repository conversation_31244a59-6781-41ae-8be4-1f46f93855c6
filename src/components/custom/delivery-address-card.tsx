"use client"

import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { cn } from "@/lib/utils"
import { DeliveryVanOutlinedIcon } from "sharepal-icons"
import { Typography } from "../ui/typography"

interface RadioOption {
  value: string
  label: string
  subLabel?: string
  description?: string
  userLabel?: string
}

interface SimpleRadioButtonProps {
  options: RadioOption[]
  selectedValue: string
  onChange: (value: string) => void
  className?: string
  showAll?: boolean
}

function DeliveryAddressCard({
  options,
  selectedValue,
  onChange,
  className,
  showAll,
}: SimpleRadioButtonProps) {
  // console.log('options', options)
  // console.log('selectedValue', selectedValue)

  return options?.length === 0 ? (
    <div className='flex h-[90%] items-center justify-center'>
      No addresses found. Please add a new address.
    </div>
  ) : (
    <RadioGroup
      className={cn("w-full", className)}
      value={selectedValue}
      onValueChange={onChange}
    >
      {(showAll
        ? options
        : options.length > 2
          ? options.slice(0, 2)
          : options
      ).map((option) => {
        const isSelected = option.value === selectedValue
        return (
          <div
            key={option.value}
            className={cn(
              "relative flex max-h-max min-h-24 w-full cursor-pointer items-start gap-2 overflow-hidden rounded-2xl border-2 border-input bg-neutral-100 p-3 transition-colors has-[[data-state=checked]]:border-primary-150",
              isSelected && "bg-primary-100",
            )}
          >
            <RadioGroupItem
              value={option.value}
              id={`radio-${option.value}`}
              aria-describedby={`radio-${option.value}-description`}
              className='after:absolute after:inset-0'
              // className="order-1 after:absolute after:inset-0"
            />
            <div className='grid grow gap-2'>
              <Label
                className='line-clamp-6 !text-b4'
                htmlFor={`radio-${option.value}`}
              >
                <Typography as={"span"} className='!text-sh4'>
                  {option.userLabel}
                </Typography>{" "}
                <br />
                {option.label}
                {/* {option.subLabel && (
                  <Typography as={'span'} className=" ">
                    ({option.subLabel})
                  </Typography>
                )} */}
                {option.description && (
                  <Typography
                    as={"span"}
                    id={`radio-${option.value}-description`}
                    className=''
                  >
                    {option.description}
                  </Typography>
                )}
              </Label>
            </div>
            <div className='absolute -bottom-1 -right-2 z-[0]'>
              <DeliveryVanOutlinedIcon
                className={cn(
                  "h-10 w-10 scale-x-[-1] !text-gray-200",
                  isSelected && "!text-primary-150",
                )}
              />
            </div>
          </div>
        )
      })}
    </RadioGroup>
  )
}

export default DeliveryAddressCard
