import { cn } from "@/lib/utils"
import { Badge } from "../ui/badge"

const RenderTag = ({
  out_of_stock,
  tag,
  absolute = true,
}: {
  out_of_stock: boolean
  tag: string
  absolute?: boolean
}) => {
  if (out_of_stock) return null

  const tagColors = {
    Best: " text-decorative-pink  border-decorative-pink  ",
    New: " text-decorative-blue border-decorative-blue",
    Trending: " text-decorative-orange border-decorative-orange",
  }
  if (!tag) return <></>
  return (
    <Badge
      variant={"outline"}
      className={cn(
        `left-2 top-2 rounded-lg border px-1.5 py-0 text-[10px] md:left-3 md:top-3 md:border-2 md:px-2.5 md:py-0.5 md:text-xs`,
        tagColors[tag as keyof typeof tagColors],
        absolute && "absolute",
      )}
    >
      {tag}
    </Badge>
  )
}

export default RenderTag
