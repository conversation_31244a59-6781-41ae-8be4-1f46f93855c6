import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

export type Faq = {
  id: number
  question: string
  answer: string
  category: string
  faq_type: string
}

export function FAQS({ faqs, limit }: { faqs: Faq[]; limit?: number }) {
  return (
    <Accordion type='single' collapsible className='w-full'>
      {faqs.slice(0, limit).map((faq, index) => (
        <SingleFaq key={index} faq={faq} />
      ))}
    </Accordion>
  )
}

const SingleFaq = ({ faq }: { faq: Faq }) => (
  <AccordionItem
    value={`item-${faq.question}`}
    className='rounded-xl border-none bg-gray-100 transition-colors duration-300 hover:bg-gray-150'
  >
    <AccordionTrigger className='px-4 text-left text-sh4 md:text-sh3'>
      {faq.question}
    </AccordionTrigger>
    <AccordionContent className='px-4 pb-4 text-xs text-gray-700 md:text-sm'>
      {faq.answer}
    </AccordionContent>
  </AccordionItem>
)
