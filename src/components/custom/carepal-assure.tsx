import { fetchFaqs } from "@/actions/product"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { useToggle } from "@/hooks/use-toggle"
import SpImage from "@/shared/SpImage/sp-image"
import { useQuery } from "@tanstack/react-query"
import { InfoCircleOutlinedIcon, TickCircleOutlinedIcon } from "sharepal-icons"
import CarepalInformation from "../checkout/carepal/carepal-information"
import { Button } from "../ui/button"

const benefits = [
  "Accidental damages covered.",
  "Easy Opt-in at Checkout.",
  "Waiver Auto-applied in case of damages.",
]

export function CarepalAssure() {
  const { value: isInfoOpen, onOpen, onClose } = useToggle()
  // fetch carepal faqs
  const { data: faqs } = useQuery({
    queryKey: ["carepa-faqs"],
    refetchOnWindowFocus: false,

    queryFn: async () => await fetchFaqs("carepal", "", "carepal"),
  })

  return (
    <>
      <Card className='rounded-xl border-carepal-lighter bg-gradient-to-b from-white to-[rgba(255,217,230,0.36)] md:rounded-2xl md:p-2'>
        <CardHeader className='relative flex-row items-center justify-between space-y-0 p-0'>
          <div className='flex items-center gap-2'>
            <SpImage
              src={`https://images.sharepal.in/carepal/carepal-secure.svg`}
              height={45}
              width={200}
              className='h-full w-40 px-2 py-1 pb-2'
            />

            <CardTitle className='text-sm font-bold'>
              <span>Damages Covered</span>
              <br />
              <span className='text-carepal-dark'>Rest Assured. </span>
            </CardTitle>
          </div>
          {/* infocircle */}

          <Button
            variant='ghost'
            className='absolute right-2 top-2 h-8 w-8 p-0'
            onClick={onOpen}
            aria-label='Carepal Information'
          >
            <InfoCircleOutlinedIcon className='h-4 w-4 text-muted-foreground' />
          </Button>
        </CardHeader>
        <CardContent className='p-2 pt-0'>
          <ul className='flex flex-col gap-2'>
            {benefits.map((benefit) => (
              <li
                key={benefit}
                className='flex items-center gap-2 text-b4 text-primary-900'
              >
                <TickCircleOutlinedIcon className='t h-4 w-4 fill-carepal-dark text-carepal-dark' />
                {benefit}
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>

      <CarepalInformation
        isOpen={isInfoOpen}
        onOpenChange={onClose}
        faqs={faqs ?? []}
        showActionButtons={false}
      />
    </>
  )
}
