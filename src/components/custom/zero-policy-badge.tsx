import Image from "next/image"
import React from "react"
import { IconLogoLeft, IconLogoRight } from "../Icons"
import { cn } from "@/lib/utils"

type Props = {
  className?: string
}

const ZeroPolicyBadge = ({ className }: Props) => (
  <div
    className={cn(
      "relative h-[40px] w-[150px] skew-x-[-15deg] rounded-[5.8px] bg-zero-policy-title-card lg:h-[54px] lg:w-[244px] lg:rounded-[12px]",
      className,
    )}
  >
    <div className='flex w-full skew-x-[15deg]'>
      <Image
        src={"https://images.sharepal.in/Static+Images/1731831867374-Star.png"}
        width={100}
        height={10}
        alt='Star'
        className='-translate-x-2 -translate-y-3 max-lg:h-16 max-lg:w-16 lg:-translate-x-2 lg:-translate-y-4'
      />
      <div className='flex w-full -translate-x-4 flex-col items-start p-0 lg:-translate-x-6'>
        <div className='flex -translate-x-8 scale-50 items-start justify-start lg:-translate-x-3 lg:scale-75'>
          <IconLogoLeft />
          <IconLogoRight color='#fff' />
        </div>
        <span className='flex h-max w-full -translate-y-4 items-center gap-1 text-pretty bg-zero-policy-title-card-text bg-clip-text p-0 text-[15px] font-medium italic leading-[34px] text-[#00000000] lg:-translate-y-2 lg:text-2xl'>
          <span className='font-[800]'>ZERO</span>Policy
        </span>
      </div>
    </div>
  </div>
)

export default ZeroPolicyBadge
