import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { cn } from "@/lib/utils"
import {
  ControllerRenderProps,
  FieldPath,
  FieldValues,
  UseFormReturn,
} from "react-hook-form"

type WrapperProps<T extends FieldValues> = {
  form: UseFormReturn<T>
  name: FieldPath<T>
  label?: string
  description?: string
  isRequired?: boolean
  renderControl: (
    field: ControllerRenderProps<T, FieldPath<T>>,
  ) => React.ReactNode
  wrapperClass?: string
  labelClass?: string
}

export function FormFieldWrapper<T extends FieldValues>({
  form,
  name,
  label,
  description,
  isRequired,
  renderControl,
  wrapperClass,
  labelClass,
}: WrapperProps<T>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn("w-full", wrapperClass)}>
          {label && (
            <FormLabel
              className={cn(
                isRequired && "after:text-destructive-500 after:content-['*']",
                labelClass,
              )}
            >
              {label}
            </FormLabel>
          )}
          <FormControl>{renderControl(field)}</FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
