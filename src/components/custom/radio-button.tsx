import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { cn } from "@/lib/utils"

interface RadioOption {
  id: string
  value: string
  label: string
}

interface RadioButtonProps {
  options: RadioOption[]
  value: string
  onValueChange: (value: string) => void
  className?: string
}

export function RadioButton({
  options,
  value,
  onValueChange,
  className,
}: RadioButtonProps) {
  return (
    <RadioGroup
      onValueChange={onValueChange}
      defaultValue={value}
      className={cn("grid-cols-1 md:grid-cols-2", className)}
    >
      {options.map((item) => (
        <div
          key={item.id}
          className='relative flex flex-col gap-4 rounded-2xl border-2 border-input border-neutral-150 bg-neutral-150 p-4 has-[[data-state=checked]]:border-secondary-500 has-[[data-state=checked]]:bg-gray-100'
        >
          <div className='flex items-center gap-3 md:gap-5'>
            <RadioGroupItem
              id={item.id}
              value={item.value}
              className='after:absolute after:inset-0'
            />
            <Label htmlFor={item.id}>{item.label}</Label>
          </div>
        </div>
      ))}
    </RadioGroup>
  )
}
