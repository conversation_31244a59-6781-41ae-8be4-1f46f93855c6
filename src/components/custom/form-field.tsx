import React from "react"
import { <PERSON>FormReturn, FieldPath, FieldValues } from "react-hook-form"
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"

interface FormFieldProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>
  name: FieldPath<TFieldValues>
  label: string
  placeholder: string
  type?: string
  required?: boolean
  className?: string
}

export function CustomFormField<TFieldValues extends FieldValues>({
  form,
  name,
  label,
  placeholder,
  type = "text",
  required = false,
  className = "",
}: FormFieldProps<TFieldValues>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel className='!text-b4'>
            {label} {required && <span className='text-red-500'>*</span>}
          </FormLabel>
          <FormControl>
            <Input
              {...field}
              type={type}
              placeholder={placeholder}
              className={`h-12 rounded-xl border-neutral-200 md:text-base ${className}`}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
