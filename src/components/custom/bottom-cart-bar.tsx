import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { moneyFormatter } from "@/functions/small-functions"
import { useCalendarStore, useRentalStore } from "@/store/rental-store"
import { RentalItem } from "@/types"
import {
  motion,
  useScroll,
  useSpring,
  useTransform,
  type MotionValue,
} from "framer-motion"
import { AlertCircle, Calendar, Loader2, ShoppingCart } from "lucide-react"
import { useRef } from "react"
import { InfoCircleFilledIcon } from "sharepal-icons"

export interface BottomCartBarProps {
  hideOnScroll?: boolean
  totalRent: number
  product: RentalItem
  setShowCart: (show: boolean) => void
  // handleAddToCart: () => Promise<void>
  handleAddToCart: () => void
  isCartAddtionLoading: boolean
  restrictTransit: boolean
}

export function BottomCartBar({
  hideOnScroll = false,
  totalRent,
  product,
  handleAddToCart,
  restrictTransit,
  isCartAddtionLoading,
}: BottomCartBarProps) {
  const { scrollY } = useScroll()
  const lastScrollY = useRef(0)
  const { openCalendar } = useCalendarStore()
  const { total_days } = useRentalStore()

  const translateY: MotionValue = useTransform(scrollY, (current) => {
    const isScrollingDown = current > lastScrollY.current
    lastScrollY.current = current
    return hideOnScroll && isScrollingDown ? 100 : 0
  })

  const smoothTranslateY = useSpring(translateY, {
    damping: 20,
    stiffness: 120,
  })

  return (
    <motion.div
      style={{ translateY: smoothTranslateY }}
      className='fixed bottom-0 left-0 right-0 z-50'
    >
      <div className='safe-area-bottom flex items-center justify-between gap-4 border-t bg-gray-100 px-4 py-3 shadow-sm'>
        {total_days > 0 ? (
          <>
            <div className='flex flex-col'>
              <div className='flex items-center gap-1 text-xs text-muted-foreground'>
                Rent for {/* {total_days} days{' '} */}
                {total_days ? (
                  <span className='font-bold text-primary-900'>
                    {" "}
                    {total_days} {total_days > 1 ? "days" : "day"}
                  </span>
                ) : (
                  <Skeleton className='h-4 w-16' />
                )}
              </div>
              {totalRent ? (
                <span className='text-2xl font-bold'>
                  {moneyFormatter(totalRent)}
                </span>
              ) : (
                <Skeleton className='mt-1 h-5 w-24' />
              )}
            </div>
            {product.out_of_stock ? (
              <Button
                variant='neutral'
                // onClick={(e) => {
                //   handleNotifyMe(e, product.ri_short_name)
                // }}
                size='lg'
                // className='w-max border-primary-900 bg-gray-100 px-4 py-3 text-base font-bold'
                className='w-max bg-neutral-200 px-4 py-3 text-center text-base font-bold'
              >
                {/* Contact Support */}
                <InfoCircleFilledIcon className='h-5 w-5' />
                Out of Stock
              </Button>
            ) : restrictTransit ? (
              <Button
                size='lg'
                className='w-max bg-primary-900 px-4 py-3 text-base font-bold hover:bg-primary-800'
                onClick={openCalendar}
              >
                <Calendar className='mr-2 h-4 w-4' />
                Change Dates
              </Button>
            ) : (
              <Button
                size='lg'
                disabled={isCartAddtionLoading}
                className='w-max min-w-36 bg-primary-500 px-4 py-3 text-base font-bold hover:bg-primary-400'
                onClick={handleAddToCart}
              >
                {isCartAddtionLoading ? (
                  <Loader2 className='h-5 w-5 animate-spin' />
                ) : (
                  <>
                    <ShoppingCart className='h-6 w-6' />
                    Add to Cart
                  </>
                )}
              </Button>
            )}
          </>
        ) : (
          <>
            <div className='flex items-center gap-2 text-xs text-destructive-700'>
              <AlertCircle className='h-4 w-4 text-destructive-500' />
              <span>Select rental dates to view prices</span>
            </div>
            <Button
              size='lg'
              className='w-max bg-primary-900 px-4 py-3 text-base font-bold hover:bg-primary-800'
              onClick={openCalendar}
            >
              <Calendar className='mr-2 h-4 w-4' />
              Select Dates
            </Button>
          </>
        )}
      </div>
    </motion.div>
  )
}
