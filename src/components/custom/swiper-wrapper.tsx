"use client"

import { ChevronLeft, ChevronRight } from "lucide-react"
import { ReactNode, useRef } from "react"
import type { Swiper as SwiperType } from "swiper"
import {
  Autoplay,
  FreeMode,
  Mousewheel,
  Navigation,
  Pagination,
} from "swiper/modules"
import { Swiper } from "swiper/react"

// Import Swiper styles
import "swiper/css"
import "swiper/css/navigation"
import "swiper/css/pagination"

interface SwiperWrapperProps {
  children: ReactNode
  // Navigation controls
  showNavigation?: boolean
  showPagination?: boolean
  // Slides configuration
  slidesPerView?: number | "auto"
  slidesPerGroup?: number
  spaceBetween?: number
  // Breakpoints
  breakpoints?: Record<
    number,
    {
      slidesPerView?: number | "auto"
      slidesPerGroup?: number
      spaceBetween?: number
    }
  >
  // Mousewheel
  enableMousewheel?: boolean
  //Free Mode
  enableFreeMode?: boolean
  // Autoplay
  enableAutoplay?: boolean
  autoplayDelay?: number
  // Styling
  containerClassName?: string
  navigationPrevClassName?: string
  navigationNextClassName?: string
  // Callbacks
  onSlideChange?: (swiper: SwiperType) => void
  onInit?: (swiper: SwiperType) => void

  // Moving to specific Card
  moveToCard?: boolean
  moveToCardIndex?: number

  //

  centeredSlides?: boolean
  loop?: boolean
}

export function SwiperWrapper({
  children,
  showNavigation = true,
  showPagination = false,
  slidesPerView = 1,
  slidesPerGroup = 1,
  spaceBetween = 20,
  breakpoints,
  enableMousewheel = true,
  enableFreeMode = true,
  enableAutoplay = false,

  moveToCard = false,
  moveToCardIndex = 0,

  autoplayDelay = 3000,
  containerClassName = "relative w-full",
  navigationPrevClassName = "absolute top-1/2 left-4 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-gray-100 shadow-md",
  navigationNextClassName = "absolute top-1/2 right-4 z-10 flex h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-gray-100 shadow-md",
  onSlideChange,
  centeredSlides = false,
  loop = false,
  onInit,
}: SwiperWrapperProps) {
  const swiperRef = useRef<SwiperType | null>(null)

  const defaultBreakpoints = {
    640: { slidesPerView: 2, slidesPerGroup: 2 },
    768: { slidesPerView: 3, slidesPerGroup: 3 },
    1024: { slidesPerView: 4, slidesPerGroup: 4 },
  }

  const modules = [
    Navigation,
    ...(enableMousewheel ? [Mousewheel] : []),
    ...(showPagination ? [Pagination] : []),
    ...(enableAutoplay ? [Autoplay] : []),
    ...(enableFreeMode ? [FreeMode] : []),
  ]

  const handlePrev = () => {
    swiperRef.current?.slidePrev()
  }

  const handleNext = () => {
    swiperRef.current?.slideNext()
  }

  const moveToCardFn = (cardIndex: number) => {
    if (swiperRef.current) {
      swiperRef.current.slideTo(cardIndex)
    }
  }

  if (moveToCard) {
    moveToCardFn(moveToCardIndex)
  }

  return (
    <div className={containerClassName}>
      <Swiper
        modules={modules}
        spaceBetween={spaceBetween}
        slidesPerView={slidesPerView}
        slidesPerGroup={slidesPerGroup}
        centeredSlides={centeredSlides}
        loop={loop}
        mousewheel={enableMousewheel ? { forceToAxis: true } : false}
        autoplay={
          enableAutoplay
            ? {
                delay: autoplayDelay,
                disableOnInteraction: true,
              }
            : false
        }
        freeMode={
          enableFreeMode
            ? {
                enabled: true,
                sticky: false,
              }
            : false
        }
        pagination={showPagination ? { clickable: true } : false}
        onBeforeInit={(swiper) => {
          swiperRef.current = swiper
          onInit?.(swiper)
        }}
        onSlideChange={onSlideChange}
        breakpoints={breakpoints || defaultBreakpoints}
        className='mySwiper'
      >
        {children}
      </Swiper>

      {showNavigation && (
        <>
          <button onClick={handlePrev} className={navigationPrevClassName}>
            <ChevronLeft className='h-6 w-6' />
          </button>
          <button onClick={handleNext} className={navigationNextClassName}>
            <ChevronRight className='h-6 w-6' />
          </button>
        </>
      )}
    </div>
  )
}

// // Usage example:
// export function ExampleUsage({ data, city }: { data: any[]; city: string }) {
//     return (
//       <SwiperWrapper
//         showNavigation={true}
//         showPagination={true}
//         slidesPerView={1}
//         spaceBetween={30}
//         enableMousewheel={true}
//         enableAutoplay={false}
//         containerClassName="relative w-full py-12"
//         breakpoints={{
//           640: { slidesPerView: 2 },
//           768: { slidesPerView: 3 },
//           1024: { slidesPerView: 4 },
//         }}
//       >
//         {data.map((item, index) => (
//           <SwiperSlide key={index}>
//             {/* Your content component here */}
//             <div>{item.name}</div>
//           </SwiperSlide>
//         ))}
//       </SwiperWrapper>
//     );
//   }
