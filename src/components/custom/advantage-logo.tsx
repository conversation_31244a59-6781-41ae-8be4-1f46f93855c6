import Image from "next/image"
import React from "react"
import { IconLogoLeft, IconLogoRight } from "../Icons"

const AdvantageLogo = () => (
  <div className='relative h-[40px] w-[150px] skew-x-[-15deg] rounded-[5.8px] bg-zero-policy-title-card md:h-[54px] md:w-[244px] md:rounded-[12px]'>
    <div className='flex w-full skew-x-[15deg]'>
      <Image
        src={"https://images.sharepal.in/Static+Images/1731831867374-Star.png"}
        width={100}
        height={10}
        alt='Star'
        className='-translate-x-2 -translate-y-3 max-md:h-16 max-md:w-16 md:-translate-x-2 md:-translate-y-4'
      />
      <div className='flex w-full -translate-x-4 flex-col items-start p-0 md:-translate-x-6'>
        <div className='flex -translate-x-8 scale-50 items-start justify-start md:-translate-x-3 md:scale-75'>
          <IconLogoLeft />
          <IconLogoRight color='#fff' />
        </div>
        <span className='flex h-max w-full -translate-y-4 items-center gap-1 text-pretty bg-zero-policy-title-card-text bg-clip-text p-0 text-[15px] font-medium italic leading-[34px] text-[#00000000] md:-translate-y-2 md:text-2xl'>
          <span className='font-[800]'>ZERO</span>Policy
        </span>
      </div>
    </div>
  </div>
)

export default AdvantageLogo
