"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { useCalendarStore, useRentalStore } from "@/store/rental-store"
import { fadeInCalendar, scaleIn } from "@/utils/animation-variants"
import { disableDates, formatShortDate } from "@/utils/date-logics"
import { addDays } from "date-fns"
import { motion } from "framer-motion"

import { trackCalenderOpened } from "@/lib/gtag-event"
import { useEffect, useState } from "react"
import { toast } from "sonner"
import { DateSelector } from "../checkout/order-summary-new/date-selector"
import { IconPickupDate } from "../Icons"
import OfferIcon from "../Icons/offer-icon"
import { AdaptiveWrapper } from "../modals/adaptive-wrapper"
import { Separator } from "../ui/separator"

interface RentalPeriodSelectorProps {
  isCalenderOpen?: boolean
  closeCalendarFn?: () => void
  onContinue?: () => void
}

export function RentalPeriodSelectorMobile({
  closeCalendarFn,
  isCalenderOpen,
  onContinue,
}: RentalPeriodSelectorProps) {
  const { isOpen, closeCalendar } = useCalendarStore()
  const {
    delivery_date,
    pickup_date,
    setDeliveryDate,
    setPickupDate,
    total_days,
    min_th,
  } = useRentalStore()

  const [isDeliveryCalendarOpen, setIsDeliveryCalendarOpen] = useState(false)
  const [isPickupCalendarOpen, setIsPickupCalendarOpen] = useState(false)

  const handleDeliveryDateSelect = (date: Date | undefined) => {
    try {
      if (date) {
        setDeliveryDate(date)
        setIsDeliveryCalendarOpen(false)
        // toast.success("Delivery date selected")
      }
    } catch (error) {
      console.error("Error selecting delivery date:", error)
      toast.error("Failed to select delivery date. Please try again.")
    }
  }

  const handlePickupDateSelect = (date: Date | undefined) => {
    try {
      if (date) {
        setPickupDate(date)
        setIsPickupCalendarOpen(false)
        // toast.success("Pickup date selected")
      }
    } catch (error) {
      console.error("Error selecting pickup date:", error)
      toast.error("Failed to select pickup date. Please try again.")
    }
  }

  const handleContinue = () => {
    try {
      closeCalendar()
      if (closeCalendarFn) closeCalendarFn()
      if (onContinue) onContinue()
      // toast.success("Dates confirmed successfully")
    } catch (error) {
      console.error("Error handling continue:", error)
      toast.error("Something went wrong. Please try again.")
    }
  }
  // const queryClient = useQueryClient()

  // const { product } = useParams()
  // useEffect(() => {
  //   if ((isOpen || isCalenderOpen) && !product)
  //     queryClient.invalidateQueries({ queryKey: ["cityMinTh"] })
  // }, [isOpen, isCalenderOpen, product])

  useEffect(() => {
    if (isOpen || isCalenderOpen) trackCalenderOpened()
  }, [isOpen, isCalenderOpen])
  return (
    <AdaptiveWrapper
      open={isOpen || isCalenderOpen}
      onOpenChange={closeCalendarFn ?? closeCalendar}
      title='Select your Dates'
      className='custom-scrollbar-black h-full max-h-[85vh] md:hidden'
    >
      <div className='flex h-full flex-col gap-6 bg-neutral-150 pt-4 lg:flex-row'>
        <motion.div
          initial='initial'
          animate='animate'
          exit='exit'
          variants={scaleIn}
          className='relative flex-1 space-y-6 px-6'
        >
          <div className='grid gap-4 md:grid-cols-2'>
            <div className='space-y-3 md:space-y-4'>
              <div className='relative'>
                <DateSelector
                  label='Delivery Date'
                  date={delivery_date}
                  onSelect={handleDeliveryDateSelect}
                  isOpen={isDeliveryCalendarOpen}
                  setIsOpen={setIsDeliveryCalendarOpen}
                  disabledDate={(date) =>
                    disableDates(date, min_th, "delivery")
                  }
                  timeRange='We deliver between: 3pm - 10pm'
                />
              </div>
              <div className='relative'>
                <DateSelector
                  label='Pickup Date'
                  date={pickup_date}
                  onSelect={handlePickupDateSelect}
                  isOpen={isPickupCalendarOpen}
                  setIsOpen={setIsPickupCalendarOpen}
                  disabledDate={(date) => {
                    // First check if the date is disabled by the global disableDates function
                    if (disableDates(date, min_th, "pickup", delivery_date))
                      return true

                    // Then check if it's before or equal to the delivery date
                    if (delivery_date && date <= delivery_date) return true

                    // Disable the next day after delivery date (require minimum 2-day gap)
                    if (delivery_date && date <= addDays(delivery_date, 1))
                      return true

                    return false
                  }}
                  timeRange='We pickup between: 9am - 1pm'
                />
              </div>
            </div>
          </div>

          {/* Your Rental Period */}
          <div className='flex h-[82px] w-full flex-col gap-1.5'>
            <div className='flex-1 text-sm font-medium leading-[20px] text-neutral-900'>
              Your Rental Period:
            </div>

            <div className='flex w-full items-end justify-start gap-3 rounded-2xl border-2 border-neutral-200 bg-gray-100 px-3 py-2.5'>
              <div className='flex items-end gap-1'>
                <div className='text-4xl font-bold leading-[40px] text-neutral-800'>
                  {total_days.toString().padStart(2, "0")}
                </div>
                <div className='flex items-center justify-center gap-1 pb-0.5 pt-0.5'>
                  <div className='text-sm font-medium leading-[20px] text-neutral-500'>
                    {total_days > 1 ? "Days" : "Day"}
                  </div>
                </div>
              </div>

              <Separator
                orientation='vertical'
                className='w-[2px] rounded-full'
              />

              <div className='flex flex-1 flex-col items-start justify-center gap-1'>
                <div className='w-full text-xs font-medium leading-[16px] text-neutral-900'>
                  Chargeable Period:
                </div>
                <div className='flex w-full items-center gap-2'>
                  <div className='flex h-4 w-4 items-center justify-center'>
                    <IconPickupDate />
                  </div>
                  <div className='flex-1 text-sm font-medium leading-[20px] text-neutral-300'>
                    {delivery_date && pickup_date ? (
                      <span>
                        {formatShortDate(addDays(delivery_date, 1))} -{" "}
                        {formatShortDate(addDays(pickup_date, -1))}
                      </span>
                    ) : (
                      "--"
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Gradient Box */}
          <motion.div
            initial='initial'
            animate='animate'
            exit='exit'
            variants={fadeInCalendar}
            className='overflow-hidden rounded-xl bg-primary-900 pb-5 pt-0 text-white'
          >
            <div className='mb-4 flex items-center gap-3'>
              <motion.div whileHover={{ scale: 1.1 }}>
                <OfferIcon />
              </motion.div>
              <span className='flex h-max w-full items-center gap-1 text-pretty bg-zero-policy-title-card-text bg-clip-text font-bold italic text-[#00000000] md:text-2xl'>
                How to select dates?
              </span>
            </div>

            <div className='space-y-3 px-5 text-sm'>
              <motion.p
                whileHover={{ x: 5 }}
                className='flex items-start justify-start gap-2 text-xs font-semibold md:gap-4'
              >
                <span className='text-lg font-extrabold italic text-secondary-500 md:text-2xl'>
                  1
                </span>
                Delivery time is 3-8pm. Return time is 10am-2pm. Delivery and
                Return dates are not chargeable. Select the dates accordingly
              </motion.p>
              <motion.p
                whileHover={{ x: 5 }}
                className='flex items-start justify-start gap-2 text-xs font-semibold md:gap-4'
              >
                <span className='text-lg font-extrabold italic text-secondary-500 md:text-2xl'>
                  2
                </span>
                When you rent longer, the per day rental reduces. Select your
                dates accordingly.
              </motion.p>
            </div>
          </motion.div>
        </motion.div>

        {/* Continue Button */}
        <motion.div
          initial='initial'
          animate='animate'
          exit='exit'
          variants={fadeInCalendar}
          className='sticky bottom-0 w-full bg-gray-100 p-3 shadow-sm md:static'
        >
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size={"lg"}
                  className='w-full bg-primary bg-primary-500 py-6 text-lg hover:bg-primary-600'
                  disabled={!delivery_date || !pickup_date}
                  onClick={handleContinue}
                >
                  Continue
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {!delivery_date || !pickup_date
                  ? "Please select both delivery and pickup dates"
                  : "Confirm your rental period"}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </motion.div>
      </div>
    </AdaptiveWrapper>
  )
}
