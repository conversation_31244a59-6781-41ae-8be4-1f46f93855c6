// 'use client'

// import * as React from 'react'
// import { format, addDays, isValid } from 'date-fns'
// import { Button } from '@/components/ui/button'
// import { Label } from '@/components/ui/label'
// import { useRentalStore } from '@/store/use-rental-store'
// import { IconPickupDate } from '@/components/Icons'
// import { formatShortDate } from '@/utils/date-logics'
// import { Clock } from 'lucide-react'
// import OfferIcon from '@/components/Icons/offer-icon'

// const formatDate = (date: Date | null) => {
//   if (!date || !isValid(date)) return ''
//   return format(date, 'yyyy-MM-dd')
// }

// const formatDisplayDate = (date: Date | null) => {
//   if (!date || !isValid(date)) return ''
//   return format(date, 'MMM dd, yyyy')
// }

// interface DateInputProps {
//   value: Date | null
//   onChange: (date: Date | null) => void
//   min: string
//   max: string
//   label: string
//   placeholder: string
// }

// const DateInput: React.FC<DateInputProps> = ({
//   value,
//   onChange,
//   min,
//   max,
//   label,
//   placeholder,
// }) => {
//   const inputRef = React.useRef<HTMLInputElement>(null)

//   const handleClick = () => {
//     inputRef.current?.showPicker()
//   }

//   return (
//     <div className="space-y-1.5">
//       <Label htmlFor={label} className="text-sm font-medium">
//         {label} <span className="text-destructive">*</span>
//       </Label>
//       <div className="relative" onClick={handleClick}>
//         <div className="w-full cursor-pointer appearance-none rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
//           {value ? formatDisplayDate(value) : placeholder}
//         </div>
//         <input
//           ref={inputRef}
//           type="date"
//           value={formatDate(value)}
//           onChange={(e) => onChange(e.target.valueAsDate)}
//           min={min}
//           max={max}
//           className="absolute inset-0 opacity-0"
//         />
//         <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
//           <Clock className="h-4 w-4 text-gray-400" />
//         </div>
//       </div>
//     </div>
//   )
// }

// export function MobileRentalPeriodSelector() {
//   const {
//     delivery_date,
//     pickup_date,
//     setDeliveryDate,
//     setPickupDate,
//     total_days,
//   } = useRentalStore()

//   const isDateValid =
//     delivery_date && pickup_date && pickup_date >= delivery_date

//   return (
//     <div className="space-y-4 p-3">
//       <div className="space-y-3">
//         <DateInput
//           value={delivery_date}
//           onChange={setDeliveryDate}
//           min={formatDate(new Date())}
//           max={formatDate(addDays(new Date(), 90))}
//           label="Delivery Date"
//           placeholder="Select delivery date"
//         />
//         <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
//           <Clock className="h-3 w-3" />
//           <span>Delivery: 15:00-20:00</span>
//         </div>

//         <DateInput
//           value={pickup_date}
//           onChange={setPickupDate}
//           min={formatDate(delivery_date || new Date())}
//           max={formatDate(addDays(new Date(), 90))}
//           label="Pickup Date"
//           placeholder="Select pickup date"
//         />
//         <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
//           <Clock className="h-3 w-3" />
//           <span>Pickup: 09:00-15:00</span>
//         </div>
//       </div>

//       {/* Rental Period Summary */}
//       <div className="flex h-[70px] w-full flex-col gap-1">
//         <div className="text-xs font-medium text-neutral-900">
//           Your Rental Period:
//         </div>
//         <div className="flex w-full items-end justify-start gap-2 rounded-xl border-2 border-neutral-200 bg-gray-100 p-2">
//           <div className="flex items-end gap-1">
//             <div className="text-3xl font-bold leading-8 text-neutral-800">
//               {total_days}
//             </div>
//             <div className="pb-0.5 text-xs font-medium text-neutral-500">
//               Days
//             </div>
//           </div>
//           <div className="mx-1 h-6 w-0 border-l border-neutral-200"></div>
//           <div className="flex flex-1 flex-col items-start justify-center">
//             <div className="text-2xs w-full font-medium text-neutral-900">
//               Chargeable Period:
//             </div>
//             <div className="flex w-full items-center gap-1">
//               <IconPickupDate className="h-3 w-3" />
//               <div className="flex-1 text-xs font-medium text-neutral-300">
//                 {delivery_date && pickup_date ? (
//                   <span>
//                     {formatShortDate(delivery_date)} -{' '}
//                     {formatShortDate(pickup_date)}
//                   </span>
//                 ) : (
//                   '--'
//                 )}
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Offer Box */}
//       <div className="overflow-hidden rounded-lg bg-primary-900 p-3 text-white">
//         <div className="mb-2 flex items-center gap-2">
//           <OfferIcon className="h-5 w-5" />
//           <span className="flex h-max w-full items-center gap-1 bg-zero-policy-title-card-text bg-clip-text text-base font-bold italic text-[#00000000]">
//             Save more with us!
//           </span>
//         </div>
//         <div className="space-y-2 text-xs">
//           <p className="flex items-start justify-start gap-1.5 font-medium">
//             <span className="text-base font-extrabold italic text-secondary-500">
//               1
//             </span>
//             Save more when you rent for longer periods, with discounts averaging
//             up to 12% on your cart value
//           </p>
//           <p className="flex items-start justify-start gap-1.5 font-medium">
//             <span className="text-base font-extrabold italic text-secondary-500">
//               2
//             </span>
//             Get a free extra day with 1-day early delivery and 1-day later
//             pickup for added convenience and more fun
//           </p>
//         </div>
//       </div>

//       {/* Continue Button */}
//       <Button
//         size="lg"
//         className="w-full bg-primary-500 py-4 text-base font-semibold hover:bg-primary-600"
//         disabled={!isDateValid}
//       >
//         Continue
//       </Button>
//     </div>
//   )
// }

const MobileRental = () => <div>MobileRental</div>

export default MobileRental
