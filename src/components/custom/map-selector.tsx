"use client"

import * as React from "react"
import { MapPin } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import type { MapLocation } from "@/lib/validations/checkout"

interface MapSelectorProps {
  onLocationSelect: (location: MapLocation) => void
  isLoading?: boolean
}

export function MapSelector({
  onLocationSelect,
  isLoading = false,
}: MapSelectorProps) {
  const [error, setError] = React.useState<string | null>(null)

  const handleSelectLocation = async () => {
    try {
      // In a real implementation, you would get these values from your map integration
      const location: MapLocation = {
        latitude: 12.9716,
        longitude: 77.5946,
        formattedAddress: "Urban Vault #99, Some Floor, Tech Office",
        city: "BENGALURU",
        state: "KARNATAKA",
        pincode: "560001",
      }
      onLocationSelect(location)
    } catch (error) {
      console.error("Failed to get location:", error)
      setError("Failed to get location. Please try again.")
    }
  }

  return (
    <Card className='overflow-hidden'>
      <div className='aspect-video w-full bg-neutral-100'>
        <div className='flex h-full items-center justify-center'>
          <p className='text-sm text-muted-foreground'>
            Map will be rendered here
          </p>
        </div>
      </div>
      {error && <p className='px-4 py-2 text-sm text-red-500'>{error}</p>}
      <div className='p-4'>
        <Button
          onClick={handleSelectLocation}
          disabled={isLoading}
          className='w-full rounded-full'
        >
          <MapPin className='mr-2 h-4 w-4' />
          {isLoading ? "Confirming location..." : "Confirm Location"}
        </Button>
      </div>
    </Card>
  )
}
