"use client"

import { useEffect, useRef, useState } from "react"
import { cn } from "@/lib/utils"
import { SharePalLogo } from "../main/logo"

interface SimpleInstagramEmbedProps {
  url: string
  className?: string
  autoplay?: boolean
}

export function SimpleInstagramEmbed({
  url,
  className,
  autoplay = true,
}: SimpleInstagramEmbedProps) {
  const [isLoading, setIsLoading] = useState(true)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  // Extract the ID from the Instagram URL
  const getReelId = (url: string) => {
    // Remove any query parameters
    const cleanUrl = url.split("?")[0]
    // Extract the ID - it's the last part of the path
    const parts = cleanUrl.split("/")
    // Get the last non-empty part
    for (let i = parts.length - 1; i >= 0; i--) {
      if (parts[i]) return parts[i]
    }
    return null
  }

  const reelId = getReelId(url)

  useEffect(() => {
    if (!reelId) {
      setIsLoading(false)
      return
    }

    // Set a timeout to handle loading issues
    const timeout = setTimeout(() => {
      if (isLoading) {
        setIsLoading(false)
      }
    }, 10000) // Timeout set to 10 seconds

    return () => clearTimeout(timeout)
  }, [reelId, isLoading])

  const handleIframeLoad = () => {
    setIsLoading(false)
  }

  if (!reelId) {
    return <div className='p-4 text-red-500'>Invalid Instagram URL</div>
  }

  // Create the embed URL using just the ID
  const embedUrl = `https://www.instagram.com/p/${reelId}/embed/${autoplay ? "?autoplay=1" : ""}`

  return (
    <div className={cn(className, "relative w-full")}>
      {isLoading ? (
        <div className='absolute inset-0 flex h-full w-full animate-pulse items-center justify-center p-8'>
          <SharePalLogo />
        </div>
      ) : (
        <div className='aspect-[9/19.5] h-full w-full overflow-hidden'>
          <iframe
            ref={iframeRef}
            src={embedUrl}
            className='h-full w-full border-0'
            onLoad={handleIframeLoad}
            allowFullScreen
            scrolling='no'
          />
        </div>
      )}
    </div>
  )
}
