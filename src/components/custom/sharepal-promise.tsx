import React from "react"
import { IconDelivery, IconSupport, IconVerified } from "../Icons"

import { cn } from "@/lib/utils"

import SpImage from "@/shared/SpImage/sp-image"
import { Typography } from "../ui/typography"

// Define props for the PromiseCard component
interface PromiseCardProps {
  icon: React.FC<{ className?: string }>
  title: string
  description: string
}

// Reusable PromiseCard component
const PromiseCard: React.FC<PromiseCardProps> = ({
  icon: Icon,
  title,
  description,
}) => (
  <div className='flex basis-1/3 items-start justify-start gap-3 lg:flex-col'>
    <Icon className='h-max w-12 md:h-14 md:w-14' />
    <div className='flex flex-col gap-1 lg:gap-2'>
      <Typography
        as={"h3"}
        className='text-sh4 font-bold text-gray-100 md:text-h6'
      >
        {title}
      </Typography>
      <Typography as={"p"} className='text-b7 text-primary-250 lg:text-b5'>
        {description}
      </Typography>
    </div>
  </div>
)

// Define props for the SharePalPromise component
interface SharePalPromiseProps {
  className?: string
}

const SharePalPromise: React.FC<SharePalPromiseProps> = ({ className }) => (
  <section className={cn("w-full px-4 md:px-6", className)}>
    <div
      className={cn(
        "mx-auto flex w-full flex-wrap gap-2 rounded-2xl bg-primary-850 max-lg:flex-col md:gap-3 md:rounded-2xl",
      )}
    >
      {/* Top Section */}
      <div className='flex w-full flex-col items-start justify-start gap-3 rounded-t-radius px-5 py-3 md:flex-row md:items-center md:justify-center md:gap-7 md:bg-primary-800 md:px-7 md:py-3 md:pr-3 lg:gap-4'>
        {/* Sharepal Promise title card */}
        {/* <div className="relative h-[40px] w-[150px] skew-x-[-15deg] rounded-[12px] bg-sharepal-promise-card px-3 md:flex-[1] lg:h-[54px] lg:w-[212px]">
            <div className="flex w-full skew-x-[15deg] md:gap-2">
              <Image
                src={`${HARD_CODED_IMAGE_URL}/shield1.webp`}
                width={80}
                height={10}
                alt="Sharepal Promise Image"
                className="max-lg:h-10 max-lg:w-10 lg:-translate-y-3"
              />
              <div className="flex w-full flex-col items-start p-0">
                <div className="flex -translate-x-8 scale-50 items-start justify-start lg:-translate-x-6 lg:scale-[0.6]">
                  <IconLogoLeft />
                  <IconLogoRight color="#fff" />
                </div>
                <span className="flex h-max w-full -translate-y-4 items-center gap-1 text-pretty bg-zero-policy-title-card-text bg-clip-text p-0 text-[15px] font-medium italic leading-[34px] text-[#00000000] lg:-translate-y-2 lg:text-2xl">
                  <span className="font-[800]">Promise</span>
                </span>
              </div>
            </div>
          </div> */}

        <SpImage
          src={`https://images.sharepal.in/benefits-of-renting/sharepal-promise.svg`}
          alt='Sharepal Promise Image'
          width={300}
          height={100}
          className='h-full w-40 md:w-80'
          containerClassName='flex-[1] h-full w-full'
        />
        {/* Sharepal Promise title */}
        <Typography
          as={"h3"}
          className='inline-flex text-h4 text-primary-100 md:hidden md:py-4 lg:text-h2'
        >
          {/* Rent your favourite products with piece of mind */}
          Rent with peace
        </Typography>
        <Typography
          as={"h3"}
          className='hidden px-6 text-2xl font-bold text-primary-100 md:inline-flex md:flex-[2] lg:text-2xl'
        >
          {/* Our Promise ensures your rental are Secure */}
          Rent with peace
        </Typography>
      </div>

      {/* Promise Cards Section */}
      <div className='flex gap-5 text-pretty px-4 py-3 pt-0 max-lg:flex-col md:p-6 md:px-5 md:py-4 md:pt-5'>
        <PromiseCard
          icon={IconVerified}
          title='12 Point Quality Check'
          description='Before delivery, we conduct a 12-point quality check to ensure your order meets our quality standard.'
        />
        <PromiseCard
          icon={IconDelivery}
          title='Lowest Price Guarantee'
          description='We provide lowest price guarantee so you can rent with confidence. No more searching here and there.'
        />
        <PromiseCard
          icon={IconSupport}
          title='Prompt Help & Support'
          description='Fast and efficient support, right at your fingertips. We are available from 10 am to 10 pm everyday.'
        />
      </div>
    </div>
  </section>
)

export default SharePalPromise
