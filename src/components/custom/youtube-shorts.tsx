"use client"
import { Play } from "lucide-react"
import { useState } from "react"

interface YouTubeShortsProps {
  videos: string[]
}

export function YouTubeShorts({ videos }: YouTubeShortsProps) {
  const [playingVideo, setPlayingVideo] = useState<string | null>(null)

  const handlePlay = (videoId: string) => {
    setPlayingVideo(videoId)
  }

  return (
    <div className='custom-scrollbar-black flex w-full snap-x snap-mandatory items-center gap-4 overflow-x-auto bg-gray-100 p-4'>
      {videos.map((videoId, index) => (
        <div
          key={videoId}
          className='relative h-[480px] w-[270px] flex-shrink-0 snap-center overflow-hidden rounded-2xl bg-black shadow-lg'
        >
          {playingVideo !== videoId && (
            <div className='pointer-events-none absolute inset-0 z-10 bg-gradient-to-b from-black/30 to-transparent' />
          )}

          {/* Logo (no need already in video) */}
          {/* <div className="absolute left-4 top-4 z-20">
            <Image
              src="/placeholder.svg?height=32&width=32"
              alt="Channel logo"
              width={32}
              height={32}
              className="rounded-full border-2 border-white shadow-lg"
            />
          </div> */}

          {/* Custom Play Button Overlay */}
          {playingVideo !== videoId && (
            <button
              className='absolute inset-0 z-20 flex cursor-pointer items-center justify-center focus:outline-none'
              onClick={() => handlePlay(videoId)}
              aria-label={`Play video ${index + 1}`}
            >
              <div className='group flex h-16 w-16 items-center justify-center rounded-full border-2 border-white bg-primary-500 transition-transform hover:scale-110 hover:bg-primary-400'>
                <Play className='ml-1 w-6 fill-white text-white transition-transform group-hover:scale-110' />
              </div>
            </button>
          )}

          {/* YouTube Embed */}
          <iframe
            className='h-full w-full'
            src={`https://www.youtube.com/embed/${videoId}?autoplay=${playingVideo === videoId ? 1 : 0}&controls=1&rel=0&modestbranding=1`}
            title={`YouTube Shorts video ${index + 1}`}
            allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
            allowFullScreen
          />
        </div>
      ))}
    </div>
  )
}
