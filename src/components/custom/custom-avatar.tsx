import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"

interface AvatarProps {
  src: string
  alt?: string
  fallbackText: string
  className?: string // Optional class for additional styling
}

export function CustomAvatar({
  src,
  alt = "Avatar",
  fallbackText,
  className,
}: AvatarProps) {
  return (
    <Avatar className={cn("bg-primary-400", className)}>
      <AvatarImage src={src} alt={alt} />
      <AvatarFallback>{fallbackText}</AvatarFallback>
    </Avatar>
  )
}
