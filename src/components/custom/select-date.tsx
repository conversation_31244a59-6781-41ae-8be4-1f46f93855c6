import { useCalendarStore, useRentalStore } from "@/store/rental-store"

import { moneyFormatter } from "@/functions/small-functions"
import { formatShortDate } from "@/utils/date-logics"
import {
  CalendarEditFilledIcon,
  CalendarEndFilledIcon,
  CalendarStartFilledIcon,
  SparkFilledIcon,
} from "sharepal-icons"
import { Button } from "../ui/button"
import { Separator } from "../ui/separator"
import { Typography } from "../ui/typography"

const SelectDate = ({
  inCart,
  extraDayPrice = 0,
}: {
  inCart?: boolean
  extraDayPrice?: number
}) => {
  const { openCalendar } = useCalendarStore()
  const { delivery_date, pickup_date } = useRentalStore()
  return (
    <div className='w-full rounded-3xl bg-secondary-150'>
      <div className='flex items-center gap-1 rounded-full border-2 border-gray-200 bg-gray-100 pl-3 md:gap-4'>
        <div className='flex items-center gap-2'>
          <CalendarStartFilledIcon className='h-5 w-5' />
          <div className='flex items-center gap-1'>
            <Typography
              as='span'
              className='text-b4 text-neutral-700 max-md:hidden'
            >
              Delivery Date:
            </Typography>
            <Typography
              as='span'
              className='text-o2 text-neutral-400 md:hidden md:text-b4'
            >
              Rent for:
            </Typography>
            <Typography
              as='span'
              className='text-sh7 text-neutral-700 md:text-sh5'
            >
              {formatShortDate(delivery_date || new Date())}
            </Typography>
          </div>
        </div>
        <Separator className='h-[15px] w-0.5 rounded-full bg-gray-200 max-md:hidden' />
        <span className='md:hidden'>•</span>
        <div className='flex items-center gap-2'>
          <span className='max-md:hidden'>
            <CalendarEndFilledIcon className='h-5 w-5' />
          </span>
          <div className='flex items-center gap-1'>
            <Typography
              as='span'
              className='text-b4 text-neutral-700 max-md:hidden'
            >
              Pickup Date:
            </Typography>
            <Typography
              as='span'
              className='text-sh7 text-neutral-700 md:text-sh5'
            >
              {formatShortDate(pickup_date || new Date())}
            </Typography>
          </div>
        </div>
        <Button onClick={openCalendar} className='ml-auto'>
          <CalendarEditFilledIcon className='h-5 w-5' />
          <Typography as='span' className='text-bt3'>
            Edit
          </Typography>
        </Button>
      </div>
      <div className='flex items-center gap-2 px-3 py-1.5 text-success-700'>
        <SparkFilledIcon className='h-4 w-4 text-success-500' />
        <Typography as='p' className='text-b6'>
          Additional day at
          <Typography as='strong' className='text-sh6 md:text-sh5'>
            {" "}
            {moneyFormatter(extraDayPrice)} only{" "}
          </Typography>
          on {inCart ? "this cart" : "your cart"}
        </Typography>
      </div>
    </div>
  )
}

export default SelectDate
