"use client"
import SpImage from "@/shared/SpImage/sp-image"
import type { ProductVideo } from "@/types"
import { useState } from "react"
import { PlayFilledIcon } from "sharepal-icons"

interface YouTubeShortsProps {
  videos: ProductVideo[]
}

function YouTubeShortsWithThumbnail({ videos }: YouTubeShortsProps) {
  const [playingVideo, setPlayingVideo] = useState<string | null>(null)

  const handlePlay = (videoId: string) => {
    setPlayingVideo(videoId)
  }

  return (
    <div className='custom-scrollbar-black flex w-full snap-x snap-mandatory items-start gap-4 overflow-x-auto py-2 md:py-4'>
      {videos.map((video) => (
        <div
          key={video.id}
          className='flex snap-center flex-col rounded-2xl border-2 border-neutral-200 bg-neutral-100'
        >
          <div className='relative aspect-[9/13] w-[230px] flex-shrink-0 overflow-hidden rounded-t-2xl bg-cover bg-center sm:w-[300px] md:w-[240px] lg:w-[280px] xl:w-[250px]'>
            <SpImage
              src={video.thumbnail_url}
              alt={video.title_text || "Video thumbnail"}
              fill
              className='object-cover'
              containerClassName='absolute inset-0'
            />

            {/* Black overlay */}
            <div
              className={`absolute inset-0 bg-black transition-opacity duration-300 ${
                playingVideo === video.video_id ? "opacity-0" : "opacity-20"
              }`}
            />

            {playingVideo !== video.video_id ? (
              <>
                {/* Play button overlay */}
                <button
                  className='absolute inset-0 z-20 flex cursor-pointer items-center justify-center focus:outline-none'
                  onClick={() => handlePlay(video.video_id)}
                  aria-label={`Play video ${video.title_text}`}
                >
                  <div className='flex h-16 w-16 items-center justify-center rounded-full border-2 border-gray-100 bg-primary-500 transition-transform hover:scale-110 sm:h-16 sm:w-16'>
                    <PlayFilledIcon className='size-6 fill-white text-white md:size-8' />
                  </div>
                </button>

                {/* Channel logo */}
                <div className='absolute left-3 top-3 z-30 rounded-full sm:left-4 sm:top-4'>
                  <SharepalVideoIcon className='size-6 md:size-7' />
                </div>
              </>
            ) : (
              <iframe
                className='absolute inset-0 h-full w-full'
                src={`https://www.youtube.com/embed/${video.video_id}?autoplay=1&controls=0&rel=0&modestbranding=1&showinfo=0&fs=0&playsinline=1`}
                title={video.title_text}
                allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
                allowFullScreen
              />
            )}
          </div>

          {/* Video Title */}
          <div className='w-full px-3 py-2'>
            <p className='text-sh7 font-medium md:text-sh5'>
              {video.title_text}
            </p>
          </div>
        </div>
      ))}
    </div>
  )
}

const SharepalVideoIcon = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
    {...props}
  >
    <g clipPath='url(#clip0_4762_58578)' style={{ mixBlendMode: "overlay" }}>
      <path
        fill='#E2E7EA'
        fillRule='evenodd'
        d='M20 0H0v20h20zM7.533 5.313h3.554c2.216 0 3.456 1.169 3.456 2.917 0 2.39-1.676 3.898-4.365 3.898H8.846l-.546 2.56H5.552l.467-2.21c2.838-.57 4.504-2.746 4.504-2.746l.947.804.698-3.833L8.5 8.014l.89.756s-1.037 1.447-3.076 2.312z'
        clipRule='evenodd'
      ></path>
    </g>
    <defs>
      <clipPath id='clip0_4762_58578'>
        <rect width='20' height='20' fill='#fff' rx='10'></rect>
      </clipPath>
    </defs>
  </svg>
)

export default YouTubeShortsWithThumbnail
