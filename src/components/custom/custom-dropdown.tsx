"use client"

import { cn } from "@/lib/utils"
import { AnimatePresence, motion } from "framer-motion"
import * as React from "react"

import { dropdownVariants, itemVariants } from "@/utils/animation-variants"
import { CrossLOutlinedIcon } from "sharepal-icons"
import { AdaptiveWrapper } from "../modals/adaptive-wrapper"
import { Badge } from "../ui/badge"
import { RadioButton } from "./radio-button"

interface CustomDropdownProps {
  options: { value: string; label: string; onClick: () => void }[]
  isOpen: boolean
  className?: string
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>
  title: string
  children?: React.ReactNode
  selectedValues?: string[] // Add this to track selected values
}

export function CustomDropdown({
  options,
  className,
  isOpen,
  children,
  setIsOpen,
  title,
  selectedValues = [], // Default to empty array
}: CustomDropdownProps) {
  const dropdownRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [setIsOpen])

  return (
    <div className='relative w-full' ref={dropdownRef}>
      <div className='flex w-full items-center gap-2 md:gap-4'>
        {selectedValues.length > 0 && (
          <div className='flex flex-wrap items-center gap-2'>
            {selectedValues.map((value) => {
              const option = options.find((opt) => opt.value === value)
              if (!option) return null

              return (
                <Badge
                  key={value}
                  onClick={option.onClick}
                  variant={"secondary"}
                  className='flex cursor-pointer items-center gap-2 rounded-full bg-neutral-100 px-3 py-1 text-sh6 md:text-sh5'
                >
                  {option.label}
                  <CrossLOutlinedIcon className='h-4 w-4 text-gray-500' />
                </Badge>
              )
            })}
          </div>
        )}
        {children}
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial='hidden'
            animate='visible'
            exit='hidden'
            variants={dropdownVariants}
            className={cn(
              "absolute right-0 top-[calc(100%+8px)] z-[10] min-w-[200px] rounded-xl border bg-gray-100 p-1 shadow-lg max-md:hidden",
              className,
            )}
          >
            {options.map((option) => {
              const isSelected = selectedValues.includes(option.value)

              return (
                <motion.button
                  key={option.value}
                  variants={itemVariants}
                  whileHover='hover'
                  initial='initial'
                  className={cn(
                    "relative flex w-full items-center justify-between rounded-lg px-4 py-2 text-left text-sm transition-colors",
                    isSelected && "bg-neutral-50 font-medium text-primary-600",
                    !isSelected && "hover:bg-neutral-50",
                  )}
                  onClick={option.onClick}
                >
                  {option.label}
                  {isSelected && (
                    <motion.div
                      layoutId='selected-dot'
                      className='h-2 w-2 rounded-full bg-primary-600'
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      exit={{ scale: 0 }}
                    />
                  )}
                </motion.button>
              )
            })}
          </motion.div>
        )}
      </AnimatePresence>

      <AdaptiveWrapper
        open={isOpen}
        only='mobile'
        onOpenChange={(value) => setIsOpen(value)}
        title={title}
        mobile={{ type: "drawer", side: "bottom" }}
      >
        <div className='p-4'>
          <RadioButton
            options={options.map((option) => ({
              id: `filter-${option.value}`,
              value: option.value,
              label: option.label,
            }))}
            value={selectedValues[0] || ""}
            onValueChange={(value) => {
              const option = options.find((opt) => opt.value === value)
              if (option) {
                option.onClick()
              }
              setIsOpen(false)
            }}
            className='space-y-2'
          />
        </div>
      </AdaptiveWrapper>
    </div>
  )
}
