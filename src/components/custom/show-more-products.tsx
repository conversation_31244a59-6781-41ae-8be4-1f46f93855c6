"use client"

import { useEffect, useState } from "react"

import { fetchAllCategoriesProductsSC } from "@/actions/category"
import { formatUrlName } from "@/functions/small-functions"
import type { RentalItem } from "@/types"
import {
  keepPreviousData,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"
import { useParams } from "next/navigation"
import ProductCard from "../cards/product-card"
import SectionTitle from "../section-title"
import ProductCardSkeleton from "../skeletons/product-card-skeleton"
import { Button } from "../ui/button"

const PRODUCTS_PER_PAGE = 12 // Number of products to show per page

const ShowMoreProducts = () => {
  const { cat, city } = useParams<{
    cat: string
    city: string
    subcat: string
    product: string
  }>()
  const queryClient = useQueryClient()

  const [currentPage, setCurrentPage] = useState(1)
  const [displayCount, setDisplayCount] = useState(PRODUCTS_PER_PAGE)
  const [inStockProducts, setInStockProducts] = useState<RentalItem[]>([])
  const [outOfStockProducts, setOutOfStockProducts] = useState<RentalItem[]>([])
  const [showOutOfStock, setShowOutOfStock] = useState(false)

  const heading = formatUrlName(cat)

  const [adminOnly, setAdminOnly] = useState(false)
  useEffect(() => {
    setAdminOnly(
      ((window &&
        window.sessionStorage &&
        window.sessionStorage.getItem("backend_order")) ??
        "") == "true",
    )
  }, [])

  const {
    data: products,
    isLoading,
    isFetching,
    isError,
    error,
  } = useQuery({
    queryKey: ["products", city, cat, currentPage],
    queryFn: () =>
      fetchAllCategoriesProductsSC(city, cat, currentPage, adminOnly),
    staleTime: 5 * 60 * 1000, // 5 minutes
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    enabled: !!city && !!cat,
  })

  useEffect(() => {
    if (products && products.items) {
      // Separate in-stock and out-of-stock products
      const newProducts = products.items.filter(
        (newItem) =>
          !inStockProducts.some(
            (existingItem) => existingItem.id === newItem.id,
          ) &&
          !outOfStockProducts.some(
            (existingItem) => existingItem.id === newItem.id,
          ),
      )

      const { inStock, outOfStock } = newProducts.reduce(
        (acc, product) => {
          if (product.out_of_stock) {
            acc.outOfStock.push(product)
          } else {
            acc.inStock.push(product)
          }
          return acc
        },
        { inStock: [] as RentalItem[], outOfStock: [] as RentalItem[] },
      )

      setInStockProducts((prev) => [...prev, ...inStock])
      setOutOfStockProducts((prev) => [...prev, ...outOfStock])
    }
  }, [products])

  const handleShowMore = async () => {
    setDisplayCount((prev) => prev + PRODUCTS_PER_PAGE)

    // If we're close to showing all currently fetched products, fetch the next page
    if (displayCount + PRODUCTS_PER_PAGE >= inStockProducts.length) {
      const nextPage = currentPage + 1
      setCurrentPage(nextPage)

      queryClient.prefetchQuery({
        queryKey: ["products", city, cat, nextPage],
        queryFn: () =>
          fetchAllCategoriesProductsSC(city, cat, nextPage, adminOnly),
      })
    }
  }

  const renderProductCards = () => {
    if (isLoading && currentPage === 1) {
      return Array.from({ length: PRODUCTS_PER_PAGE }).map((_, i) => (
        <ProductCardSkeleton key={i} />
      ))
    }

    const visibleInStockProducts = inStockProducts.slice(0, displayCount)
    const visibleOutOfStockProducts = showOutOfStock ? outOfStockProducts : []

    return (
      <>
        {visibleInStockProducts.map((item) => (
          <ProductCard key={item.id} data={item} city={city} />
        ))}
        {visibleOutOfStockProducts.map((item) => (
          <ProductCard key={item.id} data={item} city={city} />
        ))}
        {isFetching &&
          Array.from({ length: 4 }).map((_, i) => (
            <ProductCardSkeleton key={`loading-${i}`} />
          ))}
      </>
    )
  }

  const totalPages = products
    ? Math.ceil(products.itemsTotal / products.perPage)
    : 0

  const hasMoreProducts =
    displayCount < inStockProducts.length || currentPage < totalPages

  const showOutOfStockButton =
    !showOutOfStock && !hasMoreProducts && outOfStockProducts.length > 0

  return (
    <section className='container relative z-[1] pb-10'>
      <div className='flex items-center justify-between border-b-2 border-neutral-200 py-4 md:py-5'>
        <SectionTitle
          nText={heading + " on rent"}
          cText=''
          className='font-inter text-h6 capitalize md:text-h2'
        />
        <p className='flex items-center justify-center gap-1 text-b2 text-neutral-300'>
          <span className='hidden md:inline-flex'>Total items: </span>
          <span className='text-neutral-500'>
            {products?.itemsTotal || 0} items
          </span>
        </p>
      </div>

      <div className='mt-3 grid grid-cols-2 gap-x-2 gap-y-5 md:mt-6 md:grid-cols-3 md:gap-8 lg:grid-cols-4'>
        {renderProductCards()}
      </div>

      {isError && (
        <div className='mt-5 text-center text-red-500'>
          Error: {error.message}
        </div>
      )}

      <div className='mt-5 flex w-full flex-col items-center justify-center border-t border-gray-200 py-7 md:mt-10'>
        <p className='pb-3 text-sm text-gray-400 md:text-base'>
          Showing{" "}
          {Math.min(
            displayCount + (showOutOfStock ? outOfStockProducts.length : 0),
            products?.itemsTotal || 0,
          )}{" "}
          of {products?.itemsTotal || 0} results
        </p>
        {hasMoreProducts && (
          <Button
            variant='outline'
            className='w-full bg-gray-100 p-5 text-base sm:max-w-72 md:p-6'
            onClick={handleShowMore}
            disabled={isFetching}
          >
            {isFetching ? "Loading..." : "Show More"}
          </Button>
        )}
        {showOutOfStockButton && (
          <Button
            variant='outline'
            className='mt-4 w-full bg-gray-100 p-5 text-base sm:max-w-72 md:p-6'
            onClick={() => setShowOutOfStock(true)}
          >
            Show Out of Stock Products ({outOfStockProducts.length})
          </Button>
        )}
      </div>
    </section>
  )
}

export default ShowMoreProducts
