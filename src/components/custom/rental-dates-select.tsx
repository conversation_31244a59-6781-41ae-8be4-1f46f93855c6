"use client"

import { getCityMinTh } from "@/actions/city"
import useWindowSize from "@/hooks/use-window-resize"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { useQuery } from "@tanstack/react-query"
import { RentalPeriodSelectorDesktop } from "./rental-dates-select-desktop"
import { RentalPeriodSelectorMobile } from "./rental-dates-select-mobile"

export function RentalPeriodSelector() {
  const size = useWindowSize()
  const { cart_items } = useCheckoutStore()
  const { selectedCity, setMinTh } = useRentalStore()

  const catSnameLists = new Set(
    cart_items?.map((item) =>
      item?.subcat_sname ? item?.subcat_sname?.toLowerCase() : "",
    ),
  )

  const catSnameListsArray = Array.from(catSnameLists)

  useQuery({
    queryKey: [
      "cityMinTh",
      selectedCity.city_url ?? "",
      catSnameListsArray ?? [],
    ],
    queryFn: async () => {
      const data = await getCityMinTh(
        selectedCity.city_url,
        Array.from(catSnameLists),
      )
      if (data) {
        setMinTh(data?.min_th ?? 0)
        return data
      }
      return {}
    },
  })

  return (
    <>
      {size?.width && size.width > 600 ? (
        <RentalPeriodSelectorDesktop />
      ) : (
        <RentalPeriodSelectorMobile />
      )}
    </>
  )
}

//memo export
