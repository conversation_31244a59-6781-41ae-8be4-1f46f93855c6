import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { SubCategory } from "@/types"
import SubCategoryCard from "../cards/sub-category-card"

interface MobileCategoryContentProps {
  title: string
  description: string
  image: string
  color: string
  subcategories: SubCategory[]
  onClose: () => void
}

// Category Content Component
function MobileCategoryContent({
  title,
  description,
  image,
  color,
  subcategories,
  onClose,
}: MobileCategoryContentProps) {
  return (
    <div className='relative overflow-hidden'>
      <div className='sticky top-0 z-10 h-full min-h-28 overflow-hidden rounded-b-2xl px-4 py-3 shadow-sm'>
        <h2 className='text-sh2 font-bold'>{title}</h2>
        <p className='max-w-44 text-sh7 text-muted-foreground'>{description}</p>
        <div
          className={cn("absolute bottom-0 left-0 right-0 h-8 w-full", color)}
        ></div>
        <SpImage
          src={image}
          alt={title}
          width={150}
          height={150}
          className='w-full'
          containerClassName='absolute bottom-0 right-0  w-40 object-contain'
        />
      </div>

      <div className='mt-1 grid grid-cols-4 gap-4 p-4'>
        {subcategories.map((sub) => (
          <div key={sub.id} onClick={onClose}>
            <SubCategoryCard key={sub.id} data={sub} />
          </div>
        ))}
      </div>
    </div>
  )
}

export default MobileCategoryContent
