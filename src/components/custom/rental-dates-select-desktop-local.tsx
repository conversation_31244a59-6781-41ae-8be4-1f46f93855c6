"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import { useCalendarStore } from "@/store/rental-store"
import { fadeInCalendar, scaleIn } from "@/utils/animation-variants"
import { disableDates, formatShortDate } from "@/utils/date-logics"
import { useQueryClient } from "@tanstack/react-query"
import { addDays } from "date-fns"
import { motion } from "framer-motion"
import { X } from "lucide-react"
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { CalendarClockFilledIcon } from "sharepal-icons"
import { DateSelector } from "../checkout/order-summary-new/date-selector"
import OfferIcon from "../Icons/offer-icon"
import { Separator } from "../ui/separator"

// const formatDate = (date: Date | null) => {
//   if (!date) return ""
//   return format(date, "LLL dd, y")
// }

interface RentalPeriodSelectorProps {
  isCalenderOpen?: boolean
  closeCalendarFn?: () => void
  onContinue?: () => void
  setDeliveryDate: (date: Date | null) => void
  setPickupDate: (date: Date | null) => void
  delivery_date: Date
  pickup_date: Date
  total_days: number
  min_th: number
  disabled?: {
    delivery?: boolean
    pickup?: boolean
  }
}

export function RentalPeriodSelectorDesktopLocal({
  closeCalendarFn,
  isCalenderOpen,
  onContinue,
  delivery_date,
  min_th,
  pickup_date,
  setDeliveryDate,
  setPickupDate,
  total_days,
  disabled,
}: RentalPeriodSelectorProps) {
  const { isOpen, closeCalendar } = useCalendarStore()
  const [isDeliveryCalendarOpen, setIsDeliveryCalendarOpen] = useState(false)
  const [isPickupCalendarOpen, setIsPickupCalendarOpen] = useState(false)

  const { product } = useParams()

  // Add this state to track if we're in reset mode
  // const [isResetting, setIsResetting] = useState(false)

  const handleDateSelect = (range: { from?: Date; to?: Date } | undefined) => {
    if (!range) return
    setDeliveryDate(range.from || null)
    setPickupDate(range.to || null)
  }

  const closeModal = () => {
    if (closeCalendarFn) closeCalendarFn()
    closeCalendar()
  }

  const handleDeliveryDateSelect = (date: Date | undefined) => {
    if (date) {
      setDeliveryDate(date)
      setIsDeliveryCalendarOpen(false)
    }
  }

  const handlePickupDateSelect = (date: Date | undefined) => {
    if (date) {
      setPickupDate(date)
      setIsPickupCalendarOpen(false)
    }
  }
  const queryClient = useQueryClient()

  useEffect(() => {
    if ((isOpen || isCalenderOpen) && !product)
      queryClient.invalidateQueries({ queryKey: ["cityMinTh"] })
  }, [isOpen, isCalenderOpen, product])

  return (
    <Dialog open={isOpen || isCalenderOpen} onOpenChange={closeModal}>
      <DialogContent className='max-h-svh w-[95%] gap-0 overflow-auto !rounded-radius bg-gray-150 p-0 sm:max-w-7xl'>
        <DialogHeader className='p-6 pb-0'>
          <motion.div
            initial='initial'
            animate='animate'
            exit='exit'
            variants={fadeInCalendar}
            className='flex items-center justify-between'
          >
            <DialogTitle className='text-2xl font-semibold'>
              Select your Dates
            </DialogTitle>
            <Button
              variant='ghost'
              size='icon'
              className='h-8 w-8 rounded-full hover:bg-muted'
              onClick={closeModal}
            >
              <X className='h-6 w-6' />
              <span className='sr-only'>Close</span>
            </Button>
          </motion.div>
        </DialogHeader>

        <div className='flex flex-col-reverse gap-6 p-6 pt-4 lg:flex-row'>
          <motion.div
            initial='initial'
            animate='animate'
            exit='exit'
            variants={scaleIn}
            className='flex-1 space-y-5'
          >
            <div className='grid gap-3 md:grid-cols-2'>
              <DateSelector
                label='Delivery Date'
                date={delivery_date}
                onSelect={handleDeliveryDateSelect}
                isOpen={isDeliveryCalendarOpen}
                disabled={disabled?.delivery}
                setIsOpen={setIsDeliveryCalendarOpen}
                disabledDate={(date) => disableDates(date, min_th, "delivery")}
                timeRange='We deliver between: 3pm - 10pm'
              />
              <DateSelector
                label='Pickup Date'
                date={pickup_date}
                onSelect={handlePickupDateSelect}
                isOpen={isPickupCalendarOpen}
                disabled={disabled?.pickup}
                setIsOpen={setIsPickupCalendarOpen}
                disabledDate={(date) => {
                  // First check if the date is disabled by the global disableDates function
                  if (disableDates(date, min_th, "pickup", delivery_date))
                    return true

                  // Then check if it's before or equal to the delivery date
                  if (delivery_date && date <= delivery_date) return true

                  // Disable the next day after delivery date (require minimum 2-day gap)
                  if (delivery_date && date <= addDays(delivery_date, 1))
                    return true

                  return false
                }}
                timeRange='We pickup between: 9am - 1pm'
              />
            </div>

            {/* Your Rental Period Starts */}
            <div className='flex w-full flex-col gap-1.5'>
              <div className='flex-1 text-sm font-medium leading-[20px] text-neutral-900'>
                Your Rental Period:
              </div>

              <div className='flex w-full items-end justify-start gap-3 rounded-2xl border-2 border-neutral-200 bg-gray-100 px-3 py-2.5'>
                <div className='flex items-end gap-1'>
                  <div className='text-d5 text-neutral-800'>
                    {/* show days precing with 0 */}
                    {total_days.toString().padStart(2, "0")}
                  </div>
                  <div className='flex items-center justify-center gap-1 pb-0.5 pt-0.5'>
                    <div className='text-sm font-medium leading-[20px] text-neutral-500'>
                      {total_days > 1 ? "Days" : "Day"}
                    </div>
                  </div>
                </div>
                {/* <div className="w-0 h-2 border-l-2 border-neutral-200"></div> */}
                <Separator
                  orientation='vertical'
                  className='w-[2px] rounded-full'
                />
                <div className='flex flex-1 flex-col items-start justify-center gap-1'>
                  <div className='w-full text-b6 text-neutral-900'>
                    Chargeable Period:
                  </div>
                  <div className='flex w-full items-center gap-2'>
                    <div className='flex h-4 w-4 items-center justify-center'>
                      <CalendarClockFilledIcon />
                    </div>
                    <div className='flex-1 text-sm font-medium leading-[20px] text-neutral-300'>
                      {delivery_date && pickup_date ? (
                        <span className='text-sh5 text-neutral-900'>
                          {formatShortDate(addDays(delivery_date, 1))} -{" "}
                          {formatShortDate(addDays(pickup_date, -1))}
                        </span>
                      ) : (
                        "--"
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Your Rental Period Ends */}

            {/* Gradient Box */}
            <motion.div
              initial='initial'
              animate='animate'
              exit='exit'
              variants={fadeInCalendar}
              className='overflow-hidden rounded-xl bg-primary-900 pb-5 pt-0 text-white'
            >
              <div className='mb-4 flex items-center gap-3'>
                <motion.div whileHover={{ scale: 1.1 }}>
                  <OfferIcon />
                  {/* <span className="text-lg font-bold text-indigo-950">
      {total_days}
    </span> */}
                </motion.div>
                <span className='flex h-max w-full items-center gap-1 text-pretty bg-zero-policy-title-card-text bg-clip-text font-bold italic text-[#00000000] md:text-2xl'>
                  How to select dates?
                </span>
              </div>

              <div className='space-y-3 px-5 text-sm'>
                <motion.p
                  // whileHover={{ x: 5 }}
                  className='flex items-start justify-start gap-2 font-semibold md:gap-4 md:text-xs'
                >
                  <span className='text-lg font-extrabold italic text-secondary-500 md:text-2xl'>
                    1
                  </span>
                  Delivery time is 3-8pm. Return time is 10am-2pm. Delivery and
                  Return dates are not chargeable. Select the dates accordingly
                </motion.p>
                {/*  */}
                <motion.p
                  // whileHover={{ x: 5 }}
                  className='flex items-start justify-start gap-2 font-semibold md:gap-4 md:text-xs'
                >
                  <span className='text-lg font-extrabold italic text-secondary-500 md:text-2xl'>
                    2
                  </span>
                  When you rent longer, the per day rental reduces. Select your
                  dates accordingly.
                </motion.p>
              </div>
            </motion.div>
            {/* Gradient Box  end */}

            {/* continue button start */}
            <motion.div
              initial='initial'
              animate='animate'
              exit='exit'
              variants={fadeInCalendar}
            >
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size={"lg"}
                      variant={"primary"}
                      className='w-full py-6 text-lg'
                      disabled={!delivery_date || !pickup_date}
                      onClick={() => {
                        closeCalendar()
                        if (closeCalendarFn) closeCalendarFn()
                        if (onContinue) onContinue()
                      }}
                    >
                      Continue
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {!delivery_date || !pickup_date
                      ? "Please select both delivery and pickup dates"
                      : "Confirm your rental period"}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </motion.div>
            {/* continue button end */}
          </motion.div>

          {/* <span className='bg-secondary-500 bg-secondary-600'></span> */}

          {/* Calendar */}
          <motion.div
            initial='initial'
            animate='animate'
            exit='exit'
            variants={scaleIn}
            className='h-max flex-[1.5] rounded-3xl bg-card p-3'
          >
            <Calendar
              mode='range'
              selected={{
                from: delivery_date || undefined,
                to: pickup_date || undefined,
              }}
              onSelect={handleDateSelect}
              numberOfMonths={2}
              defaultMonth={delivery_date || new Date()}
              // className="w-full [&_.rdp-cell]:flex [&_.rdp-cell]:flex-1 [&_.rdp-day]:w-full [&_.rdp-day]:flex-1 [&_.rdp-day]:justify-center [&_.rdp-day]:p-0 [&_.rdp-day]:text-center [&_.rdp-day]:text-sm [&_.rdp-day_span]:w-full [&_.rdp-day_span]:p-2"
              modifiers={{
                range: (date) => {
                  if (!delivery_date || !pickup_date) return false
                  return date > delivery_date && date < pickup_date
                },
              }}
              modifiersClassNames={{
                selected: cn(
                  "bg-secondary-200 text-neutral-900 font-bold hover:bg-secondary-600   focus:bg-secondary-500",
                ),
                range: "!bg-secondary-200",

                // today: 'bg-accent text-accent-foreground',
                range_middle: "!bg-secondary-200",
                range_start: "!font-bold !bg-secondary-500 ",
                range_end: "!font-bold !bg-secondary-500  ",
              }}
              // disabled={(date) =>
              //   date < new Date() || date > addDays(new Date(), 90)
              // }
              disabled={(date) => disableDates(date, min_th)}
            />
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
