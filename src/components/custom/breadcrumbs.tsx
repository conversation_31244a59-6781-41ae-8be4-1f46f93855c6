"use client"

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { generateBreadcrumbs } from "@/lib/breadcrumbs"
import { ChevronRight } from "lucide-react"
import Link from "next/link"
import { useParams, usePathname } from "next/navigation"

import {
  addOnRent,
  capitalizeFirstLetter,
  removeOnRent,
} from "@/functions/small-functions"
import useWindowSize from "@/hooks/use-window-resize"

interface DynamicBreadcrumbProps {
  superCategoryUrl?: string
  isProductPage?: boolean
}

export function DynamicBreadcrumb({
  superCategoryUrl,
  isProductPage,
}: DynamicBreadcrumbProps) {
  const pathname = usePathname()
  const { width } = useWindowSize()
  const { city } = useParams()
  const breadcrumbs = generateBreadcrumbs(pathname)
  // console.log(pathname)

  // Replace first breadcrumb with selected city (commented out as per original)
  // const cityBreadcrumb = { href: '/', label: selectedCity.city_name || 'Home' }
  const allBreadcrumbs = [...breadcrumbs.slice(1)]

  // If superCategoryUrl is provided, replace the second breadcrumb (index 1)
  if (superCategoryUrl && isProductPage && breadcrumbs.length > 1) {
    console.log("superCategoryUrl", superCategoryUrl)

    allBreadcrumbs[1] = {
      href: `/${city}/${superCategoryUrl}`,
      label: capitalizeFirstLetter(removeOnRent(superCategoryUrl)),
    }
  }

  // Calculate maxItems based on screen size
  const maxItems = width && width < 768 ? 3 : 5

  // Calculate visible breadcrumbs
  const visibleBreadcrumbs =
    allBreadcrumbs.length <= maxItems
      ? allBreadcrumbs
      : [
          allBreadcrumbs[0], // First item
          {
            href: "#",
            label: "ellipsis",
            middle: allBreadcrumbs.slice(
              1,
              allBreadcrumbs.length - (maxItems - 2),
            ), // Middle items to hide
          },
          ...allBreadcrumbs.slice(-(maxItems - 2)), // Last (maxItems - 2) items
        ]

  return (
    <Breadcrumb className='container py-3 md:py-6'>
      <BreadcrumbList className='line-clamp-1 flex items-center justify-start overflow-hidden'>
        {visibleBreadcrumbs.map((breadcrumb, index) => (
          <BreadcrumbItem key={breadcrumb.href}>
            {"middle" in breadcrumb ? (
              <Popover>
                <PopoverTrigger asChild>
                  <button
                    className='flex items-center gap-1 text-neutral-500 hover:text-neutral-600 focus:outline-none'
                    aria-label='Show more breadcrumbs'
                  >
                    <BreadcrumbEllipsis className='h-4 w-4' />
                  </button>
                </PopoverTrigger>
                <PopoverContent align='start' className='w-48 p-0'>
                  <nav className='flex flex-col'>
                    {breadcrumb.middle.map((item) => (
                      <Link
                        key={item.href}
                        // href={item.href}
                        href={index === 0 ? item.href : addOnRent(item.href)}
                        className='px-3 py-2 !text-sh7 text-neutral-500 hover:bg-muted hover:text-neutral-600 md:!text-sh5'
                      >
                        {item.label}
                      </Link>
                    ))}
                  </nav>
                </PopoverContent>
              </Popover>
            ) : index === visibleBreadcrumbs.length - 1 ? (
              <BreadcrumbPage className='!text-sh7 font-semibold text-neutral-900 md:!text-sh5'>
                {breadcrumb.label}
              </BreadcrumbPage>
            ) : (
              <BreadcrumbLink asChild>
                <Link
                  href={
                    index === 0 ? breadcrumb.href : addOnRent(breadcrumb.href)
                  }
                  className='!text-sh7 font-semibold text-neutral-500 hover:text-neutral-600 md:!text-sh5'
                >
                  {breadcrumb.label}
                </Link>
              </BreadcrumbLink>
            )}

            {index < visibleBreadcrumbs.length - 1 && (
              <BreadcrumbSeparator>
                <ChevronRight className='h-4 w-4' />
              </BreadcrumbSeparator>
            )}
          </BreadcrumbItem>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
