"use client"

import { addOnRent, removeOnRent } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import type { SubCategory } from "@/types"
import Link from "next/link"
import React, { useEffect, useRef, useState, useSyncExternalStore } from "react"
import type { Swiper as SwiperType } from "swiper"
import "swiper/css"
import "swiper/css/free-mode"
import { FreeMode, Mousewheel } from "swiper/modules"
import { Swiper, SwiperSlide } from "swiper/react"
import { Separator } from "../ui/separator"

// Function to calculate scroll progress
const getScrollProgress = () => {
  const currentScroll = window.scrollY
  return Math.min(Math.max((currentScroll - 650) / 100, 0), 1)
}

// Custom hook to track scroll progress
const useScrollProgress = () =>
  useSyncExternalStore(
    (callback) => {
      if (typeof window !== "undefined") {
        window.addEventListener("scroll", callback)
        return () => window.removeEventListener("scroll", callback)
      }
      return () => {}
    },
    // getSnapshot for client
    () => (typeof window !== "undefined" ? getScrollProgress() : 0),
    // getServerSnapshot for SSR
    () => 0,
  )

type StickyCategoryNavProps = {
  subCategories: SubCategory[]
  city: string
  cat: string
  subcat: string | undefined
}

const StickyCategoryNav = ({
  subCategories,
  city,
  cat,
  subcat,
}: StickyCategoryNavProps) => {
  const scrollProgress = useScrollProgress()
  const swiperRef = useRef<SwiperType | null>(null)

  const isScrolled = scrollProgress > 0

  const getLinks = (isAll: boolean, data?: SubCategory) => {
    const label = isAll ? "All" : data?.sc_name || ""
    const url = isAll ? cat : data?.url || ""
    const superCategoryUrl = isAll ? cat : data?.super_category_url || ""

    const href = isAll
      ? `/${city}/${subcat ? addOnRent(superCategoryUrl) : superCategoryUrl}`
      : `/${city}/${removeOnRent(superCategoryUrl)}/${url}`

    const isActive = isAll ? !subcat : subcat === url

    return { label, href, isActive }
  }

  // Move to active item when navigating between pages
  useEffect(() => {
    if (swiperRef.current) {
      const activeIndex = subCategories.findIndex((item) => subcat === item.url)
      swiperRef.current.slideTo(activeIndex !== -1 ? activeIndex + 1 : 0)
    }
  }, [subcat, subCategories])
  const [adminOnly, setAdminOnly] = useState(false)
  useEffect(() => {
    setAdminOnly(
      ((window &&
        window.sessionStorage &&
        window.sessionStorage.getItem("backend_order")) ??
        "") == "true",
    )
  }, [])

  return (
    <div
      className={cn(
        "container sticky left-0 right-0 top-0 z-[50] mx-auto w-full overflow-hidden rounded-b-2xl bg-neutral-100 px-0 transition-all duration-300 md:w-[calc(100%-24px)]",
        isScrolled
          ? "h-auto p-3 opacity-100 md:p-6"
          : "pointer-events-none h-0 p-0 opacity-0",
      )}
      style={{
        transform: `translateY(${(1 - scrollProgress) * -10}px)`,
        transition: "opacity 0.3s ease, padding 0.3s ease, transform 0.3s ease",
      }}
    >
      <Swiper
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        slidesPerView={"auto"}
        spaceBetween={10}
        freeMode={true}
        mousewheel={{ forceToAxis: true }}
        centeredSlides={false}
        slideToClickedSlide={true}
        modules={[FreeMode, Mousewheel]}
        className='flex gap-4'
        breakpoints={{
          320: { slidesPerView: 3.5 },
          480: { slidesPerView: 4.5 },
          768: { slidesPerView: 5.5 },
          1024: { slidesPerView: 6.5 },
          1280: { slidesPerView: 7 },
        }}
      >
        <SwiperSlide className='w-auto'>
          <Link
            href={getLinks(true).href}
            scroll={false}
            className={cn(
              "line-clamp-1 min-w-max px-3 text-center !text-sh7 transition-all duration-300 md:!text-sh5",
              getLinks(true).isActive
                ? "text-primary-500"
                : "text-neutral-900 hover:text-neutral-700",
            )}
          >
            <SpImage
              src={
                "https://images.sharepal.in/misc/hard-coded/sharepal/Product=All%20Products.webp"
              }
              alt='all'
              width={100}
              height={100}
              className='mx-auto mb-2 flex h-12 w-12 object-contain md:hidden'
            />
            All
            {getLinks(true).isActive && (
              <Separator className='mx-auto mt-1 h-[2px] w-full rounded-full bg-primary-500 md:mt-3' />
            )}
          </Link>
        </SwiperSlide>
        {subCategories.map((item) => {
          const item_data = getLinks(false, item)
          if (!item.admin_only || item.admin_only == adminOnly)
            return (
              <SwiperSlide key={item.id} className='w-auto'>
                <Link
                  href={item_data.href}
                  scroll={false}
                  className={cn(
                    "line-clamp-2 break-words px-3 text-center !text-sh7 transition-all duration-300 md:!text-sh5",
                    item_data.isActive
                      ? "text-primary-500"
                      : "text-neutral-900 hover:text-neutral-700",
                  )}
                >
                  <SpImage
                    src={item.sc_image}
                    alt={item.sc_name_ri}
                    width={100}
                    height={100}
                    className='mx-auto mb-2 flex h-12 w-12 object-contain md:hidden'
                  />
                  {item.sc_name}
                  {item_data.isActive && (
                    <Separator className='mx-auto mt-1 h-[2px] w-full rounded-full bg-primary-500 md:mt-3' />
                  )}
                </Link>
              </SwiperSlide>
            )
        })}
      </Swiper>
    </div>
  )
}

export default React.memo(StickyCategoryNav)
