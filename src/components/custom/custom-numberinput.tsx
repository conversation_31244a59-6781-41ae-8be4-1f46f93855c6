import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import React from "react"
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"

interface CustomNumberInputProps {
  value?: string
  onChange?: (value: string) => void
  className?: string
  placeholder?: string
  disabled?: boolean
  readOnly?: boolean
  maxLength?: number
}

interface FormNumberInputProps<TFieldValues extends FieldValues>
  extends Omit<CustomNumberInputProps, "value" | "onChange"> {
  form: UseFormReturn<TFieldValues>
  name: FieldPath<TFieldValues>
  label?: string
  description?: string
  required?: boolean
}

// Standalone input component
export const CustomNumberInput: React.FC<CustomNumberInputProps> = ({
  value,
  onChange,
  className,
  placeholder,
  disabled,
  readOnly,
  maxLength,
  ...props
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value
    if (/^\d*$/.test(val)) {
      onChange?.(val)
    }
  }

  return (
    <Input
      type='text'
      inputMode='numeric'
      className={cn(
        "h-12 !rounded-none border-0 focus-visible:border-0",
        className,
      )}
      pattern='\d*'
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      disabled={disabled}
      readOnly={readOnly}
      maxLength={maxLength}
      {...props}
    />
  )
}

// Form-integrated version
export function FormNumberInput<TFieldValues extends FieldValues>({
  form,
  name,
  label,
  description,
  required = false,
  className,
  ...props
}: FormNumberInputProps<TFieldValues>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          {label && (
            <FormLabel className='!text-b4'>
              {label} {required && <span className='text-red-500'>*</span>}
            </FormLabel>
          )}
          <FormControl>
            <CustomNumberInput
              {...props}
              value={field.value}
              onChange={field.onChange}
              className={className}
            />
          </FormControl>
          {description && <FormMessage />}
        </FormItem>
      )}
    />
  )
}
