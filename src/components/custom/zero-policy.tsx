import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { HARD_CODED_IMAGE_URL } from "@/constants"
import SpImage from "@/shared/SpImage/sp-image"
import { IconCashOff } from "../Icons/icon-cashoff"

const benefits = [
  "Zero Security Deposit",
  "Zero Delivery Charges",
  "Zero Hidden Charges",
]

export function ZeroPolicy() {
  return (
    <Card className='rounded-xl border-[#E4DDFB] bg-[#F7F5FF] md:rounded-2xl md:p-2'>
      <CardHeader className='flex-row items-center justify-between space-y-0 p-0'>
        <div className='flex items-center gap-2'>
          <SpImage
            // src={
            //   'https://images.sharepal.in/Static+Images/Sharepal-advantage-logo.png'
            // }
            src={`${HARD_CODED_IMAGE_URL}/SharePal+Advantage+Logo+.webp`}
            height={45}
            width={200}
            className='h-full w-40'
          />

          <CardTitle className='text-sm font-bold'>
            <span>Transparent prices</span>
            <br />
            <span className='text-category-purple'>Zero Surprises </span>
          </CardTitle>
        </div>
        {/* <Info className="h-4 w-4 text-muted-foreground" /> */}
      </CardHeader>
      <CardContent className='p-2 pt-0'>
        <ul className='flex flex-col gap-2'>
          {benefits.map((benefit) => (
            <li
              key={benefit}
              className='flex items-center gap-2 text-b4 text-primary-900'
            >
              <IconCashOff className='t h-4 w-4' />
              {benefit}
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}
