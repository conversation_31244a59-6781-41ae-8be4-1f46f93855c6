"use client"

import { fetchSubCategoriesWithSC } from "@/actions/category"
import { fetchSuperCategories } from "@/actions/super-category"
import { cn } from "@/lib/utils"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { Camera, Film, Gamepad2, Mountain } from "lucide-react"
import { JSX, memo, useEffect, useRef, useState } from "react"
import { Swiper, SwiperRef, SwiperSlide } from "swiper/react"
import MobileCategoryContent from "./mobile-category-content"

// Define the icons mapping for super categories (for now, as specified)
const categoryIcons: { [key: string]: JSX.Element } = {
  camera: <Camera className='h-6 w-6' />,
  gaming: <Gamepad2 className='h-6 w-6' />,
  outdoor: <Mountain className='h-6 w-6' />,
  entertainment: <Film className='h-6 w-6' />,
}

function MobileCategory({ onClose }: { onClose: () => void }) {
  const [activeTab, setActiveTab] = useState<string | null>(null)

  const { data: superCategories, isLoading: isSuperCategoriesLoading } =
    useQuery({
      queryKey: ["super-categories"],
      queryFn: fetchSuperCategories,
    })

  const { data: subCategories } = useQuery({
    queryKey: ["sub-categories", activeTab],
    queryFn: () => fetchSubCategoriesWithSC(activeTab || ""),
    enabled: !!activeTab,
    staleTime: 1000 * 60 * 5,
  })

  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab)
  }

  const swiperRef = useRef<SwiperRef | null>(null)

  // Update Swiper when activeTab changes (and after Swiper is initialized)
  useEffect(() => {
    if (swiperRef.current && superCategories) {
      const newIndex = superCategories.findIndex((cat) => cat.url === activeTab)
      if (newIndex !== -1) {
        swiperRef.current.swiper.slideTo(newIndex)
      }
    }
  }, [activeTab, superCategories])

  const onSlideChange = () => {
    if (swiperRef.current && superCategories) {
      const activeIndex = swiperRef.current.swiper.activeIndex
      if (activeIndex >= 0 && activeIndex < superCategories.length) {
        handleTabChange(superCategories[activeIndex].url)
      }
    }
  }

  const swiperOptions = {
    spaceBetween: 0,
    slidesPerView: 1,
    speed: 400, // Default slide transition duration
    onSlideChange: onSlideChange,
  }

  useEffect(() => {
    if (superCategories && superCategories.length > 0 && !activeTab) {
      setActiveTab(superCategories[0].url)
    }
  }, [superCategories, activeTab])

  if (isSuperCategoriesLoading) {
    return <div>Loading super categories...</div>
  }

  if (!superCategories || superCategories.length === 0) {
    return <div>No super categories available.</div>
  }

  return (
    <div className='mx-auto flex h-full w-full max-w-md flex-col bg-gray-100'>
      {/* Top Tabs */}
      <div className='border-b'>
        <div className='flex justify-between'>
          {superCategories.map((category) => (
            <button
              key={category.super_category_id}
              onClick={() => {
                handleTabChange(category.url)
                // If you want to trigger swiper slide from click:
                //   const newIndex = superCategories.findIndex((cat) => cat.url === category.url);
                //   if (newIndex !== -1 && swiperRef.current) {
                //     swiperRef.current.swiper.slideTo(newIndex);
                //   }
              }}
              className={cn(
                "relative flex flex-1 flex-col items-center justify-center py-3",
                activeTab === category.url
                  ? "text-primary"
                  : "text-muted-foreground",
              )}
            >
              {categoryIcons[
                category.super_category_short_name.toLowerCase()
              ] || <Camera className='h-6 w-6' />}
              {activeTab === category.url && (
                <motion.div
                  layoutId='activeTabIndicator'
                  className='absolute bottom-0 h-1 w-full bg-primary'
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Content Area */}
      <div className='flex-1 overflow-auto'>
        <Swiper
          {...swiperOptions}
          ref={swiperRef}
          freeMode
          onSwiper={(swiper) => {
            // Set initial slide only after swiper is ready
            const initialIndex = superCategories.findIndex(
              (cat) => cat.url === activeTab,
            )
            if (initialIndex !== -1) {
              swiper.slideTo(initialIndex, 0, false) // Set without animation
            }
          }}
        >
          {superCategories.map((category) => (
            <SwiperSlide
              key={category.super_category_id}
              className='min-h-[70vh]'
            >
              <div className='h-full w-full'>
                <MobileCategoryContent
                  title={category.super_category_name}
                  description={category.super_category_desc}
                  image={category.super_category_image}
                  color={
                    category.super_category_short_name.toLowerCase() ===
                      "camera" ||
                    category.super_category_short_name.toLowerCase() ===
                      "entertainment"
                      ? "bg-red-500"
                      : category.super_category_short_name.toLowerCase() ===
                          "gaming"
                        ? "bg-purple-500"
                        : "bg-green-500"
                  }
                  subcategories={subCategories ?? []}
                  onClose={onClose}
                />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  )
}

export default memo(MobileCategory)
