"use client"

import { addCartItem } from "@/actions/cart"
import { getCookie } from "@/functions/cookies"
import { formatUrlName } from "@/functions/small-functions"
import useSurgeProduct from "@/hooks/use-prodcut-surge"
import { useThrottle } from "@/hooks/use-throttle"
import { trackAddToCart } from "@/lib/gtag-event"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { RentalItem } from "@/types"
import { ChevronRight } from "lucide-react"
import { useState } from "react"
import { toast } from "sonner"
import ComboProductCard from "../cards/combo-product-card"
import { ProductPageTitle } from "../heading/prouduct-page"
import { AdaptiveWrapper } from "../modals/adaptive-wrapper"
import { Badge } from "../ui/badge"
import { Button } from "../ui/button"

type ComboProductsProps = {
  data: RentalItem[]
  city: string
}

const ComboProducts = ({ data, city }: ComboProductsProps) => {
  const [showAllProducts, setShowAllProducts] = useState(false)

  // Store hooks
  const { user } = useUserStore()
  const { addToCart } = useCheckoutStore()
  const { total_days, surge_factor, city_surge, same_day_surge } =
    useRentalStore()

  // Initialize surge products
  useSurgeProduct({
    type: "ri_names",
    ri_names: data.map((item) => item.ri_name),
  })

  // Handle add to cart functionality
  const handleAddToCart = async (
    product: RentalItem,
    rent: number,
    mainSurge: number,
  ) => {
    try {
      const response = await addCartItem({
        user_id: user ? user.id : 0,
        id: product.id,
        user_uid: getCookie("uid") || "",
        num_days: total_days,
        size: "", // No size selection for combo products
        cart_type: "rent",
        surge_factor,
        city_surge,
        same_day_surge,
        final_surge: mainSurge,
      })

      trackAddToCart(
        product.ri_short_name,
        rent,
        product.ri_code,
        product.ri_image.split(";")[0],
        product.category_short_name,
        "", // No size for combo products
        "Rent",
        formatUrlName(product.super_category_url),
      )

      if (response) {
        toast.success("Added to cart")
        addToCart(response, same_day_surge)
      }
    } catch (error) {
      console.error(error)
      toast.error("Failed to add to cart")
    }
  }

  // Throttle the add to cart function
  const throttledAddToCart = useThrottle(handleAddToCart, 2000) as (
    product: RentalItem,
    rent: number,
    mainSurge: number,
  ) => Promise<void>

  if (!data || data.length === 0) return null

  const hasMoreProducts = data.length > 2
  const displayedProducts = data.slice(0, 2)

  const renderComboProducts = (products: RentalItem[]) =>
    products.map((product, index) => (
      <ComboProductCard
        key={product.id || index}
        product={product}
        city={city}
        className='min-w-[320px] md:min-w-[400px]'
        handleAddToCart={throttledAddToCart}
      />
    ))

  const AllProductsView = () => (
    <div className='flex flex-col gap-4 p-4'>
      <div className='grid grid-cols-1 gap-4'>{renderComboProducts(data)}</div>
    </div>
  )

  return (
    <>
      <div className='relative flex w-full flex-col items-start overflow-hidden rounded-2xl bg-gray-100 p-3 md:rounded-3xl md:p-5'>
        <ProductPageTitle
          heading='Exciting Bundle Offers for you!'
          badge={
            <Badge
              variant='secondary'
              className='flex items-center justify-center gap-1 bg-secondary-200 !text-b2'
            >
              <span className='text-sh2 text-secondary-900'>
                {data.length} Deals
              </span>
              Available
            </Badge>
          }
        />

        {/* Mobile: Stacked View (hidden on md and above) */}
        <div className='mt-4 grid w-full grid-cols-1 gap-4 md:hidden'>
          {renderComboProducts(displayedProducts)}
          {hasMoreProducts && (
            <Button
              onClick={() => setShowAllProducts(true)}
              variant='outline-primary'
            >
              <span className='text-b4 font-medium'>
                View all Products ({data.length})
              </span>
              <ChevronRight className='h-5 w-5' />
            </Button>
          )}
        </div>

        {/* Desktop: Horizontal Scroll (hidden on mobile, shown on md and above) */}
        <div className='custom-scrollbar-black hidden overflow-auto md:mt-4 md:flex md:w-full md:items-start md:gap-4 md:py-2 xl:gap-6'>
          {renderComboProducts(data)}
        </div>
      </div>

      {/* View All Products Modal */}
      <AdaptiveWrapper
        open={showAllProducts}
        onOpenChange={setShowAllProducts}
        title='All Bundle Offers'
        desktop={{ type: "sheet", side: "right" }}
        tablet={{ type: "sheet", side: "right" }}
        mobile={{ type: "drawer", side: "bottom" }}
        className='max-h-[90vh]'
      >
        <AllProductsView />
      </AdaptiveWrapper>
    </>
  )
}

export default ComboProducts
