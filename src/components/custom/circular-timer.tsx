"use client"

import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react"

interface CircularTimerProps {
  minutes: number
  size?: number
  strokeWidth?: number
  color?: string
  backgroundColor?: string
  direction?: "clockwise" | "counterclockwise" | "anticlockwise"
  theme?: "default" | "success" | "warning" | "danger" | "info"
  onComplete?: () => void
  className?: string
  showControls?: boolean
}

const THEME_COLORS = {
  default: "#4ade80", // Green
  success: "#10b981", // Emerald
  warning: "#f59e0b", // Amber
  danger: "#ef4444", // Red
  info: "#3b82f6", // Blue
} as const

const TimerCircle = ({
  center,
  radius,
  stroke,
  strokeWidth,
  progress,
  direction,
  circumference,
  isPulsing,
}: {
  center: number
  radius: number
  stroke: string
  strokeWidth: number
  progress: number
  direction: "clockwise" | "counterclockwise" | "anticlockwise"
  circumference: number
  isPulsing?: boolean
}) => {
  // Calculate rotation based on direction
  const getRotation = () => {
    switch (direction) {
      case "clockwise":
        return "-90deg"
      case "counterclockwise":
      case "anticlockwise":
        return "-90deg"
      default:
        return "0deg"
    }
  }

  // Calculate stroke dash offset based on direction
  const getStrokeDashOffset = () => {
    switch (direction) {
      case "clockwise":
        return circumference * progress
      case "anticlockwise":
        return circumference * (progress - 1)
      case "counterclockwise":
        return circumference * progress
      default:
        return circumference * progress
    }
  }

  return (
    <motion.circle
      cx={center}
      cy={center}
      r={radius}
      fill='transparent'
      stroke={stroke}
      strokeWidth={strokeWidth}
      strokeDasharray={circumference}
      // strokeLinecap='round'
      style={{
        transformOrigin: "start",
        rotate: getRotation(),
        pathLength: 1,
      }}
      initial={{
        strokeDashoffset: circumference,
      }}
      animate={{
        strokeDashoffset: getStrokeDashOffset(),
        ...(isPulsing && { strokeOpacity: [0.3, 0.7, 0.3] }),
      }}
      transition={{
        duration: 0.016, // For 60fps smooth animation
        ease: "linear",
        repeat: 0,
      }}
    />
  )
}

const TimerControls = ({
  isRunning,
  onToggle,
  onReset,
}: {
  isRunning: boolean
  onToggle: () => void
  onReset: () => void
}) => (
  <div className='mt-2 flex gap-2'>
    <motion.button
      onClick={onToggle}
      className='rounded-md bg-gray-100 px-2 py-1 text-xs text-gray-700 hover:bg-gray-200'
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {isRunning ? "Pause" : "Resume"}
    </motion.button>
    <motion.button
      onClick={onReset}
      className='rounded-md bg-gray-100 px-2 py-1 text-xs text-gray-700 hover:bg-gray-200'
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      Reset
    </motion.button>
  </div>
)

export default function CircularTimer({
  minutes,
  size = 200,
  strokeWidth = 12,
  color,
  backgroundColor = "#e6e6e6",
  direction = "clockwise",
  theme = "default",
  onComplete,
  className,
  showControls = true,
}: CircularTimerProps) {
  const [timeLeft, setTimeLeft] = useState(minutes * 60 * 1000) // Convert to milliseconds
  const [isRunning, setIsRunning] = useState(true)
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now())

  const { radius, circumference, center, progress, isPulsing } = useMemo(() => {
    const radius = (size - strokeWidth) / 2
    const circumference = radius * 2 * Math.PI
    const center = size / 2
    const totalTime = minutes * 60 * 1000 // Total time in milliseconds
    const progress = timeLeft / totalTime
    const isPulsing = progress < 0.1 && timeLeft > 0

    return { radius, circumference, center, progress, isPulsing }
  }, [size, strokeWidth, minutes, timeLeft])

  const timerColor = useMemo(() => color || THEME_COLORS[theme], [color, theme])

  const formatTimeLeft = () => {
    const minutes = Math.floor(timeLeft / (60 * 1000))
    const seconds = Math.floor((timeLeft % (60 * 1000)) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, "0")}`
  }

  useEffect(() => {
    let animationFrameId: number

    const updateTimer = () => {
      const now = Date.now()
      const delta = now - lastUpdateTime

      if (isRunning && timeLeft > 0) {
        setTimeLeft((prevTime) => {
          const newTime = Math.max(0, prevTime - delta)
          if (newTime <= 0) {
            setIsRunning(false)
            onComplete?.()
            return 0
          }
          return newTime
        })
        setLastUpdateTime(now)
        animationFrameId = requestAnimationFrame(updateTimer)
      }
    }

    if (isRunning && timeLeft > 0) {
      animationFrameId = requestAnimationFrame(updateTimer)
    }

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId)
      }
    }
  }, [isRunning, timeLeft, lastUpdateTime, onComplete])

  const handleReset = useCallback(() => {
    setTimeLeft(minutes * 60 * 1000)
    setLastUpdateTime(Date.now())
    setIsRunning(true)
  }, [minutes])

  const handleToggle = useCallback(() => {
    setIsRunning((prev) => !prev)
  }, [])

  return (
    <div className={cn("relative flex items-center justify-center", className)}>
      <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
        {/* Background circle */}
        <circle
          cx={center}
          cy={center}
          r={radius}
          fill='transparent'
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
        />

        {/* Progress circle */}
        <TimerCircle
          center={center}
          radius={radius}
          stroke={timerColor}
          strokeWidth={strokeWidth}
          progress={progress}
          direction={direction}
          circumference={circumference}
        />

        {/* Pulsing effect overlay */}
        {isPulsing && (
          <TimerCircle
            center={center}
            radius={radius}
            stroke='#ff0000'
            strokeWidth={strokeWidth}
            progress={progress}
            direction={direction}
            circumference={circumference}
            isPulsing
          />
        )}
      </svg>

      {/* Time display */}
      <div className='absolute flex flex-col items-center justify-center'>
        <motion.span
          className='text-xl font-bold'
          animate={{
            scale: isPulsing ? [1, 1.05, 1] : 1,
          }}
          transition={{
            repeat: isPulsing ? Number.POSITIVE_INFINITY : 0,
            duration: isPulsing ? 1.5 : 0,
          }}
        >
          {formatTimeLeft()}
        </motion.span>

        {showControls && (
          <TimerControls
            isRunning={isRunning}
            onToggle={handleToggle}
            onReset={handleReset}
          />
        )}
      </div>
    </div>
  )
}
