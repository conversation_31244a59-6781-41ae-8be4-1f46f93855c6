"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  ChevronRight,
  FileTextIcon,
  Loader2,
  ShoppingCart,
  TrendingUp,
} from "lucide-react"
import { memo, Suspense, useCallback, useEffect, useState } from "react"
import { CouponCard } from "../cards/coupon-card"
import { CurrencyNoteAddIcon } from "../Icons/product-page-icons"
import { PriceTag } from "./price-tag"
import { ZeroPolicy } from "./zero-policy"

import AvailableOffers from "../modals/available-offers"
import RentalChargeCalculator from "../modals/rental-charge-calculator"

import { addCartItem } from "@/actions/cart"
import { fetchRentalVarients } from "@/actions/category"
import { fetchAllCoupons } from "@/actions/common"
import {
  capitalizeFirstLetter,
  formatUrlName,
  moneyFormatter,
  splitString,
} from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"
import { useCalendarStore, useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { RentalItemProduct } from "@/types/index"
import { IconDatePicker } from "../Icons"
import CartItems from "../modals/cart"
import RecommedProducts from "../modals/recommended-products"
import SelectColorAndSize from "../modals/select-color-size"
import SelectSize from "../modals/select-size"
// import { checkProductInCart } from '@/utils/cart-check'
import { handleFavourite } from "@/actions/user"
import { trackAddToCart, trackProductViewed } from "@/lib/gtag-event"
import { toast } from "sonner"

import { BottomCartBar } from "./bottom-cart-bar"

import { getCookie } from "@/functions/cookies"
import useCalculateRent from "@/hooks/use-calculate-rent"
import useSurgeProduct from "@/hooks/use-prodcut-surge"
import { useThrottle } from "@/hooks/use-throttle"
import { useQuery } from "@tanstack/react-query"
import { AnimatePresence, motion } from "framer-motion"
import { useParams } from "next/navigation"
import SideView from "../modals/modal-wrapper"
import ReturnPolicy from "../modals/return-policy"
import SkeletonCard from "../skeletons/coupon-card-skeleton"
import { Skeleton } from "../ui/skeleton"
import { Typography } from "../ui/typography"
import RenderTag from "./product-tag"

import { getCityMinTh } from "@/actions/city"
import damagePolicies from "@/constants/damage-policy.json"
import replacementPolicies from "@/constants/replacement-policy.json"
import { useOnboardingStore } from "@/store/onboarding-store"

import {
  calculateTransitDate,
  formatDateWithOrdinal,
} from "@/utils/date-logics"
import { addDays, isBefore } from "date-fns"
import {
  CautionCircleOutlinedIcon,
  HeartFilledIcon,
  HeartOutlinedIcon,
  InfoCircleFilledIcon,
} from "sharepal-icons"
import { CarepalAssure } from "./carepal-assure"

interface ProductInfoProps {
  product: RentalItemProduct
}

function ProductInfo({ product }: ProductInfoProps) {
  const [showAvailableOffers, setShowAvailableOffers] = useState(false)
  const [showRecommededProducts, setShowRecommededProducts] = useState(false)
  const [showRentalChargesCalculator, setShowRentalChargesCalculator] =
    useState(false)
  const [selectedSize, setSelectedSize] = useState<string>("")
  const [showCart, setShowCart] = useState(false)
  const [showSizes, setShowSizes] = useState(false)
  const [showSizeColorSelect, setShowSizeColorSelect] = useState(false)
  const { openCalendar } = useCalendarStore()
  const { openModal } = useOnboardingStore()

  const { ri_short_name, ri_name } = product

  const {
    city_surge,
    delivery_date,
    selectedCity,
    same_day_surge,
    surge_factor,
    total_days,
  } = useRentalStore()

  const { rent, extraDayRent, mainSurge } = useCalculateRent({
    product,
    type: "product",
  })

  // console.log({ city_surge, same_day_surge, surge_factor })

  const { user, setUser } = useUserStore()
  const { addToCart } = useCheckoutStore()
  const [isCartAddtionLoading, setisCartAddtionLoading] = useState(false)

  useEffect(() => {
    trackProductViewed(
      product.ri_short_name,
      rent ?? 0,
      product.ri_code,
      product.ri_image?.split(",")[0],
      product.category_short_name,
      formatUrlName(product.super_category_url),
    )
  }, [])

  const handleAddToCart = async () => {
    setisCartAddtionLoading(true)
    if (product.size_specific) {
      if (selectedSize == "") {
        if (!showSizeColorSelect) setShowSizeColorSelect(true)
        setisCartAddtionLoading(false)
        return
      }
    }
    setShowSizeColorSelect(false)

    if (product.customizable) {
      setShowRecommededProducts(true)
    }
    try {
      const response = await addCartItem({
        user_id: user ? user.id : 0,
        id: product.id,
        user_uid: getCookie("uid") || "",
        num_days: total_days,
        size: selectedSize,
        cart_type: "rent",
        surge_factor,
        city_surge,
        same_day_surge,
        final_surge: mainSurge,
      })

      trackAddToCart(
        product.ri_short_name,
        rent,
        product.ri_code,
        product.ri_image.split(";")[0],
        product.category_short_name,
        selectedSize,
        "Rent",
        formatUrlName(product.super_category_url),
      )
      if (response) {
        toast.success("Added to cart")
        addToCart(response, same_day_surge)
      }
    } catch (error) {
      console.error(error)
    } finally {
      setisCartAddtionLoading(false)
    }
  }

  const throttledAddToCart = useThrottle(handleAddToCart, 0)

  // **Handle Favorite Toggle**
  const toggleFavourite = useCallback(
    async (itemId: number) => {
      if (!user) {
        openModal()
        return
      }

      try {
        const updatedData = await handleFavourite(itemId, user)
        if (updatedData) setUser(updatedData)
      } catch (error) {
        console.error(error)
      }
    },
    [user, setUser, openModal],
  )

  const {
    data: coupons = [],
    isLoading: isCouponsLoading,
    // error: couponsError,
  } = useQuery({
    queryKey: ["coupons"],
    queryFn: fetchAllCoupons,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    refetchOnWindowFocus: false,
  })

  const {
    data: sizes = [],
    isLoading: isSizesLoading,
    // error: sizesError,
  } = useQuery({
    queryKey: ["rentalVariants", product.ri_short_name],
    queryFn: () => fetchRentalVarients(product.ri_short_name),
    enabled: !!product.ri_short_name && product.size_specific, // Prevents running if product name is empty
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  })

  // Only enable when need to disable the dates from product page
  const { setMinTh } = useRentalStore()

  const [restrictTransit, setRestrictTransit] = useState(false)
  const [transitDate, setTransitDate] = useState<Date | null>(null)

  const { data: minThData } = useQuery({
    queryFn: () =>
      getCityMinTh(selectedCity.city_name.toLowerCase(), [
        product.sc_name.toLowerCase(),
      ]),
    queryKey: [
      "checkTransit",
      product.id,
      delivery_date,
      selectedCity.city_name,
    ],
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  })

  useEffect(() => {
    if (delivery_date && minThData?.min_th) {
      const transitDate = calculateTransitDate(minThData.min_th)

      setTransitDate(transitDate)
      setRestrictTransit(
        isBefore(new Date(addDays(delivery_date, 1)), transitDate),
      )
      // Only enable when need to disable the dates from product page
      setMinTh(minThData.min_th ?? 0)
    }
  }, [minThData])

  useSurgeProduct({
    type: "ri_names",
    ri_names: [product.ri_short_name],
  })

  return (
    <>
      <Card className='w-full rounded-2xl border-none bg-gray-100 md:rounded-3xl xl:max-w-[394px]'>
        <CardContent className='flex flex-col gap-4 p-3 md:gap-6 md:p-4'>
          {/* <div className="space-y-4"> */}
          {/* first part with name, desc, tag , heart , trending text  */}
          <div className='flex flex-col items-start justify-between gap-2'>
            {/* <div className="space-y-1"> */}
            <RenderTag
              out_of_stock={product.out_of_stock}
              tag={product.decoration_text}
              absolute={false}
            />
            <div className='flex w-full items-start justify-between gap-2'>
              {/* <h1 className="w-full text-2xl font-bold text-neutral-900">
                {ri_short_name}
              </h1> */}

              <Typography
                as='h1'
                className='text-h6 text-neutral-900 sm:text-h5 md:text-h4 lg:text-h3 xl:text-h2'
              >
                {ri_short_name}
              </Typography>
              <div className='flex items-center justify-center gap-2'>
                <button
                  onClick={(e) => {
                    e.preventDefault()
                    toggleFavourite(product.id)
                  }}
                  type='button'
                  className={cn(
                    "justify-end p-0 transition-all duration-500",
                    user?.favourite_items?.includes(product.id)
                      ? "text-red-500"
                      : "text-gray-500",
                  )}
                >
                  {user?.favourite_items?.includes(product.id) ? (
                    <HeartFilledIcon
                      className='size-4 md:size-6'
                      strokeWidth={2}
                    />
                  ) : (
                    <HeartOutlinedIcon
                      className='size-4 md:size-6'
                      strokeWidth={2}
                    />
                  )}
                </button>
              </div>
            </div>

            <Typography
              as='p'
              className='line-clamp-3 text-b4 text-gray-600 md:text-b3 xl:text-b2'
            >
              {product.product_qualities || ri_name}
            </Typography>

            <div className='flex items-center gap-1 text-sm font-semibold text-success-700'>
              <TrendingUp className='h-4 w-4 fill-success-400 text-success-400' />
              {product.booked_count > 20
                ? product.booked_count
                : Math.floor(Math.random() * 11) + 20}{" "}
              <Typography as='p' className='text-sh7 md:text-sh5'>
                booked this month
              </Typography>
            </div>
            {/* </div> */}
          </div>

          {/* rent information price date and all. */}
          <div className='flex flex-col items-start justify-center gap-2'>
            {total_days ? (
              <Typography
                as={"div"}
                className={cn(
                  "flex items-center justify-start gap-1 !text-sh5 text-gray-600",
                )}
              >
                Rent for{" "}
                <Typography as={"p"} className={cn("font-bold text-gray-900")}>
                  {" "}
                  <span className={cn(!total_days && "blur-sm")}>
                    {total_days}
                  </span>{" "}
                  {total_days > 1 ? "days" : "day"}
                </Typography>
              </Typography>
            ) : (
              <Typography
                as={"div"}
                className={cn(
                  "flex items-center justify-start gap-1 !text-sh5 text-gray-600",
                )}
              >
                Select Dates to view price
              </Typography>
            )}
            <div className='flex w-full items-center justify-between gap-1 md:gap-2'>
              {/* pricing information */}

              <div
                className={cn(
                  "flex items-center justify-start gap-2 lg:items-end",
                )}
              >
                {!restrictTransit && rent ? (
                  <PriceTag amount={rent} />
                ) : (
                  <Typography
                    as={"p"}
                    className={`text-h4 text-gray-900 sm:text-h3 md:text-h2 lg:text-h1`}
                  >
                    ₹<span className='inline-flex blur-md'>XXX</span>
                  </Typography>
                )}
              </div>

              {/* <Button
                onClick={() => setShowRentalChargesCalculator(true)}
                variant='link'
                className='h-auto p-0 !text-bt3 text-primary-500'
              >
                Check Rates <ChevronRight className='h-4 w-4' />
              </Button> */}
              <RentalChargeCalculator
                openSideView={showRentalChargesCalculator}
                setOpenSideView={setShowRentalChargesCalculator}
                product={product}
              />
            </div>

            <div className='w-full'>
              <Typography
                as={"div"}
                className='flex items-center gap-1 text-sh5 text-decorative-pink'
              >
                <CurrencyNoteAddIcon className={"w-5"} />{" "}
                {!restrictTransit && total_days && extraDayRent ? (
                  <>Additional day at {moneyFormatter(extraDayRent)} only</>
                ) : (
                  <p>Lowest Price Guarantee</p>
                )}{" "}
              </Typography>
              <Typography as={"span"} className='text-b6 text-gray-600'>
                Price incl. of all taxes
              </Typography>
            </div>
          </div>
          {/*  */}

          <SelectColorAndSize
            sizes={sizes}
            product={product}
            setSelectedSize={setSelectedSize}
            selectedSize={selectedSize}
            handleAddToCart={throttledAddToCart}
            isCartAddtionLoading={isCartAddtionLoading}
            openSideView={showSizeColorSelect}
            setOpenSideView={setShowSizeColorSelect}
          />

          <SelectSize
            sizes={sizes}
            openSideView={showSizes}
            setOpenSideView={setShowSizes}
            setSelectedSize={setSelectedSize}
            selectedSize={selectedSize}
          />

          {product.size_specific && (
            <>
              <div className='flex w-full items-center justify-between'>
                <h2 className='text-sm font-semibold text-gray-600'>
                  Choose Prefered
                  <strong className='text-neutral-950'> Size </strong>
                </h2>
                <Button
                  onClick={() => setShowSizes(true)}
                  variant='link'
                  className='h-auto p-0 !text-bt3 text-primary-500'
                >
                  Check Sizes <ChevronRight className='h-4 w-4' />
                </Button>
              </div>{" "}
              <div className='hide-scrollbar flex gap-3 overflow-x-auto'>
                {isSizesLoading
                  ? Array.from({ length: 5 }).map((_, index) => (
                      <Skeleton key={index} className='h-12 w-12 rounded-xl' />
                    ))
                  : sizes.map(({ size }) => (
                      <button
                        onClick={() => setSelectedSize(size)}
                        key={size}
                        className={cn(
                          "flex h-12 w-12 min-w-max cursor-pointer items-center justify-center rounded-xl border-2 border-transparent bg-neutral-150 px-3 text-center text-stone-950 transition-all",
                          selectedSize == size && "border-secondary-600",
                        )}
                      >
                        <div className='text-sm font-bold leading-tight'>
                          {size}
                        </div>
                      </button>
                    ))}
              </div>
            </>
          )}

          {!product.out_of_stock && restrictTransit && (
            <div className='flex items-center gap-2 text-b6 text-[#902922] md:text-b4'>
              <CautionCircleOutlinedIcon className='min-h-5 min-w-5 text-[#F04438]' />
              This product is available from{" "}
              {formatDateWithOrdinal(transitDate || new Date())}. Change dates
              to place an order.
            </div>
          )}

          {/* Add to cart  Button*/}
          <div className='hidden w-full md:flex'>
            {total_days > 0 ? (
              product.out_of_stock ? (
                <Button
                  variant={"neutral"}
                  // onClick={(e) => {
                  //   handleNotifyMe(e, ri_short_name)
                  // }}
                  // className='min-h-10 w-full border border-primary-900 bg-gray-100 py-3'
                  className='min-h-10 w-full bg-neutral-200 py-3 text-center font-bold'
                >
                  {/* Contact Support */}
                  <InfoCircleFilledIcon className='h-5 w-5' />
                  Out of Stock
                </Button>
              ) : restrictTransit ? (
                <Button
                  onClick={openCalendar}
                  className='h-full w-full gap-2 bg-primary-900 px-4 py-3 text-base font-semibold hover:bg-primary-900'
                >
                  <IconDatePicker />
                  Change Date
                </Button>
              ) : (
                <Button
                  disabled={
                    product.out_of_stock || !product || isCartAddtionLoading
                  }
                  onClick={throttledAddToCart}
                  className='h-full w-full gap-2 bg-primary-500 px-4 py-3 text-base font-semibold hover:bg-primary-400'
                >
                  {isCartAddtionLoading ? (
                    <Loader2 className='h-5 w-5 animate-spin' />
                  ) : (
                    <>
                      <ShoppingCart className='h-6 w-6' />
                      Add to Cart
                    </>
                  )}
                </Button>
              )
            ) : (
              <Button
                onClick={openCalendar}
                className='h-full w-full gap-2 bg-primary-900 px-4 py-3 text-base font-semibold hover:bg-primary-900'
              >
                <IconDatePicker />
                Select Date
              </Button>
            )}
            <RecommedProducts
              product={product}
              totalRent={rent}
              openView={showRecommededProducts}
              setOpenView={setShowRecommededProducts}
              setShowCart={setShowCart}
            />
            <CartItems showCart={showCart} setShowCart={setShowCart} />
          </div>

          {/* Add to cart */}

          {/* </div> */}

          {/* Available Offers */}
          <div className='flex w-full flex-col items-start justify-center gap-3 xl:max-w-[350px]'>
            <button
              onClick={() => setShowAvailableOffers(true)}
              className='flex w-full items-center justify-between'
            >
              <Typography as={"p"} className='text-sh5 text-gray-600'>
                Available Offers
                <span> ({coupons?.length} Offers) </span>
              </Typography>
              <ChevronRight className='h-4 w-4' />
            </button>
            <AvailableOffers
              coupons={coupons}
              openSideView={showAvailableOffers}
              setOpenSideView={setShowAvailableOffers}
            />
            {/* added width full here to solve the coupon container issue */}
            <div className='hide-scrollbar flex w-full items-center justify-start gap-4 overflow-x-auto'>
              {isCouponsLoading
                ? Array.from({ length: 5 }).map((_, index) => (
                    <SkeletonCard key={index} />
                  ))
                : coupons.map((coupon) => (
                    <CouponCard
                      key={coupon.coupon_code}
                      coupon={coupon}
                      className='min-w-60 max-w-60'
                    />
                  ))}
            </div>
          </div>
          {/* Available Offers Ends */}

          {/* Zero policy */}
          <ZeroPolicy />
          {/* Carepal Assure */}
          <CarepalAssure />
          {/* Zero policy ends */}
        </CardContent>
      </Card>

      <div className='md:hidden'>
        <Suspense fallback={null}>
          {/* <BottomCartBarWrapper productId={'id'} totalRent={totalRent} /> */}
          <BottomCartBar
            totalRent={rent}
            restrictTransit={restrictTransit}
            handleAddToCart={throttledAddToCart}
            product={product}
            isCartAddtionLoading={isCartAddtionLoading}
            setShowCart={setShowCart}
          />
        </Suspense>
      </div>
      {/* <Sheet>
        <RentalChargeCalculator />
      </Sheet>
      <Sheet>
        <AvailableOffers />
      </Sheet> */}
    </>
  )
}

type ProductDetailsProps = {
  desc: string
  features?: string
}

export function ProductDetails({ desc, features }: ProductDetailsProps) {
  const { selectedCity } = useRentalStore()
  const { city } = useParams()
  const [showMore, setShowMore] = useState(false)
  const featureArray =
    features && features !== "" ? splitString(features, ";") : []
  const [openRefundPolicy, setOpenRefundPolicy] = useState(false)
  const [openDamagePolicy, setOpenDamagePolicy] = useState(false)
  return (
    <div className='w-full space-y-4 rounded-2xl bg-gray-100 p-4 md:space-y-6 md:rounded-3xl md:p-6 xl:max-w-[394px]'>
      {/* Product Description */}
      {desc && (
        <div className='space-y-2'>
          <Typography
            as={"h4"}
            className='text-sh2 text-neutral-900 md:text-h6'
          >
            Product Description
          </Typography>
          <div>
            <AnimatePresence initial={false}>
              <motion.p
                key={showMore ? "full" : "truncated"}
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className={cn(
                  "overflow-hidden !text-b6 text-neutral-500 md:!text-b2",
                  !showMore && "line-clamp-4",
                )}
              >
                {desc.replace(
                  /<City Name>/g,
                  capitalizeFirstLetter(
                    city?.toString() ?? selectedCity.city_url,
                  ),
                )}
              </motion.p>
            </AnimatePresence>
            <Button
              onClick={() => setShowMore((prev) => !prev)}
              variant='link'
              className='h-auto p-0 text-bt4 font-medium text-primary-900 hover:underline md:text-bt3'
            >
              {showMore ? "Read less" : "Read more"}
            </Button>
          </div>
        </div>
      )}

      {/* Product Features */}
      {features && (
        <div className='space-y-2 md:space-y-3'>
          <Typography
            as={"h4"}
            className='text-sh2 text-neutral-900 md:text-h6'
          >
            Product Features
          </Typography>

          {featureArray.length > 0 && (
            <div className='space-y-2 text-xs md:space-y-2 md:text-sm'>
              {featureArray.map((feature) => (
                <div key={feature} className='flex w-full'>
                  <span className='w-full flex-shrink-0 text-b6 text-neutral-500 md:text-b2'>
                    • <span className='line-clamp-1'>{feature}</span>
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Policy Buttons */}
      <div className='flex gap-2 md:gap-4'>
        <Button
          variant='outline'
          onClick={() => setOpenRefundPolicy(true)}
          className='h-11 flex-1 gap-1 border-none bg-neutral-150 text-bt4 text-primary-900 shadow-transparent md:text-bt2'
        >
          <FileTextIcon className='h-4 w-4 md:mr-1' />
          Renting Policy
        </Button>
        <Button
          variant='outline'
          onClick={() => setOpenDamagePolicy(true)}
          className='h-11 flex-1 gap-1 border-none bg-neutral-150 text-bt4 text-primary-900 shadow-transparent md:text-bt2'
        >
          <FileTextIcon className='h-4 w-4 md:mr-1' />
          Damage Policy
        </Button>
      </div>

      <SideView
        className='max-w-xl'
        openSideView={openDamagePolicy}
        setOpenSideView={setOpenDamagePolicy}
        title={"Damage Policy"}
      >
        <ReturnPolicy
          title={"Damage Policy"}
          description={"We understand, sometimes things go wrong!"}
          policies={damagePolicies}
        />
      </SideView>
      <SideView
        className='max-w-xl'
        openSideView={openRefundPolicy}
        setOpenSideView={setOpenRefundPolicy}
        title={"Renting Policy"}
      >
        <ReturnPolicy
          title={"Replacement Policy"}
          description={
            "At SharePal, we are committed to providing a seamless rental experience. Our replacement policy ensures that you receive the right products in a timely manner."
          }
          policies={replacementPolicies}
        />
      </SideView>
    </div>
  )
}

export default memo(ProductInfo)
