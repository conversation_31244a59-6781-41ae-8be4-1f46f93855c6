"use client"

import type React from "react"

import { useRef } from "react"
import { motion, useAnimationFrame, useMotionValue } from "framer-motion"
import { cn } from "@/lib/utils"

interface MarqueeProps {
  children: React.ReactNode
  className?: string
  direction?: "left" | "right"
  speed?: number
  pauseOnHover?: boolean
}

export default function Marquee({
  children,
  className,
  direction = "left",
  speed = 50,
  pauseOnHover = false,
}: MarqueeProps) {
  const baseX = useMotionValue(0)
  const scrollRef = useRef<HTMLDivElement>(null)

  useAnimationFrame((time, delta) => {
    if (!scrollRef.current) return

    const contentWidth = scrollRef.current.offsetWidth

    // Calculate the total distance the text needs to travel
    const distance = contentWidth

    // Update the baseX value based on speed and direction
    let x =
      baseX.get() + ((direction === "left" ? -1 : 1) * (speed * delta)) / 1000

    // Reset position when text has moved the full distance
    if (direction === "left" && x < -distance) {
      x = 0
    } else if (direction === "right" && x > distance) {
      x = 0
    }

    baseX.set(x)
  })

  return (
    <div
      className={cn("relative overflow-hidden whitespace-nowrap", className)}
    >
      <motion.div
        ref={scrollRef}
        className='inline-block'
        style={{ x: baseX }}
        whileHover={pauseOnHover ? { animationPlayState: "paused" } : undefined}
      >
        {children}
      </motion.div>
      <motion.div
        className='inline-block'
        style={{ x: baseX }}
        whileHover={pauseOnHover ? { animationPlayState: "paused" } : undefined}
      >
        {children}
      </motion.div>
    </div>
  )
}
