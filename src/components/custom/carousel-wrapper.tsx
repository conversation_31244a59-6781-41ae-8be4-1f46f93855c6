import React from "react"
import {
  Carousel,
  CarouselContent,
  CarouselNext,
  CarouselPrevious,
} from "../ui/carousel"
import { cn } from "@/lib/utils"

type Props = {
  children: React.ReactNode
  showArrows?: boolean
  className?: string
}

const CarouselWrapper = ({ children, showArrows = true, className }: Props) => (
  <Carousel
    opts={{
      align: "start",
    }}
    className={cn("group-carousel container p-0", className)}
  >
    <CarouselContent>{children}</CarouselContent>
    {showArrows && (
      <>
        <CarouselPrevious className='md:group-carousel-hover:flex -left-12 hidden' />
        <CarouselNext className='md:group-carousel-hover:flex hidden' />
      </>
    )}
  </Carousel>
)

export default CarouselWrapper
