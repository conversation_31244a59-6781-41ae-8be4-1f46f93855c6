import { Typography } from "../ui/typography"

interface ProductPageTitleProps {
  heading: string
  badge?: React.ReactNode
}

export const ProductPageTitle = ({ heading, badge }: ProductPageTitleProps) => (
  <Typography
    as='h3'
    className='flex flex-col flex-wrap items-start gap-0.5 py-2 text-h4 text-neutral-900 md:flex-row md:items-center md:gap-2 md:text-h2'
  >
    {heading}

    {badge && badge}
  </Typography>
)
