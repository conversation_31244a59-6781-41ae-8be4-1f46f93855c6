import { getUserAddresses } from "@/actions/user"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { generateFullAddress } from "@/functions/address-utils"
import { loadScript } from "@/functions/loadScript"
import { useUserStore } from "@/store/user-store"
import { Address } from "@/types/address"
import { User } from "@/types/user"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { Truck } from "lucide-react"
import * as React from "react"
import { PlusAddOutlinedIcon } from "sharepal-icons"
import { AddDeliveryAddress } from "../checkout/delivery-address/add-delivery-address"
import { IconAddressTruck } from "../Icons/checkout"
import { AddressSkeleton } from "../skeletons/address-card-skeleton"

export default function AddressBook() {
  const { user } = useUserStore()
  const [showAddressModal, setShowAddressModal] = React.useState(false)
  // const [selectedAddressId, setSelectedAddressId] = React.useState<
  //   number | null
  // >(null)
  const [isScriptLoaded, setIsScriptLoaded] = React.useState(false)

  const {
    data: addresses,
    isLoading,
    isError,
    error,
    isSuccess,
  } = useQuery<Address[]>({
    queryKey: ["user_addresses"],
    queryFn: getUserAddresses,
    refetchOnWindowFocus: false,
    enabled: !!user,
  })

  const handleAddNew = React.useCallback(() => {
    // setSelectedAddressId(null)
    setShowAddressModal(true)
  }, [])

  const handleEdit = React.useCallback((id: number) => {
    console.info("Edit Address ID:", id)
    // setSelectedAddressId(id)
    setShowAddressModal(true)
  }, [])

  React.useEffect(() => {
    if (showAddressModal) {
      loadScript(
        `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`,
      )
      setIsScriptLoaded(true)
    }
  }, [showAddressModal])

  const renderContent = () => {
    if (isLoading && !isSuccess) {
      return (
        <>
          {[...Array(4)].map((_, index) => (
            <AddressSkeleton key={index} />
          ))}
        </>
      )
    }

    if (isError) {
      return (
        <Card className='col-span-2 rounded-2xl'>
          <CardContent className='flex flex-col items-center justify-center py-8'>
            <h3 className='mt-4 text-lg font-medium text-red-600'>
              Error loading addresses
            </h3>
            <p className='mt-2 text-sm text-gray-500'>
              {error instanceof Error ? error.message : "Something went wrong"}
            </p>
            <Button
              onClick={() => window.location.reload()}
              variant='outline'
              className='mt-4'
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      )
    }

    if (!addresses?.length && isSuccess) {
      return (
        <Card className='col-span-2 rounded-2xl'>
          <CardContent className='flex flex-col items-center justify-center py-8'>
            <div className='rounded-full bg-gray-100 p-3'>
              <Truck className='h-8 w-8 text-gray-400' />
            </div>
            <h3 className='mt-4 text-lg font-medium text-gray-900'>
              No addresses found
            </h3>
            <p className='mt-2 text-sm text-gray-500'>
              Add your first delivery address to get started.
            </p>
            <Button onClick={handleAddNew} variant='outline' className='mt-4'>
              Add New Address
            </Button>
          </CardContent>
        </Card>
      )
    }

    return addresses?.map((address, index) => (
      <motion.div
        key={address.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
      >
        <AddressCard address={address} user={user} onEdit={handleEdit} />
      </motion.div>
    ))
  }

  return (
    <>
      <AddDeliveryAddress
        open={showAddressModal}
        onOpenChange={setShowAddressModal}
        isScriptLoaded={isScriptLoaded}
        // addressId={selectedAddressId}
      />

      <div className='space-y-4 rounded-2xl bg-gray-100 p-3 md:min-h-[calc(100vh-7rem)] md:space-y-6 md:rounded-3xl md:p-6'>
        <div className='flex items-center justify-between'>
          <h2 className='text-xl font-semibold text-neutral-900 md:text-2xl'>
            Address Book
          </h2>
          <Button
            onClick={handleAddNew}
            variant={"outline-primary"}
            className='gap-1'
          >
            <PlusAddOutlinedIcon className='h-4 w-4' />
            Add New<span className='hidden md:inline-flex'>Address</span>
          </Button>
        </div>
        <div className='grid gap-4 md:grid-cols-2'>{renderContent()}</div>
      </div>
    </>
  )
}

interface AddressCardProps {
  address: Address
  user: User | null
  onEdit: (id: number) => void
}

const AddressCard: React.FC<AddressCardProps> = ({ address, user }) => (
  <Card className='relative h-full overflow-hidden rounded-2xl md:rounded-2xl'>
    <CardContent className='flex h-full min-h-28 flex-col justify-between gap-3 p-3 md:gap-4 md:p-4'>
      <div className='flex items-start justify-between'>
        <div>
          <p className='text-xs font-medium text-neutral-900 md:text-sm'>
            {address.shipment_number
              ? address.shipment_number
              : user?.whatsapp_number}{" "}
            | {address.city}
          </p>
          <p className='mt-1 text-xs text-neutral-600 md:text-sm'>
            {generateFullAddress(address)}
          </p>
        </div>
      </div>

      {/* <CardFooter className='p-0'>
        <Button
          variant='link'
          onClick={() => onEdit(address.id)}
          className='md:text h-auto p-0 text-xs text-primary-500 hover:text-primary-600 md:text-sm'
        >
          Edit Address
        </Button>
      </CardFooter> */}

      <div className='absolute bottom-0 right-0'>
        <IconAddressTruck />
      </div>
    </CardContent>
  </Card>
)
