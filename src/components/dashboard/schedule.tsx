"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { format } from "date-fns"
import { CalendarClockIcon, Loader2, MapPin } from "lucide-react"
import * as React from "react"
import { useForm } from "react-hook-form"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cn } from "@/lib/utils"
import {
  type ScheduleFormData,
  scheduleFormSchema,
  type ScheduleProps,
} from "@/types/schedule"

import { getExpectedDateScheduleDate } from "@/utils/is-time-valid"
import { generateTimeSlots } from "@/utils/time-slots"
import { IconSchedlueShipment } from "../Icons/icon-schedule-shipment"
import { Label } from "../ui/label"
import { Typography } from "../ui/typography"

export function Schedule({
  type,
  address,
  defaultDate,
  onSchedule,
  // isRental = false,
  className,
  alreadyBooked,
  // extendRentalPeriod,
  isShipmentBooking,
}: ScheduleProps) {
  const timeSlots = React.useMemo(
    () =>
      generateTimeSlots(type === "pickup", new Date(defaultDate ?? Date.now())),
    [type, defaultDate],
  )

  const expectedDate = getExpectedDateScheduleDate(defaultDate as number, type)

  const form = useForm<ScheduleFormData>({
    resolver: zodResolver(scheduleFormSchema),
    defaultValues: {
      type,
      date: new Date(expectedDate ?? Date.now()),
      address,
      timeSlot: "",
    },
  })

  function onSubmit(data: ScheduleFormData) {
    onSchedule(data)
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn(
          "relative flex h-full max-w-2xl flex-col justify-between rounded-lg bg-neutral-150",
          className,
        )}
      >
        <div className='flex h-full flex-col gap-4 p-4 md:gap-6 md:p-6'>
          <IconSchedlueShipment className='h-max w-full' />

          <AddressSection isPickupOrder={type === "pickup"} address={address} />

          <FormField
            control={form.control}
            name='date'
            render={({ field }) => (
              <FormItem>
                <Label className='text-sm text-neutral-900'>
                  Order {type === "pickup" ? "Pickup" : "Delivery"} on
                </Label>
                <div className='mt-1 flex items-center gap-2 rounded-xl bg-gray-100 px-3 py-2'>
                  <CalendarClockIcon className='h-5 w-5 text-neutral-900' />
                  <p className='flex-1 font-semibold'>
                    {format(field.value, "dd MMM yyyy")}
                  </p>
                </div>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='timeSlot'
            render={({ field }) => (
              <FormItem>
                <Label htmlFor='timeSlot'>
                  Select {type === "pickup" ? "Pickup" : "Delivery"} Time Slot
                  <span className='text-destructive-500'>*</span>
                </Label>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className='mt-1 h-10 rounded-xl bg-gray-100 p-3 md:h-11'>
                      <SelectValue
                        className='text-xs text-neutral-950'
                        placeholder='Select Time'
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {timeSlots.map((slot) => (
                      <SelectItem
                        disabled={slot.disabled}
                        key={slot.value}
                        value={slot.value}
                      >
                        {slot.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          {alreadyBooked && (
            <div className='rounded-lg bg-destructive-150 p-2 text-b4'>
              <p>
                Your shipment is already scheduled for{" "}
                {alreadyBooked.schedule_date} between{" "}
                {alreadyBooked.schedule_time}
              </p>
            </div>
          )}

          <Reminders isPickup={type === "pickup"} />
        </div>

        <div className='sticky bottom-0 w-full bg-gray-100 p-5'>
          <Button
            disabled={
              form.getValues("timeSlot") === "" ||
              alreadyBooked != null ||
              isShipmentBooking
            }
            type='submit'
            variant='blue'
            className='w-full rounded-full border-neutral-150 py-5 font-bold'
          >
            {isShipmentBooking ? (
              <Loader2 className='animate-spin' />
            ) : alreadyBooked ? (
              <>Shipment already booked</>
            ) : (
              <>Schedule Order {type === "pickup" ? "Pickup" : "Delivery"}</>
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}

type AddressSectionProps = {
  name: string
  fullAddress: string
}

function AddressSection({
  address,
  isPickupOrder,
}: {
  address: AddressSectionProps
  isPickupOrder: boolean
}) {
  return (
    <div className='rounded-2xl bg-secondary-100 p-3'>
      <Typography
        as='p'
        className='flex items-center gap-1 font-bold text-primary-500 md:text-sh2'
      >
        <MapPin className='mr-1 size-4 min-h-5 min-w-5 text-primary-500' />
        {isPickupOrder ? (
          <span className='flex items-center gap-2'>Pickup </span>
        ) : (
          <span className='flex items-center gap-2'>Delivery </span>
        )}
        Address
      </Typography>

      <div className='ml-6'>
        <h3 className='text-b4'>{address.name},</h3>
        <p className='text-b4 text-secondary-900'>{address.fullAddress}</p>
      </div>
    </div>
  )
}

const getThingsToRemember = (isPickup: boolean) => {
  const thingsToRememberForDelivery = [
    {
      number: 1,
      title: "Schedule",
      description: "Please schedule your order at least 24 hours in advance.",
    },
    {
      number: 2,
      title: "Time Slot",
      description:
        "Please select a time slot that is convenient for you. We will do our best to accommodate your request.",
    },
    {
      number: 3,
      title: "Contact Number",
      description:
        "Please provide a contact number where we can reach you in case of any issues.",
    },
  ]
  const thingsToRememberForPickup = [
    {
      number: 1,
      title: "Be Present ",
      description:
        "Be available at the pickup address during the scheduled time.",
    },
    {
      number: 2,
      title: "Secure Packaging",
      description:
        "Ensure you have locked the package as received before handing over to the delivery personnel.",
    },
  ]
  if (isPickup) {
    return thingsToRememberForPickup
  } else {
    return thingsToRememberForDelivery
  }
}

function Reminders({ isPickup }: { isPickup: boolean }) {
  const reminders = getThingsToRemember(isPickup)
  return (
    <div className='space-y-2 md:space-y-4'>
      <h3 className='text-sm font-semibold md:text-base'>
        Things to Remember:
      </h3>
      <ul className='space-y-2 md:space-y-4'>
        {reminders.map((reminder) => (
          <li key={reminder.number}>
            <p className='text-xs text-neutral-500 md:text-sm'>
              <span className='font-bold'>
                {reminder.number}. {reminder.title}:{" "}
              </span>
              {reminder.description}
            </p>
          </li>
        ))}
      </ul>
    </div>
  )
}
