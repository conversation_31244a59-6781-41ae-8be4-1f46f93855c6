"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { useQuery } from "@tanstack/react-query"
import { BadgeIndianRupee, FileBadge2Icon, InfoIcon } from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"

import { fetchAllCartItems } from "@/actions/cart"
import {
  fetchOrder,
  fetchOrderDetails,
  fetchOrderStatus,
} from "@/actions/orders"
import ApplyCouponSideView from "@/components/modals/apply-coupon"
import ContactSupportHelp from "@/components/modals/contact-support-help"
import { OrderCancellationDialog } from "@/components/modals/order-cancellation"
import ScheduleModal from "@/components/modals/schedule-modal"
import { OrderDetailsSkeleton } from "@/components/skeletons/order-details-skeleton"
import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import usePlaceOrder from "@/hooks/use-place-order"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { useSearchParams } from "next/navigation"

import { fetchAdditionalOrderSummary } from "@/actions/return-order"
import { OrderItemCard } from "@/components/cards/order-item-card"
import { Charges } from "@/components/checkout/order-summary-new/charge-item"
import CheckoutRedirectLoading from "@/components/loadings/checkout-redirect-loading"
import ChangeRentalPeriod from "@/components/modals/change-rental-period"
import DeliveryAddressSelect from "@/components/modals/delivery-address"
import ExtendRentalPeriod from "@/components/modals/extend-rental-period"
import ScheduleAgainModal from "@/components/modals/schedule-again-modal"
import CancelReturn from "@/components/return-order/cancel-return"
import { getCookie } from "@/functions/cookies"
import { AdditionalOrderSummary } from "@/types/return-order"
import ReturnDetails from "../../return-order/return-details"
import DeliveryDetails from "../delivery-details"
import OrderDetailsHeader from "../order-details-header"
import { OrderStatus } from "../order-status"

// import { useCheckoutStore } from '@/store/use-checkout-store'

type ModalKey = "apply-coupon" | "delivery-address" | "rental-period"

const showModification = (stage: string) =>
  stage === "Order Received" ||
  stage === "Order Confirmed" ||
  stage === "KYC Received"

const showPayment = (stage: string) =>
  stage === "Order Packed" ||
  stage == "Order Shipped" ||
  stage === "Order Delivered" ||
  stage === "Delivery Scheduled" ||
  stage === "Delivery Partner Booked"

export default function OrderDetails({ orderId }: { orderId: string }) {
  const [openApplyCouponModal, setOpenApplyCouponModal] = useState(false)
  const [orderCancelModal, setOrderCancelModal] = useState(false)
  const [openContactSupport, setOpenContactSupport] = useState(false)
  const [openScheduleModal, setOpenScheduleModal] = useState(false)
  const [openScheduleAgainModal, setOpenScheduleAgainModal] = useState(false)
  const [openDeliveryAddress, setOpenDeliveryAddress] = useState(false)
  const [openDateUpdate, setOpenDateUpdate] = useState(false)
  const [openExtendRentalPeriod, setOpenExtendRentalPeriod] = useState(false)
  const [openReturnDetails, setOpenReturnDetails] = useState(false)
  const [openCancelReturn, setOpenCancelReturn] = useState(false)

  const {
    setCartItems,
    setHandlingCharges,
    setAppliedCouponCode,
    setCarepalSelected,
  } = useCheckoutStore()

  const params = useSearchParams()
  const [paramData] = useState(params.get("open"))

  const { same_day_surge } = useRentalStore()

  const { user } = useUserStore()
  const { delivery_date, pickup_date, total_days } = useRentalStore()
  const {
    hanldePaymentForOrder,
    hanldePaymentForExtensionOrder,
    isExtensionPaymentLoading,
    isReturnPaymentLoading,
  } = usePlaceOrder()

  const handleExtensionPayment = (
    amount: number,
    days: number,
    new_pickup_date: Date,
  ) => {
    hanldePaymentForExtensionOrder({
      order_id: orderId,
      amount: amount,
      type: "OEX",
      days,
      new_pickup_date,
      pickup_date: pickup_date || new Date(),
    })
  }

  const {
    data: order,
    isLoading: orderLoading,
    isError: orderError,
    error: orderErrorData,
  } = useQuery({
    queryKey: ["order-details-fetch", orderId],
    queryFn: () => fetchOrderDetails(orderId),
    refetchOnWindowFocus: true,
  })

  const { data: additionalOrderSummary, isLoading: isAdditionalOrderSummary } =
    useQuery({
      queryKey: ["additoinal-order-summary", orderId],
      queryFn: () => fetchAdditionalOrderSummary(orderId),
      refetchOnWindowFocus: false,
    })

  const {
    data: orderStatus,
    isLoading: statusLoading,
    isError: statusError,
  } = useQuery({
    queryKey: ["orderStatus", orderId],
    queryFn: () => fetchOrderStatus(orderId),
    enabled: !!orderId,
    refetchOnWindowFocus: false,
  })

  const {
    data: orderData,
    isFetching: isOrderDataFetching,
    isLoading: isOrderDataLoading,
  } = useQuery({
    queryKey: ["order-data-fetch", orderId],
    queryFn: async () => {
      const data = await fetchOrder(orderId)
      if (data?.cart_items) setCartItems(data?.cart_items, same_day_surge)
      return data
    },
  })

  useEffect(() => {
    if (orderData) {
      setCarepalSelected(orderData?.order?.carepal_amount > 0 ? true : false)
      setHandlingCharges(orderData.order.cod_handling_charges ?? 0)
      setAppliedCouponCode(
        orderData?.order.coupon_code ?? "",
        orderData?.order?.coupon_discount_amount ?? 0,
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderData])

  // Fetching Cart Items when modifying orders like change rental period or update
  useQuery({
    queryKey: [
      "cart_items",
      delivery_date,
      pickup_date,
      openDateUpdate,
      openApplyCouponModal,
      openDeliveryAddress,
      openExtendRentalPeriod,
    ],
    queryFn: async () => {
      if (
        !user ||
        !orderData ||
        (!openExtendRentalPeriod &&
          !openDateUpdate &&
          !openDeliveryAddress &&
          !openApplyCouponModal)
      )
        return []
      const cartResult = await fetchAllCartItems({
        num_days: total_days,
        user_uid: getCookie("uid") ?? "",
      })
      if (!cartResult) return []
      setCartItems(cartResult, same_day_surge)
      return cartResult
    },
    refetchOnWindowFocus: false, // Prevent refetch on window focus
  })

  const [paramsStatusCheck, setParamsStatusCheck] = useState<boolean>(true)

  useEffect(() => {
    const modalActions: Record<ModalKey, () => void> = {
      "apply-coupon": () => setOpenApplyCouponModal(true),
      "delivery-address": () => setOpenDeliveryAddress(true),
      "rental-period": () => setOpenDateUpdate(true),
    }

    if (
      order &&
      paramData &&
      paramData in modalActions &&
      showModification(order?.deal_deal_stage_name) &&
      paramsStatusCheck
    ) {
      modalActions[paramData as ModalKey]() // Type-safe cast
      setParamsStatusCheck(false)
    }
  }, [paramData, order, paramsStatusCheck])

  if (orderLoading || statusLoading || isAdditionalOrderSummary)
    return <OrderDetailsSkeleton />
  if (orderError || statusError) {
    return (
      <div className='text-center text-red-500'>
        Error:{" "}
        {orderErrorData instanceof Error
          ? orderErrorData.message
          : "Failed to fetch order details"}
      </div>
    )
  }

  if (!order || !orderStatus)
    return (
      <div className='flex h-full w-full items-center justify-center px-4 py-12'>
        <div className='text-center'>
          <div className='mb-4 text-6xl text-red-500'>⚠️</div>
          <h2 className='text-2xl font-semibold text-gray-800'>
            Something Went Wrong
          </h2>
          <p className='mt-2 text-gray-600'>
            An unexpected error occurred. Please try again or contact support if
            the issue persists.
          </p>
          <div className='mt-6 flex justify-center gap-4'>
            <button
              onClick={() => location.reload()}
              className='shadow inline-flex items-center rounded-xl bg-primary px-6 py-2 text-white transition hover:bg-primary/90'
            >
              Retry
            </button>
            <Link
              href='https://api.whatsapp.com/send?phone=+917619220543&text=Hi'
              className='inline-flex items-center rounded-xl border border-gray-300 px-6 py-2 text-gray-700 transition hover:bg-gray-100'
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    )
  if (!orderData && !isOrderDataFetching && !isOrderDataLoading)
    return (
      <div className='flex h-full w-full items-center justify-center px-4 py-12'>
        <div className='text-center'>
          <div className='mb-4 text-6xl'>🛒</div>
          <h2 className='text-2xl font-semibold text-gray-800'>
            No Order Found
          </h2>
          <p className='mt-2 text-gray-600'>
            We couldn&apos;t find any order data to display. Try refreshing the
            page or check back later.
          </p>
          <Link
            href={"/dashboard/orders"}
            className='shadow mt-6 inline-flex items-center rounded-xl bg-primary px-6 py-2 text-white transition hover:bg-primary/90'
          >
            Go Back
          </Link>
        </div>
      </div>
    )

  if (isExtensionPaymentLoading || isReturnPaymentLoading)
    return <CheckoutRedirectLoading />
  return (
    <div className='flex flex-col overflow-hidden'>
      <div className='relative grid gap-6 lg:grid-cols-[1fr_380px]'>
        {/* Main Content */}
        <div className='space-y-4 rounded-3xl bg-gray-100 p-4 pt-0 md:space-y-6 md:p-6'>
          <div className='hidden lg:block'>
            <OrderDetailsHeader
              order={order}
              orderData={orderData?.order}
              setOpenApplyCouponModal={setOpenApplyCouponModal}
              isModification={showModification(order.deal_deal_stage_name)}
            />
          </div>

          {showModification(order.deal_deal_stage_name) && (
            <ApplyCouponSideView
              discount_amount={orderData?.order?.coupon_discount_amount ?? 0}
              order_id={order.main_order_id}
              coupon_code={orderData?.order.coupon_code ?? ""}
              openSideView={openApplyCouponModal}
              total_rent_amount={order.deal_cf_total_rent}
              setOpenSideView={setOpenApplyCouponModal}
            />
          )}

          <ContactSupportHelp
            openSideView={openContactSupport}
            setOpenSideView={setOpenContactSupport}
          />

          <ScheduleModal
            order={order}
            openSideView={openScheduleModal}
            setOpenSideView={setOpenScheduleModal}
          />

          {/* to halde change pickup time */}
          <ScheduleAgainModal
            order={order}
            openSideView={openScheduleAgainModal}
            setOpenSideView={setOpenScheduleAgainModal}
          />

          {orderData && (
            <OrderCancellationDialog
              orderId={orderData?.order.order_id}
              orderCancelModal={orderCancelModal}
              setOrderCancelModal={setOrderCancelModal}
            />
          )}

          <Separator className='hidden lg:block' />

          {/* Delivery & Pickup Details */}
          {orderData && (
            <DeliveryDetails
              order={{
                id: order.deal_name,
                main_order_id: order.main_order_id,
                customerName: order.first_name + " " + order.last_name,
                phoneNumber: order.calling_number,
                deliveryAddress: orderData.order.delivery_address,
                deliveryDate: new Date(order.deal_cf_delivery_date),
                pickupDate: new Date(order.deal_cf_pickup_date),
                total_rent: order.deal_cf_total_rent,
                address_id: orderData.order.address_id,
              }}
              showModification={showModification(order.deal_deal_stage_name)}
              setOpenDateUpdate={setOpenDateUpdate}
              setOpenDeliveryAddress={setOpenDeliveryAddress}
            />
          )}

          <DeliveryAddressSelect
            order_id={order.deal_name}
            total_amount={order.deal_cf_total_order_amount}
            openSideView={openDeliveryAddress}
            setOpenSideView={setOpenDeliveryAddress}
          />
          {orderData && (
            <ChangeRentalPeriod
              order_id={order.deal_name}
              order={orderData?.order}
              main_order_id={orderData ? orderData?.order.id : 0}
              openSideView={openDateUpdate}
              setOpenSideView={setOpenDateUpdate}
            />
          )}
          {orderData && (
            <ExtendRentalPeriod
              order_id={order.deal_name}
              order={orderData?.order}
              orderSummary={order}
              main_order_id={orderData ? orderData?.order.id : 0}
              openSideView={openExtendRentalPeriod}
              setOpenSideView={setOpenExtendRentalPeriod}
              handleExtensionPayment={handleExtensionPayment}
            />
          )}

          {/* return order details */}

          {orderData && (
            <ReturnDetails
              order_id={order.deal_name}
              order={orderData?.order}
              open={openReturnDetails}
              orderSummary={order}
              handleOpenChange={setOpenReturnDetails}
            />
          )}

          {orderData && (
            <CancelReturn
              order_id={order.deal_name}
              isOpen={openCancelReturn}
              handleOpenChange={setOpenCancelReturn}
              handleScheduleAgainModal={setOpenScheduleAgainModal}
            />
          )}

          <Separator />

          {/* Item Details */}

          <div className='w-full space-y-4'>
            {/* Header */}
            <div className='flex items-center gap-3'>
              <FileBadge2Icon className='h-6 w-6' />
              <Typography
                as={"h2"}
                className='text-sh4 font-bold text-neutral-900 md:text-h6'
              >
                Item Details:
              </Typography>
            </div>

            <div className='space-y-2 md:max-h-96 md:space-y-4 md:overflow-y-auto'>
              {orderData?.cart_items.map((item, index) => (
                <OrderItemCard
                  key={`${item.cat_sname}-${index}`}
                  item={item}
                  index={index}
                  show_perday_rent={false}
                />
              ))}
            </div>
          </div>

          <Separator />

          {/* Order Summary */}

          <div className='w-full space-y-4'>
            {/* Header */}
            <div className='flex items-center justify-between gap-3'>
              <div className='flex items-center gap-3'>
                <BadgeIndianRupee className='h-6 w-6' />
                <Typography
                  as={"h2"}
                  className='text-sh4 text-primary-900 md:text-h6'
                >
                  Order Summary:
                </Typography>
              </div>
            </div>

            {/* Content  */}

            {order && (
              <Charges
                chargesData={{
                  total_rent: Number(order.deal_cf_total_rent),
                  total_deposit: order.deal_cf_deposit_amount,
                  delivery_charges: order.shipping_charge,
                  pickup_charges: order.shipping_charge,
                  items_count: orderData?.cart_items?.length ?? 0,
                  coupon_discount: order.deal_cf_coupon_discount_payment,
                  wallet_balance_used: order.deal_cf_wallet_payment,
                  applied_coupon_code: orderData?.order.coupon_code,
                  handling_charges: order.pay_on_delivery_fees,
                  wallet_used: order.deal_cf_wallet_payment > 0,
                  carepal_selected: order.carepal_amount > 0,
                  carepal_fee: order.carepal_amount || 0,
                }}
              />
            )}
          </div>

          <Separator />

          {/* More Details  */}

          <div className='flex items-center justify-between gap-4'>
            <div>
              <Typography
                as={"h2"}
                className='text-sh6 text-primary-900 md:text-sh2'
              >
                Order Total
              </Typography>
              <Typography as={"p"} className='text-o4 text-gray-800 md:text-b6'>
                price incl. of all taxes
              </Typography>
            </div>

            <Typography
              as={"h2"}
              className='text-sh2 text-primary-900 md:text-h4'
            >
              {moneyFormatter(
                order.deal_cf_net_amount > 0 ? order.deal_cf_net_amount : 0,
              )}
            </Typography>
          </div>

          {/* Payment */}
          {order.deal_cf_due_amount > 2 &&
            showModification(order.deal_deal_stage_name) && (
              <>
                <Separator />
                <div className='flex flex-col items-start justify-between gap-4 md:flex-row md:items-center'>
                  <div className='space-y-3'>
                    <Typography
                      as={"p"}
                      className='underline-offset-7 text-b4 text-gray-900 underline decoration-dotted md:text-b6'
                    >
                      Payment Mode
                    </Typography>
                    <div className='flex items-center justify-start gap-2'>
                      <Typography
                        as={"p"}
                        className='text-sh6 text-primary-900 md:text-sh3'
                      >
                        Pay via Cash on Delivery{" "}
                      </Typography>
                      <Typography
                        as={"span"}
                        className='flex min-w-max items-center gap-1 text-b6 text-destructive-600 md:text-b2'
                      >
                        (<InfoIcon className='h-3 w-3' /> Payment Pending )
                      </Typography>
                    </div>
                  </div>

                  {showPayment(order.deal_deal_stage_name) && (
                    <>
                      {!order.deal_cf_balance_payment_link ? (
                        <Button
                          onClick={() =>
                            hanldePaymentForOrder({
                              order_id: orderId,
                              amount: order.deal_cf_due_amount,
                            })
                          }
                          variant={"outline-primary"}
                          className='px-4 py-3 !text-bt2 max-md:!w-full md:w-auto md:!text-bt3'
                        >
                          Pay Rental Charges Online
                        </Button>
                      ) : (
                        <Link
                          href={order.deal_cf_balance_payment_link || ""}
                          className='max-md:w-full'
                          target='_blank'
                        >
                          <Button
                            variant={"outline-primary"}
                            className='px-4 py-3 !text-bt2 text-primary-500 max-md:!w-full md:w-auto md:!text-bt3'
                          >
                            Pay Rental Charges Online
                          </Button>
                        </Link>
                      )}
                    </>
                  )}
                </div>
              </>
            )}
        </div>

        <div className='relative row-start-1 w-full space-y-4 lg:row-start-auto lg:space-y-0'>
          <div className='flex rounded-2xl bg-gray-100 p-4 lg:hidden'>
            <OrderDetailsHeader
              order={order}
              orderData={orderData?.order}
              isModification={showModification(order.deal_deal_stage_name)}
              setOpenApplyCouponModal={setOpenApplyCouponModal}
            />
          </div>

          {/* Order Status Sidebar */}
          <OrderStatus
            orderStatus={orderStatus}
            order={order}
            paramData={paramData ?? ""}
            order_id={orderId}
            additionalOrderSummary={
              additionalOrderSummary || ({} as AdditionalOrderSummary)
            }
            setOpenContactSupport={setOpenContactSupport}
            setOpenScheduleModal={setOpenScheduleModal}
            setOrderCancelModal={setOrderCancelModal}
            openApplyCouponDiscount={() => setOpenApplyCouponModal(true)}
            openAddressChange={() => setOpenDeliveryAddress(true)}
            setOpenReturnDetails={() => setOpenReturnDetails(true)}
            openRentalPeriodChange={() => setOpenDateUpdate(true)}
            openExtendRentalPeriod={() => setOpenExtendRentalPeriod(true)}
            openCancelReturn={() => setOpenCancelReturn(true)}
            openScheduleAgain={() => setOpenScheduleAgainModal(true)}
            setOpenScheduleAgainModal={() => setOpenScheduleAgainModal(true)}
          />
        </div>
      </div>
    </div>
  )
}
