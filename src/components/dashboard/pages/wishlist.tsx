"use client"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { useState } from "react"
import { toast } from "sonner"

import { addCartItem } from "@/actions/cart"
import { fetchRentalVarients } from "@/actions/category"
import { gerUserWishlist } from "@/actions/user"
import WishList<PERSON><PERSON> from "@/components/cards/wishlist-card"
import CartButton from "@/components/layout/cart-button"
import CartItems from "@/components/modals/cart"
import SelectColorAndSize from "@/components/modals/select-color-size"
import { WishlistSkeleton } from "@/components/skeletons/wishlist-skeleton"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { getCookie } from "@/functions/cookies"
import { formatUrlName, getImage } from "@/functions/small-functions"
import useCalculateRent from "@/hooks/use-calculate-rent"
import { useThrottle } from "@/hooks/use-throttle"
import { trackAddToCart } from "@/lib/gtag-event"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { RentalItem } from "@/types"

export default function MyWishlist() {
  const { user, userLoading } = useUserStore()
  const { addToCart } = useCheckoutStore()
  const { city_surge, same_day_surge, surge_factor, total_days } =
    useRentalStore()

  const [showCart, setShowCart] = useState(false)
  const [selectedSize, setSelectedSize] = useState("")
  const [shortName, setShortName] = useState("")
  const [showSizeColorSelect, setShowSizeColorSelect] = useState(false)
  const [isCartAdditionLoading, setIsCartAdditionLoading] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<RentalItem | null>(
    null,
  )

  const { data: wishlistItems, isLoading } = useQuery<RentalItem[]>({
    queryKey: ["userWishlist", user?.id],
    queryFn: gerUserWishlist,
    enabled: !!user,
  })

  const { data: sizes = [] } = useQuery({
    queryKey: ["rentalVariants", shortName],
    queryFn: () => fetchRentalVarients(shortName ?? ""),
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
    enabled: !!shortName,
  })
  const { setCartOpen } = useRentalStore()
  const { getRent } = useCalculateRent({
    type: "product",
  })
  const handleAddToCart = async (product: RentalItem) => {
    setIsCartAdditionLoading(true)
    if (product.size_specific) {
      if (selectedSize === "") {
        setSelectedProduct(product)
        setShortName(product.ri_short_name)
        setShowSizeColorSelect(true)
        setIsCartAdditionLoading(false)
        return
      }
    }

    try {
      const response = await addCartItem({
        user_id: user ? user.id : 0,
        id: product.id,
        user_uid: getCookie("uid") || "",
        num_days: total_days,
        size: selectedSize,
        cart_type: "rent",
        surge_factor,
        city_surge,
        final_surge: getRent({ type: "product", product: product }).surge,
        same_day_surge,
      })

      if (response) {
        toast.success("Added to cart")
        addToCart(response, same_day_surge)
        setShowSizeColorSelect(false)
        setSelectedSize("")
      }

      // console.log("Product added to cart:", product)
      trackAddToCart(
        product.ri_short_name,
        0, // You'll need to pass the correct rent value here
        product.ri_code,
        getImage(product.ri_image),
        product.category_short_name,
        selectedSize,
        "Rent",
        formatUrlName(product.super_category_url),
      )
    } catch (error) {
      console.error(error)
    } finally {
      setIsCartAdditionLoading(false)
    }
  }

  const throttledAddToCart = useThrottle(handleAddToCart, 4000)

  if (userLoading || isLoading) {
    return <WishlistSkeleton />
  }

  return (
    <>
      <CartItems showCart={showCart} setShowCart={setShowCart} />

      <SelectColorAndSize
        sizes={sizes}
        product={selectedProduct as RentalItem}
        setSelectedSize={setSelectedSize}
        selectedSize={selectedSize}
        handleAddToCart={() =>
          selectedProduct && throttledAddToCart(selectedProduct)
        }
        isCartAddtionLoading={isCartAdditionLoading}
        openSideView={showSizeColorSelect}
        setOpenSideView={setShowSizeColorSelect}
      />

      <Card className='rounded-3xl bg-gray-100'>
        <CardHeader className='p-4 md:p-6'>
          <CardTitle className='text-xl font-semibold text-neutral-900 md:text-2xl'>
            My Wishlist
          </CardTitle>
        </CardHeader>
        <CardContent className='grid grid-cols-2 gap-4 p-3 pt-0 md:grid-cols-1 md:p-6 md:pt-0'>
          {wishlistItems?.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <WishListCard
                product={item}
                setShowCart={setShowCart}
                selectedSize={selectedSize}
                setShortName={setShortName}
                handleAddToCart={throttledAddToCart}
                setShowSizeColorSelect={setShowSizeColorSelect}
                setSelectedProduct={setSelectedProduct}
              />
            </motion.div>
          ))}
        </CardContent>
      </Card>
      <CartButton setCartOpen={setCartOpen} />
    </>
  )
}
