"use client"

import { fetchOrders } from "@/actions/orders"
import { OrderCard } from "@/components/dashboard/order-card"
import { useQuery } from "@tanstack/react-query"
import { useCallback, useEffect, useState } from "react"
import { useInView } from "react-intersection-observer"

import { CustomDropdown } from "@/components/custom/custom-dropdown"
import { IconRefresh } from "@/components/Icons"
import { OrderCardSkeleton } from "@/components/skeletons/order-card-skeleton"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import { FilterValue, type APIOrder } from "@/types/order"
import { FilterIcon, Package } from "lucide-react"

const PER_PAGE = 5

export default function OrdersPage() {
  const [selectedFilters, setSelectedFilters] = useState<FilterValue[]>([])
  const [page, setPage] = useState(1)
  const [allOrders, setAllOrders] = useState<APIOrder[]>([])
  const { ref, inView } = useInView()
  const [openFilter, setOpenFilter] = useState(false)
  const {
    data: orderResponse,
    error,
    isError,
    isFetching,
    isLoading,
    isPaused,
    isPlaceholderData,
    isStale,
    isSuccess,
    refetch,
  } = useQuery({
    queryKey: ["orders", page, selectedFilters],
    queryFn: () => fetchOrders(page, PER_PAGE, selectedFilters),
    gcTime: 5 * 60 * 1000, // 5 minutes
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })

  const loadMoreOrders = useCallback(() => {
    if (
      orderResponse?.items?.length === PER_PAGE &&
      !isPlaceholderData &&
      !isFetching
    ) {
      setPage((prevPage) => prevPage + 1)
    }
  }, [orderResponse?.items?.length, isPlaceholderData, isFetching])

  useEffect(() => {
    if (inView && !isLoading && !isFetching) {
      loadMoreOrders()
    }
  }, [inView, isLoading, isFetching, loadMoreOrders])

  useEffect(() => {
    if (isSuccess && orderResponse?.items) {
      if (page === 1) {
        setAllOrders(orderResponse.items)
      } else {
        setAllOrders((prevOrders) => [...prevOrders, ...orderResponse.items])
      }
    }
  }, [isSuccess, orderResponse?.items, page])

  const handleToggleFilter = useCallback(
    (filter: FilterValue) => {
      if (filter == selectedFilters[0]) {
        setSelectedFilters([])
      } else {
        setSelectedFilters([filter])
      }
      setPage(1)
      setAllOrders([])
    },
    [selectedFilters],
  )

  const hasMoreOrders = orderResponse?.items?.length === PER_PAGE
  const noOrders = allOrders.length === 0 && !isFetching
  const showLoadMore = !isFetching && hasMoreOrders

  return (
    <div className='flex flex-col overflow-hidden rounded-3xl bg-gray-100 md:h-svh'>
      <div className='sticky top-0 z-10 bg-gray-100 px-4 py-3 md:p-6'>
        <div className='flex items-center justify-between'>
          <h1 className='text-xl font-bold md:text-2xl'>My Orders</h1>

          <div className='flex items-center justify-end gap-3'>
            {isStale && !isFetching && (
              <Button
                onClick={() => refetch()}
                size={"icon"}
                className='fixed bottom-4 right-4 rounded-md bg-blue-500 p-2 text-white'
              >
                <IconRefresh />
              </Button>
            )}

            <CustomDropdown
              title='Filter Options'
              options={[
                {
                  value: "cancelled",
                  label: "Cancelled Orders",
                  onClick: () => handleToggleFilter("cancelled"),
                },
                {
                  value: "active",
                  label: "Active Orders",
                  onClick: () => handleToggleFilter("active"),
                },
                {
                  value: "completed",
                  label: "Completed Orders",
                  onClick: () => handleToggleFilter("completed"),
                },
              ]}
              isOpen={openFilter}
              setIsOpen={setOpenFilter}
              selectedValues={selectedFilters}
            >
              <button
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-full border-2 transition-colors md:h-10 md:w-10",
                  openFilter ? "border-neutral-400" : "border-neutral-200",
                )}
                onClick={() => setOpenFilter(!openFilter)}
              >
                <FilterIcon className='h-4 w-4' />
              </button>
            </CustomDropdown>
          </div>
        </div>
      </div>
      <ScrollArea className='h-full flex-grow'>
        <div className='p-3 !pt-0 md:p-6'>
          {isLoading && page === 1 ? (
            <OrdersLoading />
          ) : isError ? (
            <div className='text-center text-red-500'>
              Error:{" "}
              {error instanceof Error
                ? error.message
                : "Failed to fetch orders"}
            </div>
          ) : noOrders ? (
            <Card className='rounded-2xl border-none'>
              <CardContent className='flex flex-col items-center justify-center py-8'>
                <div className='rounded-full bg-gray-200 p-3'>
                  <Package className='h-8 w-8 text-gray-400' />
                </div>
                <h3 className='mt-4 text-lg font-medium text-gray-900'>
                  No Orders Found
                </h3>
                <p className='mt-2 text-center text-sm text-gray-500'>
                  {selectedFilters.length > 0
                    ? "No orders match the selected filters. Try changing your filters."
                    : "You haven't placed any orders yet. Start shopping to see your orders here."}
                </p>
              </CardContent>
            </Card>
          ) : (
            <>
              <div className='grid gap-6'>
                {allOrders.map((order: APIOrder, idx) => (
                  <OrderCard
                    key={idx}
                    order={order}
                    onModify={() => console.info("Modify order:", order.id)}
                  />
                ))}
              </div>
              {isFetching && <OrdersLoading />}
              {showLoadMore && <div ref={ref} className='h-10' />}
              {!isFetching && !hasMoreOrders && allOrders.length > 0 && (
                <div className='mt-4 text-center'>No more orders</div>
              )}
            </>
          )}
        </div>
      </ScrollArea>
      {isPaused && (
        <div className='fixed bottom-4 right-4 rounded-md bg-yellow-100 p-2'>
          Network is paused. Some data may be stale.
        </div>
      )}
    </div>
  )
}

const OrdersLoading = () => (
  <div className='mb-4 grid gap-6'>
    {Array.from({ length: 5 }).map((_, i) => (
      <OrderCardSkeleton key={i} />
    ))}
  </div>
)
