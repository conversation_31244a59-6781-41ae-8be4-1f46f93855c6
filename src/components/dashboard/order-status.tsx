"use client"

import { AnimatePresence, motion } from "framer-motion"
import { useEffect, useState } from "react"

import { fetchOrderAgain } from "@/actions/orders"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import usePlaceOrder from "@/hooks/use-place-order"
import { useThrottle } from "@/hooks/use-throttle"
import { cn } from "@/lib/utils"
import { OrderStatusResponse, OrderSummary } from "@/types/order"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import CartItems from "../modals/cart"
// check
import useWindowSize from "@/hooks/use-window-resize"
import { useRentalStore } from "@/store/rental-store"
import { RentalPeriodSelectorDesktop } from "../custom/rental-dates-select-desktop"
import { RentalPeriodSelectorMobile } from "../custom/rental-dates-select-mobile"

import { trackContactSupportSelected } from "@/lib/gtag-event"
import { useUserStore } from "@/store/user-store"
import { AdditionalOrderSummary } from "@/types/return-order"
import {
  CautionTriangleOutlinedIcon,
  ChevronDownIcon,
  CrossCircleOutlinedIcon,
  InfoCircleFilledIcon,
  TickCircleOutlinedIcon,
} from "sharepal-icons"
import { CustomDropdown } from "../custom/custom-dropdown"
import { Typography } from "../ui/typography"

function formatTimestamp(isoString: string) {
  if (!isoString) return ""
  const date = new Date(isoString)

  // Helper to get ordinal suffix for the day
  function getOrdinalSuffix(n: number) {
    if (n > 10 && n < 14) return "th" // special case for 11th-13th
    switch (n % 10) {
      case 1:
        return "st"
      case 2:
        return "nd"
      case 3:
        return "rd"
      default:
        return "th"
    }
  }

  // Format day with ordinal suffix
  const day = date.getDate()
  const ordinal = getOrdinalSuffix(day)

  // Month abbreviation
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ]
  const monthAbbrev = months[date.getMonth()]

  // Format time in 12-hour format
  let hours = date.getHours()
  const minutes = date.getMinutes()
  const period = hours >= 12 ? "pm" : "am"
  hours = hours % 12 || 12 // convert 0 to 12 for midnight/noon
  const minutesPadded = minutes < 10 ? "0" + minutes : minutes

  // Build the formatted date and time strings
  const formattedDate = `${day}${ordinal} ${monthAbbrev}`
  const formattedTime = `${hours}:${minutesPadded} ${period}`

  return `${formattedDate} | ${formattedTime}`
}

// Example usage:
// console.log(formatTimestamp("2025-03-03T09:12:06.932Z"))
// Output might be "3rd Mar | 9:12 am" depending on the timestamp provided.

interface OrderStatusProps {
  orderStatus: OrderStatusResponse
  setOpenContactSupport: (value: boolean) => void
  order: OrderSummary
  setOpenScheduleModal: (value: boolean) => void
  openAddressChange: () => void
  openRentalPeriodChange: () => void
  openApplyCouponDiscount: () => void
  openScheduleAgain: () => void
  setOpenReturnDetails: () => void
  openCancelReturn: () => void
  setOrderCancelModal: (value: boolean) => void
  openExtendRentalPeriod: (value: boolean) => void
  setOpenScheduleAgainModal: (value: boolean) => void
  actionButton?: {
    active: boolean
    button_text: string
  }
  order_id: string
  paramData: string
  additionalOrderSummary: AdditionalOrderSummary
}

const ACTION_TYPES = {
  VERIFY_PROFILE: "VERIFY_PROFILE",
  PAY_RENTAL_CHARGES: "PAY_RENTAL_CHARGES",
  MODIFY_ITEMS: "MODIFY_ITEMS",
  GET_SUPPORT: "GET_SUPPORT",
  EXTEND_RENTAL: "EXTEND_RENTAL",
  SCHEDULE_PICKUP: "SCHEDULE_PICKUP",
  SCHEDULE_DELIVERY: "SCHEDULE_DELIVERY",
  CONTACT_SUPPORT: "CONTACT_SUPPORT",
  ORDER_AGAIN: "ORDER_AGAIN",
  CANCEL_ORDER: "CANCEL_ORDER",
  PROVIDE_FEEDBACK: "PROVIDE_FEEDBACK",
  MODIFY_ORDER: "MODIFY_ORDER",
  REDIRECT: "REDIRECT",
  SETTLE_DAMAGE_CHARGES: "SETTLE_DAMAGE_CHARGES",
  UNDO_CANCELLATION: "UNDO_CANCELLATION",
  CANCEL_SCHEDULE: "CANCEL_SCHEDULE",
  RETURN_STATUS: "RETURN_STATUS",
  CHANGE_PICKUPTIME: "CHANGE_PICKUPTIME",
  PAY_RETURN_PAYMENT: "PAY_RETURN_PAYMENT",
}

const getStatusColor = (stage: string, isWarning = false, isError = false) => {
  if (stage.includes("Damage") || isWarning) {
    return "text-warning-500"
  }
  if (stage.includes("Cancelled") || isError) {
    return "text-destructive-500"
  }
  return "text-success-500"
}

const getStatusIcon = (
  stage: string,
  isActive = false,
  isCurrent = false,
  isWarning = false,
  isError = false,
) => {
  if (stage.includes("Damage") || isWarning) {
    return (
      <CautionTriangleOutlinedIcon className='min-h-5 min-w-5 text-warning-500' />
    )
  }
  // console.log(stage, isActive, isCurrent)
  if (stage.includes("Cancelled") || isError) {
    return (
      <CrossCircleOutlinedIcon className='min-h-5 min-w-5 text-destructive' />
    )
  }

  if (isCurrent) {
    return (
      <TickCircleOutlinedIcon
        strokeWidth={4}
        className={cn(
          "min-h-5 min-w-5",
          isCurrent ? "text-success-400" : "text-muted-foreground",
        )}
      />
    )
  }
  if (isActive) {
    return
  }
}

export function OrderStatus({
  orderStatus,
  order,
  paramData,
  order_id,
  additionalOrderSummary,
  setOpenScheduleModal,
  setOpenContactSupport,
  setOrderCancelModal,
  openAddressChange,
  openRentalPeriodChange,
  // openApplyCouponDiscount,
  setOpenReturnDetails,
  openExtendRentalPeriod,
  openCancelReturn,
  setOpenScheduleAgainModal,
}: OrderStatusProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [showCart, setShowCart] = useState(false)
  const [showAllUpdates, setShowAllUpdates] = useState(false)
  const [showAllAdditionalUpdates, setShowAllAdditionalUpdates] =
    useState(false)
  const router = useRouter()
  const [openCalendar, setOpenCalendar] = useState(false)
  const { user } = useUserStore()

  const { hanldePaymentForOrder, hanldePaymentForReturnOrder } = usePlaceOrder()

  const queryClient = useQueryClient()
  // const [shipmentDetailError, setShipmentDetailError] = useState('')

  // const { mutate: fetchShipmentDetails } = useMutation({
  //   mutationFn: async () => {
  //     return fetchWithAuthPost<ShipmentDetailsResponse>('shipment/details', { order_id: order_id })
  //   },
  //   onSuccess: (data) => {
  //     console.log(data)
  //   },
  //   onError: (error) => {
  //     setShipmentDetailError(JSON.parse((error as Error).message).message ?? 'Failed To Fetch Shipment Details')
  //   },
  // })

  // useEffect(() => {
  //   if (orderStatus.activeStage === 'Order Packed' || orderStatus.activeStage === 'Order Return Due')
  //     fetchShipmentDetails()
  // }, [fetchShipmentDetails, orderStatus.activeStage])

  // const checkDisabledStatus = (actionType: string,) => {
  //   if (actionType === ACTION_TYPES.SCHEDULE_PICKUP || actionType === ACTION_TYPES.SCHEDULE_DELIVERY) {
  //     if (shipmentDetailError) {
  //       return true
  //     }
  //     return false
  //   }
  // }

  // const checkRedirectStatus = (actionType: string,) => {
  //   if (actionType === ACTION_TYPES.SCHEDULE_PICKUP || actionType === ACTION_TYPES.SCHEDULE_DELIVERY) {
  //     if (shipmentDetailError) {
  //       return true
  //     }
  //     return false
  //   }
  // }

  const { mutate: undoCancellation } = useMutation({
    mutationFn: () =>
      fetchWithAuthPost(
        "https://api.sharepal.in/api:AIoqxnqr/return/undo-cancellation",
        {
          order_id,
        },
      ),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["orderStatus"] })
    },
    onError: () => {
      toast.error("Failed to undo cancellation")
    },
  })

  // const { mutate: cancelAndReschduleShipment } = useMutation({
  //   mutationFn: () =>
  //     fetchWithAuthPost("https://api.sharepal.in/api:AIoqxnqr/return/cancel-and-reschedule", {
  //       order_id,
  //     }),
  //   onSuccess: () => {
  //     // queryClient.invalidateQueries({ queryKey: ["orderStatus"] })
  //     router.push("/return-order/" + order_id)
  //   },
  //   onError: () => {
  //     toast.error("Unable to cancel and reschedule shipment")
  //   },
  // })

  const handleActionButton = async (
    actionType: string,
    actionMessage: string,
    e?: React.MouseEvent<HTMLButtonElement>,
  ) => {
    if (e) e.preventDefault()

    if (
      orderStatus.stageInfo.actions.filter(
        (action) => action.action_type === actionType,
      ).length === 0
    ) {
      // toast.error("Action Not Allowed")
      return
    }

    if (actionType === ACTION_TYPES.VERIFY_PROFILE) {
      //perform action based on actionType
      router.push("/complete-verification")
    }
    if (actionType === ACTION_TYPES.PAY_RENTAL_CHARGES) {
      if (order.deal_cf_balance_payment_link) {
        window.open(order.deal_cf_balance_payment_link, "_blank")
        return
      }
      hanldePaymentForOrder({ order_id, amount: order.deal_cf_due_amount })
    }
    if (actionType === ACTION_TYPES.MODIFY_ITEMS) {
      // handle modify items logic here
      // e.g., open a modal to modify items
    }
    if (actionType === ACTION_TYPES.GET_SUPPORT) {
      // console.log('Get Support')
      setOpenContactSupport(true)
    }
    if (actionType === ACTION_TYPES.EXTEND_RENTAL) {
      // extendRentalPeriod()
      openExtendRentalPeriod(true)
    }
    if (actionType === ACTION_TYPES.SCHEDULE_PICKUP) {
      // setOpenScheduleModal(true)
      router.push("/return-order/" + order_id)
    }
    if (actionType === ACTION_TYPES.SCHEDULE_DELIVERY) {
      setOpenScheduleModal(true)
    }
    if (actionType === ACTION_TYPES.CONTACT_SUPPORT) {
      // console.log('Contact Support')
      setOpenContactSupport(true)
    }
    if (actionType === ACTION_TYPES.ORDER_AGAIN) {
      setOpenCalendar(true)
    }
    if (actionType === ACTION_TYPES.PROVIDE_FEEDBACK) {
      setOpenContactSupport(true)
    }
    if (actionType === ACTION_TYPES.SETTLE_DAMAGE_CHARGES) {
    }
    if (actionType === ACTION_TYPES.CANCEL_ORDER) {
      setOrderCancelModal(true)
    }
    if (actionType === ACTION_TYPES.UNDO_CANCELLATION) {
      undoCancellation()
    }
    if (actionType === ACTION_TYPES.CANCEL_SCHEDULE) {
      // cancelAndReschduleShipment()
      openCancelReturn()
    }
    if (actionType === ACTION_TYPES.PAY_RETURN_PAYMENT) {
      // cancelAndReschduleShipment()
      hanldePaymentForReturnOrder({
        amount: additionalOrderSummary.total_return_charges,
        order_id,
      })
    }
    if (actionType === ACTION_TYPES.RETURN_STATUS) {
      // Open Return Status Modal
      setOpenReturnDetails()
    }
    if (actionType === ACTION_TYPES.CHANGE_PICKUPTIME) {
      // Open Return Status Modal
      setOpenScheduleAgainModal(true)
    }
    if (actionType === ACTION_TYPES.REDIRECT) {
      if (actionMessage.includes("Need support")) {
        trackContactSupportSelected({
          order_id,
          user_id: user?.id ?? 0,
        })
      }
      if (actionMessage) window.open(actionMessage, "_blank")
      else {
        toast.error("Unable to Open Page, Please try Later")
      }
    }
  }

  const size = useWindowSize()
  const throttleActionButton = useThrottle(handleActionButton, 1000)

  const [openDropDown, setOpenDropDown] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      if (size.width) {
        setIsMobile(size.width < 768)
        if (size.width >= 768) {
          setShowAllUpdates(true)
        }
      }
    }
    checkMobile()
  }, [size.width])

  const { delivery_date } = useRentalStore()

  useEffect(() => {
    handleActionButton(paramData, "")
  }, [])

  // to remove query params after 5 seconds
  useEffect(() => {
    // Function to remove the 'open' query parameter
    const removeQueryParam = () => {
      // Create a new object without the 'open' parameter
      // Create a URL object from the current URL
      const url = new URL(window.location.href)

      // Remove the 'open' parameter
      url.searchParams.delete("open")

      // Update the URL without the 'open' parameter
      router.replace(url.href, { scroll: true })
    }

    // Call the function to remove the query parameter after 5 seconds
    const timer = setTimeout(removeQueryParam, 5000)

    // Cleanup function to clear the timeout if the component unmounts before the timeout completes
    return () => clearTimeout(timer)
  }, [router])

  return (
    <>
      {size?.width && size.width > 600 ? (
        <RentalPeriodSelectorDesktop
          isCalenderOpen={openCalendar}
          closeCalendarFn={() => {
            setOpenCalendar(false)
          }}
          onContinue={async () => {
            if (delivery_date) {
              await fetchOrderAgain(order_id, delivery_date)
              setShowCart(true)
            }
          }}
        />
      ) : (
        <RentalPeriodSelectorMobile
          isCalenderOpen={openCalendar}
          onContinue={async () => {
            if (delivery_date) {
              await fetchOrderAgain(order_id, delivery_date)
              setShowCart(true)
            }
          }}
          closeCalendarFn={() => {
            setOpenCalendar(false)
          }}
        />
      )}

      {orderStatus && (
        <div className='sticky top-0 w-full space-y-4'>
          <Card className='shadow-none border-none bg-gray-100'>
            <CardHeader className='pb-3'>
              <CardTitle>
                <Typography className='text-h6 md:!text-h2' as={"span"}>
                  Order Status
                </Typography>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className='rounded-lg bg-gray-100'>
                <div className='space-y-4 md:space-y-6'>
                  {/* Status Header */}
                  <div className='flex items-start gap-3'>
                    <div
                      className={cn(
                        "flex h-6 w-6 items-center justify-center rounded-full p-1",
                        orderStatus.activeStage.includes("Cancelled") ||
                          orderStatus.activeStage.includes("Damage") ||
                          orderStatus.stageInfo.warning ||
                          orderStatus.stageInfo.error
                          ? "bg-destructive/10"
                          : "",
                      )}
                    >
                      {getStatusIcon(
                        orderStatus.activeStage,
                        false,
                        true,
                        orderStatus.stageInfo.warning,
                        orderStatus.stageInfo.error,
                      )}
                    </div>
                    <div className='flex-1 space-y-1'>
                      <Typography
                        as={"h6"}
                        className={cn(
                          "!text-sh4 md:!text-h6",
                          getStatusColor(
                            orderStatus.activeStage,
                            orderStatus.stageInfo.warning,
                            orderStatus.stageInfo.error,
                          ),
                        )}
                      >
                        {orderStatus.stageInfo.title}!
                      </Typography>
                      <Typography
                        as={"p"}
                        className='text-b4 text-muted-foreground md:text-b2'
                      >
                        {/* {orderStatus.messages[orderStatus.activeStage]?.[0] || ''} */}
                        {orderStatus.stageInfo.message}
                      </Typography>
                    </div>
                  </div>

                  {orderStatus.stageInfo?.specialMessage?.show && (
                    <SpecialInfoText
                      description={
                        orderStatus.stageInfo.specialMessage.description
                      }
                      title={orderStatus.stageInfo.specialMessage.title}
                      type={orderStatus.stageInfo.specialMessage.type}
                    />
                  )}
                  {orderStatus.additionalStageInfo?.specialMessage?.show && (
                    <SpecialInfoText
                      description={
                        orderStatus.additionalStageInfo.specialMessage
                          .description
                      }
                      title={
                        orderStatus.additionalStageInfo.specialMessage.title
                      }
                      type={orderStatus.additionalStageInfo.specialMessage.type}
                    />
                  )}

                  {/* Action Buttons */}
                  <div className='flex flex-col gap-2'>
                    {orderStatus.stageInfo.actions.map((button, index) =>
                      button.action_type === ACTION_TYPES.MODIFY_ORDER ? (
                        <CustomDropdown
                          title='Modify Options'
                          key={button.text + index}
                          options={[
                            {
                              label: "Change Delivery Address",
                              value: "Change Delivery Address",
                              onClick: openAddressChange,
                            },
                            {
                              label: "Change Rental Period",
                              value: "Change Rental Period",
                              onClick: openRentalPeriodChange,
                            },
                            // {
                            //   label: "Apply Discount Coupon",
                            //   value: "Apply Discount Coupon",
                            //   onClick: openApplyCouponDiscount,
                            // },
                          ]}
                          isOpen={openDropDown}
                          setIsOpen={() => setOpenDropDown(false)}
                        >
                          <Button
                            key={button.text + index}
                            size={"lg"}
                            onClick={() => {
                              setOpenDropDown(true)
                            }}
                            disabled={button.disabled}
                            variant={button.variant}
                            className={cn("!w-full !text-bt2", button.variant)}
                          >
                            {button.text}
                          </Button>
                        </CustomDropdown>
                      ) : (
                        <Button
                          key={button.text + index}
                          size={"lg"}
                          onClick={(e) => {
                            throttleActionButton(
                              button.action_type,
                              button.message,
                              e,
                            )
                          }}
                          disabled={button.disabled}
                          variant={button.variant}
                          className={cn("w-full !text-bt2", button.variant)}
                        >
                          {button.text}
                        </Button>
                      ),
                    )}
                  </div>

                  {/* Timeline */}
                  <div className='w-full'>
                    {/* Order status */}
                    <TimelineStatus
                      title='Order Timeline'
                      stages={orderStatus.stages}
                      activeStage={orderStatus.activeStage}
                      stageInfo={orderStatus.stageInfo}
                      order_timeline={orderStatus.order_timeline}
                      isMobile={isMobile}
                      showAllUpdates={showAllUpdates}
                      setShowAllUpdates={setShowAllUpdates}
                    />

                    {/* Refund Status */}
                    {orderStatus.showAdditionalStages && (
                      <TimelineStatus
                        title='Refund Timeline'
                        stages={orderStatus.additionalStages}
                        activeStage={orderStatus.additionalActiveStage}
                        stageInfo={orderStatus.additionalStageInfo}
                        order_timeline={orderStatus.order_timeline}
                        isMobile={isMobile}
                        showAllUpdates={showAllAdditionalUpdates}
                        setShowAllUpdates={setShowAllAdditionalUpdates}
                      />
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <CartItems showCart={showCart} setShowCart={setShowCart} />
        </div>
      )}
    </>
  )
}

interface DividerText {
  text?: string
  icon?: React.ReactNode
}
export function DividerText({ text = "Order Timeline", icon }: DividerText) {
  return (
    <div className='relative flex w-full items-center justify-center'>
      <div className='absolute inset-0 flex items-center'>
        <div className='w-full border-t border-gray-200' />
      </div>
      <div className='relative'>
        <Typography
          as='span'
          className='flex items-center gap-2 rounded-full bg-neutral-150 px-4 py-1.5 text-b6 text-neutral-500'
        >
          {icon}
          {text}
          {icon}
        </Typography>
      </div>
    </div>
  )
}

interface SpecialInfoText {
  title: string
  description: string
  type: "warning" | "error" | "success" | "info" | "neutral-info"
}

export function SpecialInfoText({ title, description, type }: SpecialInfoText) {
  const icon = {
    warning: (
      <CautionTriangleOutlinedIcon className='min-h-5 min-w-5 text-warning-600' />
    ),
    error: (
      <CrossCircleOutlinedIcon className='min-h-5 min-w-5 text-destructive-600' />
    ),
    success: (
      <TickCircleOutlinedIcon className='min-h-5 min-w-5 text-success-600' />
    ),
    info: <InfoCircleFilledIcon className='min-h-5 min-w-5 text-primary-500' />,
    "neutral-info": (
      <InfoCircleFilledIcon className='min-h-5 min-w-5 text-neutral-500' />
    ),
  }[type]

  const bg = {
    warning: "bg-warning-100",
    error: "bg-destructive-100",
    success: "bg-success-100",
    info: "bg-primary-100",
    "neutral-info": "bg-neutral-100",
  }[type]

  const text = {
    warning: "text-warning-700",
    error: "text-destructive-700",
    success: "text-success-500",
    info: "text-primary-700",
    "neutral-info": "text-neutral-700",
  }[type]

  return (
    <div
      className={cn(
        "flex max-h-max w-full items-center gap-2 rounded-full px-4 py-2",
        bg,
      )}
    >
      {icon}
      <div className=''>
        {title && (
          <Typography as='span' className={cn("min-w-max !text-sh4", text)}>
            {title}
          </Typography>
        )}
        {description && (
          <Typography
            as='span'
            className={cn("min-w-max !text-b6 !leading-[10px]", text)}
          >
            {description}
          </Typography>
        )}
      </div>
    </div>
  )
}

const TimelineStatus = ({
  showAllUpdates,
  stages,
  activeStage,
  stageInfo,
  isMobile,
  setShowAllUpdates,
  order_timeline,
  title,
}: {
  showAllUpdates: boolean
  activeStage: string
  stages: string[]
  title: string
  stageInfo: {
    warning?: boolean
    error?: boolean
  }
  isMobile: boolean
  setShowAllUpdates: (val: boolean) => void
  order_timeline: {
    [key: string]: string
  }
}) => (
  <>
    <div className='flex items-center justify-between'>
      <DividerText text={title} />
    </div>
    <AnimatePresence mode='wait'>
      <motion.div
        key={showAllUpdates ? "full" : "compact"}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className={cn(
          "mt-4 h-full overflow-hidden transition-all",
          showAllUpdates ? "h-auto" : "h-[100px]",
        )}
      >
        {/* Render the refund stages */}
        {
          // Full Timeline
          stages.map((stage, index) => {
            const activeIndex = stages.indexOf(activeStage)
            const isCompleted = index <= activeIndex
            const isCurrent = stage === activeStage

            return (
              <div key={stage} className='relative flex gap-3'>
                <div className='flex flex-col items-center'>
                  <div
                    className={cn(
                      "flex min-h-5 min-w-5 items-center justify-center rounded-full",
                      isCompleted ? "bg-success-500" : "bg-neutral-150",
                      isCurrent && "bg-gray-50",
                    )}
                  >
                    {getStatusIcon(
                      stage,
                      isCompleted,
                      isCurrent,
                      stage == activeStage && stageInfo.warning,
                      stage == activeStage && stageInfo.error,
                    )}
                  </div>
                  {index <
                    (isMobile && !showAllUpdates ? 1 : stages.length - 1) && (
                    <div
                      className={cn(
                        "h-full w-0.5 rounded-full",
                        isCompleted && index < activeIndex
                          ? "bg-success-500"
                          : "bg-neutral-300",
                      )}
                    />
                  )}
                </div>
                <div className='flex flex-1 items-center justify-between gap-2 pb-8'>
                  <Typography
                    as={"p"}
                    className={cn(
                      "!text-sh5 md:!text-sh3",
                      isCurrent ? "text-primary" : "text-foreground",
                    )}
                  >
                    {stage}
                  </Typography>
                  {index <= activeIndex && (
                    <Typography
                      as={"p"}
                      className='!text-o4 text-muted-foreground md:!text-b6'
                    >
                      {formatTimestamp(order_timeline[stage])}
                    </Typography>
                  )}
                </div>
              </div>
            )
          })
        }
      </motion.div>
    </AnimatePresence>
    {isMobile && stages?.length > 2 && (
      // onClick = {() => setShowAllUpdates(!showAllUpdates)}
      <button
        className='w-full'
        onClick={() => setShowAllUpdates(!showAllUpdates)}
      >
        <DividerText
          icon={
            <ChevronDownIcon
              className={cn(
                "h-4 w-4 !text-neutral-900 transition-all",
                showAllUpdates ? "rotate-180" : "rotate-0",
              )}
            />
          }
          text={showAllUpdates ? "Hide Status" : "View Status"}
        />
      </button>
    )}
  </>
)
