"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"

import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import { useThrottle } from "@/hooks/use-throttle"
import { useToggle } from "@/hooks/use-toggle"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { useCheckoutStore } from "@/store/checkout-store"
import { FaqType } from "@/types"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { Loader } from "lucide-react"
import { toast } from "sonner"
import CarepalInformation from "../checkout/carepal/carepal-information"
import { ChargeItem } from "../checkout/order-summary-new/charge-item"
import { ProductSummary } from "../checkout/product-summary"
import { CarepalHeartIcon } from "../Icons/carepal-heart"
import { AdaptiveWrapper } from "../modals/adaptive-wrapper"
import { Alert, AlertDescription } from "../ui/alert"

interface GetCarePalProps {
  faqs: FaqType[] | []
  orderId: string
}

const GetCarepal = ({ faqs, orderId }: GetCarePalProps) => {
  const {
    value: isOpen,
    set,
    onOpen: onOpenReview,
    onClose: onCloseReview,
  } = useToggle()
  const {
    value: isInfoOpen,
    onClose: onCloseInfo,
    onOpen: onOpenInfo,
  } = useToggle()
  const queryClient = useQueryClient()
  const { carepal_fee } = useCheckoutStore()
  const { mutate: handleCarepalUpdate, isPending } = useMutation({
    mutationFn: () =>
      fetchWithAuthPost(
        "https://api.sharepal.in/api:AIoqxnqr/order/carepal-update",
        {
          order_id: orderId,
          amount: carepal_fee,
        },
      ),
    onSuccess: () => {
      toast.success("Carepal applied")
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: ["order-details-fetch", orderId],
        })
        onCloseReview()
        onCloseInfo()
      }, 3000)
    },
    onError: (error) => {
      toast.error(JSON.parse(error.message)?.message ?? "Something Went Wrong")
    },
  })

  const throttleCarepalPayment = useThrottle(handleCarepalUpdate, 1000)

  return (
    <>
      <div className='inline-flex w-full flex-col items-center justify-start gap-4 overflow-hidden rounded-2xl border-2 border-carepal-lighter bg-gradient-to-b from-white to-[rgba(255,217,230,0.36)] p-3 px-3 md:flex-row md:p-2'>
        <div className='flex items-center justify-start gap-1 md:gap-4'>
          <SpImage
            src='https://images.sharepal.in/carepal/heart-filled2.webp'
            alt='CarePal Assure'
            width={100}
            height={100}
            className='md:w-50 h-auto w-28'
            containerClassName='p-3'
          />

          <Alert
            className={cn(
              "rounded-3xl border border-gray-100 p-0",
              "border-0 bg-transparent",
            )}
          >
            <AlertDescription className='text-green-900'>
              <div
                className={cn(
                  "flex h-24 items-center gap-4 overflow-hidden rounded-3xl p-3",
                )}
              >
                <div className='flex flex-col gap-2 text-start'>
                  <div>
                    <Typography
                      as={"h6"}
                      className='text-sh4 text-neutral-900 md:text-sh2'
                    >
                      Accidents happen, Pal!
                    </Typography>

                    <span className='text-sh4 font-bold text-pink-500 md:text-sh2'>
                      Cover damage costs up to ₹25,000
                    </span>
                    <span className='text-base font-bold text-zinc-950'></span>
                  </div>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        </div>

        <Button
          onClick={onOpenInfo}
          variant='outline-primary'
          className='w-full md:w-auto'
        >
          <CarepalHeartIcon className='size-7 h-5 w-5 fill-primary-500' />
          <Typography as={"p"} className='!text-bt3'>
            Get CarePal at Just {moneyFormatter(carepal_fee)}
          </Typography>
        </Button>
      </div>

      <AdaptiveWrapper
        open={isOpen}
        desktop={{
          type: "dialog",
        }}
        tablet={{
          type: "dialog",
        }}
        title='Review & Confirm Order Modification'
        className='overflow-x-hidden md:max-h-[95%] md:p-0'
        onOpenChange={(value) => set(value)}
      >
        <div className='flex h-[95%] flex-col justify-between'>
          <div className='flex items-center justify-center space-x-1 text-o4 text-neutral-500 md:text-b4'>
            <Typography as='p'>
              Order No: <span>{orderId}</span>
            </Typography>
            {/* <Typography as='p'>#{order_id}</Typography> */}
          </div>

          <div className='space-y-4 p-4'>
            <div
              className={cn(
                "flex flex-col gap-4 rounded-lg border-2 border-neutral-200 bg-gray-100 p-4",
              )}
            >
              <div className='flex items-start justify-between gap-2'>
                <ProductSummary
                  showControls={false}
                  showViewItem={false}
                  totalCharges={0}
                />
              </div>

              {/* <ChargeItem */}

              <div className='border-y border-neutral-200 py-5'>
                <ChargeItem
                  item={{
                    label: "Damage Waiver",
                    amount: carepal_fee,
                    badge: {
                      text: "Carepal Assure",
                      variant: "carepal",
                      icon: (
                        <CarepalHeartIcon className='mr-1 !h-4 !w-4 fill-carepal-darker' />
                      ),
                    },
                  }}
                />
              </div>
              <div className='flex w-full items-center justify-between gap-4'>
                <div>
                  <div className='flex cursor-pointer items-center gap-2 text-neutral-900'>
                    <Typography as={"h4"} className='text-sh6 md:text-h4'>
                      Total Charges
                    </Typography>
                  </div>
                  <p className='text-sm text-gray-800'>
                    price incl. of all taxes
                  </p>
                </div>

                <h2 className='text-lg font-bold text-primary-900'>
                  {moneyFormatter(carepal_fee)}
                </h2>
              </div>
            </div>
          </div>

          <div className='sticky bottom-0 grid grid-cols-1 gap-4 bg-gray-100 p-4 shadow-sm md:grid-cols-2'>
            {/* cancel button */}
            <Button
              onClick={onCloseReview}
              variant={"outline-primary"}
              size='lg'
            >
              Cancel
            </Button>

            <Button
              disabled={isPending}
              // onClick={() => updateOrder()}
              onClick={() => {
                throttleCarepalPayment()
              }}
              variant={"primary"}
              className='w-full text-bt2 text-gray-100'
              size='lg'
            >
              {isPending && <Loader className='animate-spin' />}
              {isPending ? "Please Wait" : "Confirm"}
            </Button>
          </div>
        </div>
      </AdaptiveWrapper>

      <CarepalInformation
        isOpen={isInfoOpen}
        onOpenChange={onCloseInfo}
        faqs={faqs ?? []}
        showActionButtons
      >
        <div className='flex w-full flex-col items-start justify-start gap-2 md:flex-row md:items-center md:justify-center md:gap-6'>
          <div className='mr-4 w-max'>
            <div className='flex cursor-pointer items-center gap-2 text-neutral-900'>
              <Typography as={"p"} className='text-sh7 md:text-sh4'>
                Total Charges
              </Typography>
            </div>
            <p className='text-h4 font-bold text-gray-900'>
              {moneyFormatter(carepal_fee)}
            </p>
          </div>
          <Button
            size={"lg"}
            className='w-full flex-1 rounded-full'
            variant={"primary"}
            onClick={onOpenReview}
          >
            Get CarePal Assure
          </Button>
        </div>
      </CarepalInformation>
    </>
  )
}

export default GetCarepal
