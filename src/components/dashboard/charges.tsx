// import { IconCouponDiscount } from "@/components/Icons"
// import { Badge } from "@/components/ui/badge"
// import { moneyFormatter } from "@/functions/small-functions"
// import { AnimatePresence, motion } from "framer-motion"
// import { ShieldEllipsisIcon } from "lucide-react"
// import type React from "react"
// import { Typography } from "../ui/typography"

// export interface ChargeItem {
//   label: string
//   amount: number
//   strikethrough?: number
//   badge?: {
//     text: string
//     variant?: "primary" | "success"
//     icon?: React.ReactNode
//   }
//   items_count?: number
// }

// interface ChargeItemProps {
//   item: ChargeItem
// }

// interface ChargesProps {
//   total_rent: number
//   total_deposit: number
//   delivery_charges?: number
//   pickup_charges?: number
//   items_count: number
//   total_discount?: number
//   wallet_balance_used?: number
//   applied_coupon_code?: string
//   coupon_discount?: number
//   handling_charges?: number
//   wallet_used?: boolean
// }

// export function Charges({
//   total_rent,
//   total_deposit,
//   delivery_charges,
//   pickup_charges,
//   items_count,
//   total_discount,
//   wallet_balance_used,
//   applied_coupon_code,
//   coupon_discount,
//   handling_charges,
//   wallet_used,
// }: ChargesProps) {
//   const charges: ChargeItem[] = [
//     {
//       label: "Total Rental Charges",
//       amount: total_rent,
//       items_count: items_count,
//     },
//     {
//       label: "Deposit Charges",
//       amount: total_deposit,
//       badge: {
//         text: "Zero Policy",
//         variant: "primary",
//       },
//     },
//   ]

//   if (delivery_charges !== undefined) {
//     charges.push({
//       label: "Delivery Charges",
//       amount: delivery_charges,
//       strikethrough: delivery_charges > 0 ? 0 : 299,
//       badge: {
//         text: "Zero Policy",
//         variant: "primary",
//       },
//     })
//   }

//   if (pickup_charges !== undefined) {
//     charges.push({
//       label: "Pickup Charges",
//       amount: pickup_charges,
//       strikethrough: 299,
//       badge: {
//         text: "Zero Policy",
//         variant: "primary",
//       },
//     })
//   }

//   if (coupon_discount !== undefined && applied_coupon_code) {
//     charges.push({
//       label: "Coupon Discount",
//       amount: -coupon_discount,
//       badge: {
//         icon: <IconCouponDiscount className='mr-1 h-4 w-4 fill-success-600' />,
//         text: applied_coupon_code,
//         variant: "success",
//       },
//     })
//   }

//   if (wallet_balance_used !== undefined && wallet_used) {
//     charges.push({
//       label: "Wallet Used",
//       amount: -wallet_balance_used,
//     })
//   }

//   if (total_discount !== undefined && total_discount > 0) {
//     charges.push({
//       label: "Discount Coupon",
//       amount: -total_discount,
//       badge: {
//         text: applied_coupon_code || "Coupon Applied",
//         variant: "success",
//       },
//       strikethrough: total_discount + total_rent,
//     })
//   }

//   if (
//     total_rent > 0 &&
//     handling_charges !== undefined &&
//     handling_charges > 0
//   ) {
//     charges.push({
//       label: "Pay On Delivery Charges",
//       amount: handling_charges,
//     })
//   }

//   return (
//     <div className='w-full space-y-3'>
//       <AnimatePresence>
//         {charges.map((item) => (
//           <motion.div
//             key={item.label}
//             initial={{ opacity: 0, height: 0 }}
//             animate={{ opacity: 1, height: "auto" }}
//             exit={{ opacity: 0, height: 0 }}
//             transition={{ duration: 0.3 }}
//           >
//             <ChargeItem item={item} />
//           </motion.div>
//         ))}
//       </AnimatePresence>
//     </div>
//   )
// }

// export function ChargeItem({ item }: ChargeItemProps) {
//   return (
//     <motion.div
//       initial={{ opacity: 0, y: 20 }}
//       animate={{ opacity: 1, y: 0 }}
//       exit={{ opacity: 0, y: -20 }}
//       transition={{ duration: 0.3 }}
//       className='flex items-center justify-between'
//     >
//       <div className='flex flex-wrap items-center justify-start gap-1 text-right md:gap-2'>
//         <motion.span
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//           className='underline-offset-7 text-b6 text-gray-900 underline decoration-dotted md:text-b4'
//         >
//           {item.label}
//         </motion.span>
//         {item.items_count !== undefined && item.items_count > 0 && (
//           <motion.span
//             initial={{ scale: 0.8 }}
//             animate={{ scale: 1 }}
//             className='block rounded-full bg-neutral-150 px-2 py-1 text-o4 text-gray-500 md:text-b6'
//           >
//             {item.items_count} items added
//           </motion.span>
//         )}
//         {item.badge?.text && (
//           <motion.div
//             initial={{ opacity: 0, x: -10 }}
//             animate={{ opacity: 1, x: 0 }}
//           >
//             <Badge
//               variant={item.badge.variant === "success" ? "success" : "primary"}
//               className=''
//             >
//               {item.badge.icon ? (
//                 item.badge.icon
//               ) : (
//                 <ShieldEllipsisIcon className='mr-1 h-4 w-4' />
//               )}
//               <Typography className='text-o4 md:text-b6' as={"span"}>
//                 {item.badge.text}
//               </Typography>
//             </Badge>
//           </motion.div>
//         )}
//       </div>
//       <div className='flex items-center justify-end gap-1'>
//         {item.strikethrough !== undefined && item.strikethrough > 0 && (
//           <motion.span
//             initial={{ opacity: 0, x: 10 }}
//             animate={{ opacity: 1, x: 0 }}
//             className='text-b5 text-gray-400 line-through md:text-b3'
//           >
//             {moneyFormatter(item.strikethrough)}
//           </motion.span>
//         )}
//         <motion.span
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//           className='text-sh5 md:text-sh3'
//         >
//           {item.amount < 0 ? "-" : ""}
//           {moneyFormatter(Math.abs(item.amount))}
//         </motion.span>
//       </div>
//     </motion.div>
//   )
// }
