import { But<PERSON> } from "@/components/ui/button"
import { X } from "lucide-react"

interface FilterChipProps {
  label: string
  onRemove: () => void
}

export function FilterChip({ label, onRemove }: FilterChipProps) {
  return (
    <div className='inline-flex items-center gap-1 rounded-full border border-neutral-200 bg-gray-100 px-4 py-2'>
      <span className='text-sm font-medium'>{label}</span>
      <Button
        variant='ghost'
        size='icon'
        className='h-4 w-4 p-0 hover:bg-transparent'
        onClick={onRemove}
      >
        <X className='h-3 w-3' />
        <span className='sr-only'>Remove filter</span>
      </Button>
    </div>
  )
}
