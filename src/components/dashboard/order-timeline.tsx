import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { ChevronRight } from "lucide-react"

interface OrderTimelineProps {
  stages: string[]
  activeStage: string
  compact?: boolean
  onViewAll?: () => void
  onBack?: () => void
  timestamp?: string
}

export function OrderTimeline({
  stages,
  activeStage,
  compact = false,
  onViewAll,
  onBack,
  timestamp,
}: OrderTimelineProps) {
  const activeIndex = stages.indexOf(activeStage)

  if (compact) {
    return (
      <div className='space-y-4'>
        {/* Show previous stage if exists */}
        {activeIndex > 0 && (
          <TimelineItem
            stage={stages[activeIndex - 1]}
            status='completed'
            showConnector
          />
        )}

        {/* Current stage */}
        <TimelineItem
          stage={activeStage}
          status='current'
          timestamp={timestamp}
          showConnector={activeIndex < stages.length - 1}
        />

        {/* Show next stage if exists */}
        {activeIndex < stages.length - 1 && (
          <TimelineItem stage={stages[activeIndex + 1]} status='upcoming' />
        )}

        {onViewAll && (
          <Button
            variant='link'
            size='sm'
            className='mt-2 h-auto p-0 text-blue-600'
            onClick={onViewAll}
          >
            View all updates
            <ChevronRight className='ml-1 h-4 w-4' />
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className='space-y-4'>
      {stages.map((stage, index) => (
        <TimelineItem
          key={stage}
          stage={stage}
          status={
            index < activeIndex
              ? "completed"
              : index === activeIndex
                ? "current"
                : "upcoming"
          }
          showConnector={index < stages.length - 1}
          timestamp={index === activeIndex ? timestamp : undefined}
        />
      ))}
      {onBack && (
        <Button
          variant='link'
          size='sm'
          className='mt-2 h-auto p-0 text-blue-600'
          onClick={onBack}
        >
          Back to current status
          <ChevronRight className='ml-1 h-4 w-4' />
        </Button>
      )}
    </div>
  )
}

interface TimelineItemProps {
  stage: string
  status: "completed" | "current" | "upcoming"
  showConnector?: boolean
  timestamp?: string
}

function TimelineItem({
  stage,
  status,
  showConnector,
  timestamp,
}: TimelineItemProps) {
  return (
    <div className='relative'>
      <div className='flex items-start gap-3'>
        <div className='relative flex h-6 items-center'>
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className={`h-2.5 w-2.5 rounded-full ${
              status === "upcoming"
                ? "bg-gray-300"
                : status === "current"
                  ? "bg-green-500"
                  : "bg-green-500"
            }`}
          />
          {showConnector && (
            <motion.div
              initial={{ scaleY: 0 }}
              animate={{ scaleY: 1 }}
              className={`absolute left-1 top-3 h-full w-0.5 origin-top ${
                status === "completed" ? "bg-green-500" : "bg-gray-200"
              }`}
            />
          )}
        </div>
        <div className='flex min-h-[24px] flex-col'>
          <p
            className={`text-sm ${
              status === "current"
                ? "font-medium text-green-700"
                : status === "completed"
                  ? "text-gray-900"
                  : "text-gray-500"
            }`}
          >
            {stage}
          </p>
          {timestamp && (
            <p className='mt-0.5 text-xs text-gray-500'>{timestamp}</p>
          )}
        </div>
      </div>
    </div>
  )
}
