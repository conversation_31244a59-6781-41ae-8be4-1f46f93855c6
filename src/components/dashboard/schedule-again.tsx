"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { format } from "date-fns"
import {
  AlertCircleIcon,
  CalendarClockIcon,
  Loader2,
  MapPin,
} from "lucide-react"

import { useForm } from "react-hook-form"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { cn } from "@/lib/utils"
import {
  ScheduleAgainProps,
  type ScheduleFormData,
  scheduleFormSchema,
} from "@/types/schedule"

import { moneyFormatter } from "@/functions/small-functions"
import { useReturnOrderStore } from "@/store/return-order-store"
import { getExpectedDateScheduleDate } from "@/utils/is-time-valid"
import { generateTimeSlots } from "@/utils/time-slots"
import { useEffect, useMemo } from "react"
import { ChargeItem } from "../checkout/order-summary-new/charge-item"
import { IconSchedlueShipment } from "../Icons/icon-schedule-shipment"
import { Label } from "../ui/label"
import { Typography } from "../ui/typography"

export function ScheduleAgain({
  type,
  address,
  defaultDate,
  handleScheduleAgain,
  className,
  alreadyBooked,
  isShipmentBooking,
  additionalOrderSummary,
  order,
}: ScheduleAgainProps) {
  const timeSlots = useMemo(
    () =>
      generateTimeSlots(
        type === "pickup",
        new Date(defaultDate ?? Date.now()),
        true,
        order.deal_cf_total_per_day_rent,
      ),
    [type, defaultDate],
  )

  const expectedDate = getExpectedDateScheduleDate(defaultDate as number, type)

  const { setTimeSlotCharges, timeSlotCharges } = useReturnOrderStore()

  const form = useForm<ScheduleFormData>({
    resolver: zodResolver(scheduleFormSchema),
    defaultValues: {
      type,
      date: new Date(expectedDate ?? Date.now()),
      address,
      timeSlot: "",
    },
  })

  const isAlreadyPaid = useMemo(
    () =>
      additionalOrderSummary?.timeslot_charges &&
      additionalOrderSummary?.return_payment_option === "pay_now" &&
      additionalOrderSummary?.return_charge_received > 0,
    [additionalOrderSummary],
  )

  useEffect(() => {
    if (isAlreadyPaid) {
      // If the return payment option is "pay_now", we set the time slot charges to 0
      setTimeSlotCharges(0)
    }
  }, [isAlreadyPaid, setTimeSlotCharges])

  useEffect(() => {
    if (form.getValues("timeSlot")) {
      const selectedOption = timeSlots.find(
        (option) => option.value === form.getValues("timeSlot"),
      )
      const pickup_charges = selectedOption?.cost || 0

      setTimeSlotCharges(pickup_charges)
    }
    return () => setTimeSlotCharges(0)
  }, [setTimeSlotCharges, form.watch("timeSlot"), isAlreadyPaid])

  function onSubmit(data: ScheduleFormData) {
    handleScheduleAgain(data)
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn(
          "relative flex h-full max-w-2xl flex-col justify-between rounded-lg bg-neutral-150",
          className,
        )}
      >
        <div className='flex h-full flex-col gap-4 p-4 md:gap-6 md:p-6'>
          <IconSchedlueShipment className='h-max w-full' />

          <AddressSection isPickupOrder={type === "pickup"} address={address} />

          <FormField
            control={form.control}
            name='date'
            render={({ field }) => (
              <FormItem>
                <Label className='text-sm text-neutral-900'>
                  Order {type === "pickup" ? "Pickup" : "Delivery"} on
                </Label>
                <div className='mt-1 flex items-center gap-2 rounded-xl bg-gray-100 px-3 py-2'>
                  <CalendarClockIcon className='h-5 w-5 text-neutral-900' />
                  <p className='flex-1 font-semibold'>
                    {format(field.value, "dd MMM yyyy")}
                  </p>
                </div>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='timeSlot'
            render={({ field }) => (
              <FormItem>
                <Label htmlFor='timeSlot'>
                  Select {type === "pickup" ? "Pickup" : "Delivery"} Time Slot
                  <span className='text-destructive-500'>*</span>
                </Label>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className='mt-1 h-10 rounded-xl bg-gray-100 p-3 md:h-11'>
                      <SelectValue
                        className='text-xs text-neutral-950'
                        placeholder='Select Time'
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {timeSlots.map((slot) => (
                      <SelectItem
                        disabled={slot.disabled}
                        key={slot.value}
                        value={slot.value}
                      >
                        {slot.label}
                        {slot.cost > 0 && (
                          <span className='ml-2 text-sh4 font-bold text-destructive-500'>
                            +{moneyFormatter(slot.cost)}
                          </span>
                        )}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
                <FormDescription className='flex items-center justify-start gap-1'>
                  <AlertCircleIcon className='size-4' />
                  Delay in returns will incur a cost equivalent to the order
                  rent for a day.
                </FormDescription>
              </FormItem>
            )}
          />
          {/* {alreadyBooked && (
            <div className='rounded-2xl bg-destructive-100 p-2 text-b4'>
              <p>
                Your shipment is already scheduled for{" "}
                {alreadyBooked.schedule_date} between{" "}
                {alreadyBooked.schedule_time}
              </p>
            </div>
          )} */}
        </div>

        <div className='sticky bottom-0 w-full bg-gray-100 p-5'>
          {timeSlotCharges > 0 && (
            <div className='mb-3 px-2 md:mb-5'>
              <ChargeItem
                item={{
                  label: "Post 2PM Time Slot Charges",
                  amount: isAlreadyPaid ? 0 : timeSlotCharges,
                  strikethrough: isAlreadyPaid ? timeSlotCharges : undefined,
                }}
              />
            </div>
          )}

          {alreadyBooked &&
            form.getValues("timeSlot") &&
            timeSlotCharges <= 0 && (
              <div className='mb-3 flex items-center gap-2 rounded-2xl bg-primary-100 px-4 py-3'>
                <AlertCircleIcon className='size-4 min-w-4 text-primary-700' />
                <Typography
                  as={"p"}
                  className='flex items-center gap-1 text-b6 text-primary-700'
                >
                  You have changed your time slot to before 2PM, any time slot
                  charges you paid earlier will be automatically refunded to
                  your payment source within 3 to 7 days.
                </Typography>
              </div>
            )}
          <Button
            disabled={form.getValues("timeSlot") === "" || isShipmentBooking}
            type='submit'
            variant='blue'
            className='w-full rounded-full border-neutral-150 py-5 font-bold'
          >
            {isShipmentBooking ? (
              <Loader2 className='animate-spin' />
            ) : (
              <>
                {type === "pickup" && (
                  <span>
                    {timeSlotCharges > 0
                      ? isAlreadyPaid
                        ? `Change Pickup Time For Free`
                        : `Pay ${moneyFormatter(timeSlotCharges)} and Change Pickup Time`
                      : "Change Pickup Time"}
                  </span>
                )}
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  )
}

type AddressSectionProps = {
  name: string
  fullAddress: string
}

function AddressSection({
  address,
  isPickupOrder,
}: {
  address: AddressSectionProps
  isPickupOrder: boolean
}) {
  return (
    <div className='rounded-2xl bg-secondary-100 p-3'>
      <Typography
        as='p'
        className='flex items-center gap-1 font-bold text-primary-500 md:text-sh2'
      >
        <MapPin className='mr-1 size-4 min-h-5 min-w-5 text-primary-500' />
        {isPickupOrder ? (
          <span className='flex items-center gap-2'>Pickup </span>
        ) : (
          <span className='flex items-center gap-2'>Delivery </span>
        )}
        Address
      </Typography>

      <div className='ml-6'>
        <h3 className='text-sh4'>{address.name} </h3>
        <p className='break-words text-b4 text-secondary-900'>
          {address.fullAddress}
        </p>
      </div>
    </div>
  )
}
