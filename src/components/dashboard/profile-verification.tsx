"use client"

import { getUserVerification } from "@/actions/user"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"
import { useUserStore } from "@/store/user-store"
import { VERIFICATION_STATUS } from "@/types/verification"
import { isStatusRequested } from "@/utils/verification"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { AlertCircle, CheckCircle2, InfoIcon } from "lucide-react"
import Link from "next/link"

type VerificationStatus = "pending" | "submitted"

export function ProfileVerification() {
  const { user, userLoading } = useUserStore()
  const { data: userVerification, isLoading: verificationLoading } = useQuery({
    queryKey: ["user_verification"],
    queryFn: async () => await getUserVerification(),
    staleTime: 1000 * 60 * 5, // Cache for 5 minutes
    refetchOnWindowFocus: false,
  })

  const LoadingSkeleton = () => (
    <div className='space-y-6 rounded-3xl bg-gray-100 p-2 md:p-6'>
      <Skeleton className='mb-6 h-8 w-48' />

      <div className='grid grid-cols-2 gap-4'>
        <div className='space-y-6'>
          <div className='grid gap-4'>
            {[1, 2, 3].map((item) => (
              <Skeleton key={item} className='h-14 w-full rounded-2xl' />
            ))}
          </div>
        </div>
        <Skeleton className='h-full w-full rounded-2xl' />
      </div>
    </div>
  )

  if (userLoading || !user || verificationLoading || !userVerification) {
    return <LoadingSkeleton />
  }

  const getVerificationStatus = (
    status: VERIFICATION_STATUS,
  ): VerificationStatus => (isStatusRequested(status) ? "pending" : "submitted")

  const statuses = {
    identity: getVerificationStatus(userVerification.identity_status),
    occupation: getVerificationStatus(userVerification.occupation_status),
    pan: getVerificationStatus(userVerification.credit_status),
  }

  const getStatusIcon = (status: VerificationStatus) => {
    switch (status) {
      case "submitted":
        return <CheckCircle2 className='h-5 w-5 text-success-500' />
      case "pending":
        return <AlertCircle className='h-5 w-5 text-warning-500' />
    }
  }

  const getStatusText = (status: VerificationStatus) => {
    switch (status) {
      case "submitted":
        return (
          <p className='text-sm font-semibold text-success-600 md:text-base'>
            Submitted
          </p>
        )
      case "pending":
        return (
          <p className='text-sm font-semibold text-warning-600 md:text-base'>
            Pending
          </p>
        )
    }
  }

  const verificationItems = [
    { label: "Identity Verification", status: statuses.identity },
    { label: "Occupation Verification", status: statuses.occupation },
    { label: "PAN Verification", status: statuses.pan },
  ]

  const allSubmitted = Object.values(statuses).every(
    (status) => status === "submitted",
  )
  const noneSubmitted = Object.values(statuses).every(
    (status) => status === "pending",
  )

  return (
    <div className='space-y-3 rounded-3xl bg-gray-100 py-2 md:space-y-6 md:p-6'>
      <h2 className='text-xl font-semibold text-neutral-900 md:text-2xl'>
        Profile Verification
      </h2>
      <div className='grid gap-3 md:grid-cols-2 md:gap-4'>
        <div className='grid gap-3 md:gap-4'>
          {verificationItems.map((item, index) => (
            <motion.div
              key={item.label}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className='flex flex-col items-start justify-start gap-2 rounded-2xl px-2 md:px-4'
            >
              <span className='text-xs font-medium text-neutral-300 md:text-sm'>
                {item.label}
              </span>

              <div className='flex items-center gap-2'>
                {getStatusIcon(item.status)}
                {getStatusText(item.status)}
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={cn(
            "row-start-1 h-full w-full rounded-2xl p-4 md:row-start-auto",
            allSubmitted
              ? "bg-success-100"
              : noneSubmitted
                ? "bg-warning-100"
                : "bg-warning-100",
          )}
        >
          <div className='flex h-full flex-col items-start gap-2'>
            <div className='flex items-center justify-start gap-2'>
              {allSubmitted ? (
                <CheckCircle2 className='mt-1 h-5 w-5 text-success-500' />
              ) : (
                <InfoIcon className='mt-1 h-5 w-5 text-warning-500' />
              )}
              <p
                className={cn(
                  "text-sm font-bold text-neutral-900",
                  allSubmitted ? "text-success-600" : "text-warning-600",
                )}
              >
                {allSubmitted
                  ? "All Documents Submitted"
                  : noneSubmitted
                    ? "Customer Verification Pending"
                    : "Partially Submitted Profile"}
              </p>
            </div>

            <div
              className={"flex h-full w-full flex-col justify-between gap-3"}
            >
              <p
                className={cn(
                  "text-xs font-medium text-neutral-900 md:text-sm",
                  allSubmitted ? "text-success-800" : "text-warning-800",
                )}
              >
                {allSubmitted
                  ? "Your documents are submitted. We'll verify them shortly."
                  : "Complete your verification process to enjoy hassle-free rentals."}
              </p>
              <div className='flex w-full items-center justify-end'>
                {!allSubmitted && (
                  <Button
                    asChild
                    variant={"outline"}
                    className={cn(
                      "h-9 w-full border-2 bg-gray-100 font-bold md:h-11",
                      noneSubmitted
                        ? "border-primary-500 text-primary-500"
                        : "border-primary-500 text-primary-500",
                    )}
                  >
                    <Link href='/complete-verification'>
                      {noneSubmitted
                        ? "Start Verification"
                        : "Complete Verification"}
                    </Link>
                  </Button>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
