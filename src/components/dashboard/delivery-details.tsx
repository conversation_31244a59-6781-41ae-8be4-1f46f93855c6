"use client"

import { CalendarD<PERSON>Icon, MapPin, RefreshCwIcon, Truck } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useRentalStore } from "@/store/rental-store"
import { formatDate } from "@/utils/date-logics"
import { Dispatch, SetStateAction, useEffect, useState } from "react"
import { Typography } from "../ui/typography"
// import RentalChargeCalculator from '../modals/rental-charge-calculator'

interface DeliveryDetailsProps {
  order: {
    id: string
    customerName: string
    phoneNumber: string
    deliveryAddress: string
    deliveryDate: Date
    pickupDate: Date
    total_rent: number
    main_order_id: number
    address_id: number
  }
  showModification?: boolean
  setOpenDeliveryAddress: Dispatch<SetStateAction<boolean>>
  setOpenDateUpdate: Dispatch<SetStateAction<boolean>>
}

export default function DeliveryDetails({
  order,
  showModification = true,
  setOpenDateUpdate,
  setOpenDeliveryAddress,
}: DeliveryDetailsProps) {
  const [date, setDate] = useState({
    deliveryDateTime: formatDate(order.deliveryDate),
    pickupDateTime: formatDate(order.pickupDate),
  })

  const { setDeliveryDate, setPickupDate } = useRentalStore()
  useEffect(() => {
    setDate({
      deliveryDateTime: formatDate(order.deliveryDate),
      pickupDateTime: formatDate(order.pickupDate),
    })
  }, [order])
  return (
    <>
      <div className='w-full space-y-4'>
        {/* Header */}
        <div className='flex items-center gap-3'>
          <Truck className='h-6 w-6' />
          <Typography
            as={"h6"}
            className='text-sh4 text-primary-900 md:text-h6'
          >
            Delivery & Pickup Details:
          </Typography>
        </div>

        {/* Content Grid */}
        <div className='grid gap-4 md:grid-cols-2'>
          <div className='flex h-full w-full flex-col gap-3'>
            {/* Delivery Address Card */}
            <Card className='w-full overflow-hidden !rounded-2xl'>
              <CardContent className='p-0'>
                <div className='w-full'>
                  <div className='flex items-center gap-1 bg-neutral-150 px-4 py-3'>
                    <MapPin className='h-4 w-4 text-gray-600' />
                    <Typography as={"h5"} className='text-sh7 md:text-sh6'>
                      Delivering To:
                    </Typography>
                  </div>
                  <div className='px-4 py-3'>
                    <Typography
                      as={"h4"}
                      className='text-sh6 text-neutral-850 md:text-sh5'
                    >
                      {order.customerName} | {order.phoneNumber}{" "}
                    </Typography>
                    <Typography
                      as={"p"}
                      className='mt-1 max-w-max text-wrap text-b6 text-gray-700'
                    >
                      {order.deliveryAddress}
                    </Typography>
                  </div>
                </div>
              </CardContent>
            </Card>

            {showModification && (
              <Button
                variant='link'
                size={"sm"}
                className='flex h-auto items-center justify-start gap-2 p-0 text-blue-600 hover:text-blue-700'
                asChild
              >
                <Button
                  onClick={() => {
                    setOpenDeliveryAddress(true)
                  }}
                  variant={"link"}
                  className='!text-bt4 md:!text-bt3'
                >
                  <RefreshCwIcon className='h-5 w-5 text-blue-600' />
                  Change Delivery Address
                </Button>
              </Button>
            )}
          </div>

          <div className='flex h-full w-full flex-col gap-3'>
            {/* Dates Card */}
            <Card className='h-full w-full overflow-hidden !rounded-2xl'>
              <CardContent className='h-full w-full p-0'>
                {/* card top */}
                <div className='grid grid-cols-2 gap-2 bg-neutral-150 px-4 py-3 md:gap-10'>
                  <div className='flex items-center gap-2'>
                    <CalendarDaysIcon className='h-4 w-4 text-gray-600' />
                    <Typography as={"h5"} className='text-sh7 md:text-sh5'>
                      Delivery Date:
                    </Typography>
                  </div>
                  <div className='flex items-center gap-2 self-start'>
                    <CalendarDaysIcon className='h-4 w-4 text-gray-600' />
                    <Typography as={"h5"} className='text-sh7 md:text-sh5'>
                      Pickup Date:
                    </Typography>
                  </div>
                </div>

                <div className='grid h-full grid-cols-2 items-center gap-1 overflow-hidden px-4'>
                  <div className='h-full border-r border-gray-200 py-3 pr-4'>
                    <Typography as={"p"} className='text-sh6 md:text-sh5'>
                      {date.deliveryDateTime}
                    </Typography>
                    <Typography as={"p"} className='text-b6 text-gray-600'>
                      Delivery between: <span>3 pm to 10 pm</span>
                    </Typography>
                  </div>
                  <div className='ml-4 h-full py-3'>
                    <Typography as={"p"} className='text-sh6 md:text-sh5'>
                      {date.pickupDateTime}
                    </Typography>
                    <Typography as={"p"} className='text-b6 text-gray-600'>
                      Pickup between: <span>9 am to 1 pm</span>
                    </Typography>
                  </div>
                </div>
              </CardContent>
            </Card>

            {showModification && (
              <Button
                variant='link'
                size={"sm"}
                onClick={() => {
                  setOpenDateUpdate(true)
                  setDeliveryDate(order.deliveryDate)
                  setPickupDate(order.pickupDate)
                }}
                className='flex h-auto items-center justify-start gap-2 p-0 !text-bt4 font-semibold text-blue-600 hover:text-blue-700 md:!text-bt3'
              >
                <RefreshCwIcon className='h-5 w-5 text-blue-600' />
                Change Delivery/Pickup Dates
              </Button>
            )}
          </div>
        </div>
      </div>
    </>
  )
}
