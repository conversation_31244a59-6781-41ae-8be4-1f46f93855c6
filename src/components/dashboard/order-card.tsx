"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Action, type APIOrder } from "@/types/order"
import { useQuery } from "@tanstack/react-query"
import {
  CalendarCheckIcon,
  CheckCircle,
  ChevronRight,
  HelpCircle,
  XCircleIcon,
} from "lucide-react"

import { moneyFormatter } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { formatDateWithOrdinal } from "@/utils/date-logics"
import Link from "next/link"
import { Separator } from "../ui/separator"
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip"

import { fetchOrderItems, fetchOrderStatus } from "@/actions/orders"
import useMediaQuery from "@/hooks/use-media-query"
import { showModification } from "@/lib/order-status"
import { differenceInDays, startOfDay } from "date-fns"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { CustomDropdown } from "../custom/custom-dropdown"

interface OrderCardProps {
  order: APIOrder
  // onVerify?: () => void
  onModify?: () => void
}

export function OrderCard({ order, onModify }: OrderCardProps) {
  const {
    data: orderItems,
    isLoading: isLoadingItems,
    isError: isErrorItems,
  } = useQuery({
    queryKey: ["orderItems", order.id],
    queryFn: () => fetchOrderItems(order.deal_name),
    select: (data) => (data ? data.items : []),
  })

  const { data: stageActions } = useQuery({
    queryKey: ["stageActions", order.id],
    queryFn: () => fetchOrderStatus(order.deal_name),
    select: (data) => data,
  })

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case "Order Cancelled":
        return {
          label: stageActions?.activeStage ?? "",
          description: stageActions?.stageInfo.message ?? "",
          className: "text-red-600 !border-red-600",
          icon: <XCircleIcon className='h-6 w-6' />,
        }
      default:
        return {
          label: stageActions?.activeStage ?? "",
          description: stageActions?.stageInfo.message ?? "",
          action: "Modify Order",
          onAction: onModify,
          className: "text-green-600",
          icon: <CheckCircle className='h-6 w-6' />,
        }
    }
  }

  const statusDisplay = getStatusDisplay(order.deal_deal_stage_name)

  const formatDate = (timestamp: Date | number) =>
    new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })

  const calculateRentalPeriod = () => {
    const start = startOfDay(new Date(order.deal_cf_delivery_date))
    const end = startOfDay(new Date(order.deal_cf_pickup_date))
    const days = differenceInDays(end, start) - 1

    return `${days} Days`
  }
  const isMobile = useMediaQuery("(max-width: 767px)")

  const itemNumber = isMobile ? 2 : 4
  return (
    <Card className='overflow-hidden rounded-3xl border-2 border-neutral-200'>
      <CardHeader className='flex flex-row items-center justify-between gap-2 space-y-0 bg-neutral-150 p-4 px-3 py-2 md:gap-6'>
        {/* left  */}
        <div className='hidden items-center justify-between gap-3 md:flex md:gap-6'>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              ORDER PLACED ON:
            </p>
            <p className='text-sm font-medium'>
              {formatDate(order.order_date)}
            </p>
          </div>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              SHIPPING TO:
            </p>
            <div className='flex items-center gap-1'>
              <p className='text-sm font-medium'>{order.calling_number}</p>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className='h-3.5 w-3.5 text-neutral-400' />
                  </TooltipTrigger>
                  <TooltipContent className='max-w-[300px]'>
                    <p className='text-xs'>Shipping address not available</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              RENTAL PERIOD:
            </p>
            <p className='text-sm font-medium'>{calculateRentalPeriod()}</p>
          </div>
        </div>

        <div className='flex w-full items-center justify-between gap-2 md:w-auto'>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              ORDER NO:
            </p>
            <p className='text-sm font-medium'>{order.deal_name}</p>
          </div>
          <Button
            size={"icon"}
            // onClick={onVerify}
            variant={"outline"}
            asChild
            className='border-neutral-200 bg-gray-100'
          >
            <Link href={`/dashboard/orders/${order.deal_name}`}>
              <ChevronRight className='h-5 w-5 text-neutral-400' />
            </Link>
          </Button>
        </div>
      </CardHeader>

      <CardContent className='bg-gray-100 p-2 md:space-y-6 md:px-4 md:py-3'>
        {/* desktop */}
        <div className='hidden w-full md:block'>
          {/* {statusDisplay && ( */}
          <StatusDisplay
            stageActions={stageActions?.stageInfo.actions || []}
            statusDisplay={statusDisplay}
            order={order}
          />
          {/* )} */}
        </div>

        <div className='grid grid-cols-1 items-start gap-y-2 md:grid-cols-3 md:gap-3'>
          <div className='col-span-2 flex w-full items-start justify-start gap-4 rounded-xl bg-neutral-150 p-2 md:rounded-2xl md:p-3'>
            <div className='flex -space-x-4 lg:max-w-56 lg:-space-x-14'>
              {isLoadingItems ? (
                <div className='h-14 w-14 animate-pulse rounded-lg bg-gray-150 md:h-20 md:w-20'></div>
              ) : isErrorItems ? (
                <div className='flex h-24 w-24 items-center justify-center rounded-lg bg-red-100 text-red-500'>
                  Error
                </div>
              ) : orderItems && orderItems.length > 0 ? (
                <>
                  {orderItems.slice(0, itemNumber).map((item, index) => (
                    <div
                      key={index}
                      className='relative h-14 w-14 overflow-hidden rounded-lg border bg-gray-100 md:h-20 md:w-20 md:rounded-xl'
                      style={{ zIndex: orderItems.length - index }}
                    >
                      <SpImage
                        src={item.cart_image}
                        alt={item.item_name}
                        fill
                        className='h-full w-full object-cover p-0.5 md:p-2'
                      />
                    </div>
                  ))}
                  {orderItems.length > itemNumber && (
                    <div
                      className='relative flex h-14 w-11 items-center justify-end rounded-xl border bg-neutral-200 p-1 text-end text-sh4 text-gray-700 md:h-20 md:w-20'
                      style={{ zIndex: orderItems.length - itemNumber }}
                    >
                      +{orderItems.length - itemNumber}
                    </div>
                  )}
                </>
              ) : (
                <div className='flex h-24 w-24 items-center justify-center rounded-lg bg-gray-100 text-gray-500'>
                  No items
                </div>
              )}
            </div>

            {/* order details */}
            <div className='flex-1'>
              <p className='mb-0 text-[10px] font-medium uppercase tracking-wide text-neutral-500 md:mb-1'>
                ORDER ITEMS:
              </p>
              <div className='space-y-0.5'>
                {isLoadingItems ? (
                  <p className='text-xs text-neutral-700'>
                    Loading order items...
                  </p>
                ) : isErrorItems ? (
                  <p className='text-xs text-red-500'>
                    Failed to load order items
                  </p>
                ) : (
                  <div className='flex flex-wrap justify-start md:items-start md:gap-2'>
                    {orderItems && orderItems.length > 0 ? (
                      orderItems.slice(0, itemNumber).map((item, index) => (
                        <p
                          key={index}
                          className='line-clamp-1 text-wrap break-words text-o4 text-neutral-700 md:text-b6'
                        >
                          {/* {item.quantity}x {item.item_name}, */}
                          {item.item_name} ({item.quantity} Item),
                        </p>
                      ))
                    ) : (
                      <p className='text-sm text-neutral-700'>
                        No items available
                      </p>
                    )}
                    {orderItems && orderItems.length > itemNumber && (
                      <p className='block text-b6'>
                        +{orderItems.length - itemNumber}
                      </p>
                    )}
                  </div>
                )}
              </div>
              <p className='mt-1 flex items-center gap-1 md:mt-4'>
                <span className='text-sm font-semibold'>
                  {moneyFormatter(order.deal_cf_net_amount)}
                </span>
                <span className='text-xs text-neutral-600'>
                  {/* (Payment status not available) */}
                </span>
              </p>
            </div>
          </div>

          <div className='flex h-full w-full flex-row items-center justify-evenly gap-1 rounded-xl bg-neutral-150 p-2 md:w-auto md:flex-col md:items-start md:justify-evenly md:gap-3 md:rounded-2xl md:p-4'>
            <div className='w-max'>
              <p className='flex w-max items-center text-[10px] text-neutral-500 md:gap-1 md:text-sm'>
                <CalendarCheckIcon className='mr-1 h-2.5 w-2.5 md:h-4 md:w-4' />
                Delivery Date:{" "}
                <span className='ml-0.5 hidden text-xs font-bold text-primary-900 md:flex'>
                  {formatDate(order.deal_cf_delivery_date)}
                </span>
                <span className='ml-0.5 flex text-[10px] font-bold text-primary-900 md:hidden'>
                  {formatDateWithOrdinal(order.deal_cf_delivery_date)}
                </span>
              </p>
            </div>

            <Separator orientation='vertical' className='flex md:hidden' />

            <Separator className='hidden md:flex' />

            <div className='w-max'>
              <p className='flex items-center text-[10px] text-neutral-500 md:gap-1 md:text-sm'>
                <CalendarCheckIcon className='mr-1 h-2.5 w-2.5 md:h-4 md:w-4' />
                Pickup Date:{" "}
                <span className='ml-0.5 hidden text-xs font-bold text-primary-900 md:flex'>
                  {formatDate(order.deal_cf_pickup_date)}
                </span>
                <span className='ml-0.5 flex text-[10px] font-bold text-primary-900 md:hidden'>
                  {formatDateWithOrdinal(order.deal_cf_pickup_date)}
                </span>
              </p>
            </div>
          </div>
        </div>

        {/* mobile */}
        <div className='mt-3 block w-full md:hidden'>
          {/* {statusDisplay && ( */}
          <StatusDisplay
            stageActions={stageActions?.stageInfo.actions || []}
            statusDisplay={statusDisplay}
            order={order}
          />
          {/* )} */}
        </div>
      </CardContent>
    </Card>
  )
}

interface StatusDisplayProps {
  statusDisplay: {
    label: string
    description: string
    action?: string
    onAction?: () => void
    className: string
    icon: React.ReactNode
  }
  stageActions: Action[]
  order: APIOrder
}

const StatusDisplay = ({
  statusDisplay,
  stageActions,
  order,
}: StatusDisplayProps) => {
  const [isModifyOrderOpen, setIsModifyOrderOpen] = useState(false)
  const router = useRouter()
  return (
    <div className='flex flex-col items-start justify-between gap-3 pb-1 md:flex-row md:pb-0'>
      <div className={cn("space-y-1")}>
        <h6
          className={cn(
            "flex items-center gap-2 text-sm font-semibold md:text-xl",
            statusDisplay.className,
          )}
        >
          {statusDisplay.icon}
          {statusDisplay.label}
        </h6>
        <p className='text-xs font-medium text-primary-900 md:text-sm'>
          {statusDisplay.description}
        </p>
      </div>

      {/* {statusDisplay.action == "Modify Order" && ( */}
      {showModification(order.deal_deal_stage_name) && (
        <CustomDropdown
          title='Modify Options'
          options={stageActions
            .filter((data) => data.text != "Modify Orders")
            .map((data) => ({
              label: data.text,
              value: data.text,
              onClick: () => {
                router.push(
                  "/dashboard/orders/" +
                    order.deal_name +
                    "?open=" +
                    data.action_type,
                )
              },
            }))}
          isOpen={isModifyOrderOpen}
          setIsOpen={() => setIsModifyOrderOpen(false)}
        >
          <Button
            size={"lg"}
            onClick={() => {
              setIsModifyOrderOpen(true)
            }}
            variant='outline-primary'
            className='ml-auto h-9 w-full md:h-10 md:w-auto'
          >
            Modify Order
          </Button>
        </CustomDropdown>
      )}

      {statusDisplay.action && statusDisplay.action != "Modify Order" && (
        <Button
          variant='outline-primary'
          className='ml-auto h-9 w-full md:h-10 md:w-auto'
          onClick={statusDisplay.onAction}
        >
          {statusDisplay.action}
        </Button>
      )}
    </div>
  )
}
