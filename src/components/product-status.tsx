// import {
//   NoSymbolIcon,
//   ClockIcon,
//   SparklesIcon,
// } from "@heroicons/react/24/outline";
import { FC } from "react"

import { ClockIcon, EqualNotIcon, SparkleIcon } from "lucide-react"
import IconDiscount from "./Icons/icon-discount"

interface Props {
  // status: Product["status"];
  status:
    | "New in"
    | "50% Discount"
    | "Out of Stock"
    | "Sold Out"
    | "limited edition"
    | null
  className?: string
}

const ProductStatus: FC<Props> = ({
  status,
  className = "absolute top-3 start-3 px-2.5 py-1.5 text-xs bg-gray-100 dark:bg-slate-900 text-slate-700 dark:text-slate-300",
}) => {
  const renderStatus = () => {
    if (!status) {
      return null
    }
    const CLASSES = `nc-shadow-lg rounded-full flex items-center justify-center ${className}`
    if (status === "New in") {
      return (
        <div className={CLASSES}>
          <SparkleIcon className='h-3.5 w-3.5' />
          <span className='ms-1 leading-none'>{status}</span>
        </div>
      )
    }
    if (status === "50% Discount") {
      return (
        <div className={CLASSES}>
          <IconDiscount className='h-3.5 w-3.5' />
          <span className='ms-1 leading-none'>{status}</span>
        </div>
      )
    }
    if (status === "Sold Out") {
      return (
        <div className={CLASSES}>
          <EqualNotIcon className='h-3.5 w-3.5' />
          <span className='ms-1 leading-none'>{status}</span>
        </div>
      )
    }
    if (status === "limited edition") {
      return (
        <div className={CLASSES}>
          <ClockIcon className='h-3.5 w-3.5' />
          <span className='ms-1 leading-none'>{status}</span>
        </div>
      )
    }
    return null
  }

  return renderStatus()
}

export default ProductStatus
