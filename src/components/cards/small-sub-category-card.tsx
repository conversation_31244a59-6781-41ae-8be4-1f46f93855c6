import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { SubCategory } from "@/types"
import { Separator } from "@radix-ui/react-select"
import Link from "next/link"
import { useParams } from "next/navigation"
import { Typography } from "../ui/typography"

export interface SmallSubCategoryCardProps {
  className?: string
  data: SubCategory
  category: string
}

const SmallSubCategoryCard = ({
  className,
  data,
  category,
}: SmallSubCategoryCardProps) => {
  const { sc_image, sc_name_ri, url } = data
  const { city = "bangalore" } = useParams()
  const isActive = category == url
  return (
    <Link scroll={false} href={`/${city}?category=${url}`} prefetch={true}>
      <Card
        className={cn(
          `group flex flex-col items-center justify-center gap-0 border-none`,
          className,
        )}
      >
        {/* <div className="relative aspect-square overflow-hidden object-contain flex items-center justify-center px-3"> */}
        <CardContent
          className={cn(
            "group relative aspect-square min-w-10 overflow-hidden rounded-lg border bg-gray-100 p-0 leading-5 transition-all duration-300 hover:bg-gray-150 md:h-[100px] md:w-[100px] md:rounded-[9.455px]",
            isActive && "border-2 border-primary-500",
          )}
        >
          <SpImage
            src={sc_image}
            alt={sc_name_ri}
            className={cn(
              "h-full w-20 object-contain p-2 group-hover:scale-110",
              isActive && "scale-95",
            )}
            width={92}
            height={92}
            containerClassName={
              "relative  aspect-square overflow-hidden object-contain flex items-center justify-center  "
            }
          />
        </CardContent>

        <CardFooter className='relative h-full flex-col p-0 px-2'>
          <Typography
            as={"h5"}
            className={cn(
              "mb-2 line-clamp-2 w-full pb-0 pt-2 text-center !text-sh7 text-primary-900 md:pb-4 md:pt-5 md:!text-sh5",
              isActive ? "text-primary-500" : "text-primary-900",
            )}
          >
            {data.sc_name}
          </Typography>
          {isActive && (
            <Separator className='mx-auto mb-1 block h-[2px] w-8 rounded-full bg-primary-500 md:mb-0 md:w-14' />
          )}
        </CardFooter>

        {/* </div> */}
      </Card>
    </Link>
  )
}

export default SmallSubCategoryCard
