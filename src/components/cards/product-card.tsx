"use client"
import { handleFavourite } from "@/actions/user"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { generateLinkPath } from "@/functions/generate-link-path"
import { getImage } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { useCalendarStore, useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { RentalItem } from "@/types"
import { InfoIcon, TrendingUp } from "lucide-react"
import Link from "next/link"
import { Suspense, useCallback, useEffect, useState } from "react"
import { Ratings } from "../ui/ratings"

import { useOnboardingStore } from "@/store/onboarding-store"
import {
  calculateTransitDate,
  formatDateWithOrdinal,
} from "@/utils/date-logics"
import { addDays, isBefore } from "date-fns"
import { HeartFilledIcon, HeartOutlinedIcon } from "sharepal-icons"
import { toast } from "sonner"
import RenderTag from "../custom/product-tag"
import ProductCardExtraDayPrice from "../suspense/product-card-extra-day-price"
import ProductCardPrice from "../suspense/product-card-price"
import Marquee from "../ui/marquee"
import { Skeleton } from "../ui/skeleton"

export interface ProductCardProps {
  className?: string
  data: RentalItem
  isLiked?: boolean
  city: string
  hideDateAndPrice?: boolean
}

export default function ProductCard({
  className = "",
  data,
  city,
  hideDateAndPrice = false,
}: ProductCardProps) {
  const {
    id,
    ri_name,
    ri_short_name,
    ri_image,
    ri_image_alt_text,
    category_short_name,
    sc_name,
    out_of_stock,
    rating,
    booked_count,
    product_qualities,
    decoration_text: tag,
  } = data

  const { total_days, cityMinthMap } = useRentalStore()
  const { openCalendar } = useCalendarStore()
  const { user, setUser } = useUserStore()
  const { openModal } = useOnboardingStore()
  // const { rent, extraDayRent } = useCalculateRent({
  //   product: data,
  //   type: "product",
  // })

  const getUrl = generateLinkPath(ri_name, city, category_short_name, sc_name)

  const toggleFavourite = useCallback(
    async (itemId: number) => {
      if (!user) {
        // toast.info("Login to like product!")
        openModal()
        return
      }

      const updatedData = await handleFavourite(itemId, user)
      if (updatedData) {
        setUser(updatedData)
      }
    },
    [user, setUser, openModal],
  )

  const finalRating = rating ?? Number((Math.random() * 0.8 + 4.2).toFixed(1))
  const [transitDate, setTransitDate] = useState<Date | null>(null)
  const [restrictTransit, setRestrictTransit] = useState(false)
  const { delivery_date, selectedCity } = useRentalStore()
  useEffect(() => {
    const minth = cityMinthMap[sc_name.toLowerCase()] ?? 0

    if (delivery_date && minth != null) {
      const transitDate = calculateTransitDate(minth)
      setTransitDate(transitDate)
      setRestrictTransit(isBefore(addDays(delivery_date, 1), transitDate))
    }
  }, [selectedCity, delivery_date, cityMinthMap, sc_name])

  const renderBookedCount = () => (
    <div className='flex items-center justify-center gap-1 overflow-hidden text-sh7 text-success-700 md:gap-2 md:text-sh5'>
      <TrendingUp className='w-4 text-success-500 md:w-5' />{" "}
      {/* Mobile version - marquee animation */}
      <div className='block md:hidden'>
        <Marquee
          className='min-w-max'
          containerClassName='p-0 !gap-0'
          repeat={2}
          duration={5}
          onlyMobile
        >
          <span className='mr-4'>
            {booked_count ? booked_count : Math.floor(Math.random() * 11) + 20}{" "}
            booked this month
          </span>
        </Marquee>
      </div>
      {/* Desktop version - no animation */}
      <p className='hidden min-w-max md:inline-flex'>
        <span className='line-clamp-1 w-full'>
          {booked_count ? booked_count : Math.floor(Math.random() * 11) + 20}{" "}
          booked this month
        </span>
      </p>
    </div>
  )

  return (
    <Link
      onClick={(e) => {
        if (!total_days) {
          e.preventDefault()
          e.nativeEvent.stopImmediatePropagation()
          openCalendar()
        }
      }}
      href={getUrl}
      prefetch={true}
    >
      <Card
        className={cn(
          `group relative h-max overflow-hidden rounded-2xl border-none bg-transparent p-2.5 leading-5 transition-all duration-300 md:rounded-3xl md:p-3`,
          className,
          out_of_stock ? "hover:bg-gray-200" : "hover:bg-gray-100",
        )}
      >
        <div className='relative aspect-square overflow-hidden rounded-lg bg-gray-100 p-1.5 md:rounded-2xl md:p-3'>
          <SpImage
            src={getImage(ri_image)}
            alt={ri_image_alt_text}
            className='h-full w-full object-contain'
            width={300}
            height={300}
            containerClassName='p-3 md:p-5'
          />
          <RenderTag out_of_stock={out_of_stock} tag={tag} />
          {/* Heart Button (casuing double click issue*/}

          <button
            type='button'
            onClick={(e) => {
              e.preventDefault()
              e.nativeEvent.stopImmediatePropagation()
              toggleFavourite(id)
            }}
            className={cn(
              "scale-80 absolute right-1 top-1 h-max w-max transform justify-end p-0 opacity-10 transition-all duration-300 group-hover:scale-100 group-hover:opacity-100 md:right-3 md:top-3 md:opacity-0",
              user?.favourite_items?.includes(id)
                ? "text-red-500"
                : "text-gray-500",
            )}
          >
            {user?.favourite_items?.includes(id) ? (
              <HeartFilledIcon className='size-4 md:size-6' strokeWidth={2} />
            ) : (
              <HeartOutlinedIcon className='size-4 md:size-6' strokeWidth={2} />
            )}
          </button>
          {!out_of_stock && restrictTransit ? (
            <div className='absolute bottom-0 left-0 right-0 flex h-14 w-full flex-col items-center justify-center bg-[#e3e3e3] bg-opacity-80 p-1.5 text-neutral-600 md:p-2 md:leading-5'>
              <p className='text-xs font-bold md:text-sm md:leading-5'>
                Available from{" "}
                {formatDateWithOrdinal(transitDate ?? new Date()) || ""}
                {/* <p className='text-center text-[10px] md:text-xs md:leading-5'>
                  Change Dates
                </p> */}
              </p>
            </div>
          ) : (
            out_of_stock && (
              <div className='absolute bottom-0 left-0 right-0 flex h-max w-full flex-col items-center justify-center bg-black bg-opacity-80 p-1.5 text-gray-100 md:p-2 md:leading-5'>
                <p className='text-xs font-bold md:text-sm md:leading-5'>
                  Out of Stock
                </p>
                <p className='text-[10px] md:text-xs md:leading-5'>
                  Will be available soon
                </p>
              </div>
            )
          )}
        </div>
        <CardContent className='flex flex-col items-start justify-center gap-1 p-0 pt-2.5 md:gap-2.5 md:p-3'>
          <div className='w-full'>
            <h2 className='line-clamp-1 text-sh4 md:text-sh2'>
              {ri_short_name}
            </h2>
            <p className='line-clamp-1 text-sh7 text-gray-800 md:text-sh5'>
              {product_qualities || ri_name}
            </p>
          </div>
          <Suspense fallback={<Skeleton className='h-5 w-10' />}>
            {!hideDateAndPrice && !out_of_stock && !restrictTransit && (
              <ProductCardPrice total_days={total_days} data={data} />
            )}
          </Suspense>
        </CardContent>
        <CardFooter className='flex w-full flex-col items-start justify-between gap-0 px-0 py-0 md:gap-1 md:px-3'>
          {!out_of_stock && restrictTransit ? (
            <div className='w-full'>
              <p className='flex items-center justify-center gap-1 overflow-hidden text-sh7 text-gray-700 md:gap-2 md:text-sh5'>
                <InfoIcon className='w-4 text-gray-500 md:w-5' />{" "}
                <span className='line-clamp-1 w-full'>Unavailable</span>
              </p>
            </div>
          ) : (
            !hideDateAndPrice && (
              <div className='w-full'>
                {out_of_stock ? (
                  <p className='flex items-center justify-center gap-1 overflow-hidden text-sh7 text-gray-700 md:gap-2 md:text-sh5'>
                    <InfoIcon className='w-4 text-gray-500 md:w-5' />{" "}
                    <span className='line-clamp-1 w-full'>Out of Stock</span>
                  </p>
                ) : (
                  <Suspense fallback={<Skeleton className='h-5 w-10' />}>
                    {/* // renderExtraDayInfo() */}
                    <ProductCardExtraDayPrice
                      total_days={total_days}
                      data={data}
                    />
                  </Suspense>
                )}
              </div>
            )
          )}

          {renderBookedCount()}
          <div className='flex items-center'>
            <Ratings
              rating={finalRating}
              size={15}
              className='hidden md:flex'
            />
            <Ratings
              rating={finalRating}
              size={11}
              className='flex md:hidden'
            />
            <span className='ml-2 text-xs text-gray-900 md:text-sm'>
              {" "}
              ({finalRating})
            </span>
          </div>

          {!out_of_stock && restrictTransit ? (
            <Button
              onClick={openCalendar}
              variant={"outline"}
              className='mt-2 h-8 w-full border border-primary-900 bg-gray-100 py-3 md:min-h-10'
            >
              Change Date
            </Button>
          ) : (
            // show add to whishlist button
            out_of_stock && (
              <Button
                onClick={(e) => {
                  e.preventDefault()
                  e.nativeEvent.stopImmediatePropagation()
                  if (!user?.favourite_items?.includes(id)) {
                    toggleFavourite(id)
                  } else {
                    toast.info("Already in Wishlist!")
                    return
                  }
                }}
                variant={"outline"}
                className='mt-2 h-8 w-full border border-primary-900 bg-gray-100 py-3 md:min-h-10'
              >
                {user?.favourite_items?.includes(id)
                  ? "In Wishlist"
                  : "Add to Wishlist"}
              </Button>
            )
            // out_of_stock && (
            //   <Button
            //     onClick={(e) => {
            //       handleNotifyMe(e, ri_short_name)
            //     }}
            //     variant={"outline"}
            //     className='mt-2 h-8 w-full border border-primary-900 bg-gray-100 py-3 md:min-h-10'
            //   >
            //     Contact Support
            //   </Button>
            // )
          )}
        </CardFooter>
      </Card>
    </Link>
  )
}
