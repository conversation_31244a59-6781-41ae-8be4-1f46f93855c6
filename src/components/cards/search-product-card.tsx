"use client"
import { handleFavourite } from "@/actions/user"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { generateLinkPath } from "@/functions/generate-link-path"
import { getImage, moneyFormatter } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { useCalendarStore, useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { RentalItem } from "@/types"
import { InfoIcon, TrendingUp } from "lucide-react"
import Link from "next/link"
import React, { useCallback } from "react"
import { Ratings } from "../ui/ratings"

import useCalculateRent from "@/hooks/use-calculate-rent"
import { useOnboardingStore } from "@/store/onboarding-store"
import { HeartFilledIcon, HeartOutlinedIcon } from "sharepal-icons"
import RenderTag from "../custom/product-tag"
import { CurrencyNoteAddIcon } from "../Icons/product-page-icons"
import Marquee from "../ui/marquee"

export interface SearchProductCardProps {
  className?: string
  data: RentalItem
  isLiked?: boolean
  city: string
  hideDateAndPrice?: boolean
}

export default function SearchProductCard({
  className = "",
  data,
  city,
  hideDateAndPrice = false,
}: SearchProductCardProps) {
  const {
    id,
    ri_name,
    ri_short_name,
    ri_image,
    ri_image_alt_text,
    category_short_name,
    sc_name,
    out_of_stock,
    rating,
    booked_count,
    decoration_text: tag,
  } = data

  const { openCalendar } = useCalendarStore()

  const { total_days } = useRentalStore()
  const { user, setUser } = useUserStore()
  const { rent, extraDayRent } = useCalculateRent({
    product: data,
    type: "product",
  })
  const { openModal } = useOnboardingStore()

  const getUrl = generateLinkPath(ri_name, city, category_short_name, sc_name)

  const toggleFavourite = useCallback(
    async (itemId: number) => {
      if (!user) {
        // toast.info("Login to like product!")
        openModal()

        return
      }

      const updatedData = await handleFavourite(itemId, user)
      if (updatedData) {
        setUser(updatedData)
      }
    },
    [user, setUser, openModal],
  )

  const handleNotifyMe = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault()
      e.nativeEvent.stopImmediatePropagation()
      // handle notify me here
      window.open(
        `https://api.whatsapp.com/send?phone=+917619220543&text=Hi, I am interested in ${ri_short_name}.`,
        "_blank",
      )
    },
    [ri_short_name],
  )

  const finalRating = rating ?? Number((Math.random() * 0.8 + 4.2).toFixed(1))

  const renderPriceInfo = () => (
    <>
      <div className='flex flex-col items-baseline justify-between gap-0'>
        {total_days ? (
          <div className='flex items-center gap-1 text-o3 text-gray-600'>
            Rent for
            {total_days ? (
              <span className='text-xs font-semibold leading-5 text-gray-900'>
                {" "}
                {total_days}
              </span>
            ) : (
              <span className='inline-flex text-gray-900 blur-sm'>00</span>
            )}
            {total_days > 1 ? "days" : "day"}
          </div>
        ) : (
          <div className='flex items-center gap-1 text-o3 text-gray-600'>
            Select Dates
          </div>
        )}
        <div
          className={`flex items-center justify-center text-sh4 text-gray-900`}
        >
          {rent ? (
            <span area-label={`Rent for ${total_days} days`}>
              {moneyFormatter(rent)}{" "}
            </span>
          ) : (
            <p>
              ₹<span className='inline-flex blur-sm'>N/A</span>
            </p>
          )}
        </div>
      </div>
    </>
  )

  const renderExtraDayInfo = () => (
    <div className='mt-1 line-clamp-1 flex items-center gap-1 text-sh7 text-decorative-pink'>
      <CurrencyNoteAddIcon className={"size-6"} />{" "}
      <Marquee
        className='min-w-max'
        containerClassName='p-0 !gap-0'
        repeat={2}
        duration={5}
      >
        {total_days && extraDayRent ? (
          <p className='mr-4'>
            Additional day at {moneyFormatter(extraDayRent)} only
          </p>
        ) : (
          <p>Lowest Price Guarantee</p>
        )}
      </Marquee>
    </div>
  )

  const renderBookedCount = () => (
    <div className='flex items-center justify-center gap-1 overflow-hidden text-sh7 text-success-700'>
      <TrendingUp className='w-4 text-success-500' />{" "}
      <div className='block'>
        <Marquee
          className='min-w-max'
          containerClassName='p-0 !gap-0'
          repeat={2}
          duration={5}
        >
          <span className='mr-4'>
            {booked_count ? booked_count : Math.floor(Math.random() * 11) + 20}{" "}
            booked this month
          </span>
        </Marquee>
      </div>
    </div>
  )

  return (
    <Link
      onClick={(e) => {
        if (!total_days) {
          e.preventDefault()
          e.nativeEvent.stopImmediatePropagation()
          openCalendar()
        }
      }}
      prefetch={true}
      href={getUrl}
    >
      <Card
        className={cn(
          `group relative h-max max-w-[151px] overflow-hidden rounded-2xl border-none bg-transparent p-2.5 leading-5 transition-all duration-300`,
          className,
          out_of_stock ? "hover:bg-gray-200" : "hover:bg-gray-100",
        )}
      >
        <div className='relative overflow-hidden rounded-lg bg-gray-100 p-1.5'>
          <SpImage
            src={getImage(ri_image)}
            alt={ri_image_alt_text}
            className='h-full max-h-[131px] w-full max-w-[131px] scale-90 object-contain'
            width={300}
            height={300}
            containerClassName='p-3'
          />
          <RenderTag out_of_stock={out_of_stock} tag={tag} />
          <button
            type='button'
            onClick={(e) => {
              e.preventDefault()
              e.nativeEvent.stopImmediatePropagation()
              toggleFavourite(id)
            }}
            className={cn(
              "absolute right-1 top-1 h-max w-max justify-end p-0 opacity-0 transition-all duration-500 group-hover:opacity-100",
              user?.favourite_items?.includes(id)
                ? "text-red-500"
                : "text-gray-500",
            )}
          >
            {user?.favourite_items?.includes(id) ? (
              <HeartFilledIcon className='size-4' strokeWidth={2} />
            ) : (
              <HeartOutlinedIcon className='size-4' strokeWidth={2} />
            )}
          </button>
          {out_of_stock && (
            <div className='absolute bottom-0 left-0 right-0 flex h-max w-full flex-col items-center justify-center bg-black bg-opacity-80 p-1.5 text-gray-100'>
              <p className='text-xs font-bold'>Out of Stock</p>
              <p className='text-[10px]'>Will be available soon</p>
            </div>
          )}
        </div>
        <CardContent className='flex flex-col items-start justify-center gap-1 p-0 pt-2.5'>
          <div className='w-full'>
            <h2 className='line-clamp-1 text-sh4'>{ri_short_name}</h2>
            <p className='line-clamp-1 text-sh7 text-gray-800'>{ri_name}</p>
          </div>

          {!hideDateAndPrice && !out_of_stock && renderPriceInfo()}
        </CardContent>
        <CardFooter className='flex w-full flex-col items-start justify-between gap-0 px-0 py-0'>
          {!hideDateAndPrice && (
            <div className='w-full'>
              {out_of_stock ? (
                <p className='flex items-center justify-center gap-1 overflow-hidden text-sh7 text-gray-700'>
                  <InfoIcon className='w-4 text-gray-500' />{" "}
                  <span className='line-clamp-1 w-full'>Out of Stock</span>
                </p>
              ) : (
                renderExtraDayInfo()
              )}
            </div>
          )}

          {renderBookedCount()}
          <div className='flex items-center'>
            <Ratings rating={finalRating} size={11} className='flex' />
            <span className='ml-2 text-xs text-gray-900'> ({finalRating})</span>
          </div>

          {out_of_stock && (
            <Button
              onClick={handleNotifyMe}
              variant={"outline"}
              className='mt-2 h-8 w-full border border-primary-900 bg-gray-100 py-3'
            >
              Contact Support
            </Button>
          )}
        </CardFooter>
      </Card>
    </Link>
  )
}
