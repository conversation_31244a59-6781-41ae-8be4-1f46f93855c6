"use client"

import { motion } from "framer-motion"
import { LucideIcon } from "lucide-react"

interface ValuesCardProps {
  title: string
  description: string
  description1?: string
  icon: LucideIcon
  direction: "left" | "right"
}

export function ValuesCard({
  title,
  description,
  description1,
  icon: Icon,
  direction,
}: ValuesCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: direction === "left" ? -20 : 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: direction === "left" ? 0.2 : 0.3 }}
      className='group relative overflow-hidden rounded-3xl bg-gray-100 p-8 transition-all duration-300 hover:-translate-y-1 md:p-10'
    >
      {/* Decorative Elements */}
      <div className='relative'>
        <div className='mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl bg-primary/5 transition-colors group-hover:bg-primary/10'>
          <Icon className='h-8 w-8 text-primary' />
        </div>

        <h3 className='mb-4 text-3xl font-bold tracking-tight text-gray-900'>
          {title}
        </h3>

        <p className='text-lg leading-relaxed text-gray-600'>{description}</p>
        {description1 && (
          <p className='text-lg leading-relaxed text-gray-600'>
            {description1}
          </p>
        )}
      </div>
    </motion.div>
  )
}
