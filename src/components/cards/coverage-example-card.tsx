"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { ReactNode } from "react"

export interface CoverageExampleCardProps {
  icon: ReactNode
  iconBgColor: string
  iconColor: string
  title: string
  value?: string
  valueColor: string
  subValue?: ReactNode
  imageSrc: string
  imageAlt: string
  className?: string
}

const CoverageExampleCard = ({
  icon,
  iconBgColor,
  iconColor,
  title,
  value,
  valueColor,
  subValue,
  imageSrc,
  imageAlt,
  className,
}: CoverageExampleCardProps) => (
  <Card
    className={cn(
      "overflow-hidden rounded-xl border border-neutral-200 bg-white",
      className,
    )}
  >
    <CardContent className='flex h-full flex-col items-center p-6 text-center'>
      {/* Icon */}
      <div
        className={cn(
          "mb-4 flex h-12 w-12 items-center justify-center rounded-full",
          iconBgColor,
        )}
      >
        <div className={cn("h-6 w-6", iconColor)}>{icon}</div>
      </div>

      {/* Title */}
      <Typography
        as='h3'
        className='mb-4 text-sh4 font-medium text-neutral-800'
      >
        {title}
      </Typography>

      {/* Value */}
      {value && (
        <Typography as='p' className={cn("mb-2 text-h4 font-bold", valueColor)}>
          {value}
        </Typography>
      )}

      {/* Sub Value */}
      {subValue && subValue}

      {/* Image */}
      <div className='relative mt-4 h-32 w-32 overflow-hidden rounded-full bg-neutral-100 p-2'>
        <SpImage
          src={imageSrc}
          alt={imageAlt}
          width={144}
          height={144}
          className='h-full w-full object-contain'
        />
      </div>
    </CardContent>
  </Card>
)

export default CoverageExampleCard
