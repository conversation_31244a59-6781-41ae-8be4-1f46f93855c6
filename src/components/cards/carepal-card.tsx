import SpImage from "@/shared/SpImage/sp-image"
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card"

type Props = {
  title: string
  description: string
  image_url: string
}

const CarePalCard = ({ title, description, image_url }: Props) => (
  <Card className='relative h-[284px] min-w-[248px] max-w-[248px] flex-1 space-y-0 rounded-2xl border border-carepal-lighter bg-carepal-card p-0 bg-blend-multiply md:h-[416px] md:min-w-[360px] md:max-w-full md:rounded-3xl'>
    {/* background text */}
    {/* <div className="absolute w-full h-full inset-0 flex items-center justify-center">
        <h1 className="w-full bg-zero-policy-card-text bg-clip-text text-center text-[45.44px] font-extrabold italic text-[#00000000] md:text-[69.62px]">
          ZERO
        </h1>
      </div> */}

    {/*  <Card className="relative h-[284px] space-y-0 border border-primary-100 bg-zero-policy-card p-0 bg-blend-multiply md:h-[416px]"> */}
    <CardHeader className='p-3 pb-0 md:p-6'>
      <CardTitle className='w-full font-bold text-primary-900 md:!text-h2'>
        {title}
      </CardTitle>
      <CardDescription className='w-full text-xs font-medium leading-4 text-neutral-500 md:text-base md:leading-6'>
        {description}
      </CardDescription>
    </CardHeader>
    <CardContent className='flex flex-col items-end justify-center p-0'>
      {/* <h1 className="w-full bg-zero-policy-card-text bg-clip-text text-center text-[45.44px] font-extrabold italic text-[#00000000] md:text-[69.62px]">
          ZERO
        </h1> */}
      <SpImage
        // containerClassName="md:w-[306px] w-[180px]"
        src={image_url}
        alt='Zero Deposit Image'
        width={800}
        height={800}
        className='h-full w-full'
        // containerClassName="absolute bottom-0 left-1/2 z-[1] -translate-x-1/2 md:w-[306px] w-[200px]"
        containerClassName='absolute bottom-0 left-1/2 z-[1] -translate-x-1/2 md:w-[360px] w-[260px]'
      />
      {/* <Image
          className="absolute bottom-0 left-1/2 z-10 -translate-x-1/2 md:w-[306px]"
          src={image_url}
          alt="Zero Deposit Image"
          width={'180'}
          height={0}
        /> */}
    </CardContent>
  </Card>
)

export default CarePalCard
