"use client"

import { handleNotifyMe } from "@/actions/cart"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { generateLinkPath } from "@/functions/generate-link-path"
import { getImage, moneyFormatter } from "@/functions/small-functions"
import useCalculateRent from "@/hooks/use-calculate-rent"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { useRentalStore } from "@/store/rental-store"
import type { RentalItem } from "@/types"
import { ArrowUpRight, InfoIcon, Loader2, ShoppingCart } from "lucide-react"
import Link from "next/link"
import { useMemo, useState } from "react"

type ComboProductCardProps = {
  product: RentalItem
  city: string
  className?: string
  handleAddToCart?: (
    product: RentalItem,
    rent: number,
    mainSurge: number,
  ) => Promise<void>
}

const ComboProductCard = ({
  product,
  city,
  className,
  handleAddToCart,
}: ComboProductCardProps) => {
  const [isCartAdditionLoading, setIsCartAdditionLoading] = useState(false)
  const { rent, mainSurge } = useCalculateRent({ product, type: "product" })
  const { total_days } = useRentalStore()
  // const { user } = useUserStore()
  // const { openModal } = useOnboardingStore()

  const {
    ri_name,
    category_short_name,
    sc_name,
    out_of_stock,
    ri_image,
    ri_image_alt_text,
    ri_short_name,
  } = product

  const getUrl = useMemo(
    () => generateLinkPath(ri_name, city, category_short_name, sc_name),
    [ri_name, city, category_short_name, sc_name],
  )

  const bundleImages = useMemo(() => {
    if (!ri_image) return []

    return ri_image.split(",").map((url, index) => ({
      image: url.trim(),
      alt: ri_image_alt_text || `Product image ${index + 1}`,
    }))
  }, [ri_image, ri_image_alt_text])

  // const gridLayout = useMemo(() => {
  //   if (bundleImages.length === 0) return "hidden"
  //   if (bundleImages.length === 1) return "grid-cols-1"
  //   if (bundleImages.length === 2) return "grid-cols-2"
  //   return "grid-cols-2 grid-rows-2"
  // }, [bundleImages.length])

  // const displayedImages = useMemo(() => {
  //   if (bundleImages.length <= 3) {
  //     return bundleImages
  //   }
  //   return bundleImages.slice(0, 3)
  // }, [bundleImages])

  // const remainingImagesCount = bundleImages.length - displayedImages.length

  return (
    <Link href={getUrl} prefetch={true} className='w-full'>
      <Card
        className={cn(
          "group h-full w-full min-w-[320px] overflow-hidden rounded-2xl border-none transition-all duration-300 md:min-w-[400px] lg:max-w-xs",
          out_of_stock ? "hover:bg-gray-200" : "hover:bg-primary-100",
          className,
        )}
      >
        <CardContent className='relative flex flex-row p-4'>
          {/* Grid of product images */}
          <div
            className={cn(
              "grid gap-2",
              // gridLayout,
              "aspect-square h-24 w-24 rounded-xl bg-primary-100 p-1 md:h-32 md:w-32 md:p-2",
            )}
          >
            {bundleImages.length === 0 && (
              <div className='flex items-center justify-center rounded-md bg-gray-100'>
                <p className='text-b6 text-gray-400'>No images</p>
              </div>
            )}

            {/* {displayedImages.map((item, index) => ( */}
            <div
              // key={index}
              className='flex aspect-square h-max items-center justify-center rounded-md bg-gray-100 p-1'
            >
              <SpImage
                width={100}
                height={100}
                className={cn(
                  "h-full w-full rounded-sm object-contain transition-all duration-300",
                  // bundleImages.length === 1
                  //   ? "h-20 w-20 md:h-28 md:w-28"
                  //   : "h-10 w-10 md:h-14 md:w-14",
                )}
                src={getImage(ri_image)}
                alt={ri_image_alt_text}
                containerClassName='relative w-full h-full flex items-center justify-center'
              />
            </div>
            {/* ))} */}
            {/*
            {remainingImagesCount > 0 && (
              <div className='flex items-center justify-center rounded-md bg-gray-100 text-sh6 font-bold text-blue-600 md:text-sh4'>
                +{remainingImagesCount}
              </div>
            )} */}
          </div>

          {/* Product information */}
          <div className='ml-4 flex flex-1 flex-col justify-between md:ml-6'>
            <div>
              <h3 className='line-clamp-1 text-sh4 font-bold md:text-sh2'>
                {ri_short_name}
              </h3>
              <p className='mt-1 line-clamp-2 text-b6 text-gray-700 md:text-b4'>
                {ri_name}
              </p>
            </div>

            <div>
              {out_of_stock ? (
                <div className='flex flex-col gap-2'>
                  <p className='flex items-center gap-2 text-b6 text-gray-700 md:text-b4'>
                    <InfoIcon className='h-4 w-4' />
                    <span>Out of Stock</span>
                  </p>
                  <Button
                    onClick={(e) => {
                      handleNotifyMe(e, product.ri_short_name)
                    }}
                    variant='outline'
                    className='mt-2 h-8 w-fit border border-primary-900 bg-gray-100 px-4 py-1 text-b6'
                  >
                    Notify Me
                  </Button>
                </div>
              ) : (
                <>
                  <p className='text-b6 text-gray-600 md:text-b4'>
                    Rent for{" "}
                    {total_days ? (
                      <span className='font-bold text-black'>{total_days}</span>
                    ) : (
                      <span className='blur-sm'>XX</span>
                    )}
                    {total_days > 1 ? " days" : " day"}
                  </p>
                  <p className='mt-1 text-sh4 font-bold text-black md:text-sh2'>
                    {rent ? (
                      <span aria-label={`Rent for ${total_days} days`}>
                        {moneyFormatter(rent)}
                      </span>
                    ) : (
                      <span>
                        ₹<span className='inline-flex blur-sm'>XXX</span>
                      </span>
                    )}
                  </p>
                </>
              )}
            </div>
          </div>

          {/* Add to Cart Button or Arrow icon */}
          {!out_of_stock && handleAddToCart && total_days > 0 ? (
            <div className='absolute bottom-2 right-4'>
              <Button
                onClick={async (e) => {
                  e.preventDefault()
                  e.nativeEvent.stopImmediatePropagation()
                  try {
                    await handleAddToCart(product, rent, mainSurge)
                    setIsCartAdditionLoading(true)
                  } catch (error) {
                    console.error(error)
                  } finally {
                    setIsCartAdditionLoading(false)
                  }
                }}
                disabled={isCartAdditionLoading}
                size='sm'
                className='rounded-full bg-primary-500 p-0 px-3 hover:bg-primary-400'
              >
                {isCartAdditionLoading ? (
                  <Loader2 className='h-4 w-4 animate-spin' />
                ) : (
                  <ShoppingCart className='h-4 w-4' />
                )}
                <span className='text-nowrap'>Add to Cart</span>
              </Button>
            </div>
          ) : (
            <div className='absolute bottom-4 right-4 hidden text-gray-700 transition-colors duration-300 group-hover:flex group-hover:text-blue-600'>
              <ArrowUpRight className='h-4 w-4 md:h-6 md:w-6' />
            </div>
          )}

          {/* Out of stock overlay */}
          {out_of_stock && (
            <div className='absolute inset-0 flex items-center justify-center bg-black/60'>
              <div className='text-center text-white'>
                <p className='text-sh4 font-bold md:text-sh2'>Out of Stock</p>
                <p className='text-b6 md:text-b4'>Will be available soon</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  )
}

export default ComboProductCard
