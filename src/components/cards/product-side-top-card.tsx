import { IconAddToCart, IconHeart, IconRate, IconStockIncrease } from "../Icons"
import { But<PERSON> } from "../ui/button"

const ProductSideTopCard = () => (
  <div className='flex w-full max-w-[384px] flex-col gap-6 overflow-hidden rounded-3xl bg-gray-100 p-4'>
    <div className='flex flex-col gap-2'>
      <div className='flex items-center gap-3'>
        <h1 className='w-full text-2xl font-bold'>XBox One S</h1>
        <div className='flex items-center gap-2'>
          <span className='rounded-[8px] border-[1.6px] border-[#0079BC] px-2 py-1 text-xs font-[600] text-[#0079BC]'>
            New
          </span>
          <IconHeart />
        </div>
      </div>
      <h3 className='font-medium text-gray-800'>
        XBox One S Console + 1 Controller
      </h3>
      <div className='flex gap-1 text-sm font-[600] text-success-700'>
        <IconStockIncrease /> <span>24 booked this month</span>
      </div>
    </div>
    <div className='flex flex-col gap-2 font-[600]'>
      <h3 className='text-sm font-[600] text-gray-800'>Total Rental charge</h3>
      <div className='flex items-center gap-3'>
        <div className='flex w-full items-center gap-2 text-gray-900'>
          <h1 className='text-[32px] font-bold'>₹2,450 </h1>
          <span className='flex w-full items-center gap-2 font-[600]'>
            {" "}
            <span className='text-gray-600'> for</span> 10 days
          </span>
        </div>
        <div className='flex items-center gap-2 rounded-full border-[1.6px] border-primary-900 bg-gray-100 px-3 py-2'>
          <IconRate />
          Rate
        </div>
      </div>
      <div className='flex text-xs font-medium text-gray-600'>
        Price incl. of all taxes
      </div>
    </div>
    <div>
      <Button className='w-full bg-primary-500 py-5 font-[600]'>
        <IconAddToCart /> Add To Cart
      </Button>
    </div>
    <div></div>
    <div></div>
  </div>
)

export default ProductSideTopCard
