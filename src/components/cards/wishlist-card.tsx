"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RentalItem } from "@/types"
import { useQueryClient } from "@tanstack/react-query"
import { Dispatch, SetStateAction, useCallback } from "react"

import { handleFavourite } from "@/actions/user"
import { IconDatePicker } from "@/components/Icons"
import { Ratings } from "@/components/ui/ratings"
import { getImage } from "@/functions/small-functions"
import SpImage from "@/shared/SpImage/sp-image"

import { handleNotifyMe } from "@/actions/cart"
import { generateLinkPath } from "@/functions/generate-link-path"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useCalendarStore, useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { Share2Icon, ShoppingCart, Trash2Icon, TrendingUp } from "lucide-react"

type Props = {
  product: RentalItem

  setShowCart: Dispatch<SetStateAction<boolean>> | (() => void)
  selectedSize: string
  setShortName: Dispatch<SetStateAction<string>>
  handleAddToCart: (product: RentalItem) => void

  setShowSizeColorSelect: Dispatch<SetStateAction<boolean>>
  setSelectedProduct: Dispatch<SetStateAction<RentalItem | null>>
}

const WishListCard = ({
  product,
  selectedSize,
  setShortName,
  handleAddToCart,

  setShowSizeColorSelect,
  setSelectedProduct,
}: Props) => {
  const { openCalendar } = useCalendarStore()
  const { total_days } = useRentalStore()
  const { user, setUser } = useUserStore()
  const { openModal } = useOnboardingStore()
  const queryClient = useQueryClient()
  const { selectedCity } = useRentalStore()

  const {
    id,
    size_specific,
    ri_short_name,
    ri_name,
    category_short_name,
    sc_name,
    booked_count,
    out_of_stock,
    ri_image_alt_text,
    ri_image,
    category_name,
    rating,
  } = product

  const finalRating = rating ?? Number((Math.random() * 0.8 + 4.2).toFixed(1))

  const toggleFavourite = useCallback(
    async (itemId: number) => {
      if (!user) {
        openModal()
        return
      }

      const updatedData = await handleFavourite(itemId, user)
      if (updatedData) {
        setUser(updatedData)
        // queryClient.invalidateQueries({"userWishlist", user?.id})
        queryClient.invalidateQueries({ queryKey: ["userWishlist", user?.id] })
      }
    },
    [user, openModal, setUser, queryClient],
  )

  const handleAddToCartClick = () => {
    if (size_specific && selectedSize === "") {
      setSelectedProduct(product)
      setShortName(ri_short_name)
      setShowSizeColorSelect(true)
      return
    }
    handleAddToCart(product)
  }

  const handleShare = () => {
    const url = generateLinkPath(
      ri_name,
      selectedCity.city_url,
      category_short_name,
      sc_name,
    )

    if (navigator.share) {
      navigator.share({
        title: ri_name,
        text: "Check out this product on Rentify",
        url: String(url),
      })
    }
  }

  return (
    <div className='relative flex flex-col gap-2 overflow-hidden rounded-2xl border border-neutral-200 bg-gray-100 p-2 md:flex-row md:p-4'>
      <div className='products-start flex w-full flex-col gap-4 md:flex-row md:gap-8'>
        <div className='flex w-full flex-col gap-4 md:flex-row md:gap-6'>
          <div className='relative aspect-square h-auto w-auto flex-shrink-0 overflow-hidden rounded-2xl md:h-32 md:w-32'>
            <SpImage
              src={getImage(ri_image)}
              alt={ri_image_alt_text || ri_name}
              fill
              className='aspect-square rounded-lg object-cover'
              containerClassName='w-full h-full'
            />
            {out_of_stock && (
              <div className='products-center absolute bottom-0 flex w-full flex-col justify-center bg-neutral-900/75 p-2 text-center text-xs text-white'>
                <p className='font-semibold'>Out of Stock</p>
                <p className='text-[10px]'>Will be available soon</p>
              </div>
            )}
          </div>
          <div className='flex flex-1 flex-col gap-2'>
            <div>
              <h3 className='line-clamp-1 text-sm font-medium text-neutral-900 md:text-base'>
                {ri_name}
              </h3>
              <p className='line-clamp-1 text-xs text-neutral-500 md:text-sm'>
                {category_name}
              </p>
            </div>
            <div className='space-y-2'>
              <p className='products-center flex justify-center gap-1 overflow-hidden text-xs font-medium text-success-700 md:gap-2 md:text-sm'>
                <TrendingUp className='w-4 text-success-500 md:w-5' />
                <span className='line-clamp-1 w-full'>
                  {booked_count
                    ? booked_count
                    : Math.floor(Math.random() * 11) + 20}{" "}
                  booked this month
                </span>
              </p>
              <div className='products-center flex gap-1'>
                <Ratings size={14} rating={finalRating} />
                <span className='text-sm text-neutral-500'>
                  ({finalRating})
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className='products-end flex w-full flex-col gap-2 md:max-w-[200px]'>
          {total_days > 0 ? (
            out_of_stock ? (
              <Button
                onClick={(e) => {
                  handleNotifyMe(e, ri_short_name)
                }}
                variant={"default"}
                className='h-10'
              >
                Notify Me
              </Button>
            ) : (
              <Button
                disabled={out_of_stock || !product}
                onClick={handleAddToCartClick}
                className='h-10 w-full'
                variant={"primary"}
              >
                <ShoppingCart className='h-6 w-6' />
                Add to Cart
              </Button>
            )
          ) : (
            <Button onClick={openCalendar} className='h-10'>
              <IconDatePicker />
              Select Date
            </Button>
          )}
          <Button
            variant='outline'
            type='button'
            aria-label='Share'
            onClick={handleShare}
            className='products-center flex h-10 w-full justify-center gap-2'
          >
            <Share2Icon className='h-4 w-4' />
            Share
          </Button>
          <Button
            onClick={() => toggleFavourite(id)}
            variant='outline-destructive'
            className='products-center flex h-10 w-full justify-center gap-2'
          >
            <Trash2Icon className='h-4 w-4' />
            Remove
          </Button>
        </div>
      </div>
    </div>
  )
}

export default WishListCard
