import React from "react"
import { Card, CardContent, CardDescription, CardTitle } from "../ui/card"
import SpImage from "@/shared/SpImage/sp-image"

type Props = {
  title: string
  desc: string
  image: string
}

const KeyBenefitCard = ({ title, desc, image }: Props) => (
  <Card
    className={`w-full flex-[1_0_180px] rounded-xl border-none bg-neutral-150 px-3 py-4 pb-6`}
  >
    {/* <div className="relative aspect-square overflow-hidden object-contain flex items-center justify-center px-3"> */}
    <CardContent className='relative h-full overflow-hidden rounded-xl p-0 leading-5 transition-all duration-300'>
      <SpImage
        src={image}
        alt={"icon" + title}
        className='h-full w-full max-w-20 object-contain transition-all group-hover:scale-125'
        width={150}
        height={150}
        containerClassName={
          "relative  overflow-hidden object-contain flex  mb-3 md:mb-4  items-center justify-center "
        }
      />
      <CardTitle className='min-w-max py-1 text-center !text-sh4 text-primary-850'>
        {title}
      </CardTitle>
      <CardDescription className='line-clamp-2 text-center !text-b6 text-primary-850 md:line-clamp-1'>
        {" "}
        {desc}{" "}
      </CardDescription>
    </CardContent>

    {/* </div> */}
  </Card>
)

export default KeyBenefitCard
