import React from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { IconGoogle, IconStar } from "../Icons/design-icons"
import { Review } from "../reviews"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"

type Props = {
  review: Review
}

const ReviewCard = ({ review }: Props) => (
  <Card className='flex min-w-[328px] max-w-[328px] snap-center snap-always flex-col justify-between gap-4 rounded-2xl border border-neutral-200 bg-neutral-100 p-3 md:rounded-3xl lg:min-w-[360px] lg:max-w-[360px] lg:p-4 lg:px-0'>
    <CardHeader className='gap-2 p-0 md:px-4'>
      <CardTitle className='flex gap-2'>
        <IconGoogle className='w-6' />
        <div className='flex gap-1'>
          {Array.from({ length: review.reviewer_rating }, (_, index) => (
            <IconStar key={index} className='w-4' />
          ))}
        </div>
      </CardTitle>

      <CardDescription className='line-clamp-4 !text-sh6 text-primary-900 lg:!text-sh2'>
        “{" "}
        {review.full_review ??
          ` Great experience 👍🏻. Excellent product quality👌🏻 recommended for
          every adventurer out there 😁”`}{" "}
        ”
      </CardDescription>
    </CardHeader>
    <CardContent className='flex gap-5 p-0 md:px-4'>
      {/* <CustomAvatar
          src={'https://randomuser.me/api/portraits'}
          // src={review.reviewer_image}
          fallbackText={
            review.reviewer_first_name.charAt(0) +
            review.reviewer_last_name.charAt(0)
          }
          className="h-10 w-10 !bg-primary-150 text-primary-600 font-semibold text-xs md:text-base"
        /> */}

      <Avatar className='h-10 w-10 !bg-primary-150 text-xs font-semibold text-primary-600 md:text-base'>
        <AvatarImage
          src={"/"}
          alt={review.reviewer_first_name + review.reviewer_last_name}
        />
        <AvatarFallback className='!bg-primary-150 text-xs font-semibold text-primary-600 md:text-base'>
          {review.reviewer_first_name.charAt(0) +
            review.reviewer_last_name.charAt(0)}
        </AvatarFallback>
      </Avatar>

      <div>
        <h4 className='text-xs font-medium text-gray-600 lg:text-sm'>
          {review.reviewer_first_name ?? "Venkatesh"}{" "}
        </h4>
        <p className='text-[10px] text-gray-400 lg:text-sm'>
          {review.reviewer_city} • {review.category_rented}
        </p>
      </div>
    </CardContent>
  </Card>
)

export default ReviewCard
