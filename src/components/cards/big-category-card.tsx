import { Card, CardContent } from "@/components/ui/card"
import { getImage, removeOnRent } from "@/functions/small-functions"
import SpImage from "@/shared/SpImage/sp-image"
import { SubCategory } from "@/types"
import Link from "next/link"
import { Typography } from "../ui/typography"

export interface BigCategoryCardProps {
  className?: string
  data: SubCategory
  city: string
}

const BigCategoryCard = ({ className, data, city }: BigCategoryCardProps) => {
  const { sc_image, sc_name, url, super_category_url } = data
  // console.log(data)
  return (
    <Link
      prefetch={true}
      href={`/${city}/${removeOnRent(super_category_url)}/${url}`}
      className='snap-center snap-always'
    >
      <Card
        className={`group relative max-h-[124px] min-h-[122px] w-full overflow-hidden rounded-xl border border-primary-150 bg-gray-100 pb-[10px] leading-5 transition-all duration-300 hover:border-gray-100 hover:bg-primary-100 md:min-h-[220px] md:min-w-[208px] md:max-w-52 md:rounded-2xl md:pb-4 ${className} flex flex-col items-center justify-between gap-1 md:gap-0`}
      >
        {/* <div className="relative aspect-square overflow-hidden object-contain flex items-center justify-center px-3"> */}

        <SpImage
          src={getImage(sc_image)}
          alt={sc_name}
          className='aspect-square w-20 object-contain group-hover:scale-110 md:w-[120px]'
          width={128}
          height={128}
          containerClassName={
            "relative aspect-square  p-2 md:p-4 overflow-hidden object-contain  w-max mx-auto px-1 md:px-3 flex items-start justify-start"
          }
        />
        {/* </div> */}
        <CardContent className='flex w-full flex-col items-start justify-center gap-2.5 p-0 px-2 md:px-6'>
          <Typography
            as={"h4"}
            className='mx-auto !line-clamp-2 flex w-full items-start justify-center px-1 text-center text-sh7 sm:text-sh5 md:max-w-[90%] md:px-6 md:text-sh3 lg:text-h7'
          >
            {sc_name}
          </Typography>
        </CardContent>
      </Card>
    </Link>
  )
}

export default BigCategoryCard
