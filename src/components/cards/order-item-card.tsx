import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import SpImage from "@/shared/SpImage/sp-image"
import { motion } from "framer-motion"

interface OrderItemCardProps {
  item: {
    cart_image: string
    item_name: string
    cat_sname: string
    size?: string
    rent: number
    per_day_rent: number
    quantity: number
  }
  show_perday_rent?: boolean
  index: number
}

export function OrderItemCard({
  item,
  index,
  show_perday_rent = true,
}: OrderItemCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className='flex items-start gap-2 rounded-lg p-2 hover:bg-neutral-50 md:items-center md:gap-6 md:p-4'
    >
      <SpImage
        src={item.cart_image}
        alt={item.item_name}
        width={68}
        height={68}
        className='rounded-lg object-cover md:h-20 md:w-20'
        containerClassName='w-[68px] h-[68px] md:w-20 md:h-20 bg-neutral-100 rounded-lg flex-shrink-0'
      />

      <div className='flex flex-1 flex-col gap-1'>
        <Typography
          as={"h3"}
          className='text-sh6 font-bold text-neutral-900 md:text-lg md:text-sh2'
        >
          {item.item_name}
        </Typography>
        <Typography
          as={"p"}
          className='text-o4 text-neutral-600 md:text-b6 md:text-sm'
        >
          {item.cat_sname}
        </Typography>
        {item.size && (
          <Typography
            as={"p"}
            className='text-o4 text-neutral-500 md:text-b6 md:text-xs'
          >
            Size: {item.size}
          </Typography>
        )}
        <div className='mt-2 flex items-baseline gap-2 md:hidden'>
          <Typography
            as={"span"}
            className='text-sh6 font-bold text-neutral-900 md:text-sh2'
          >
            {moneyFormatter(item.rent)}
          </Typography>
          {/* <Typography
            as={"span"}
            className='text-o4 text-neutral-600 md:text-b6'
          >
            {moneyFormatter(item.per_day_rent)}/day
          </Typography> */}
        </div>
      </div>

      <div className='hidden md:flex md:items-center md:gap-12'>
        <div className='flex items-center gap-2'>
          <Typography
            as={"span"}
            className='text-sh6 font-bold text-neutral-900 md:text-sh2'
          >
            x {item.quantity}
          </Typography>
          <span className='text-o4 text-neutral-600 md:text-b6'>item(s)</span>
        </div>

        <div className='text-right'>
          <Typography
            as={"span"}
            className='text-sh6 font-bold text-neutral-900 md:text-sh2'
          >
            {moneyFormatter(item.rent)}
          </Typography>
          <br />
          {show_perday_rent && (
            <Typography
              as={"span"}
              className='text-o4 text-neutral-600 md:text-b6'
            >
              {moneyFormatter(item.per_day_rent)}/day
            </Typography>
          )}
        </div>
      </div>

      <div className='flex flex-col items-center gap-2 md:hidden'>
        <Typography as={"span"} className='text-sh6 font-bold md:text-sh2'>
          x {item.quantity}
        </Typography>
        <Typography as={"span"} className='text-o4 text-neutral-600 md:text-b6'>
          item(s)
        </Typography>
      </div>
    </motion.div>
  )
}
