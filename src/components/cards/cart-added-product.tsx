// Optimized and Refactored CartItems Component
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import React from "react"
import { Button } from "../ui/button"

import { moneyFormatter } from "@/functions/small-functions"
import useCalculateRent from "@/hooks/use-calculate-rent"
import { CartItem } from "@/types"
import { Loader2 } from "lucide-react"
import {
  DeleteOutlinedIcon,
  MinusOutlinedIcon,
  PlusAddOutlinedIcon,
} from "sharepal-icons"
import { Typography } from "../ui/typography"

const MAX_QUANTITY = 50

// Types
interface UpdateCartItemQuantityArgs {
  type: "add" | "remove"
  cartItem: CartItem
}

interface CartAddedProductProps {
  cartItem: CartItem
  sameDaySurge: number
  onUpdateQuantity: (args: UpdateCartItemQuantityArgs) => void
  onDeleteItem: (cartItem: CartItem) => void
  cartItemsDeleteLoading: boolean
  changingCartItem: CartItem | null
  cartItemsUpdateLoading: boolean
  showControls?: boolean
}

// Reusable Component for Individual Cart Item
export const CartAddedProduct: React.FC<CartAddedProductProps> = ({
  cartItem,
  onUpdateQuantity,
  onDeleteItem,
  changingCartItem,
  cartItemsDeleteLoading,
  cartItemsUpdateLoading,
  showControls = true,
}) => {
  const { rent } = useCalculateRent({ type: "cart", cart: cartItem })

  return (
    <div className='flex w-full gap-5 overflow-hidden rounded-xl bg-gray-100 p-2'>
      <SpImage
        width={96}
        height={96}
        className='object-contain'
        src={cartItem.cart_image}
        alt='Product Item'
      />
      <div className='flex w-full flex-col justify-center gap-2'>
        {/* Product Details */}
        <div className='flex w-full items-start justify-between'>
          <div className='space-y-1'>
            <Typography
              as={"h2"}
              className='line-clamp-1 max-w-80 !text-sh4 !text-neutral-900 md:!text-sh2'
            >
              {cartItem.item_name}
            </Typography>
            {/* <div className='flex items-center justify-start gap-1 md:gap-2'> */}
            {cartItem.size && (
              <Typography
                as={"p"}
                className='!text-o4 text-gray-400 md:!text-b6'
              >
                Size: <span className='text-gray-600'> {cartItem.size} </span>
              </Typography>
            )}
            {/* {cartItem.size && <span className='text-gray-600'>&#8226;</span>} */}
            <Typography as={"p"} className='!text-o4 text-gray-400 md:!text-b6'>
              {cartItem.cat_sname}
            </Typography>
            {/* </div> */}
          </div>
          {/* Rent Summary */}
          <div className='hidden pr-1 text-right md:block md:pr-3'>
            <Typography as={"p"} className='!text-sh2 text-neutral-900'>
              {moneyFormatter(rent * cartItem.quantity)}
            </Typography>
            {cartItem.deposit > 0 && (
              <Typography as={"p"} className='!text-sh6 text-neutral-900'>
                Deposit: {moneyFormatter(cartItem.deposit)}
              </Typography>
            )}
            <Typography as='p' className='!text-b6 text-gray-400'>
              Rent for {cartItem.num_days}{" "}
              {cartItem.num_days > 1 ? " days" : " day"}
            </Typography>

            {/* <Typography as='p' className="!text-b6  text-gray-400">
              {moneyFormatter(rent / cartItem.num_days)}/day
            </Typography> */}
          </div>
        </div>

        {/* Quantity Controls */}
        {showControls && (
          <div className='flex items-center justify-between gap-2'>
            <div className='space-y-1 md:hidden'>
              <Typography as={"div"} className='!text-sh4 text-neutral-900'>
                {moneyFormatter(rent * cartItem.quantity)}
              </Typography>
            </div>

            <div className='flex items-center gap-2 md:hidden'>
              <Button
                onClick={() => onDeleteItem(cartItem)}
                variant='normal'
                className='p-2'
              >
                {changingCartItem?.id == cartItem.id &&
                cartItemsDeleteLoading ? (
                  <Loader2 className='animate-spin' />
                ) : (
                  <DeleteOutlinedIcon className='h-5 w-5 text-gray-300' />
                )}
              </Button>
              <div className='flex h-auto min-h-9 w-[97px] items-center justify-between rounded-full border-2 border-neutral-900 !bg-neutral-150 px-3 py-2 text-primary-500'>
                <button
                  onClick={() => onUpdateQuantity({ type: "remove", cartItem })}
                  disabled={
                    cartItemsUpdateLoading ||
                    (cartItem.quantity <= 1 && cartItemsDeleteLoading)
                  }
                  className={cn(
                    "transition-opacity",
                    (cartItemsUpdateLoading || cartItem.quantity <= 1) &&
                      "opacity-50",
                  )}
                >
                  <MinusOutlinedIcon className='h-4 w-4 text-primary-500' />
                </button>
                <Typography
                  as={"div"}
                  className='px-1 text-sh5 text-neutral-900 md:!text-sh7'
                >
                  {changingCartItem?.id == cartItem.id &&
                  cartItemsUpdateLoading ? (
                    <Loader2 className='animate-spin' />
                  ) : (
                    cartItem.quantity
                  )}
                </Typography>
                <button
                  onClick={() => onUpdateQuantity({ type: "add", cartItem })}
                  disabled={
                    cartItemsUpdateLoading || cartItem.quantity >= MAX_QUANTITY
                  }
                  className={cn(
                    "transition-opacity",
                    (cartItemsUpdateLoading ||
                      cartItem.quantity >= MAX_QUANTITY) &&
                      "opacity-50",
                  )}
                >
                  <PlusAddOutlinedIcon className='h-4 w-4 text-primary-500' />
                </button>
              </div>
            </div>

            <div className='flex items-center gap-2 max-md:hidden'>
              <div className='flex h-auto min-h-7 w-[70px] items-center justify-center rounded-full border-2 border-neutral-900 !bg-neutral-150 px-2 py-[3px] text-primary-500'>
                <button
                  onClick={() => onUpdateQuantity({ type: "remove", cartItem })}
                  disabled={cartItemsUpdateLoading}
                >
                  <MinusOutlinedIcon className='h-4 w-4 text-primary-500' />
                </button>
                <Typography
                  as={"div"}
                  className='px-1 text-sh5 text-neutral-900 md:!text-sh7'
                >
                  {changingCartItem?.id == cartItem.id &&
                  cartItemsUpdateLoading ? (
                    <Loader2 className='h-3 w-3 animate-spin' />
                  ) : (
                    cartItem.quantity
                  )}
                </Typography>
                <button
                  onClick={() => onUpdateQuantity({ type: "add", cartItem })}
                  disabled={cartItemsUpdateLoading}
                >
                  <PlusAddOutlinedIcon className='h-4 w-4 text-primary-500' />
                </button>
              </div>
              {/* <Button variant="normal">
              <IconHeart />
            </Button> */}
              <Button
                disabled={cartItemsDeleteLoading}
                onClick={() => onDeleteItem(cartItem)}
                variant='normal'
              >
                {changingCartItem?.id == cartItem.id &&
                cartItemsDeleteLoading &&
                cartItem.id ? (
                  <Loader2 className='animate-spin' />
                ) : (
                  <DeleteOutlinedIcon className='h-5 w-5 text-gray-300' />
                )}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
