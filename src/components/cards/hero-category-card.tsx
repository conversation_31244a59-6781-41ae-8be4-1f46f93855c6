import {
  <PERSON>,
  CardContent,
  CardDes<PERSON>,
  Card<PERSON>ooter,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>it<PERSON>,
} from "@/components/ui/card"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { SuperCategory } from "@/types/super-category"
import { getSuperCategoryBgColor } from "@/utils/get-bg-color"
import { ChevronRightIcon } from "lucide-react"
import Link from "next/link"
import { Typography } from "../ui/typography"

interface HeroCategoryCardProps extends SuperCategory {
  city: string
}

function HeroCategoryCard({
  super_category_desc,
  super_category_image,
  super_category_short_name,
  url,
  city,
}: HeroCategoryCardProps) {
  const bgColor = getSuperCategoryBgColor(super_category_short_name)

  return (
    <Link prefetch={true} href={`/${city}/${url}`}>
      <Card className='group/card relative z-0 flex aspect-square h-[10rem] w-full cursor-pointer flex-col items-start overflow-hidden rounded-2xl border border-neutral-200 bg-gray-100 transition-all max-lg:h-full lg:h-full lg:rounded-3xl'>
        <CardHeader className='space-y-1 px-3 py-2 lg:space-y-2 lg:pb-3 lg:pl-5 lg:pt-4'>
          <CardTitle className='z-0 flex items-center justify-between font-bold tracking-normal text-gray-900 group-hover/card:text-gray-100'>
            <Typography as={"p"} className='text-sh6 md:text-h4'>
              {super_category_short_name}
            </Typography>
            <ChevronRightIcon className='h-3 w-3 opacity-0 group-hover/card:opacity-100 md:h-5 md:w-5' />
          </CardTitle>
          <CardDescription className='font-medium group-hover/card:text-gray-100'>
            <Typography
              as={"p"}
              className='max-w-[90%] text-o3 sm:text-o2 md:max-w-[85%] lg:text-sh5'
            >
              {super_category_desc}
            </Typography>
          </CardDescription>
        </CardHeader>
        {/* change images styles according to image width */}
        {/* <CardContent className="absolute bottom-0 right-0 -z-0 w-[70%] p-0"> */}
        <CardContent className='absolute bottom-0 right-0 -z-0 w-full p-0'>
          <SpImage
            src={super_category_image}
            width={350}
            height={350}
            // containerClassName="relative bottom-0 max-lg:w-[80px]"
            containerClassName='relative bottom-0 w-full'
          />
        </CardContent>
        {/* <CardFooter  className="absolute w-full min-h-[20%] bottom-0 group-hover/card:h-full transition-all  duration-700 -z-[1] bg-category-orange rounded-b-[28] group-hover/card:rounded-[28]"></CardFooter> */}

        {/* added below span tag to load these colors */}
        <span className='hidden bg-category-green bg-category-orange bg-category-purple bg-category-red'></span>
        <CardFooter
          className={cn(
            `absolute inset-x-0 bottom-0 -z-[1] h-[17%] transition-all duration-100 ease-linear group-hover/card:h-full`,
            bgColor,
          )}
        ></CardFooter>
      </Card>
    </Link>
  )
}

export default HeroCategoryCard
