import { HARD_CODED_IMAGE_URL } from "@/constants"
import { cn } from "@/lib/utils"
import { Coupon } from "@/types/coupon"
import Image from "next/image"
import { toast } from "sonner"
import { Card, CardContent } from "../ui/card"
import { Typography } from "../ui/typography"

import { CautionCircleFilledIcon } from "sharepal-icons"
import { Checkbox } from "../ui/checkbox"

interface CouponCardProps {
  coupon: Coupon
  className?: string
  onApply?: () => void
  isValid?: boolean
  isSelected?: boolean
  showCheckbox?: boolean
}

export function CouponCard({
  coupon,
  className,
  onApply,
  isValid = true,
  isSelected,
  showCheckbox,
}: CouponCardProps) {
  return (
    <Card
      className={cn(
        "relative flex w-full cursor-pointer overflow-hidden rounded-xl border-0 bg-gray-50 transition-all hover:bg-neutral-50 md:rounded-xl",
        isValid ? "" : "opacity-60",
        className,
      )}
      onClick={() => {
        if (!isValid) {
          toast.info("Coupon is not applicable")
          return
        }
        if (onApply) onApply()
      }}
    >
      <div className='relative flex items-center'>
        <div className='flex h-full w-9 items-center justify-center bg-transparent mix-blend-normal'>
          <Image
            src={`${HARD_CODED_IMAGE_URL}/Offer+Card+(1).webp`}
            alt='coupon'
            fill
          />

          <Typography
            as={"span"}
            className='mr-2 rotate-[270deg] whitespace-nowrap text-sh6 font-bold text-secondary-500'
          >
            {coupon.cart_discount * 100 + "% Off"}
          </Typography>
        </div>
      </div>
      <CardContent className='flex-1 p-2'>
        <Typography as={"h6"} className='mb-1 text-sh6 md:text-sh4'>
          {coupon.coupon_code}
        </Typography>
        <Typography
          as={"p"}
          className='line-clamp-3 text-ellipsis text-o4 text-gray-600 md:text-b6'
        >
          {coupon.coupon_description}
        </Typography>
      </CardContent>
      {!isValid && (
        <div className='absolute flex h-full w-full cursor-not-allowed items-center justify-center bg-primary-900/50 opacity-0 transition-all hover:opacity-100'>
          <CautionCircleFilledIcon className='h-10 w-10 text-gray-50' />
        </div>
      )}
      {showCheckbox && isValid && (
        <Checkbox checked={isSelected} className='absolute right-2 top-2' />
      )}
    </Card>
  )
}
