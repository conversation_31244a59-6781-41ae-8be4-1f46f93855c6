"use client"

import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { ReactNode } from "react"

export interface CoverageCalculationCardProps {
  text: ReactNode
  imageSrc: string
  imageAlt: string
  className?: string
}

const CoverageCalculationCard = ({
  text,
  imageSrc,
  imageAlt,
  className,
}: CoverageCalculationCardProps) => (
  <Card
    className={cn(
      "flex max-h-[370px] flex-row justify-between overflow-hidden rounded-[28px] border border-primary-100 bg-gradient-to-br from-[rgba(248,248,248,0.00)] via-[rgba(248,248,248,0.00)] to-[#EFF3FF] bg-blend-multiply even:flex-row-reverse md:flex-col md:even:flex-col",
      className,
    )}
  >
    <CardHeader className='p-4 text-center md:!p-6 md:pb-0'>{text}</CardHeader>
    <CardContent className='p-0'>
      {/* Image */}
      <SpImage
        src={imageSrc}
        alt={imageAlt}
        width={500}
        height={500}
        className='h-full w-full object-cover'
        containerClassName='relative max-w-[160px] md:max-w-[300px] mt-2 h-max w-full overflow-hidden rounded-lg p-2'
      />
    </CardContent>
  </Card>
)

export default CoverageCalculationCard
