"use client"

import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { addOnRent, getImage, removeOnRent } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import type { SubCategory } from "@/types"
import Link from "next/link"
import { useParams } from "next/navigation"
import { Dispatch, SetStateAction, useEffect } from "react"
import { Separator } from "../ui/separator"
// import useMediaQuery from '@/hooks/use-media-query'

export interface SubCategoryCardProps {
  className?: string
  data?: SubCategory // Optional for "All" variant
  scrollProgress?: number
  isAll?: boolean // Flag to indicate "All" variant
  setActiveCardID?: Dispatch<SetStateAction<number>>
}

const SubCategoryCard = ({
  className,
  data,
  // scrollProgress,
  isAll = false,
  setActiveCardID,
}: SubCategoryCardProps) => {
  const { city, cat, subcat } = useParams<{
    city: string
    cat: string
    subcat: string
  }>()
  // const isMobile = useMediaQuery('(max-width: 768px)')

  // Determine card data based on isAll
  const imageSrc = isAll
    ? "https://images.sharepal.in/misc/hard-coded/sharepal/Product=All%20Products.webp"
    : getImage(data?.sc_image || "")
  const imageAlt = isAll ? "all" : data?.sc_image_alt_text || ""
  const label = isAll ? "All" : data?.sc_name || ""
  const url = isAll ? cat : data?.url || ""
  const superCategoryUrl = isAll ? cat : data?.super_category_url || ""

  // Construct href
  const href = isAll
    ? `/${city}/${subcat ? addOnRent(superCategoryUrl) : superCategoryUrl}`
    : `/${city}/${removeOnRent(superCategoryUrl)}/${url}`

  // Determine active state
  const isActive = isAll ? !subcat : subcat === url

  useEffect(() => {
    if (isActive && !isAll && data && setActiveCardID) {
      setActiveCardID(data?.id || 0)
    }
  }, [data, isActive, isAll, setActiveCardID])

  return (
    <Link href={href} prefetch={true}>
      <Card className={cn("group border-none", className)}>
        <CardContent
          className={cn(
            "group relative mx-auto aspect-square w-20 min-w-10 overflow-hidden rounded-lg border bg-gray-100 p-1 transition-all duration-500 ease-in-out hover:bg-gray-150 md:w-28 md:rounded-[9.455px]",
            isActive && "border-2 border-primary-500",
            // scrollProgress > 0 && 'origin-bottom',
          )}
          // style={{
          //   transform: `scale(${Math.max(1 - scrollProgress, 0.2)})`,
          //   opacity: Math.max(1 - scrollProgress * 1.5, 0),
          //   height: `${Math.max(
          //     (isMobile ? 80 : 112) - scrollProgress * (isMobile ? 80 : 112),
          //     0,
          //   )}px`,
          // }}
        >
          <SpImage
            src={imageSrc}
            alt={imageAlt}
            className={cn(
              "h-full w-full scale-90 object-contain transition-transform duration-300 group-hover:scale-100",
              isActive && "scale-100 group-hover:scale-100",
            )}
            width={300}
            height={300}
            containerClassName={
              "relative aspect-square overflow-hidden object-contain flex items-center justify-center md:p-2"
            }
          />
        </CardContent>

        <CardFooter className='p-0'>
          <h3
            className={cn(
              "mb-2 line-clamp-2 w-full p-2 pb-0 text-center !text-sh7 transition-all duration-300 md:!text-sh5",
              isActive
                ? "font-bold text-primary-500 group-hover:text-primary-600"
                : "text-neutral-900 group-hover:text-neutral-900",
            )}
          >
            {label}
            {isActive && (
              <Separator className='mx-auto mt-3 block h-[3px] w-10 rounded-full bg-primary-500' />
            )}
          </h3>
        </CardFooter>
      </Card>
    </Link>
  )
}

export default SubCategoryCard
