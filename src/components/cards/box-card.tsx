import SpImage from "@/shared/SpImage/sp-image"
import { Card, CardContent, CardFooter } from "../ui/card"
// import { getImage } from '@/functions/smallFunctions'
import { cn } from "@/lib/utils"
import { Typography } from "../ui/typography"

export interface InclusionCardProps {
  className?: string

  image: string
  name: string
}

const InclusionCard = ({ image, name, className }: InclusionCardProps) => (
  <Card
    className={cn(
      // `group flex-[1_0_80px] border-none md:flex-[1_0_110px]`,
      `group flex flex-col items-center gap-2 border-none`,
      className,
    )}
  >
    <CardContent className='group relative aspect-square h-full min-w-16 overflow-hidden rounded-xl bg-gray-100 p-0 leading-5 transition-all duration-300 md:p-2'>
      <SpImage
        // src={getImage(category_image)}
        // alt={category_name}
        // src={'https://images.sharepal.in/Static+Images/1731658278637-PS5.png'}
        src={image}
        alt={name}
        className='g h-full w-full object-contain transition-all'
        width={300}
        height={300}
        containerClassName={
          "relative aspect-square  md:w-24 md:h-24 p-1 overflow-hidden object-contain flex items-center justify-center"
        }
      />
    </CardContent>

    <CardFooter className='p-0'>
      <Typography
        as={"h6"}
        className='line-clamp-1 w-full px-2 py-0 text-center text-sh7 text-neutral-900'
      >
        {" "}
        {name}
      </Typography>
    </CardFooter>

    {/* </div> */}
  </Card>
)

export default InclusionCard
