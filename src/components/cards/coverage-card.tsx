"use client"

import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { CheckIcon, XIcon } from "lucide-react"

export type CoverageItem = {
  title: string
  image: string
  alt?: string
  type: "covered" | "not-covered"
}

export const CoverageCard = ({ item }: { item: CoverageItem }) => {
  const isCovered = item.type === "covered"

  return (
    <div className='group flex flex-col items-center'>
      <div className='mb-4 aspect-square w-full max-w-[248px] overflow-hidden rounded-lg border border-neutral-200 bg-gray-100 p-2 sm:p-3 md:p-4'>
        <SpImage
          src={item.image}
          alt={item.alt || item.title}
          width={300}
          height={300}
          className='h-full w-full object-contain'
        />
      </div>
      <div className='flex items-start justify-center gap-1 text-center md:max-w-[80%]'>
        <div
          className={cn(
            "flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full border sm:h-6 sm:w-6",
            isCovered
              ? "border-success-500 bg-success-100"
              : "border-destructive-500 bg-destructive-100",
          )}
        >
          {isCovered ? (
            <CheckIcon className='h-3 w-3 text-success-600 sm:h-4 sm:w-4' />
          ) : (
            <XIcon className='h-3 w-3 text-destructive-500 sm:h-4 sm:w-4' />
          )}
        </div>
        <Typography
          as='span'
          className='text-b5 font-medium text-neutral-800 sm:text-b4'
        >
          {item.title}
        </Typography>
      </div>
    </div>
  )
}
