"use client"

import { SubCategory } from "@/types"
import { useRef } from "react"
import type { Swiper as SwiperType } from "swiper"
import { FreeMode, Mousewheel, Scrollbar } from "swiper/modules"
import { Swiper, SwiperSlide } from "swiper/react"
import BigCategoryCard from "./big-category-card"

const BigCategoryCardSwipper = ({
  subCategories,
  city,
  adminOnly,
}: {
  subCategories: SubCategory[]
  city: string
  adminOnly: boolean
}) => {
  const swiperRef = useRef<SwiperType | null>(null)

  return (
    <div className='relative hidden w-full px-0 md:block'>
      <Swiper
        key={subCategories.length}
        observer={true}
        observeParents={true}
        watchSlidesProgress={true}
        modules={[FreeMode, Mousewheel, Scrollbar]}
        freeMode={{
          enabled: true,
          sticky: false,
        }}
        mousewheel={{ forceToAxis: true }}
        slidesPerView='auto'
        // spaceBetween={20}
        scrollbar={{ el: ".custom-swiper-scrollbar", draggable: true }}
        onBeforeInit={(swiper) => {
          swiperRef.current = swiper
        }}
        onInit={(swiper) => {
          swiper.update()
        }}
        onResize={(swiper) => {
          swiper.update()
        }}
        className='mySwiper !py-5 !pl-[max(calc((100vw-1200px)/2),0.5rem)] !pr-2'
        direction='horizontal'
      >
        {subCategories
          .filter((data) => !data.admin_only || adminOnly == data.admin_only)
          ?.map((item) => (
            <SwiperSlide key={item.id} className='mr-4 w-auto max-w-56 md:mr-5'>
              <BigCategoryCard data={item} city={city} />
            </SwiperSlide>
          ))}
      </Swiper>

      {/* Custom Scrollbar Container */}
      <div className='relative mx-auto mt-3 w-full max-w-[1200px] px-4'>
        <div className='custom-swiper-scrollbar relative h-[3px] w-full overflow-hidden rounded-full bg-primary-200'>
          <div className='swiper-scrollbar-drag h-full rounded-full bg-primary-500'></div>
        </div>
      </div>

      {/* Custom Styling */}
      <style jsx>{`
        .custom-swiper-scrollbar {
          position: relative;
          width: 100%;
          max-width: 1200px;
          height: 3px;
          background: #bac7f8;
          border-radius: 6px;
        }
        .swiper-scrollbar-drag {
          background: #1437ba;
          height: 100%;
          border-radius: 6px;
          transition: background 0.3s ease-in-out;
        }
        .swiper-scrollbar-drag:hover {
          background: #bac7f8;
        }
      `}</style>
    </div>
  )
}

export default BigCategoryCardSwipper
