import SectionTitle from "./section-title"

import { customFetch } from "@/utils/customFetch"
import ReviewCard from "./cards/review-card"
// import InfinityScroll from './wrappers/infinity-scroll'
import { fetchGoogleReviews } from "@/actions/product"
import Marquee from "./ui/marquee"

// // Return a list of `params` to populate the [slug] dynamic segment
// export async function generateStaticParams() {
//   const posts = await fetch('https://.../posts').then((res) => res.json())

//   return posts.map((post) => ({
//     slug: post.slug,
//   }))
// }

// itemsReceived: 10,
//   curPage: 1,
//   nextPage: 2,
//   prevPage: null,
//   offset: 0,
//   perPage: 10,
//   items:[]

export interface Review {
  id: number
  campaign: string
  category_rented: string
  customer_occupation: string
  full_review: string
  period: string
  review_platform: string
  review_summary: string
  reviewer_city: string
  reviewer_first_name: string
  reviewer_last_name: string
  reviewer_rating: number
  reviewer_image: string
}

const fetchReviews = async () => {
  try {
    const data = customFetch("/get_reviews")
    return data
  } catch (error) {
    console.error("Error fetching reviews", error)
  }
}

const Reviews = async ({ reviews }: { reviews?: Review[] }) => {
  const reivewsData = await fetchReviews()
  // console.log(reivewsData)
  return (
    <section className='flex flex-col gap-5 bg-gray-100 py-4 lg:gap-12 lg:py-12'>
      <SectionTitle
        cText='1 Lakh+ happy customers'
        cTColor='text-decorative-orange'
        nText='Trusted by '
        className='px-4 text-center'
      />
      {/* <div className="flex gap-5 overflow-x-scroll py-3"> */}

      <div className='flex snap-x snap-mandatory gap-5 overflow-hidden py-3'>
        <Marquee key={"1"} pauseOnHover className='gap-4 px-4 [--duration:60s]'>
          {!reviews || reviews.length == 0
            ? reivewsData?.items?.map((review: Review, index: number) => (
                <ReviewCard
                  key={review.reviewer_first_name + index}
                  review={review}
                />
              ))
            : reviews?.map((review: Review, index) => (
                <ReviewCard
                  key={review.reviewer_first_name + index}
                  review={review}
                />
              ))}
        </Marquee>
      </div>
      <div className='container mx-auto grid w-full grid-cols-3 justify-between gap-3 border-y-2 border-neutral-150 px-0 py-4 md:gap-6 md:py-6'>
        <ReviewNumberText title={"250Cr+"} desc={"Saved Together"} />

        <ReviewNumberText title={"4.5M Kg"} desc={"CO₂e Emissions Saved"} />

        <ReviewNumberText title={"100K+"} desc={"Products in Circulation"} />
      </div>
    </section>
  )
}

export const ProductReviews = async ({ category }: { category: string }) => {
  const reviews = await fetchGoogleReviews(category)
  // console.log(reivewsData)
  return (
    <section className='flex flex-col gap-5 bg-gray-100 py-4 lg:gap-12 lg:py-12'>
      <SectionTitle
        cText='1 Lakh+ happy customers'
        cTColor='text-decorative-orange'
        nText='Trusted by '
        className='px-4 text-center'
      />
      {/* <div className="flex gap-5 overflow-x-scroll py-3"> */}

      <div className='flex snap-x snap-mandatory gap-5 overflow-hidden py-3'>
        <Marquee key={"1"} pauseOnHover className='px-4 [--duration:60s]'>
          {reviews?.map((review: Review, index: number) => (
            <ReviewCard
              key={review.reviewer_first_name + index}
              review={review}
            />
          ))}
        </Marquee>
      </div>
      <div className='container mx-auto grid w-full grid-cols-3 justify-between gap-3 border-y-2 border-neutral-150 px-0 py-4 md:gap-6 md:py-6'>
        <ReviewNumberText title={"250Cr+"} desc={"Saved Together"} />
        <ReviewNumberText title={"4.5M Kg"} desc={"CO₂e Emissions Saved"} />

        <ReviewNumberText title={"100K+"} desc={"Products in Circulation"} />
      </div>
    </section>
  )
}

type ReviewNumberTextProps = {
  title: string
  desc: string
}

const ReviewNumberText = ({ title, desc }: ReviewNumberTextProps) => (
  <div className='flex flex-col gap-2'>
    <h3 className='bg-review-gradient bg-clip-text py-0 text-center font-ubuntu text-2xl font-bold text-transparent md:py-3 lg:text-6xl'>
      {title}
    </h3>
    <p className='font-interr text-center text-xs capitalize text-gray-800 sm:text-xl'>
      {desc}
    </p>
  </div>
)

export default Reviews
