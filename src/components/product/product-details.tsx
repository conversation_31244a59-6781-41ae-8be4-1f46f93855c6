"use client"
import { But<PERSON> } from "@/components/ui/button"
import { FileTextIcon } from "lucide-react"
import { useState } from "react"

import { capitalizeFirstLetter, splitString } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import { useRentalStore } from "@/store/rental-store"

import { AnimatePresence, motion } from "framer-motion"
import { useParams } from "next/navigation"
import SideView from "../modals/modal-wrapper"
import ReturnPolicy from "../modals/return-policy"
import { Typography } from "../ui/typography"

import damagePolicies from "@/constants/damage-policy.json"
import replacementPolicies from "@/constants/replacement-policy.json"

type ProductDetailsProps = {
  desc: string
  features?: string
}

export function ProductDetails({ desc, features }: ProductDetailsProps) {
  const { selectedCity } = useRentalStore()
  const { city } = useParams()
  const [showMore, setShowMore] = useState(false)
  const featureArray =
    features && features !== "" ? splitString(features, ";") : []
  const [openRefundPolicy, setOpenRefundPolicy] = useState(false)
  const [openDamagePolicy, setOpenDamagePolicy] = useState(false)
  return (
    <div className='w-full space-y-4 rounded-2xl bg-gray-100 p-4 md:space-y-6 md:rounded-3xl xl:max-w-[394px]'>
      {/* Product Description */}
      {desc && (
        <div className='space-y-2 md:p-2'>
          <Typography
            as={"h4"}
            className='text-sh2 text-neutral-900 md:text-h6'
          >
            Product Description
          </Typography>
          <div>
            <AnimatePresence initial={false}>
              <motion.p
                key={showMore ? "full" : "truncated"}
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className={cn(
                  "overflow-hidden !text-b6 text-neutral-500 md:!text-b2",
                  !showMore && "line-clamp-4",
                )}
              >
                {desc.replace(
                  /<City Name>/g,
                  capitalizeFirstLetter(
                    city?.toString() ?? selectedCity.city_url,
                  ),
                )}
              </motion.p>
            </AnimatePresence>
            <Button
              onClick={() => setShowMore((prev) => !prev)}
              variant='link'
              className='h-auto p-0 text-bt4 font-medium text-primary-900 hover:underline md:text-bt3'
            >
              {showMore ? "Read less" : "Read more"}
            </Button>
          </div>
        </div>
      )}

      {/* Product Features */}
      {features && (
        <div className='space-y-2 md:space-y-3 md:p-2'>
          <Typography
            as={"h4"}
            className='text-sh2 text-neutral-900 md:text-h6'
          >
            Product Features
          </Typography>

          {featureArray.length > 0 && (
            <div className='space-y-2 text-xs md:space-y-2 md:text-sm'>
              {featureArray.map((feature) => (
                <div key={feature} className='flex w-full'>
                  <span className='w-full flex-shrink-0 break-words text-b6 text-neutral-500 md:text-b2'>
                    • {feature}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Policy Buttons */}
      <div className='flex gap-2'>
        <Button
          variant='outline'
          onClick={() => setOpenRefundPolicy(true)}
          className='h-11 flex-1 gap-1 border-none bg-neutral-150 text-bt4 text-primary-900 shadow-transparent max-md:p-2 md:text-bt2'
        >
          <FileTextIcon className='h-4 w-4 md:mr-1' />
          Exchange Policy
        </Button>
        <Button
          variant='outline'
          onClick={() => setOpenDamagePolicy(true)}
          className='h-11 flex-1 gap-1 border-none bg-neutral-150 text-bt2 text-primary-900 shadow-transparent max-md:p-2 md:text-bt2'
        >
          <FileTextIcon className='h-4 w-4 md:mr-1' />
          Damage Policy
        </Button>
      </div>

      <SideView
        className='max-w-xl max-md:max-h-[95%]'
        openSideView={openDamagePolicy}
        setOpenSideView={setOpenDamagePolicy}
        title={"Damage Policy"}
      >
        <ReturnPolicy
          title={"Damage Policy"}
          description={"We understand, sometimes things go wrong!"}
          policies={damagePolicies}
        />
      </SideView>
      <SideView
        className='max-w-xl'
        openSideView={openRefundPolicy}
        setOpenSideView={setOpenRefundPolicy}
        title={"Exchange Policy"}
      >
        <ReturnPolicy
          title={"Exchange Policy"}
          description={
            "At SharePal, we are committed to providing a seamless rental experience. Our replacement policy ensures that you receive the right products in a timely manner."
          }
          policies={replacementPolicies}
        />
      </SideView>
    </div>
  )
}
