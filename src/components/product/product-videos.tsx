import { fetchProductVideos } from "@/actions/product"
import YouTubeShortsWithThumbnail from "../custom/youtube-shorts-with-thumnail"
import { ProductPageTitle } from "../heading/prouduct-page"

interface ProductVideosProps {
  productName: string
}

const ProductVideos = async ({ productName }: ProductVideosProps) => {
  const videos = await fetchProductVideos(productName).catch((error) => {
    console.error("Failed to fetch product videos:", error)
    return []
  })
  if (videos.length === 0) return null

  return (
    <div className='flex h-full w-full flex-col items-start gap-2 px-3 md:px-6'>
      <ProductPageTitle heading={`More About ${productName}`} />
      <YouTubeShortsWithThumbnail videos={videos} />
    </div>
  )
}

export default ProductVideos
