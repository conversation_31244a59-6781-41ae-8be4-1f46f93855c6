import { fetchInclusions } from "@/actions/product"
import { Inclusions } from "@/types"
import InclusionCard from "../cards/box-card"
import { ProductPageTitle } from "../heading/prouduct-page"

interface WhatsInBoxProps {
  productName: string
  has_inclusions: boolean
}

const WhatsInBox = async ({ productName, has_inclusions }: WhatsInBoxProps) => {
  if (!has_inclusions) return <></>
  const inclusions = await fetchInclusions(productName)

  if (!inclusions || inclusions.length === 0) return null

  return (
    <div className='flex w-full flex-col items-start gap-4 space-y-5 rounded-2xl bg-gray-100 pb-4 md:gap-8 md:rounded-3xl md:pt-6'>
      <div className='flex h-full w-full flex-col items-start gap-2 overflow-hidden px-3 md:px-6'>
        <ProductPageTitle heading="What's in the Box?" />

        <div className='custom-scrollbar-black flex w-full items-start gap-4 overflow-x-auto py-2'>
          {inclusions?.map((item: Inclusions) => (
            <InclusionCard
              key={item?.id}
              image={item?.sub_product_image}
              name={item?.sub_product_name}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

export default WhatsInBox
