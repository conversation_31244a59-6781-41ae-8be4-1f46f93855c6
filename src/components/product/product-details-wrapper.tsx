import { fetchProductDescription } from "@/actions/product"
import { ProductDetails } from "./product-details"

interface ProductDetailsWrapperProps {
  productName: string
  featureSpecs: string
}

const ProductDetailsWrapper = async ({
  productName,
  featureSpecs,
}: ProductDetailsWrapperProps) => {
  const productDesc = await fetchProductDescription(productName).catch(
    (error) => {
      console.error("Failed to fetch product description:", error)
      return []
    },
  )
  return (
    <ProductDetails desc={productDesc?.product_desc} features={featureSpecs} />
  )
}

export default ProductDetailsWrapper
