"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { motion } from "framer-motion"

import { useUserStore } from "@/store/user-store"
import { IconVerification } from "../Icons/icon-verification"

import { HARD_CODED_IMAGE_URL } from "@/constants"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import React from "react"
import {
  ClockOutlinedIcon,
  IdentityCardHorizontalOutlineIcon,
  IdentityCardVerticalOutlineIcon,
  PlayCircleFilledIcon,
  ThumbsUpOutlinedIcon,
  WorkBriefCaseOutlinedIcon,
} from "sharepal-icons"
import { Separator } from "../ui/separator"
import { Typography } from "../ui/typography"

interface VerificationScreenProps {
  onStart: () => void
}

export default function VerificationScreen({
  onStart,
}: VerificationScreenProps) {
  const fadeIn = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5 },
  }

  const { user } = useUserStore()

  return (
    <motion.div
      initial='initial'
      animate='animate'
      className='w-full max-w-2xl'
    >
      <Card className='space-y-4 border-0 bg-gray-100 px-6 py-8 md:space-y-8 md:px-16'>
        {/* Header */}
        <motion.div {...fadeIn} className='space-y-4 text-center'>
          <div className='flex justify-center'>
            <div className='relative p-5'>
              <div className='flex h-20 w-20 items-center justify-center rounded-full'>
                <IconVerification />
              </div>
            </div>
          </div>
          <Typography
            as={"h1"}
            className='text-sh5 font-semibold text-gray-900 md:text-sh3'
          >
            Hi, {user?.first_name}!
          </Typography>
          <Typography
            as={"h2"}
            className='text-h4 font-bold text-gray-900 md:text-h1'
          >
            ✅ Verify Yourself
          </Typography>
          <Typography
            as={"p"}
            className='mx-auto max-w-md text-b4 text-neutral-500 md:text-b2'
          >
            Fast, Secure & Hassle-Free Verification We ensure your safety with a
            quick 3-step verification to prevent fraud and confirm your
            identity. 🔹 Why Verify? – Stay secure and unlock seamless
            transactions.
          </Typography>
        </motion.div>

        {/* Video Section */}
        <motion.div
          {...fadeIn}
          transition={{ delay: 0.2 }}
          className='mx-auto flex max-w-[432px] cursor-pointer items-center gap-2 rounded-lg bg-neutral-150 p-2 transition-colors hover:bg-neutral-100 md:gap-4 md:p-2'
        >
          <div className='relative flex aspect-video h-[102px] w-[297px] items-center justify-center overflow-hidden rounded-lg bg-neutral-200'>
            <PlayCircleFilledIcon className='absolute h-8 w-8 text-category-red' />
          </div>
          <div className='space-y-1 md:space-y-2'>
            <Typography
              as={"h3"}
              className='!text-sh6 font-semibold text-gray-900 md:!text-h7'
            >
              Why Verification is required?
            </Typography>
            <Typography
              as={"p"}
              className='!text-b6 text-neutral-500 md:!text-b4'
            >
              Watch video to know more about our verification process
            </Typography>
          </div>
          <PlayCircleFilledIcon className='h-4 w-4 self-start text-neutral-300 md:h-8 md:w-8' />
        </motion.div>

        {/* Steps */}
        <VerificationSteps />

        {/* Footer */}
        <motion.div
          {...fadeIn}
          transition={{ delay: 0.7 }}
          className='fixed bottom-0 left-0 right-0 z-50 flex w-full flex-col gap-4 bg-gray-100 p-4 max-md:shadow-sm md:static md:flex md:justify-start md:gap-4 md:space-y-4 md:bg-transparent md:px-0 md:pb-4 md:pt-0'
        >
          <div className='flex items-center justify-center gap-2 text-success-600'>
            <ClockOutlinedIcon className='h-4 w-4' />
            <Typography as={"p"} className='text-b4'>
              Complete verification in less than 5 minutes!
            </Typography>
          </div>
          <Button
            onClick={onStart}
            size='lg'
            className='w-full bg-primary-500 text-bt2 text-white hover:bg-primary-600'
          >
            Start Verification Now
            <svg
              className='ml-2 h-5 w-5'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeWidth='2'
            >
              <path d='M5 12h14M12 5l7 7-7 7' />
            </svg>
          </Button>
        </motion.div>
      </Card>
    </motion.div>
  )
}

const steps = [
  {
    id: 1,
    title: "Identity\nVerification",
    icon: (
      <IdentityCardVerticalOutlineIcon className='w-6 text-secondary-800 md:w-10' />
    ),
    number: "1",
    color: "text-blue-700",
  },
  {
    id: 2,
    title: "Occupation\nVerification",
    icon: (
      <WorkBriefCaseOutlinedIcon className='w-6 text-secondary-800 md:w-10' />
    ),
    number: "2",
    color: "text-green-600",
  },
  {
    id: 3,
    title: "PAN\nVerification",
    icon: (
      <IdentityCardHorizontalOutlineIcon className='w-6 text-secondary-800 md:w-10' />
    ),
    number: "3",
    color: "text-blue-700",
  },
]

const fadeIn = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
}

function VerificationSteps() {
  return (
    <motion.div
      {...fadeIn}
      transition={{ duration: 0.4 }}
      className='space-y-6'
    >
      <Typography
        as={"h3"}
        className='text-center text-sh3 font-semibold text-gray-900 md:text-h7'
      >
        Our 3-Step Verification Process
      </Typography>

      <div className='space-y-4 rounded-2xl bg-secondary-100 p-4 md:space-y-8'>
        <div className='grid md:grid-cols-3'>
          {steps.map((step, index) => (
            <React.Fragment key={index}>
              <motion.div
                key={step.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                className={cn(
                  "space-y-3 p-2 text-center",
                  index !== 0 &&
                    index < steps.length &&
                    "border-secondary-900 border-opacity-10 md:border-l",
                )}
              >
                <div className='flex items-start justify-between gap-2 py-2 max-md:w-full'>
                  <div className='flex items-start justify-center gap-4 md:gap-2'>
                    <Typography
                      as={"p"}
                      className='text-sh4 text-secondary-800'
                    >
                      {step.number}
                    </Typography>
                    <Typography as={"h4"} className='text-sh5 text-primary-700'>
                      {step.title}
                    </Typography>
                  </div>
                  <div className='flex h-full items-center justify-center'>
                    {step.icon}
                  </div>
                </div>
              </motion.div>
              {index < steps.length - 1 && (
                <Separator className='my-1 h-[1px] w-full bg-secondary-900 opacity-10 md:hidden' />
              )}
            </React.Fragment>
          ))}
        </div>

        <motion.div
          {...fadeIn}
          transition={{ duration: 0.4, delay: 0.3 }}
          className='space-y-4'
        >
          <div className='flex flex-col items-center gap-2 rounded-xl bg-success-100 p-3 md:flex-row md:items-start md:gap-3'>
            <SpImage
              src={`${HARD_CODED_IMAGE_URL}/shield1.webp`}
              width={80}
              height={10}
              alt='Sharepal Promise Image'
              className=' '
              containerClassName=''
            />

            <div>
              <Typography
                as={"h4"}
                className='text-sh6 text-success-900 max-md:text-center md:text-sh5'
              >
                Your Privacy Matters to Us
              </Typography>
              <Typography
                as={"p"}
                className='text-b7 text-success-700 max-md:text-center md:text-b5 md:text-sm'
              >
                We use your details solely for verification to ensure security
                and prevent fraud. Your data is never shared without consent.
              </Typography>
            </div>
          </div>
          <div className='flex w-full items-center justify-center text-center md:gap-2'>
            <div className='space-x-2'>
              <ThumbsUpOutlinedIcon className='inline-block h-5 w-5 text-green-600' />
              <Typography
                as={"span"}
                className='text-sh6 text-primary-900 md:text-sh4'
              >
                99.9%{" "}
                <Typography as={"span"} className='text-b6'>
                  {" "}
                  of our users trust our secure verification process.
                </Typography>
              </Typography>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  )
}
