import { z } from "zod"

export const identityVerificationSchema = z
  .object({
    verificationType: z.enum(["online", "offline", "digilocker"]),
    aadharNumber: z
      .string()
      .length(12, "Aadhar number must be 12 digits")
      .regex(/^\d+$/, "Aadhar number must contain only digits")
      .optional(),
    otp: z.string().min(6, "OTP must be 6 digits").max(6).optional(),
    frontFile: z.any().optional(),
    backFile: z.any().optional(),
  })
  .refine(
    (data) => {
      if (data.verificationType === "online") {
        return !!data.aadharNumber && !!data.otp
      }
      if (data.verificationType === "offline") {
        return !!data.frontFile && !!data.backFile
      }
      if (data.verificationType === "digilocker") {
        return true // DigiLocker verification doesn't require form fields
      }
      return true
    },
    {
      message: "Required fields are missing based on verification type",
      path: [], // Form-level error; specific paths handled by individual fields
    },
  )

export type IdentityVerificationForm = z.infer<
  typeof identityVerificationSchema
>
