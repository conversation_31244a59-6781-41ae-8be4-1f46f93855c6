"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { Loader2 } from "lucide-react"
import {
  useEffect,
  // useMemo,
  useState,
} from "react"
import { useForm } from "react-hook-form"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { AnimatePresence, motion } from "framer-motion"
import { FileUpload } from "../file-upload"
import {
  identityVerificationSchema,
  type IdentityVerificationForm,
} from "./schema"

import { useUserStore } from "@/store/user-store"
import {
  authHeader,
  fetchWithAuth,
  fetchWithAuthPost,
} from "@/utils/fetchWithAuth"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"

import { Typography } from "@/components/ui/typography"
// import { useThrottle } from "@/hooks/use-throttle"
import {
  // OtpRequestInVerification,
  trackCompleteVerification,
} from "@/lib/gtag-event"
import { ZoopFailureResponse, ZoopInitResponse } from "@/types/zoop-digilocker"
import { isStatusRequested } from "@/utils/verification"
import axios, { AxiosProgressEvent } from "axios"
import { useRouter, useSearchParams } from "next/navigation"
import { WhatsAppLogoFilledIcon } from "sharepal-icons"
import VerificationReceivedModal from "../verification-received"

interface IdentityVerificationDialogProps {
  isOpen: boolean
  onClose: (value: boolean) => void
  unableToVerify: () => void
}

// const AADHAR_NUMBER_LENGTH = 12
// const OTP_LENGTH = 6
const OTP_TIMER_DURATION = 30

// const VERIFICATION_API_URL = "https://api.sharepal.in/api:YeisLDqw"
const FILE_UPLOAD_API_URL = `https://api.sharepal.in/api:AIoqxnqr/verification/multiple-file-upload`

// const REQUEST_OTP_API_URL = `${VERIFICATION_API_URL}/request_otp`
const VERIFY_OTP_API_URL = `https://api.sharepal.in/api:YeisLDqw/verify_otp`

export function IdentityVerification({
  onClose,
  unableToVerify,
}: IdentityVerificationDialogProps) {
  const [isOtpSent, setIsOtpSent] = useState(false)
  const [otpTimer, setOtpTimer] = useState(OTP_TIMER_DURATION)
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {},
  )
  const [uploadError, setUploadError] = useState<Record<string, string>>({})
  const [isUploading, setIsUploading] = useState<Record<string, boolean>>({})
  const [isUploadSuccess, setIsUploadSuccess] = useState<
    Record<string, boolean>
  >({})
  const [open, setOpen] = useState(false)
  const { userVerification } = useUserStore()
  const queryClient = useQueryClient()
  const router = useRouter()

  const { user, setUser } = useUserStore()

  const params = useSearchParams()
  const success = params?.get("success")
  const requestId = params.get("id")
  const scope = params?.get("scope")
  const errCode = params?.get("errCode")

  //call api based on search params to update aadhar details.
  useEffect(() => {
    const getAadharDetailsUsingRequestId = async () => {
      try {
        const data = await fetchWithAuthPost(
          "https://api.sharepal.in/api:YeisLDqw/digilocker_init",
          // "https://api.sharepal.in/api:YeisLDqw/setu_digilocker_aadhar",
          {
            request_id: requestId,
          },
        )

        if (data) {
          queryClient.invalidateQueries({ queryKey: ["user_verification"] })

          toast.success("Aadhaar Verified Successfully!")
        }
      } catch {
        toast.error("Failed to get Aadhar details")
      } finally {
        router.replace("/complete-verification")
      }
    }

    if (success && success === "True" && requestId && requestId !== "") {
      getAadharDetailsUsingRequestId()
    } else if (success && success === "False" && errCode && errCode !== "") {
      toast.error("Please Verify your Aadhaar through Digilocker!")
      router.replace("/complete-verification")
    } else if (success && success === "False" && !errCode) {
      toast.error("Aadhar Verification Failed")
      router.replace("/complete-verification")
    }
  }, [success, requestId, scope, errCode, router, queryClient])

  const form = useForm<IdentityVerificationForm>({
    resolver: zodResolver(identityVerificationSchema),
    defaultValues: {
      verificationType: "online",
      aadharNumber: "",
      otp: "",
      frontFile: null,
      backFile: null,
    },
    mode: "onChange",
  })
  const frontFileData = form.getValues("frontFile")
  const backFileData = form.getValues("frontFile")

  const verificationType = form.watch("verificationType")

  // Reset fields when verificationType changes
  useEffect(() => {
    const resetFormFields = () => {
      if (verificationType === "online") {
        form.resetField("frontFile")
        form.resetField("backFile")
      } else {
        form.resetField("aadharNumber")
        form.resetField("otp")
        setIsOtpSent(false)
        setOtpTimer(OTP_TIMER_DURATION)
      }
      setIsUploadSuccess({})
      setUploadProgress({})
      setUploadError({})
      setIsUploading({})
    }

    resetFormFields()
  }, [verificationType, form])

  // Handle OTP timer
  useEffect(() => {
    let timer: NodeJS.Timeout
    if (isOtpSent && otpTimer > 0) {
      timer = setInterval(() => {
        setOtpTimer((prev) => prev - 1)
      }, 1000)
    }
    return () => clearInterval(timer)
  }, [isOtpSent, otpTimer])

  // const { mutate: handleSendOtp, isPending: isSendingOTP } = useMutation({
  //   mutationFn: async () => {
  //     const aadharNumber = form.getValues("aadharNumber")
  //     const isValid = await form.trigger("aadharNumber")
  //     if (!isValid) {
  //       throw new Error("Invalid Aadhar number")
  //     }

  //     const response = await fetchWithAuth(REQUEST_OTP_API_URL, {
  //       method: "POST",
  //       body: JSON.stringify({
  //         customer_aadhaar_number: aadharNumber,
  //       }),
  //     })

  //     if (user) {
  //       OtpRequestInVerification("Get OTP Initiated", user, "Identity")
  //     }
  //     return response
  //   },
  //   onSuccess: (data) => {
  //     if (data?.success) {
  //       setIsOtpSent(true)
  //       setOtpTimer(OTP_TIMER_DURATION)
  //       setRequestID(data?.request_id || "")
  //       toast.success("OTP Sent")
  //     } else {
  //       const errorMessage =
  //         data?.metadata?.reason_message || "Failed to send OTP"
  //       form.setError("aadharNumber", {
  //         type: "manual",
  //         message: errorMessage,
  //       })
  //       toast.error("Failed to send OTP")
  //     }
  //   },
  //   onError: () => {
  //     toast.error("Failed to send OTP")
  //   },
  // })

  const { mutate: handleFileUpload } = useMutation({
    mutationFn: async () => {
      const frontFile = form.getValues("frontFile")
      const backFile = form.getValues("backFile")
      const token = localStorage.getItem("token")

      if (!token) throw new Error("Authentication required")
      if (!frontFile || !backFile) throw new Error("No file selected")

      setIsUploading({ frontFile: true, backFile: true })

      const formData = new FormData()
      formData.append("file1", frontFile)
      formData.append("file2", backFile)
      formData.append("file_type", "identity")

      try {
        const res = await axios.post(FILE_UPLOAD_API_URL, formData, {
          headers: authHeader(token, "multipart/form-data"),
          onUploadProgress: ({ loaded, total }: AxiosProgressEvent) => {
            if (total) {
              const percent = Math.round((loaded * 100) / total)
              setUploadProgress({ frontFile: percent, backFile: percent })
            }
          },
        })

        return res
      } finally {
        setIsUploading({ frontFile: false, backFile: false })
      }
    },

    onSuccess: (res) => {
      const isSuccess = res.status === 200
      const commonUpdate = {
        frontFile: isSuccess,
        backFile: isSuccess,
      }

      setIsUploadSuccess(commonUpdate)
      setUploadError({
        frontFile: isSuccess ? "" : "Upload failed. Please try again.",
        backFile: isSuccess ? "" : "Upload failed. Please try again.",
      })

      if (isSuccess) {
        if (user) {
          setUser({
            ...user,
            identity_verified_type: res.data?.identity_verified_type,
          })
        }
        toast.success("Aadhaar uploaded successfully")
        // queryClient.invalidateQueries({ queryKey: ["user_verification"] })
        setOpen(true)
        // onClose(false)
      } else {
        toast.error("Upload failed. Please try again.")
      }
    },

    onError: () => {
      setIsUploading({ frontFile: false, backFile: false })
      setIsUploadSuccess({ frontFile: false, backFile: false })
      setUploadError({
        frontFile: "Upload failed. Please try again.",
        backFile: "Upload failed. Please try again.",
      })
      toast.error("Upload failed. Please try again.")
    },
  })

  const { mutate: onSubmit, isPending: isSubmitting } = useMutation({
    mutationFn: async () => {
      if (verificationType === "online") {
        const token = localStorage.getItem("token")
        if (!token) {
          throw new Error("Authentication required")
        }

        const otp = form.getValues("otp")
        const aadharNumber = form.getValues("aadharNumber")

        const data = await fetchWithAuth(VERIFY_OTP_API_URL, {
          method: "POST",
          body: JSON.stringify({
            request_id: requestId,
            otp: otp,
            aadhar_number: aadharNumber,
            user_id: user?.id,
            user_email: user?.email,
          }),
        })

        if (user) {
          trackCompleteVerification("OTP Verification", user, "Identity", true)
        }
        return data
      } else {
        if (!isUploadSuccess.frontFile || !isUploadSuccess.backFile) {
          throw new Error("Please upload both front and back files")
        }
        // Placeholder: Add actual API call for offline verification if needed
        return { success: true }
      }
    },
    onSuccess: (data) => {
      if (verificationType === "online") {
        if (data?.id) {
          setUser(data)
          toast.success("Successfully Submitted")
          if (user) {
            trackCompleteVerification(
              "OTP Verification",
              user,
              "Identity",
              true,
            )
          }
          // queryClient.invalidateQueries({ queryKey: ["user_verification"] })
          setOpen(true)
          // onClose(false)
        } else if (!data?.payload?.success) {
          const errorMessage = data?.payload?.response_message || "Invalid OTP"
          form.setError("otp", {
            type: "manual",
            message: errorMessage,
          })
          toast.error(errorMessage ?? data?.message ?? "Something Went Wrong")
          if (user) {
            trackCompleteVerification(
              "OTP Verification",
              user,
              "Identity",
              false,
            )
          }
        }
      } else if (verificationType === "offline") {
        toast.success("Aadhar Details Submitted Successfully")
        if (user) {
          trackCompleteVerification("File Verification", user, "Identity", true)
        }
        // queryClient.invalidateQueries({ queryKey: ["user_verification"] })
        setOpen(true)
        // onClose(false)
      }
    },
    onError: (error) => {
      console.error("Verification error:", error)
      if (user) {
        trackCompleteVerification("Verification", user, "Identity", false)
      }
      toast.error(error.message ?? "Something Went Wrong")
    },
  })

  const {
    mutate: handleDigiLockerVerification,
    isPending: isDigilockerPending,
  } = useMutation({
    mutationFn: async (): Promise<ZoopInitResponse | ZoopFailureResponse> => {
      if (!user) {
        throw new Error("User not authenticated")
      }
      const response = (await fetchWithAuthPost(
        "https://api.sharepal.in/api:YeisLDqw/digilocker_init",
        // "https://api.sharepal.in/api:YeisLDqw/setu_digilocker_init",
        {},
      )) as ZoopInitResponse
      // console.log("DigiLocker verification response:", response)
      return response
    },
    onSuccess: (response) => {
      if (response?.success) {
        window.open(response.sdk_url, "_self", "noopener,noreferrer")
        // window.open(response.url, "_self", "noopener,noreferrer")
      } else {
        toast.error("Failed to initialize DigiLocker")
      }
    },
    onError: (error) => {
      console.error("DigiLocker verification error:", error)
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to initialize DigiLocker",
      )
    },
  })

  // const aadharNumberValue = form.watch("aadharNumber")

  // const formattedAadharNumber = useMemo(
  //   () =>
  //     aadharNumberValue
  //       ?.replace(/\D/g, "")
  //       .replace(/(\d{4})/g, "$1 ")
  //       .trim() || "",
  //   [aadharNumberValue],
  // )

  // const handleAadharNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const rawValue = e.target.value.replace(/\D/g, "").slice(0, 12)
  //   form.setValue("aadharNumber", rawValue)
  // }

  // const throttleSendOtp = useThrottle(handleSendOtp, 1000)
  // const otpValue = form.watch("otp")

  return (
    <>
      <VerificationReceivedModal
        onClose={onClose}
        messages={{
          buttonText: isStatusRequested(
            userVerification?.occupation_status ?? "",
          )
            ? "Verify Occupation"
            : isStatusRequested(userVerification?.credit_status ?? "")
              ? "Verify Pan"
              : "Continue",
        }}
        open={open}
        setOpen={setOpen}
      />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(() => onSubmit())}
          className='space-y-3'
        >
          <div className='min-h-[60vh] space-y-6 max-md:p-4 md:min-h-max'>
            <FormField
              control={form.control}
              name='verificationType'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className='space-y-4'
                    >
                      <Card className='shadow-none border-0'>
                        <CardContent className='p-0'>
                          {/* <div className='rounded-lg bg-neutral-150'>
                            <div className='flex items-center space-x-2 p-4'>
                              <RadioGroupItem value='online' id='online' />

                              <Label
                                htmlFor='online'
                                className='flex items-center gap-2'
                              >
                                <Typography as={"p"} className='text-sh5'>
                                  Aadhar Verification - Online
                                </Typography>
                                <Badge
                                  variant='secondary'
                                  className='ml-2 min-w-max rounded-full bg-primary-100 text-b6 text-primary-500 hover:bg-primary-100'
                                >
                                  Safer & Faster
                                </Badge>
                              </Label>
                            </div>

                            <AnimatePresence>
                              {field.value === "online" && (
                                <motion.div
                                  key='online'
                                  initial={{ height: 0, opacity: 0 }}
                                  animate={{ height: "auto", opacity: 1 }}
                                  exit={{ height: 0, opacity: 0 }}
                                  transition={{ duration: 0.2 }}
                                  className='overflow-hidden'
                                >
                                  <div className='space-y-4 p-4 md:ml-2'>
                                    <FormField
                                      control={form.control}
                                      name='aadharNumber'
                                      render={() => (
                                        <FormItem>
                                          <FormLabel className='text-b4'>
                                            Your Aadhar Number
                                            <span className='text-destructive-500'>
                                              *
                                            </span>
                                          </FormLabel>

                                          <FormControl>
                                            <Input
                                              placeholder='Enter your 12-digit Aadhar number'
                                              className='text-b2'
                                              maxLength={14} // Includes spaces
                                              value={formattedAadharNumber}
                                              onChange={
                                                handleAadharNumberChange
                                              }
                                            />
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />

                                    {!isOtpSent ? (
                                      <Button
                                        type='button'
                                        variant='link'
                                        className='bg text-bt3 text-primary-500'
                                        onClick={() => handleSendOtp()}
                                        disabled={
                                          !form.getValues("aadharNumber") ||
                                          form.getValues("aadharNumber")
                                            ?.length !== AADHAR_NUMBER_LENGTH ||
                                          isSendingOTP
                                        }
                                      >
                                        {isSendingOTP ? (
                                          <Loader2 className='animate-spin' />
                                        ) : (
                                          <>
                                            Send OTP
                                            <ArrowRight className='ml-1 h-4 w-4' />
                                          </>
                                        )}
                                      </Button>
                                    ) : (
                                      <motion.div
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        className='space-y-4'
                                      >
                                        <FormField
                                          control={form.control}
                                          name='otp'
                                          render={({ field }) => (
                                            <FormItem>
                                              <FormLabel className='text-bt3'>
                                                Enter OTP
                                                <span className='text-destructive-500'>
                                                  *
                                                </span>
                                              </FormLabel>
                                              <FormControl>
                                                <InputOTP
                                                  value={field.value || ""}
                                                  onChange={(value) =>
                                                    field.onChange(value)
                                                  }
                                                  maxLength={OTP_LENGTH}
                                                >
                                                  <InputOTPGroup className='w-full justify-start gap-1'>
                                                    {Array.from({
                                                      length: OTP_LENGTH,
                                                    }).map((_, index) => (
                                                      <InputOTPSlot
                                                        className='h-12 w-12 rounded-2xl border-2 border-neutral-200 text-sh3'
                                                        key={index}
                                                        index={index}
                                                      />
                                                    ))}
                                                  </InputOTPGroup>
                                                </InputOTP>
                                              </FormControl>
                                              <FormMessage />
                                            </FormItem>
                                          )}
                                        />
                                        {otpTimer > 0 ? (
                                          <p className='text-bt3 text-neutral-500'>
                                            Resend OTP in {otpTimer} sec
                                          </p>
                                        ) : (
                                          <Button
                                            type='button'
                                            variant='link'
                                            className='p-0 text-bt3 text-primary-500'
                                            onClick={() => throttleSendOtp()}
                                          >
                                            <RefreshCcw className='mr-1 h-4 w-4' />
                                            Resend OTP
                                          </Button>
                                        )}
                                      </motion.div>
                                    )}
                                  </div>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div> */}
                          <div className='rounded-lg bg-neutral-150'>
                            <div className='mt-4 flex items-center space-x-2 p-4'>
                              <RadioGroupItem
                                value='digilocker'
                                id='digilocker'
                              />
                              <Label htmlFor='digilocker' className='flex'>
                                <Typography as={"p"} className='text-sh5'>
                                  Aadhar Verification - DigiLocker
                                </Typography>
                                <Badge
                                  variant='secondary'
                                  className='ml-2 min-w-max rounded-full bg-primary-100 text-b6 text-primary-500 hover:bg-primary-100'
                                >
                                  Safer & Faster
                                </Badge>
                              </Label>
                            </div>

                            <AnimatePresence>
                              {field.value === "digilocker" && (
                                <motion.div
                                  key='digilocker'
                                  initial={{ height: 0, opacity: 0 }}
                                  animate={{ height: "auto", opacity: 1 }}
                                  exit={{ height: 0, opacity: 0 }}
                                  transition={{ duration: 0.2 }}
                                  className='overflow-hidden'
                                >
                                  <div className='ml-2 space-y-4 p-4'>
                                    <Button
                                      type='button'
                                      className='w-full bg-primary-500 hover:bg-primary-600'
                                      onClick={() =>
                                        handleDigiLockerVerification()
                                      }
                                      disabled={isDigilockerPending}
                                    >
                                      {isDigilockerPending ? (
                                        <>
                                          <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                                          Initializing DigiLocker...
                                        </>
                                      ) : (
                                        "Verify with DigiLocker"
                                      )}
                                    </Button>
                                    <Typography as={"p"} className='text-bt3'>
                                      This will open the DigiLocker portal in a
                                      new tab. Please complete the verification
                                      there.
                                    </Typography>
                                  </div>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                          <div className='rounded-lg bg-neutral-150'>
                            <div className='mt-4 flex items-center space-x-2 p-4'>
                              <RadioGroupItem value='offline' id='offline' />
                              <Label htmlFor='offline' className='flex'>
                                <Typography as={"p"} className='text-sh5'>
                                  Upload Identity proof - Aadhar, DL, Passport
                                  etc
                                </Typography>
                              </Label>
                            </div>

                            <AnimatePresence>
                              {field.value === "offline" && (
                                <motion.div
                                  key='offline'
                                  initial={{ height: 0, opacity: 0 }}
                                  animate={{ height: "auto", opacity: 1 }}
                                  exit={{ height: 0, opacity: 0 }}
                                  transition={{ duration: 0.2 }}
                                  className='overflow-hidden'
                                >
                                  <div className='ml-2 space-y-4 p-4'>
                                    <FormField
                                      control={form.control}
                                      name='frontFile'
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormControl>
                                            <FileUpload
                                              label='Upload Aadhar Front'
                                              onChange={(file) => {
                                                field.onChange(file)
                                                // if (file) {
                                                //   handleFileUpload("frontFile")
                                                // }
                                                setUploadError({})
                                              }}
                                              value={field.value}
                                              progress={
                                                uploadProgress.frontFile
                                              }
                                              error={uploadError.frontFile}
                                              isUploading={
                                                isUploading.frontFile
                                              }
                                            />
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />
                                    <FormField
                                      control={form.control}
                                      name='backFile'
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormControl>
                                            <FileUpload
                                              label='Upload Aadhar Back'
                                              onChange={(file) => {
                                                field.onChange(file)
                                                // if (file) {
                                                //   handleFileUpload("backFile")
                                                // }
                                                setUploadError({})
                                              }}
                                              value={field.value}
                                              progress={uploadProgress.backFile}
                                              error={uploadError.backFile}
                                              isUploading={isUploading.backFile}
                                            />
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />
                                  </div>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                        </CardContent>
                      </Card>
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <div className='max-sm:sticky max-sm:bottom-0 max-sm:w-full max-sm:bg-gray-100 max-sm:p-4 max-sm:shadow-sm md:space-y-3'>
            <Button
              type={verificationType === "online" ? "submit" : "button"}
              className='w-full bg-primary-500 hover:bg-primary-600'
              onClick={() => {
                if (verificationType === "offline") handleFileUpload()
              }}
              disabled={
                isSubmitting ||
                (verificationType === "online" &&
                  (!form.formState.isValid ||
                    !isOtpSent ||
                    !form.getValues("otp"))) ||
                (verificationType === "offline" &&
                  (!frontFileData || !backFileData))
              }
            >
              {isSubmitting ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Verifying...
                </>
              ) : (
                "Submit"
              )}
            </Button>

            <Button
              type='button' // Prevent form submission
              variant='link'
              className='w-full text-center text-primary-500'
              onClick={() => {
                unableToVerify()
              }}
            >
              <WhatsAppLogoFilledIcon /> Need help?
            </Button>
          </div>
        </form>
      </Form>
    </>
  )
}
