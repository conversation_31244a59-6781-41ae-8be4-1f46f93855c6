// "use client"

// import { motion } from "framer-motion"
// import { But<PERSON> } from "@/components/ui/button"
// import { Card } from "@/components/ui/card"

// import Link from "next/link"
// import { IdentityVerificationDialog } from "./identity-verificatoin"
// import React, { Dispatch, JSX, SetStateAction, useState } from "react"
// import { useUserStore } from "@/store/use-user-store"
// import { cn } from "@/lib/utils"
// import { trackCompleteVerificationStarted } from "@/lib/gtag-event"
// import { OccupationVerificationDialog } from "./occupation-verification"
// import { PanVerificationDialog } from "./pan-verification"

// import { Typography } from "../ui/typography"
// import { useQuery } from "@tanstack/react-query"
// import Faqs from "../modals/faqs-modal"
// import { fetchFaqs } from "@/actions/product"

// interface Step {
//   id: number
//   title: string
//   description: string
//   icon: JSX.Element
//   isVerified: boolean | undefined
//   tag: string
//   hide?: boolean
// }

// import animationData from "./confirm_tick.json"
// import { Player } from "@lottiefiles/react-lottie-player"
// import { useRouter } from "next/navigation"
// import { Separator } from "@radix-ui/react-dropdown-menu"
// import {
//   ChevronLeftIcon,
//   IdentityCardHorizontalOutlineIcon,
//   IdentityCardVerticalOutlineIcon,
//   TickCircleOutlinedIcon,
//   WorkBriefCaseOutlinedIcon,
// } from "sharepal-icons"
// import { IconVerification } from "../Icons/icon-verification"

// export default function VerificationSteps({ onBack }: { onBack: () => void }) {
//   const [isSubmitted, setIsSubmitted] = useState(false)

//   const fadeIn = {
//     initial: { opacity: 0, y: 20 },
//     animate: { opacity: 1, y: 0 },
//     exit: { opacity: 0, y: -20 },
//     transition: { duration: 0.5 },
//   }

//   const router = useRouter()

//   return (
//     <motion.div
//       initial='initial'
//       animate='animate'
//       exit='exit'
//       className='w-full max-w-2xl p-4'
//     >
//       {isSubmitted && (
//         <Card className='relative space-y-4 border-0 bg-gray-100 p-3 md:space-y-8 md:px-16 md:py-6'>
//           {/* Header */}

//           {/* Header */}
//           <motion.div {...fadeIn} className='space-y-2 text-center'>
//             <div className='flex justify-center'>
//               <Player
//                 src={animationData}
//                 keepLastFrame
//                 className='h-[200px] w-[200px] md:h-[240px] md:w-[240px]'
//                 autoplay
//               />
//             </div>
//             <Typography
//               as={"h1"}
//               className='text-sh5 font-semibold text-gray-900 md:text-sh3'
//             >
//               Woohoo!
//             </Typography>
//             <Typography
//               as={"h2"}
//               className='text-h4 font-bold text-gray-900 md:text-h1'
//             >
//               Verification Request submitted successfully!
//             </Typography>
//             <Typography
//               as={"p"}
//               className='mx-auto max-w-md text-b4 text-neutral-500 md:text-b2'
//             >
//               Our team will review your details and verify your profile shortly
//               if everything is in order. The verification process takes 24hr to
//               complete. For any assistance, contact <NAME_EMAIL> or
//               on WhatsApp at +91-9876543210 (Available 11am to 6pm, 24x7).
//             </Typography>
//             <Typography
//               as={"p"}
//               className='mx-auto max-w-md text-b4 text-neutral-500 md:text-b2'
//             >
//               For any assistance, contact <NAME_EMAIL> or on
//               WhatsApp at +91-9876543210 (Available 11am to 6pm, 24x7).
//             </Typography>
//           </motion.div>

//           {/* Footer */}
//           <motion.div
//             {...fadeIn}
//             transition={{ delay: 0.5 }}
//             className='space-y-4'
//           >
//             <Button
//               variant={"primary"}
//               className='w-full text-bt2 text-white'
//               onClick={() => {
//                 router.push("/dashboard/orders")
//               }}
//             >
//               Go to My Orders
//             </Button>
//           </motion.div>
//         </Card>
//       )}

//       {!isSubmitted && (
//         <VerificationStep
//           fadeIn={fadeIn}
//           setIsSubmitted={setIsSubmitted}
//           onBack={onBack}
//         />
//       )}
//     </motion.div>
//   )
// }

// const VerificationStep = ({
//   fadeIn,
//   setIsSubmitted,
//   onBack,
// }: {
//   fadeIn: {
//     initial: {
//       opacity: number
//       y: number
//     }
//     animate: {
//       opacity: number
//       y: number
//     }
//     exit: {
//       opacity: number
//       y: number
//     }
//   }
//   onBack: () => void
//   setIsSubmitted: Dispatch<SetStateAction<boolean>>
// }) => {
//   const [isIdentityDialogOpen, setIsIdentityDialogOpen] = useState(false)
//   const [isOccupationDialogOpen, setIsOccupationDialogOpen] = useState(false)
//   const [isPanDialogOpen, setIsPanDialogOpen] = useState(false)
//   const { user } = useUserStore()
//   const steps: Step[] = [
//     {
//       id: 1,
//       title: "Identity Verification",
//       description:
//         "A government-issued ID to confirm your identity and ensure secure transactions.",
//       icon: (
//         <IdentityCardVerticalOutlineIcon className='h-6 w-6 text-secondary-800 md:h-9 md:w-9' />
//       ),
//       isVerified: user?.identity_kyc_received,
//       tag: "Identity",
//     },
//     {
//       id: 2,
//       title: "Occupation Verification",
//       description:
//         "This helps us understand your profession, reducing the risk of fraudulent activity.",
//       icon: (
//         <WorkBriefCaseOutlinedIcon className='h-6 w-6 text-secondary-800 md:h-9 md:w-9' />
//       ),
//       isVerified: user?.occupation_kyc_received,
//       tag: "Occupation",
//     },
//     {
//       id: 3,
//       title: "PAN Verification",
//       description:
//         "Required to assess eligibility for credit on high-value products and ensure a smooth rental experience.",
//       icon: (
//         <IdentityCardHorizontalOutlineIcon className='h-6 w-6 text-secondary-800 md:h-9 md:w-9' />
//       ),
//       isVerified: user?.credit_report,
//       tag: "PAN",
//       hide: user?.credit_verification_type === "",
//     },
//   ]

//   const handleVerify = (step: Step) => {
//     if (step.isVerified) return
//     if (step.id === 1) setIsIdentityDialogOpen(true)
//     else if (step.id === 2) setIsOccupationDialogOpen(true)
//     else if (step.id === 3) setIsPanDialogOpen(true)
//     if (user) trackCompleteVerificationStarted(user, step.tag)
//   }

//   const unableToVerifiy = () => {
//     window.open(
//       "https://api.whatsapp.com/send?phone=+************&text=Hi, I am unable to verify my account. Can you help me with the process?",
//       "_blank",
//     )
//   }

//   const { data: faqs } = useQuery({
//     queryKey: ["fetch/faqs"],
//     queryFn: async () => await fetchFaqs(),
//   })

//   const [openFaq, setOpenFaq] = useState(false)

//   return (
//     <>
//       <Card className='relative space-y-4 border-0 bg-gray-100 px-6 py-8 md:space-y-8 md:px-16'>
//         {/* Header */}
//         <motion.div {...fadeIn} className='space-y-6'>
//           <button
//             onClick={onBack}
//             className='absolute left-5 top-5 flex items-center text-neutral-500 transition-colors hover:text-neutral-700 md:left-8 md:top-7'
//           >
//             <ChevronLeftIcon className='mr-1 !h-8 !w-8' />
//           </button>

//           {/* Header */}
//           <motion.div {...fadeIn} className='space-y-4 text-center'>
//             <div className='flex justify-center'>
//               <div className='relative p-5'>
//                 <div className='flex h-20 w-20 items-center justify-center rounded-full'>
//                   <IconVerification />
//                 </div>
//               </div>
//             </div>
//             <Typography
//               as={"h1"}
//               className='text-sh5 text-gray-900 md:text-sh3'
//             >
//               Hi, {user?.first_name}!
//             </Typography>
//             <Typography
//               as={"p"}
//               className='text-h4 font-bold text-gray-900 md:text-h1'
//             >
//               Please Verify Yourself
//             </Typography>
//             <Typography
//               as={"p"}
//               className='mx-auto max-w-md text-b4 text-neutral-500 md:text-b2'
//             >
//               Complete the verification steps and submit for review. Some orders
//               are auto-confirmed, while others may need verification. If
//               verification is required, the review process typically takes 24 to
//               48 hours.
//             </Typography>
//           </motion.div>
//           <Faqs
//             openSideView={openFaq}
//             setOpenSideView={setOpenFaq}
//             faqs={faqs || []}
//           />
//           <div className='flex justify-center'>
//             <Button
//               variant={"link"}
//               onClick={() => setOpenFaq(true)}
//               className='flex items-center'
//             >
//               <Typography
//                 as={"p"}
//                 className='text-wrap text-center text-bt4 text-primary-500 md:text-bt3'
//               >
//                 Frequently Asked Questions (FAQs) for Verification
//               </Typography>
//             </Button>
//           </div>
//         </motion.div>

//         {/* Steps */}
//         <motion.div
//           {...fadeIn}
//           transition={{ delay: 0.2 }}
//           className='space-y-6'
//         >
//           {steps.map(
//             (step, index) =>
//               !step.hide && (
//                 <React.Fragment key={index}>
//                   <motion.div
//                     key={step.id}
//                     initial={{ opacity: 0, x: -20 }}
//                     animate={{ opacity: 1, x: 0 }}
//                     transition={{ delay: 0.3 + index * 0.1 }}
//                     className='flex flex-col items-start justify-center gap-2 overflow-hidden rounded-xl border-neutral-150 max-md:border max-md:bg-neutral-100 md:flex-row md:items-center md:p-4'
//                   >
//                     <div className='flex items-start justify-center gap-2 max-md:p-2 md:items-center md:gap-4'>
//                       <div className='flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-lg md:h-16 md:w-16'>
//                         {step.icon}
//                       </div>

//                       <div className='flex flex-col items-start justify-center'>
//                         <Typography
//                           as='h3'
//                           className='text-sh3 font-semibold text-primary-700 md:text-h7'
//                         >
//                           {step.title}
//                         </Typography>
//                         <div className='flex w-full items-start justify-between'>
//                           <Typography as='p' className='!text-b6 text-gray-700'>
//                             {step.description}
//                           </Typography>
//                           <Button
//                             variant='default'
//                             // size="sm"
//                             className={cn(
//                               "text-gray-50 shadow-transparent disabled:opacity-100 max-md:hidden",
//                               step.isVerified
//                                 ? "bg-success-600 text-gray-100 hover:bg-success-500 hover:text-gray-100"
//                                 : "bg-primary-100 text-primary-500 hover:bg-primary-200",
//                             )}
//                             onClick={(e) => {
//                               e.preventDefault()
//                               handleVerify(step)
//                             }}
//                           >
//                             {step.isVerified && (
//                               <span className='flex items-center justify-center rounded-full p-[0.5px]'>
//                                 <TickCircleOutlinedIcon />
//                               </span>
//                             )}
//                             {step.isVerified ? "Done" : "Verify Now"}
//                           </Button>
//                         </div>
//                       </div>
//                     </div>

//                     <Button
//                       variant='default'
//                       // size="sm"
//                       className={cn(
//                         "flex w-full items-center justify-center rounded-none text-center text-gray-50 shadow-transparent disabled:opacity-100 md:hidden",
//                         step.isVerified
//                           ? "bg-success-600 text-gray-100 hover:bg-success-500 hover:text-gray-100"
//                           : "bg-primary-100 text-primary-500 hover:bg-primary-200",
//                       )}
//                       onClick={(e) => {
//                         e.preventDefault()
//                         handleVerify(step)
//                       }}
//                     >
//                       {step.isVerified && (
//                         <span className='flex items-center justify-center rounded-full p-[0.5px]'>
//                           <TickCircleOutlinedIcon />
//                         </span>
//                       )}
//                       {step.isVerified ? "Done" : "Verify Now"}
//                     </Button>
//                   </motion.div>

//                   {index < steps.length && (
//                     <Separator className='h-[1px] w-full bg-neutral-200 max-md:hidden' />
//                   )}
//                 </React.Fragment>
//               ),
//           )}
//         </motion.div>

//         {/* Footer */}
//         <motion.div
//           {...fadeIn}
//           transition={{ delay: 0.5 }}
//           className='space-y-4'
//         >
//           <div className='fixed bottom-0 left-0 right-0 z-50 flex w-full flex-col gap-4 bg-gray-100 p-4 max-md:shadow-sm md:static md:flex md:justify-start md:gap-4 md:space-y-4 md:bg-transparent md:px-0 md:pb-4 md:pt-0'>
//             <div className='flex items-center justify-center gap-2 text-sm text-success-500'>
//               <svg
//                 viewBox='0 0 24 24'
//                 className='h-4 w-4'
//                 fill='none'
//                 stroke='currentColor'
//                 strokeWidth='2'
//               >
//                 <path d='M20 6L9 17l-5-5' />
//               </svg>
//               Complete all steps to increase your approval chances
//             </div>

//             <Button
//               size='lg'
//               variant={"primary"}
//               disabled={steps.some((step) => !step.isVerified && !step.hide)}
//               className='w-full'
//               onClick={() => {
//                 setIsSubmitted(true)
//               }}
//             >
//               Submit for Review
//             </Button>
//           </div>
//           <Typography
//             as={"p"}
//             className='mx-auto max-w-md text-center text-b6 text-neutral-500 md:text-b4'
//           >
//             By continuing, you agree to the{" "}
//             <Link
//               href='/terms-and-conditions'
//               className='text-sh6 text-primary-900 underline hover:text-primary-600 md:text-sh4'
//             >
//               Terms of Service
//             </Link>{" "}
//             and acknowledge the{" "}
//             <Link
//               href='/privacy-policy'
//               className='text-sh6 text-primary-900 underline hover:text-primary-600 md:text-sh4'
//             >
//               Privacy Policy
//             </Link>
//             .
//           </Typography>{" "}
//         </motion.div>
//       </Card>
//       <IdentityVerificationDialog
//         isOpen={isIdentityDialogOpen}
//         unableToVerifiy={unableToVerifiy}
//         onClose={(value) => setIsIdentityDialogOpen(value)}
//       />

//       <OccupationVerificationDialog
//         isOpen={isOccupationDialogOpen}
//         unableToVerifiy={unableToVerifiy}
//         onClose={(value) => setIsOccupationDialogOpen(value)}
//       />

//       <PanVerificationDialog
//         isOpen={isPanDialogOpen}
//         unableToVerifiy={unableToVerifiy}
//         onClose={(value) => setIsPanDialogOpen(value)}
//       />
//     </>
//   )
// }

const VerificationSteps = () => <div>VerificationSteps</div>

export default VerificationSteps
