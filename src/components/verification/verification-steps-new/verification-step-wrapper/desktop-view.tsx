"use client"
import { Badge } from "@/components/ui/badge"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import { VERIFICATION_STATUS } from "@/types/verification"
import { isStatusNotRequested } from "@/utils/verification"
import { motion } from "framer-motion"
import React from "react"
import { StatusButton, StatusConfig, StatusIndicatorIcon } from "./common"

interface DesktopViewProps {
  icon: React.ReactElement
  title: string
  description: string
  status: VERIFICATION_STATUS
  onAction: () => void
  isOptional: boolean
  statusConfig: StatusConfig
  isOpen: boolean
}

export const DesktopView = ({
  icon,
  title,
  description,
  status,
  onAction,
  isOptional,
  statusConfig,
  isOpen,
}: DesktopViewProps) => (
  <motion.div
    className='mb-4 hidden flex-col md:flex'
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <motion.div
      className={cn(
        "flex items-center justify-between gap-3 overflow-hidden rounded-xl p-4",
        isStatusNotRequested(status) ? "items-center bg-secondary-100" : "",
      )}
    >
      <div className='flex items-start gap-3'>
        <StatusIndicatorIcon status={status} icon={icon} />

        <div className='flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <Typography
              as='h3'
              className='text-base font-semibold text-[#1E293B]'
            >
              {title}
            </Typography>
            {isOptional && status === VERIFICATION_STATUS.Requested && (
              <Badge
                variant={"secondary"}
                className='text-xs font-medium text-gray-600'
              >
                Not Required
              </Badge>
            )}
          </div>
          {status === VERIFICATION_STATUS.Requested && (
            <Typography as='p' className='mt-1 !text-b6 text-gray-700'>
              {description}
            </Typography>
          )}
        </div>
      </div>
      {!isOpen && (
        <StatusButton statusConfig={statusConfig} onAction={onAction} />
      )}
    </motion.div>
  </motion.div>
)
