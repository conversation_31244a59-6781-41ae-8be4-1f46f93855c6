"use client"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { VERIFICATION_STATUS } from "@/types/verification"
import { AlertCircle, CheckCircle, Clock } from "lucide-react"
import React from "react"

export interface StatusConfig {
  icon?: React.ReactElement
  variant: "primary" | "secondary" | "success" | "default"
  text: string
  className?: string
}

export const StatusButton = ({
  statusConfig,
  onAction,
}: {
  statusConfig: StatusConfig
  onAction: () => void
}) => (
  <Button
    size='sm'
    variant={statusConfig.variant}
    className={cn(
      statusConfig.className,
      "rounded-b-xl rounded-t-none md:rounded-3xl",
    )}
    onClick={onAction}
  >
    {statusConfig.icon && <>{statusConfig.icon}</>}
    <span>{statusConfig.text}</span>
  </Button>
)

export const getStatusConfig = (
  status: VERIFICATION_STATUS,
  isOptional: boolean,
): StatusConfig => {
  switch (status) {
    case VERIFICATION_STATUS.Received:
      return {
        icon: <Clock className='h-4 w-4' />,
        variant: "primary",
        text: "Submitted", // temporaray change from under review to submitted
        className: "bg-[#4747F3] text-white hover:bg-[#4747F3]/90 md:w-auto",
      }
    case VERIFICATION_STATUS.Verified:
      return {
        icon: <CheckCircle className='h-4 w-4' />,
        variant: "success",
        text: "Approved",
        className: "bg-green-600 text-white hover:bg-green-700 md:w-auto",
      }
    default:
      return {
        icon: <AlertCircle className='h-4 w-4' />,
        variant: "primary",
        text: isOptional ? "Verify Anyway" : "Verify Now",
        className:
          "bg-primary-100 text-primary-500 hover:bg-primary-150  md:w-auto",
      }
  }
}

export const StatusIndicatorIcon = ({
  status,
  icon,
}: {
  status: VERIFICATION_STATUS
  icon: React.ReactElement
}) => {
  const iconColorClass =
    status === VERIFICATION_STATUS.Verified
      ? "text-green-600"
      : status === VERIFICATION_STATUS.Received
        ? "text-[#4747F3]"
        : status === VERIFICATION_STATUS.Requested
          ? "text-[#4C9D2F]"
          : ""

  return (
    <div
      className={cn(
        "flex h-6 w-6 flex-shrink-0 items-center justify-center",
        iconColorClass,
      )}
    >
      {icon}
    </div>
  )
}
