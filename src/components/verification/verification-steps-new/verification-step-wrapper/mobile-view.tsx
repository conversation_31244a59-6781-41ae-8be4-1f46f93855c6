"use client"
import { Badge } from "@/components/ui/badge"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import { VERIFICATION_STATUS } from "@/types/verification"
import { motion } from "framer-motion"
import React from "react"
import { StatusButton, StatusConfig, StatusIndicatorIcon } from "./common"

interface MobileViewProps {
  icon: React.ReactElement
  title: string
  description: string
  status: VERIFICATION_STATUS
  onAction: () => void
  isOptional: boolean
  statusConfig: StatusConfig
  isOpen: boolean
}

export const MobileView = ({
  icon,
  title,
  description,
  status,
  onAction,
  isOptional,
  statusConfig,
}: MobileViewProps) => (
  <motion.div
    className='mb-4 flex flex-col md:hidden'
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <motion.div
      className={cn(
        "flex flex-col overflow-hidden rounded-xl bg-gray-100",
        "border border-gray-200",
      )}
    >
      <div className='flex flex-col p-4'>
        {/* Header with icon and title */}
        <div className='mb-2 flex items-start gap-3'>
          <StatusIndicatorIcon status={status} icon={icon} />

          <div className='flex w-full items-center justify-between'>
            <Typography
              as='h3'
              className='line-clamp-1 text-sh3 text-primary-700'
            >
              {title}
            </Typography>

            {isOptional && status === VERIFICATION_STATUS.Requested && (
              <Badge
                variant={"secondary"}
                className='min-w-max font-medium text-gray-600'
              >
                Not Required
              </Badge>
            )}
          </div>
        </div>

        {/* Description */}
        <Typography as='p' className='mb-4 text-b6 text-gray-600'>
          {description}
        </Typography>
      </div>

      {/* Action button - full width at the bottom */}

      <StatusButton statusConfig={statusConfig} onAction={onAction} />
    </motion.div>
  </motion.div>
)
