"use client"
import { VERIFICATION_STATUS } from "@/types/verification"
import type { ReactNode } from "react"
import React from "react"
import DeviceWrapper from "../../device-wrapper"
import { getStatusConfig } from "./common"
import { DesktopView } from "./desktop-view"
import { MobileView } from "./mobile-view"

interface StepWrapperProps {
  icon: React.ReactElement
  title: string
  description: string
  status: VERIFICATION_STATUS
  onAction: () => void
  isExpanded?: boolean
  isOptional?: boolean
  children?: ReactNode
  infoText?: string
  isHeader?: boolean
}

const StepWrapper = ({
  icon,
  title,
  description,
  status,
  onAction,
  isExpanded = false,
  isOptional = false,
  children,
}: StepWrapperProps) => {
  const statusConfig = React.useMemo(
    () => getStatusConfig(status, isOptional),
    [status, isOptional],
  )

  return (
    <>
      <MobileView
        icon={icon}
        title={title}
        description={description}
        status={status}
        onAction={onAction}
        isOptional={isOptional}
        statusConfig={statusConfig}
        isOpen={isExpanded}
      />
      <DesktopView
        icon={icon}
        title={title}
        description={description}
        status={status}
        onAction={onAction}
        isOptional={isOptional}
        statusConfig={statusConfig}
        isOpen={isExpanded}
      />

      <DeviceWrapper
        isOpen={isExpanded}
        onClose={() => onAction()}
        title={title}
        description={description}
      >
        {children}
      </DeviceWrapper>
    </>
  )
}

export default StepWrapper
