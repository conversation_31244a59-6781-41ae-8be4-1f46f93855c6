import useMediaQuery from "@/hooks/use-media-query"
import { useUserStore } from "@/store/user-store"
import {
  USER_VERIFICATION,
  VERIFICATION_CASE,
  VERIFICATION_STATUS,
} from "@/types/verification"
import {
  getDefaultExpandedStep,
  getShowChoice,
  getVerificationCaseFromOrderType,
  isPanAutoFetched,
  isStatusNotRequested,
  isStatusRequested,
} from "@/utils/verification"
import { Separator } from "@radix-ui/react-dropdown-menu"
import React, { useEffect, useMemo } from "react"
import PanStep from "../../pan-verification/pan-step"
import ChoiceStep from "../../steps/choice-step"
import IdentityStep from "../../steps/identity-step"
import OccupationStep from "../../steps/occupation-step"
import { StepHeader } from "./step-header"

interface StepProps {
  userVerification: USER_VERIFICATION
  activeStep: number | null
  handleVerify: (stepId: number, tag: string) => void
  unableToVerify: () => void
  setActiveStep: (step: number | null) => void
}

interface StepConfig {
  status: VERIFICATION_STATUS
  render: React.ReactNode
}

// Move these outside the component to prevent unnecessary recreation
const createIdentityStep = (
  identity_status: VERIFICATION_STATUS,
  isExpanded: boolean,
  handleVerify: (stepId: number, tag: string) => void,
  unableToVerify: () => void,
): StepConfig => ({
  status: identity_status,
  render: (
    <IdentityStep
      key='identity'
      status={identity_status}
      isExpanded={isExpanded}
      onAction={() => handleVerify(1, "Identity")}
      onUnableToVerify={unableToVerify}
    />
  ),
})

const createChoiceStep = (
  occupation_status: VERIFICATION_STATUS,
  userVerification: USER_VERIFICATION,
  isExpanded: boolean,
  handleVerify: (stepId: number, tag: string) => void,
  unableToVerify: () => void,
): StepConfig => ({
  status: occupation_status,
  render: (
    <>
      <StepHeader userVerification={userVerification} />

      <ChoiceStep
        key='choice'
        status={occupation_status}
        isExpanded={isExpanded}
        onAction={() => handleVerify(2, "Choice")}
        onUnableToVerify={unableToVerify}
      />
    </>
  ),
})

const createOccupationStep = (
  occupation_status: VERIFICATION_STATUS,
  userVerification: USER_VERIFICATION,
  verification_case: VERIFICATION_CASE,
  isExpanded: boolean,
  handleVerify: (stepId: number, tag: string) => void,
  unableToVerify: () => void,
): StepConfig => ({
  status: occupation_status,
  render: (
    <>
      {isStatusRequested(occupation_status) && (
        <StepHeader userVerification={userVerification} />
      )}
      <OccupationStep
        key='occupation'
        status={occupation_status}
        isExpanded={isExpanded}
        onAction={() => handleVerify(3, "Occupation")}
        onUnableToVerify={unableToVerify}
        isOptional={
          verification_case !== VERIFICATION_CASE.AllRequired &&
          verification_case !== VERIFICATION_CASE.IdentityAndChoice
        }
      />
    </>
  ),
})

const createPanStep = (
  credit_status: VERIFICATION_STATUS,
  occupation_status: VERIFICATION_STATUS,
  verification_case: VERIFICATION_CASE,
  userVerification: USER_VERIFICATION,
  isExpanded: boolean,
  handleVerify: (stepId: number, tag: string) => void,
  unableToVerify: () => void,
): StepConfig => ({
  status: credit_status,
  render: (
    <>
      {isStatusRequested(credit_status) &&
        !isStatusRequested(occupation_status) && (
          <StepHeader userVerification={userVerification} />
        )}
      <PanStep
        key='pan'
        status={credit_status}
        isExpanded={isExpanded}
        onAction={() => handleVerify(4, "PAN")}
        onUnableToVerify={unableToVerify}
        isOptional={
          verification_case !== VERIFICATION_CASE.AllRequired &&
          (verification_case !== VERIFICATION_CASE.IdentityAndChoice ||
            isStatusNotRequested(occupation_status))
        }
      />
    </>
  ),
})

const VerificationStep = ({
  userVerification,
  activeStep,
  handleVerify,
  unableToVerify,
  setActiveStep,
}: StepProps) => {
  const {
    identity_status,
    occupation_status,
    credit_status,
    credit_verified_type,
    last_order_type,
  } = userVerification

  const verification_case = getVerificationCaseFromOrderType(last_order_type)

  const isMobile = useMediaQuery("(max-width: 768px)")
  const showChoice = getShowChoice(userVerification)

  const effectiveActiveStep = isMobile
    ? activeStep
    : (activeStep ?? getDefaultExpandedStep(userVerification))

  useEffect(() => {
    if (activeStep == null) setActiveStep(effectiveActiveStep)
  }, [effectiveActiveStep])

  const steps = useMemo(() => {
    const allSteps: StepConfig[] = [
      createIdentityStep(
        identity_status,
        effectiveActiveStep === 1,
        handleVerify,
        unableToVerify,
      ),
    ]

    if (
      verification_case !== VERIFICATION_CASE.AllRequired &&
      verification_case !== VERIFICATION_CASE.IdentityAndChoice &&
      isStatusRequested(identity_status)
    ) {
      return allSteps
    } else if (
      verification_case === VERIFICATION_CASE.IdentityAndChoice &&
      isPanAutoFetched(credit_verified_type)
    ) {
      return allSteps
    }

    const panAutFetched = credit_verified_type === "auto_fetched"
    const shouldShowPan =
      panAutFetched &&
      !(
        verification_case === VERIFICATION_CASE.IdentityAndChoice &&
        isStatusRequested(occupation_status)
      )

    // Handle other cases
    if (showChoice && !isMobile && !shouldShowPan) {
      allSteps.push(
        createChoiceStep(
          occupation_status,
          userVerification,
          effectiveActiveStep === 2,
          handleVerify,
          unableToVerify,
        ),
      )
      return allSteps
    } else {
      allSteps.push(
        createOccupationStep(
          occupation_status,
          userVerification,
          verification_case,
          effectiveActiveStep === 3,
          handleVerify,
          unableToVerify,
        ),
      )
    }

    if (!panAutFetched) {
      allSteps.push(
        createPanStep(
          credit_status,
          occupation_status,
          verification_case,
          userVerification,
          effectiveActiveStep === 4,
          handleVerify,
          unableToVerify,
        ),
      )
    }

    // Sort steps: Non-requested first, then requested
    return allSteps.sort((a, b) => {
      const aRequested = isStatusRequested(a.status)
      const bRequested = isStatusRequested(b.status)
      return aRequested === bRequested ? 0 : aRequested ? 1 : -1
      // if (isMobile) {
      //   return bRequested === bRequested ? 0 : aRequested ? -1 : 1
      // } else {
      //   return aRequested === bRequested ? 0 : aRequested ? 1 : -1
      // }
    })
  }, [
    identity_status,
    verification_case,
    userVerification,
    showChoice,
    credit_verified_type,
    occupation_status,
    isMobile,
    credit_status,
    effectiveActiveStep,
    handleVerify,
    unableToVerify,
  ])

  return (
    <>
      {steps.map((step, index) => (
        <React.Fragment key={`step-${index}`}>
          {step.render}
          {index < steps.length - 1 && (
            <Separator className='h-[1px] w-full bg-neutral-200 max-md:hidden' />
          )}
        </React.Fragment>
      ))}
    </>
  )
}

interface RenderStepsProps {
  // userVerification: USER_VERIFICATION
  activeStep: number | null
  handleVerify: (stepId: number, tag: string) => void
  unableToVerify: () => void
  setActiveStep: (step: number | null) => void
}

const VerificationStepsToRender: React.FC<RenderStepsProps> = ({
  ...props
}) => {
  const { userVerification: verification } = useUserStore()

  return (
    <VerificationStep
      userVerification={verification as USER_VERIFICATION}
      {...props}
    />
  )
}

export default VerificationStepsToRender
