import useMediaQuery from "@/hooks/use-media-query"
import {
  USER_VERIFICATION,
  VERIFICATION_CASE,
  VERIFICATION_STATUS,
} from "@/types/verification"
import {
  getVerificationCaseFromOrderType,
  isPanAutoFetched,
  isStatusNotRequested,
  isStatusRequested,
} from "@/utils/verification"
import React from "react"
import { InfoCircleOutlinedIcon } from "sharepal-icons"

interface HeaderProps {
  title: string
}

interface StepHeaderProps {
  userVerification: USER_VERIFICATION
}

interface HeaderConfig {
  title: string
}

// Header component to display title and description
export const Header: React.FC<HeaderProps> = ({ title }) => (
  <div className='flex flex-col items-center justify-center gap-1'>
    <h2 className='text-center text-b6 text-gray-700 md:text-b4'>{title}</h2>
    <p className='flex items-center justify-center gap-1 text-b6 text-success-600'>
      <InfoCircleOutlinedIcon className='h-4 w-4' />
      <span className='md:hidden'>
        Complete all verifications for easy checkouts
      </span>
      <span className='hidden md:block'>
        Completing all verifications ensure seamless order confirmations.
      </span>
    </p>
  </div>
)

const getIdentityAndChoiceMobileHeader = (
  occupationStatus: VERIFICATION_STATUS,
  creditStatus: VERIFICATION_STATUS,
  credit_verified_type: "auto_fetched" | "fetched_with_pan",
): HeaderConfig | null => {
  if (
    isStatusRequested(occupationStatus) &&
    isStatusRequested(creditStatus) &&
    !isPanAutoFetched(credit_verified_type)
  ) {
    return {
      title: "Please verify either Occupation or PAN to proceed",
    }
  }

  if (
    occupationStatus !== VERIFICATION_STATUS.Requested &&
    isStatusRequested(creditStatus)
  ) {
    return {
      title: "You can verify PAN for additional benefits",
    }
  }

  if (
    creditStatus !== VERIFICATION_STATUS.Requested &&
    isStatusRequested(occupationStatus)
  ) {
    return {
      title: "You can verify Occupation for additional benefits",
    }
  }

  return null
}

const getIdentityOnlyHeader = (
  occupationStatus: VERIFICATION_STATUS,
  creditStatus: VERIFICATION_STATUS,
): HeaderConfig | null => {
  if (isStatusRequested(occupationStatus) && isStatusRequested(creditStatus)) {
    return {
      title: "You can verify Occupation & PAN too if you like :)",
    }
  }

  if (
    isStatusRequested(occupationStatus) &&
    isStatusNotRequested(creditStatus)
  ) {
    return {
      title: "You can verify Occupation too if you like :)",
    }
  }

  if (
    occupationStatus !== VERIFICATION_STATUS.Requested &&
    isStatusRequested(creditStatus)
  ) {
    return {
      title: "You can verify PAN too if you like :)",
    }
  }
  return null
}

const getIdentityAndChoiceDesktopHeader = (
  occupationStatus: VERIFICATION_STATUS,
  creditStatus: VERIFICATION_STATUS,
): HeaderConfig | null => {
  if (isStatusNotRequested(creditStatus)) {
    return {
      title: "You can verify Occupation too if you like :)",
    }
  }
  if (isStatusNotRequested(occupationStatus)) {
    return {
      title: "You can verify PAN too if you like :)",
    }
  }
  if (isStatusRequested(occupationStatus) && isStatusRequested(creditStatus)) {
    return {
      title:
        "For this step you can select either Occupation or PAN Verification",
    }
  }
  return null
}

export const StepHeader: React.FC<StepHeaderProps> = ({ userVerification }) => {
  const isMobile = useMediaQuery("(max-width: 768px)")
  const {
    occupation_status,
    credit_status,
    credit_verified_type,
    last_order_type,
  } = userVerification

  const verification_case = getVerificationCaseFromOrderType(last_order_type)

  const headerConfig = React.useMemo(() => {
    switch (verification_case) {
      case VERIFICATION_CASE.IdentityAndChoice:
        if (isMobile) {
          return getIdentityAndChoiceMobileHeader(
            occupation_status,
            credit_status,
            credit_verified_type,
          )
        } else {
          return getIdentityAndChoiceDesktopHeader(
            occupation_status,
            credit_status,
          )
        }
      case VERIFICATION_CASE.IdentityOnly:
        return getIdentityOnlyHeader(occupation_status, credit_status)
      default:
        return null
    }
  }, [
    credit_status,
    isMobile,
    credit_verified_type,
    occupation_status,
    verification_case,
  ])

  return headerConfig ? <Header title={headerConfig.title} /> : null
}
