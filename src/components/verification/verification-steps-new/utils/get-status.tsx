import { VERIFICATION_STATUS } from "@/types/verification"
import { AlertCircleIcon } from "lucide-react"
import { ClockOutlinedIcon, TickCircleOutlinedIcon } from "sharepal-icons"

export const getVerificationStatus = (stage: VERIFICATION_STATUS) => {
  switch (stage) {
    case VERIFICATION_STATUS.Verified:
      return {
        status: "completed",
        icon: <TickCircleOutlinedIcon className='text-success-500' />,
        actionText: "Verified",
      }
    case VERIFICATION_STATUS.Received:
      return {
        status: "pending",
        icon: <ClockOutlinedIcon className='text-warning-500' />,
        actionText: "Under Review",
      }
    default:
      return {
        status: "required",
        icon: <AlertCircleIcon className='text-error-500' />,
        actionText: "Verify Now",
      }
  }
}
