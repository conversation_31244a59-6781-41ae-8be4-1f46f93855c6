import { IconVerification } from "@/components/Icons/icon-verification"
import { But<PERSON> } from "@/components/ui/button"
import { Typography } from "@/components/ui/typography"
import { useUserStore } from "@/store/user-store"
import { User } from "@/types/user"
import { USER_VERIFICATION, VERIFICATION_CASE } from "@/types/verification"

import {
  getVerificationCaseFromOrderType,
  isStatusNotRequested,
  isStatusVerified,
} from "@/utils/verification"
import React from "react"
import { ChevronRightIcon } from "sharepal-icons"

type MainHeaderContent = {
  title: string
  description: string
}

interface MainHeaderProps {
  // userVerification: USER_VERIFICATION
  user: User | null
  setOpenFaq: React.Dispatch<React.SetStateAction<boolean>>
  isFaqsLoading: boolean
  setOpenAboutVerification: (open: boolean) => void
}

export const MainHeader: React.FC<MainHeaderProps> = ({
  // userVerification,
  user,
  setOpenFaq,
  isFaqsLoading,
  setOpenAboutVerification,
}) => {
  const { userVerification } = useUserStore()
  const headerContent = React.useMemo<MainHeaderContent>(() => {
    const {
      identity_status,
      occupation_status,
      credit_status,
      last_order_type,
    } = userVerification as USER_VERIFICATION

    const verification_case = getVerificationCaseFromOrderType(last_order_type)

    let allRequiredApproved = false

    switch (verification_case) {
      case VERIFICATION_CASE.IdentityOnly:
        allRequiredApproved = isStatusVerified(identity_status)
        break
      case VERIFICATION_CASE.AllRequired:
        allRequiredApproved =
          isStatusVerified(identity_status) &&
          isStatusVerified(occupation_status) &&
          isStatusVerified(credit_status)
        break
      case VERIFICATION_CASE.IdentityAndChoice:
        allRequiredApproved =
          isStatusVerified(identity_status) &&
          (isStatusVerified(occupation_status) ||
            isStatusVerified(credit_status))
        break
      default:
        allRequiredApproved = false
    }

    if (allRequiredApproved) {
      return {
        title: "Details Submitted!",
        description:
          "All required verifications are complete. You can now proceed with your orders.",
      }
    }

    const isUnderReview = (() => {
      switch (verification_case) {
        case VERIFICATION_CASE.IdentityOnly:
          return isStatusNotRequested(identity_status)

        case VERIFICATION_CASE.AllRequired:
          return (
            isStatusNotRequested(identity_status) &&
            isStatusNotRequested(occupation_status) &&
            isStatusNotRequested(credit_status)
          )

        case VERIFICATION_CASE.IdentityAndChoice:
          return (
            isStatusNotRequested(identity_status) &&
            (isStatusNotRequested(occupation_status) ||
              isStatusNotRequested(credit_status))
          )

        default:
          return false
      }
    })()

    if (isUnderReview) {
      return {
        title: "You’re All Done!",
        description:
          "Our team is verifying your details. You'll get the status update via WhatsApp.",
      }
    }
    return {
      title: "You’re Almost There!",
      description:
        "Please complete the required verifications to ensure a smooth experience.",
    }
  }, [userVerification])

  return (
    <>
      <div className='mb-4 flex w-full flex-row gap-2 md:mb-2 md:flex-col md:gap-1'>
        <div className='relative flex w-max items-center justify-center md:w-full md:p-5'>
          <IconVerification className='size-16 md:size-20' />
        </div>

        <div className='flex flex-col items-start justify-center gap-2 md:items-center md:gap-0'>
          <Typography as='h1' className='text-gray-90 text-sh5 md:text-sh3'>
            Hi, {user?.first_name || "there"}!
          </Typography>
          <Typography
            as='p'
            className='text-h4 font-bold text-gray-900 md:text-h1'
          >
            {headerContent.title}
          </Typography>
        </div>
      </div>

      <div className='flex flex-col items-center justify-center gap-3 md:gap-2'>
        <Typography
          as='p'
          className='max-w-md text-center text-b4 text-neutral-500 md:text-b2'
        >
          {headerContent.description}
        </Typography>

        <div className='flex justify-center'>
          <Button
            variant='link'
            onClick={() => setOpenFaq(true)}
            className='hidden items-center md:flex'
            disabled={isFaqsLoading}
          >
            <Typography
              as='p'
              className='text-wrap text-center text-bt4 text-primary-500 md:text-bt3'
            >
              Frequently Asked Questions (FAQs) for Verification
            </Typography>
          </Button>
          <Button
            onClick={() => setOpenAboutVerification(true)}
            variant={"outline-primary"}
            className='flex w-max items-center justify-center md:hidden'
          >
            Learn more about Verification
            <ChevronRightIcon />
          </Button>
        </div>
      </div>
    </>
  )
}
