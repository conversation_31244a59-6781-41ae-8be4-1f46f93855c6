// "use client"

// import { Button } from "@/components/ui/button"
// import { Card } from "@/components/ui/card"
// import { Typography } from "@/components/ui/typography"
// import { fadeIn } from "@/utils/animation-variants"
// import { Player } from "@lottiefiles/react-lottie-player"
// import { motion } from "framer-motion"
// import { useRouter } from "next/navigation"

// export default function VerificationSubmitted() {
//   const router = useRouter()

//   return (
//     <Card className='relative space-y-4 border-0 bg-gray-100 p-3 md:space-y-8 md:px-16 md:py-6'>
//       {/* Header */}
//       <motion.div {...fadeIn} className='space-y-2 text-center'>
//         <div className='flex justify-center'>
//           <Player
//             src={"lottie-animations/confirm_tick.json"}
//             keepLastFrame
//             className='h-[200px] w-[200px] md:h-[240px] md:w-[240px]'
//             autoplay
//           />
//         </div>
//         <Typography
//           as={"h1"}
//           className='text-sh5 font-semibold text-gray-900 md:text-sh3'
//         >
//           Woohoo!
//         </Typography>
//         <Typography
//           as={"h2"}
//           className='text-h4 font-bold text-gray-900 md:text-h1'
//         >
//           Verification Request submitted successfully!
//         </Typography>
//         <Typography
//           as={"p"}
//           className='mx-auto max-w-md text-b4 text-neutral-500 md:text-b2'
//         >
//           Our team will review your details and verify your profile shortly if
//           everything is in order. The verification process takes 24hr to
//           complete. For any assistance, contact <NAME_EMAIL> or on
//           WhatsApp at +91-9876543210 (Available 11am to 6pm, 24x7).
//         </Typography>
//         <Typography
//           as={"p"}
//           className='mx-auto max-w-md text-b4 text-neutral-500 md:text-b2'
//         >
//           For any assistance, contact <NAME_EMAIL> or on WhatsApp
//           at +91-9876543210 (Available 11am to 6pm, 24x7).
//         </Typography>
//       </motion.div>

//       {/* Footer */}
//       <motion.div {...fadeIn} transition={{ delay: 0.5 }} className='space-y-4'>
//         <Button
//           variant={"primary"}
//           className='w-full text-bt2 text-white'
//           onClick={() => {
//             router.push("/dashboard/orders")
//           }}
//         >
//           Go to My Orders
//         </Button>
//       </motion.div>
//     </Card>
//   )
// }

const VerificationSubmitted = () => <div>VerificationSubmitted</div>

export default VerificationSubmitted
