import * as z from "zod"

const restrictedEmailDomains = [
  "gmail.com",
  "yahoo.com",
  "outlook.com",
  "hotmail.com",
]

export const VerificationTypeSchema = z.object({
  id: z.number(),
  created_at: z.number(),
  verification_type: z.string(),
  verification_text: z.string(),
  verification_desc: z.string(),
  files_count: z.number(),
})

export type VerificationType = z.infer<typeof VerificationTypeSchema>

export const OccupationVerificationSchema = z
  .object({
    occupation_type: VerificationTypeSchema,
    otp: z
      .string()
      .min(6, "OTP must be 6 digits")
      .max(6, "OTP must be 6 digits")
      .optional()
      .or(z.literal("")),
    occupation_email: z
      .string()
      .email("Please enter a valid email")
      .refine(
        (email) => {
          const domain = email.split("@")[1]
          return !restrictedEmailDomains.includes(domain)
        },
        {
          message:
            "Personal email addresses are not allowed. Use a work email.",
        },
      )
      .optional()
      .or(z.literal("")),
    occupation_document: z.any().optional(),
    occupation_info: z
      .string()
      .min(1, "This field is required")
      .optional()
      .or(z.literal("")),
  })
  .refine(
    (data) => {
      // For student or salaried occupations, email is required
      if (
        ["student", "salaried"].includes(
          data.occupation_type?.verification_type,
        )
      ) {
        return !!data.occupation_email
      }

      // For business, smi, or freelancer occupations, occupation_info is required
      if (
        ["business", "smi", "freelancer"].includes(
          data.occupation_type?.verification_type,
        )
      ) {
        return !!data.occupation_info
      }

      // For occupations requiring file upload, occupation_document is required
      if (data.occupation_type?.files_count > 0) {
        return !!data.occupation_document
      }

      return true
    },
    // {
    //   message: 'Required fields are missing',
    //   path: ['occupation_type'],
    // },
  )

export type OccupationVerification = z.infer<
  typeof OccupationVerificationSchema
>
