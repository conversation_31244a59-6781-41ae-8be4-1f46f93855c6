"use client"

import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { <PERSON>Right, Loader2, Refresh<PERSON>cw } from "lucide-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"

import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp"
import { AnimatePresence, motion } from "framer-motion"
import { FileUpload } from "../file-upload"
import {
  OccupationVerificationSchema,
  type OccupationVerification,
  type VerificationType,
} from "./schema"

import { useUserStore } from "@/store/user-store"
import { authHeader, fetchWithAuth } from "@/utils/fetchWithAuth"
import { useMutation, useQuery } from "@tanstack/react-query"
import { toast } from "sonner"

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Typography } from "@/components/ui/typography"
import {
  OtpRequestInVerification,
  trackCompleteVerification,
} from "@/lib/gtag-event"
import { customFetch } from "@/utils/customFetch"
import { isStatusRequested } from "@/utils/verification"
import axios, { AxiosProgressEvent } from "axios"
import VerificationReceivedModal from "../verification-received"

interface OccupationVerificationDialogProps {
  isOpen: boolean
  onClose: (value: boolean) => void
  unableToVerify: () => void
}

export function OccupationVerification({
  unableToVerify,
  onClose,
}: OccupationVerificationDialogProps) {
  const [isOtpSent, setIsOtpSent] = useState(false)
  const [otpTimer, setOtpTimer] = useState(30)
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {},
  )
  const [uploadError, setUploadError] = useState<Record<string, string>>({})
  const [isUploading, setIsUploading] = useState<Record<string, boolean>>({})
  const [_, setIsUploadSuccess] = useState<Record<string, boolean>>({})
  const [open, setOpen] = useState(false)
  // const queryClient = useQueryClient()

  const { user, setUser, userVerification } = useUserStore()

  const form = useForm<OccupationVerification>({
    resolver: zodResolver(OccupationVerificationSchema),
    defaultValues: {
      occupation_type: undefined,
      occupation_email: "",
      occupation_info: "",
      otp: "",
      occupation_document: undefined,
    },
    mode: "onChange", // Changed from onBlur to onChange for better validation UX
  })

  const [isUnableToVerify, setIsUnableToVerify] = useState(false)

  // Get the current selected occupation type
  const selectedOccupationType = form.watch("occupation_type")
  const needsEmail =
    selectedOccupationType?.verification_type === "student" ||
    selectedOccupationType?.verification_type === "salaried"
  const needsFileUpload =
    (selectedOccupationType?.files_count &&
      selectedOccupationType.files_count > 0) ||
    isUnableToVerify
  const needsInfo =
    selectedOccupationType &&
    !needsEmail &&
    !needsFileUpload &&
    !isUnableToVerify

  const [needsEmailType, setNeedEmailType] = useState<"otp" | "file">("otp")
  const occupationDocumentFile = form.watch("occupation_document")
  const occupationInfo = form.watch("occupation_info")

  // Reset fields when occupation type changes
  useEffect(() => {
    if (selectedOccupationType) {
      form.resetField("otp")
      form.resetField("occupation_document")
      form.resetField("occupation_email")
      form.resetField("occupation_info")
      setIsOtpSent(false)
    }
  }, [selectedOccupationType, form])

  // Handle OTP timer
  useEffect(() => {
    let timer: NodeJS.Timeout
    if (isOtpSent && otpTimer > 0) {
      timer = setInterval(() => {
        setOtpTimer((prev) => prev - 1)
      }, 1000)
    }
    return () => clearInterval(timer)
  }, [isOtpSent, otpTimer])

  // Fetch Verification Types
  const { data: verificationTypes } = useQuery<VerificationType[]>({
    queryKey: ["get_verification_types"],
    queryFn: async () => {
      const response = await customFetch(
        "https://api.sharepal.in/api:BV_IWA_a/get_verification_types",
        { method: "GET" },
      )
      return response
    },
    refetchOnMount: true,
  })

  // Send OTP mutation
  const { mutate: handleSendOtp, isPending: isSendingOTP } = useMutation({
    mutationFn: async () => {
      form.setValue("otp", "")

      // Validate the email first
      const emailValid = await form.trigger("occupation_email")
      if (!emailValid) {
        throw new Error(
          JSON.stringify({ message: "Please enter a valid email" }),
        )
      }

      const email = form.getValues("occupation_email")
      if (!email) {
        throw new Error(JSON.stringify({ message: "Email is required" }))
      }

      const response = await fetchWithAuth(
        "https://api.sharepal.in/api:YeisLDqw:v1/email/send_otp",
        {
          method: "POST",
          body: JSON.stringify({ occupation_email: email }),
        },
      )

      if (user) {
        OtpRequestInVerification("Get OTP Initiated", user, "Occupation")
      }

      return response
    },
    onSuccess: () => {
      toast.success("OTP Sent")
      setOtpTimer(30)
      setIsOtpSent(true)
    },
    onError: (error) => {
      let errorMessage = "Something Went Wrong"
      try {
        const parsedError = JSON.parse(error.message)
        errorMessage = parsedError?.message || errorMessage
      } catch (e) {
        // If parsing fails, use the default message
        console.error(e)
      }
      toast.error(errorMessage)
    },
  })

  // Verify OTP mutation
  const { mutate: handleVerifyOtp, isPending: isSubmitting } = useMutation({
    mutationFn: async () => {
      const token = localStorage.getItem("token")
      if (!token) {
        throw new Error(JSON.stringify({ message: "Authentication required" }))
      }

      // Prepare request data based on verification type
      let data
      let url: "gst_detail" | "other_occupation" | "email/verify_otp" =
        "email/verify_otp"
      const occupationType =
        form.getValues("occupation_type")?.verification_type

      if (!occupationType) {
        throw new Error(
          JSON.stringify({ message: "Please select an occupation type" }),
        )
      }

      if (["student", "salaried"].includes(occupationType)) {
        data = {
          occupation_email: form.getValues("occupation_email"),
          otp: form.getValues("otp"),
          occupation_type: occupationType,
        }
        url = "email/verify_otp"
      } else if (["smi", "freelancer"].includes(occupationType)) {
        data = {
          occupation_type: occupationType,
          occupation_info: form.getValues("occupation_info"),
        }
        url = "other_occupation"
      } else if (occupationType === "business") {
        data = {
          gst_number: form.getValues("occupation_info"),
        }
        url = "gst_detail"
      } else {
        throw new Error(JSON.stringify({ message: "Invalid occupation type" }))
      }

      const response = await fetchWithAuth(
        `https://api.sharepal.in/api:YeisLDqw/${url}`,
        {
          method: "POST",
          body: JSON.stringify(data),
        },
      )

      if (user) {
        trackCompleteVerification("OTP Verification", user, "Occupation", true)
      }

      return response
    },
    onSuccess: () => {
      toast.success("Successfully Submitted")
      if (user) {
        trackCompleteVerification(
          "Occupation Verification",
          user,
          "Occupation",
          true,
        )
      }
      // queryClient.invalidateQueries({ queryKey: ["user_verification"] })
      setOpen(true)
      // onClose(false)
    },
    onError: (error) => {
      if (user) {
        trackCompleteVerification("Verification", user, "Occupation", false)
      }

      let errorMessage = "Something Went Wrong"
      try {
        const parsedError = JSON.parse(error.message)
        errorMessage = parsedError?.message || errorMessage
      } catch (e) {
        // If parsing fails, use the default message
        console.error(e)
      }

      toast.error(errorMessage)
    },
  })

  // Handle file upload
  const { mutate: handleFileUpload } = useMutation({
    mutationFn: async (fileType: "occupation_document") => {
      form.clearErrors(fileType)
      setUploadError({})
      setIsUploading({ [fileType]: true })

      const token = localStorage.getItem("token")
      if (!token) {
        throw new Error("Authentication required")
      }

      const file = form.getValues(fileType)
      if (!file) {
        throw new Error("No file selected")
      }

      const formData = new FormData()
      formData.append("file1", file)
      formData.append("file_type", "occupation")

      const response = await axios.post(
        "https://api.sharepal.in/api:AIoqxnqr/verification/multiple-file-upload",
        formData,
        {
          headers: authHeader(token, "multipart/form-data"),
          onUploadProgress: (progressEvent: AxiosProgressEvent) => {
            const { loaded, total } = progressEvent
            if (total) {
              const percentCompleted = Math.round((loaded * 100) / total)
              setUploadProgress({ [fileType]: percentCompleted })
            }
          },
        },
      )

      return { response, fileType }
    },
    onSuccess: ({ response, fileType }) => {
      setIsUploading({ [fileType]: false })

      if (response.status === 200 && response.data) {
        setIsUploadSuccess({ [fileType]: true })

        if (user) {
          const data = response.data
          setUser({
            ...user,
            occupation_email: data?.occupation_email,
            occupation_kyc_received: data?.occupation_kyc_received,
            occupation_type: data?.occupation_type,
            occupation_verified_type: data?.occupation_verified_type,
          })
        }

        // queryClient.invalidateQueries({ queryKey: ["user_verification"] })
        setOpen(true)
        // onClose(false)
      } else {
        setUploadError({ [fileType]: "Upload failed. Please try again." })
        setIsUploadSuccess({ [fileType]: false })
        toast.error("Upload failed. Please try again.")
      }
    },
    onError: (error, fileType) => {
      setIsUploading({ [fileType]: false })
      setUploadError({ [fileType]: "Upload failed. Please try again." })
      setIsUploadSuccess({ [fileType]: false })
      toast.error("Upload failed. Please try again.")
    },
  })

  // Form submission handler
  const onSubmit = () => {
    if (needsFileUpload) {
      // if (isUploadSuccess.occupation_document) {
      //   toast.success("Occupation Submitted Successfully")
      //   queryClient.invalidateQueries({ queryKey: ["user_verification"] })
      //   onClose(true)
      // } else {
      //   toast.error("Please upload the required document")
      // }
      handleFileUpload("occupation_document")
    } else {
      handleVerifyOtp()
    }
  }

  return (
    <>
      <VerificationReceivedModal
        onClose={onClose}
        messages={{
          buttonText: isStatusRequested(userVerification?.identity_status ?? "")
            ? "Verify Identity"
            : isStatusRequested(userVerification?.credit_status ?? "")
              ? "Verify Pan"
              : "Continue",
        }}
        open={open}
        setOpen={setOpen}
      />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-3'>
          <div className='min-h-[50vh] space-y-6 max-md:p-4 md:min-h-max'>
            <Card className='shadow-none border-0'>
              <CardContent className='p-0'>
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className='space-y-4'
                >
                  <div className='space-y-4 overflow-hidden rounded-lg bg-neutral-150 p-4'>
                    {/* Occupation Type Selection */}
                    <FormField
                      control={form.control}
                      name='occupation_type'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Occupation Type</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              try {
                                const parsedValue = JSON.parse(value)
                                field.onChange(parsedValue)
                                setIsUnableToVerify(false)
                              } catch (e) {
                                console.error(
                                  "Failed to parse occupation type:",
                                  e,
                                )
                              }
                            }}
                            value={
                              field.value
                                ? JSON.stringify(field.value)
                                : undefined
                            }
                          >
                            <FormControl>
                              <SelectTrigger className='shadow-none w-full rounded-2xl border-2 border-gray-200 bg-gray-100 px-3 py-5 focus:ring-0'>
                                <SelectValue placeholder='Select Occupation' />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className='shadow-none border-0 outline-none ring-0'>
                              {verificationTypes?.map((item) => (
                                <SelectItem
                                  key={item.id}
                                  value={JSON.stringify(item)}
                                >
                                  {item.verification_text}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Render different form fields based on occupation type */}
                    {selectedOccupationType && (
                      <AnimatePresence mode='wait'>
                        {/* Case 1: Email and OTP verification */}

                        {/* Case 2: Info fields (for business, freelancer, etc.) */}
                        {needsInfo && !isUnableToVerify && (
                          <motion.div
                            key='info-verification'
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <FormField
                              control={form.control}
                              name='occupation_info'
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>
                                    {selectedOccupationType.verification_desc}
                                    <span className='text-destructive-500'>
                                      *
                                    </span>
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder={
                                        selectedOccupationType.verification_desc
                                      }
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>
                        )}

                        {/* Case 3: File upload */}
                        {needsFileUpload && (
                          <motion.div
                            key='file-verification'
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <FormField
                              control={form.control}
                              name='occupation_document'
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <FileUpload
                                      label={
                                        selectedOccupationType.verification_desc
                                      }
                                      onChange={(file) => {
                                        field.onChange(file)
                                        setUploadError({
                                          occupation_document: "",
                                        })
                                        setIsUploading({
                                          occupation_document: false,
                                        })
                                        setUploadError({})
                                        // if (file) {
                                        //   handleFileUpload(
                                        //     "occupation_document",
                                        //   )
                                        // }
                                      }}
                                      value={field.value}
                                      progress={
                                        uploadProgress.occupation_document
                                      }
                                      error={uploadError.occupation_document}
                                      isUploading={
                                        isUploading.occupation_document
                                      }
                                      onRetry={
                                        () => {}
                                        // handleFileUpload("occupation_document")
                                      }
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    )}
                  </div>

                  {needsEmail && !isUnableToVerify && (
                    <RadioGroup
                      onValueChange={(value: "otp" | "file") =>
                        setNeedEmailType(value)
                      }
                      value={needsEmailType}
                      className='space-y-4'
                    >
                      <Card className='shadow-none border-0'>
                        <CardContent className='p-0'>
                          <div className='rounded-lg bg-neutral-150'>
                            <div className='flex items-center space-x-2 p-4'>
                              <RadioGroupItem value='otp' id='otp' />

                              <Label
                                htmlFor='otp'
                                className='flex items-center gap-2'
                              >
                                <Typography as={"p"} className='text-sh5'>
                                  Email Verification
                                </Typography>
                                <Badge
                                  variant='secondary'
                                  className='ml-2 rounded-full bg-primary-100 text-b6 text-primary-500 hover:bg-primary-100'
                                >
                                  Safer & Faster
                                </Badge>
                              </Label>
                            </div>

                            <AnimatePresence>
                              {needsEmailType === "otp" && (
                                <motion.div
                                  key='online'
                                  initial={{ height: 0, opacity: 0 }}
                                  animate={{ height: "auto", opacity: 1 }}
                                  exit={{ height: 0, opacity: 0 }}
                                  transition={{ duration: 0.2 }}
                                  className='overflow-hidden'
                                >
                                  <div className='px-4'>
                                    <motion.div
                                      key='email-verification'
                                      initial={{ opacity: 0, height: 0 }}
                                      animate={{ opacity: 1, height: "auto" }}
                                      exit={{ opacity: 0, height: 0 }}
                                      transition={{ duration: 0.2 }}
                                      className='space-y-4'
                                    >
                                      <FormField
                                        control={form.control}
                                        name='occupation_email'
                                        render={({ field }) => (
                                          <FormItem>
                                            <FormLabel>
                                              {
                                                selectedOccupationType.verification_desc
                                              }
                                              <span className='text-destructive-500'>
                                                *
                                              </span>
                                            </FormLabel>
                                            <FormControl>
                                              <Input
                                                placeholder={
                                                  selectedOccupationType.verification_desc
                                                }
                                                {...field}
                                              />
                                            </FormControl>
                                            <FormMessage />
                                          </FormItem>
                                        )}
                                      />

                                      {!isOtpSent ? (
                                        <Button
                                          type='button'
                                          variant='link'
                                          className='p-0 text-primary-500'
                                          onClick={() => handleSendOtp()}
                                          disabled={
                                            !!form.formState.errors
                                              .occupation_email ||
                                            isSendingOTP ||
                                            !form.getValues("occupation_email")
                                          }
                                        >
                                          {isSendingOTP ? (
                                            <Loader2 className='animate-spin' />
                                          ) : (
                                            <>
                                              Send OTP
                                              <ArrowRight className='ml-1 h-4 w-4' />
                                            </>
                                          )}
                                        </Button>
                                      ) : (
                                        <motion.div
                                          initial={{ opacity: 0 }}
                                          animate={{ opacity: 1 }}
                                          className='space-y-4'
                                        >
                                          <FormField
                                            control={form.control}
                                            name='otp'
                                            render={({ field }) => (
                                              <FormItem>
                                                <FormLabel>
                                                  Enter OTP
                                                  <span className='text-destructive-500'>
                                                    *
                                                  </span>
                                                </FormLabel>
                                                <FormControl>
                                                  <InputOTP
                                                    value={field.value || ""}
                                                    onChange={(value) =>
                                                      field.onChange(value)
                                                    }
                                                    maxLength={6}
                                                  >
                                                    <InputOTPGroup className='w-full justify-center gap-1'>
                                                      {Array.from({
                                                        length: 6,
                                                      }).map((_, index) => (
                                                        <InputOTPSlot
                                                          className='h-12 w-12 rounded-2xl border-2 border-neutral-200'
                                                          key={index}
                                                          index={index}
                                                        />
                                                      ))}
                                                    </InputOTPGroup>
                                                  </InputOTP>
                                                </FormControl>
                                                <FormMessage />
                                              </FormItem>
                                            )}
                                          />

                                          {otpTimer > 0 ? (
                                            <p className='text-sm text-neutral-500'>
                                              Resend OTP in {otpTimer} sec
                                            </p>
                                          ) : (
                                            <Button
                                              type='button'
                                              variant='link'
                                              className='p-0 text-primary-500'
                                              onClick={() => handleSendOtp()}
                                            >
                                              <RefreshCcw className='mr-1 h-4 w-4' />
                                              Resend OTP
                                            </Button>
                                          )}
                                        </motion.div>
                                      )}
                                    </motion.div>
                                  </div>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                          <div className='rounded-lg bg-neutral-150'>
                            <div className='mt-4 flex items-center space-x-2 p-4'>
                              <RadioGroupItem value='file' id='file' />
                              <Label htmlFor='file' className='flex'>
                                <Typography as={"p"} className='text-sh5'>
                                  Upload{" "}
                                  {
                                    form.getValues("occupation_type")
                                      .verification_type
                                  }{" "}
                                  Identity proof
                                </Typography>
                              </Label>
                            </div>

                            <AnimatePresence>
                              {needsEmailType === "file" && (
                                <motion.div
                                  key='offline'
                                  initial={{ height: 0, opacity: 0 }}
                                  animate={{ height: "auto", opacity: 1 }}
                                  exit={{ height: 0, opacity: 0 }}
                                  transition={{ duration: 0.2 }}
                                  className='overflow-hidden'
                                >
                                  <div className='px-4'>
                                    <FormField
                                      control={form.control}
                                      name='occupation_document'
                                      render={({ field }) => (
                                        <FormItem>
                                          <FormControl>
                                            <FileUpload
                                              label={`Upload ${form.getValues("occupation_type").verification_type} Identity proof`}
                                              onChange={(file) => {
                                                field.onChange(file)
                                                setUploadError({
                                                  occupation_document: "",
                                                })
                                                setIsUploading({
                                                  occupation_document: false,
                                                })
                                                // if (file) {
                                                //   handleFileUpload(
                                                //     "occupation_document",
                                                //   )
                                                // }
                                              }}
                                              value={field.value}
                                              progress={
                                                uploadProgress.occupation_document
                                              }
                                              error={
                                                uploadError.occupation_document
                                              }
                                              isUploading={
                                                isUploading.occupation_document
                                              }
                                              onRetry={
                                                () => {}
                                                // handleFileUpload(
                                                //   "occupation_document",
                                                // )
                                              }
                                            />
                                          </FormControl>
                                          <FormMessage />
                                        </FormItem>
                                      )}
                                    />
                                  </div>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                        </CardContent>
                      </Card>
                    </RadioGroup>
                  )}
                </motion.div>
              </CardContent>
            </Card>
          </div>

          <div className='max-sm:sticky max-sm:bottom-0 max-sm:w-full max-sm:bg-gray-100 max-sm:p-4 max-sm:shadow-sm md:space-y-3'>
            {/* Submit button */}
            <Button
              type={
                needsEmailType == "file" || needsFileUpload
                  ? "button"
                  : "submit"
              }
              className='w-full bg-primary-500 hover:bg-primary-600'
              disabled={
                isSubmitting ||
                isUploading["occupation_document"] ||
                (needsEmailType == "otp" &&
                  !needsInfo &&
                  !needsFileUpload &&
                  (!isOtpSent ||
                    !((form.getValues("otp") ?? "")?.length == 6))) ||
                (needsEmailType == "file" && !occupationDocumentFile) ||
                (needsFileUpload && !occupationDocumentFile) ||
                (needsInfo && !occupationInfo)
              }
              onClick={() => {
                if (needsEmailType == "file" || needsFileUpload)
                  handleFileUpload("occupation_document")
              }}
            >
              {needsEmailType == "file" || needsFileUpload ? (
                isUploading["occupation_document"] ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Verifying...
                  </>
                ) : (
                  "Submit"
                )
              ) : (
                <>
                  {isSubmitting ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Verifying...
                    </>
                  ) : (
                    "Submit For Occupation Verification."
                  )}
                </>
              )}
              {/* {isSubmitting ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Verifying...
                </>
              ) : (
                "Submit For Occupation Verification."
              )} */}
            </Button>

            <Button
              type='button'
              variant='link'
              className='w-full text-center text-primary-500'
              onClick={() => {
                unableToVerify()
              }}
            >
              Unable to Verify {!needsFileUpload && " Online"}
            </Button>

            {isUnableToVerify && (
              <Button
                type='button'
                variant='outline-primary'
                className='w-full text-center text-primary-500'
                onClick={() => {
                  setIsUnableToVerify(false)
                }}
              >
                Verify Online
              </Button>
            )}
          </div>
        </form>
      </Form>
    </>
  )
}
