import useWindowSize from "@/hooks/use-window-resize"
import { cn } from "@/lib/utils"

import { AnimatePresence, motion } from "framer-motion"
import { useMemo } from "react"

import { AdaptiveWrapper } from "../modals/adaptive-wrapper"

interface DeviceWrapperProps {
  children: React.ReactNode
  isOpen: boolean
  onClose: (value: boolean) => void
  title?: string
  description?: string
  className?: string
}

const DeviceWrapper = ({
  children,
  isOpen,
  onClose,
  title,
  description,
  className,
}: DeviceWrapperProps) => {
  const size = useWindowSize()

  const isMobile = useMemo(
    () => (size?.width ? size.width <= 640 : false),
    [size?.width],
  )

  if (isMobile) {
    return (
      <AdaptiveWrapper
        open={isOpen}
        title={title}
        className={cn(className)}
        mobile={{ type: "drawer" }}
        onOpenChange={onClose}
      >
        {description && (
          <p className='px-4 py-3 text-b6 text-neutral-600'>{description}</p>
        )}
        {children}
      </AdaptiveWrapper>
    )
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: "auto", opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.3 }}
          className={
            className ||
            "overflow-hidden rounded-b-xl border-x border-b border-neutral-150 bg-gray-100 p-4"
          }
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default DeviceWrapper
