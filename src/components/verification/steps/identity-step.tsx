import { VERIFICATION_STATUS } from "@/types/verification"
import { IdentityCardVerticalOutlineIcon } from "sharepal-icons"
import { IdentityVerification } from "../identity-verification"
import StepWrapper from "../verification-steps-new/verification-step-wrapper"

interface IdentityStepProps {
  status: VERIFICATION_STATUS
  isExpanded: boolean
  onAction: () => void
  onUnableToVerify: () => void
}

const IdentityStep = ({
  status,
  isExpanded,
  onAction,
  onUnableToVerify,
}: IdentityStepProps) => (
  <StepWrapper
    icon={
      <IdentityCardVerticalOutlineIcon className='h-6 w-6 text-secondary-800 md:h-9 md:w-9' />
    }
    title='Identity Verification'
    description='A government-issued ID to confirm your identity and ensure secure transactions.'
    status={status}
    onAction={onAction}
    isExpanded={isExpanded}
    // isHeader
  >
    <IdentityVerification
      isOpen={isExpanded}
      unableToVerify={onUnableToVerify}
      onClose={() => onAction()}
    />
  </StepWrapper>
)

export default IdentityStep
