import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Typography } from "@/components/ui/typography"
import { VERIFICATION_STATUS } from "@/types/verification"
import { AnimatePresence, motion } from "framer-motion"
import { useState } from "react"
import { WorkBriefCaseOutlinedIcon } from "sharepal-icons"

import { cn } from "@/lib/utils"
import { OccupationVerification } from "../occupation-verification"
import { PanVerification } from "../pan-verification"
import StepWrapper from "../verification-steps-new/verification-step-wrapper"

interface ChoiceStepProps {
  status: VERIFICATION_STATUS
  isExpanded: boolean
  onAction: () => void
  onUnableToVerify: () => void
}

const ChoiceStep = ({
  status,
  isExpanded,
  onAction,
  onUnableToVerify,
}: ChoiceStepProps) => {
  const [selectedOption, setSelectedOption] = useState<
    "occupation" | "pan" | null
  >("occupation")

  const handleOptionSelect = (option: "occupation" | "pan") => {
    setSelectedOption(option)
  }

  const handleClose = () => {
    setSelectedOption(null)
    onAction()
  }

  return (
    <StepWrapper
      icon={
        <WorkBriefCaseOutlinedIcon className='h-6 w-6 text-secondary-800 md:h-9 md:w-9' />
      }
      title='Occupation or PAN Verification'
      description='Please complete either Occupation or PAN verification to proceed.'
      status={status}
      onAction={onAction}
      isExpanded={isExpanded}
    >
      <div className='min-h-[70vh] space-y-6'>
        <RadioGroup
          value={selectedOption || undefined}
          onValueChange={(value) =>
            handleOptionSelect(value as "occupation" | "pan")
          }
          className='space-y-4'
        >
          <Card className='shadow-none border-0'>
            <CardContent className='p-0'>
              {/* Occupation Option */}
              <div
                className={cn(
                  "rounded-lg bg-neutral-150",
                  selectedOption === "occupation" && "bg-gray-100",
                )}
              >
                <div className='flex items-center space-x-2 p-4'>
                  <RadioGroupItem value='occupation' id='occupation' />
                  <Label
                    htmlFor='occupation'
                    className='flex items-center gap-2'
                  >
                    <Typography as='p' className='text-sh5'>
                      Occupation Verification
                    </Typography>
                    <Badge
                      variant='secondary'
                      className='ml-2 rounded-full bg-secondary-100 text-b6 text-secondary-900 hover:bg-secondary-100'
                    >
                      Alternative
                    </Badge>
                  </Label>
                </div>
                <AnimatePresence>
                  {selectedOption === "occupation" && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className='overflow-hidden'
                    >
                      <div className='space-y-4 p-4'>
                        <OccupationVerification
                          isOpen={true}
                          unableToVerify={onUnableToVerify}
                          onClose={handleClose}
                        />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* PAN Option */}
              <div
                className={cn(
                  "mt-4 rounded-lg bg-neutral-150",
                  selectedOption === "pan" && "bg-gray-100",
                )}
              >
                <div className='flex items-center space-x-2 p-4'>
                  <RadioGroupItem value='pan' id='pan' />
                  <Label htmlFor='pan' className='flex items-center gap-2'>
                    <Typography as='p' className='text-sh5'>
                      PAN Verification
                    </Typography>

                    <Badge
                      variant='secondary'
                      className='ml-2 rounded-full bg-primary-100 text-b6 text-primary-500 hover:bg-primary-100'
                    >
                      Recommended
                    </Badge>
                  </Label>
                </div>
                <AnimatePresence>
                  {selectedOption === "pan" && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className='overflow-hidden'
                    >
                      <div className='space-y-4 p-4'>
                        <PanVerification
                          isOpen={true}
                          unableToVerify={onUnableToVerify}
                          onClose={handleClose}
                        />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </CardContent>
          </Card>
        </RadioGroup>
      </div>
    </StepWrapper>
  )
}

export default ChoiceStep
