import { VERIFICATION_STATUS } from "@/types/verification"
import { WorkBriefCaseOutlinedIcon } from "sharepal-icons"

import { OccupationVerification } from "../occupation-verification"
import StepWrapper from "../verification-steps-new/verification-step-wrapper"

interface OccupationStepProps {
  status: VERIFICATION_STATUS
  isExpanded: boolean
  onAction: () => void
  onUnableToVerify: () => void
  isOptional?: boolean
}

const OccupationStep = ({
  status,
  isExpanded,
  onAction,
  onUnableToVerify,
  isOptional = false,
}: OccupationStepProps) => (
  <StepWrapper
    icon={
      <WorkBriefCaseOutlinedIcon className='h-6 w-6 text-secondary-800 md:h-9 md:w-9' />
    }
    title='Occupation Verification'
    description='This helps us understand your profession, reducing the risk of fraudulent activity.'
    status={status}
    onAction={onAction}
    isExpanded={isExpanded}
    isOptional={isOptional}
  >
    <OccupationVerification
      isOpen={isExpanded}
      unableToVerify={onUnableToVerify}
      onClose={() => onAction()}
    />
  </StepWrapper>
)

export default OccupationStep
