import * as z from "zod"

export const panVerificationSchema = z
  .object({
    verificationType: z.enum(["online", "offline"]),
    panNumber: z
      .string()
      .regex(
        /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
        "Invalid PAN format (e.g., **********)",
      )
      .optional(),
    frontFile: z.any().optional(),
    full_name: z.string().min(1, "Full name is required").optional(),
    mobile_number: z
      .string()
      .min(10, "Must be 10 digits")
      .max(10, "Must be 10 digits")
      .regex(/^\d+$/, "Must be numeric")
      .optional(),
  })
  .superRefine((data, ctx) => {
    if (data.verificationType === "offline" && !data.frontFile) {
      ctx.addIssue({
        path: ["frontFile"],
        code: z.ZodIssueCode.custom,
        message: "frontFile is required for offline verification",
      })
    }
  })

export type PanVerificationForm = z.infer<typeof panVerificationSchema>
