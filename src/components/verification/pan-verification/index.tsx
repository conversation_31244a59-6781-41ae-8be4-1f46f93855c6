"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { zodResolver } from "@hookform/resolvers/zod"
import { AnimatePresence, motion } from "framer-motion"
import { Loader2 } from "lucide-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { FileUpload } from "../file-upload"
import { panVerificationSchema, type PanVerificationForm } from "./schema"

import { Card, CardContent } from "@/components/ui/card"

import { useUserStore } from "@/store/user-store"
import {
  authHeader,
  fetchWithAuth,
  fetchWithAuthPost,
} from "@/utils/fetchWithAuth"
import { useMutation } from "@tanstack/react-query"
import { toast } from "sonner"

import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Typography } from "@/components/ui/typography"
import { trackCompleteVerification } from "@/lib/gtag-event"
import { isStatusRequested } from "@/utils/verification"
import axios, { AxiosProgressEvent } from "axios"
import { WhatsAppLogoFilledIcon } from "sharepal-icons"
import VerificationReceivedModal from "../verification-received"

interface PanVerificationDialogProps {
  isOpen: boolean
  onClose: (value: boolean) => void
  unableToVerify: () => void
}

export function PanVerification({
  isOpen,
  onClose,
  unableToVerify,
}: PanVerificationDialogProps) {
  // const queryClient = useQueryClient()
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {},
  )
  const [uploadError, setUploadError] = useState<Record<string, string>>({})
  const [isUploading, setIsUploading] = useState<Record<string, boolean>>({})
  const [_, setIsUploadSuccess] = useState<Record<string, boolean>>({})

  const [open, setOpen] = useState(false)
  const { user, setUser, userVerification } = useUserStore()
  const form = useForm<PanVerificationForm>({
    resolver: zodResolver(panVerificationSchema),
    defaultValues: {
      verificationType: "online",
      panNumber: "",
      full_name: "",
      mobile_number: "",
      frontFile: null,
      // backFile: null,
    },
    mode: "onChange",
  })

  // Watch verificationType to dynamically adjust form requirements
  const verificationType = form.watch("verificationType")
  const frontFile = form.watch("frontFile")

  // Reset dependent fields when verificationType changes
  useEffect(() => {
    if (verificationType === "online") {
      form.resetField("frontFile")
      // form.resetField("backFile")
    } else {
      form.resetField("panNumber")
      form.resetField("full_name")
      form.resetField("mobile_number")
    }
    setIsUploadSuccess({})
    setUploadProgress({})
    setUploadError({})
    setIsUploading({})
    setPanFetch(false)
  }, [verificationType, form])

  // Handle file upload for PAN
  const { mutate: handleFileUpload } = useMutation({
    mutationFn: async (fileType: "frontFile") => {
      setIsUploading({ [fileType]: true })
      const file = form.getValues(fileType)

      const token = localStorage.getItem("token")
      if (!token) throw new Error("Authentication required")
      if (!file) throw new Error("No file selected")

      const formData = new FormData()
      formData.append("file1", file)
      formData.append("file_type", "pan")

      const config = {
        headers: authHeader(token, "multipart/form-data"),
        method: "POST",
        body: formData,
      }

      const res = await axios.post(
        "https://api.sharepal.in/api:AIoqxnqr/verification/multiple-file-upload",
        formData,
        {
          ...config,
          onUploadProgress: (progressEvent: AxiosProgressEvent) => {
            const { loaded, total } = progressEvent
            if (total) {
              const percentCompleted = Math.round((loaded * 100) / total)
              setUploadProgress({ [fileType]: percentCompleted })
            }
          },
        },
      )
      setIsUploading({ [fileType]: false })
      return res
    },
    onSuccess: (res, fileType) => {
      if (res.status === 200) {
        const data = res.data
        if (user) {
          setUser({
            ...user,
            credit_report: data?.credit_report,
            credit_verification_type: data?.credit_verification_type,
          })
        }
        setIsUploadSuccess((prev) => ({
          ...prev,
          [fileType]: true,
        }))
        // Invalidate queries after successful file upload
        // queryClient.invalidateQueries({ queryKey: ["user_verification"] })
        toast.success(
          "Pan Uploaded Successfully. Please wait for verification.",
        )
        setOpen(true)
        // onClose(false)
      } else {
        setUploadError((prev) => ({
          ...prev,
          [fileType]: "Upload failed. Please try again.",
        }))
        setIsUploadSuccess((prev) => ({
          ...prev,
          [fileType]: false,
        }))
      }
    },
    onError: (error, fileType) => {
      setUploadError((prev) => ({
        ...prev,
        [fileType]: "Upload failed. Please try again.",
      }))
      setIsUploadSuccess((prev) => ({
        ...prev,
        [fileType]: false,
      }))
      setIsUploading({ [fileType]: false }) // Reset uploading state on error
    },
  })

  const [panFetch, setPanFetch] = useState(false)

  const { mutate: fetchPanDetails, isPending: fetchPanDetailsLoading } =
    useMutation({
      mutationFn: async () => {
        const token = localStorage.getItem("token")
        if (!token) throw new Error("Authentication required")

        const data = await fetchWithAuthPost<string>(
          "https://api.sharepal.in/api:YeisLDqw/pan_lite",
          {
            pan_number: form.getValues("panNumber"),
          },
        )
        return data
      },
      onSuccess: (data: string) => {
        if (data) form.setValue("full_name", data)
        setPanFetch(true)
      },
      onError: (error) => {
        setPanFetch(false)
        toast.error(error.message ?? "Something Went Wrong")
      },
    })

  const { mutate: onSubmit, isPending: isSubmitting } = useMutation({
    mutationFn: async () => {
      // For online mode, submit the PAN details
      const token = localStorage.getItem("token")
      if (!token) throw new Error("Authentication required")

      return await fetchWithAuth(
        "https://api.sharepal.in/api:YeisLDqw/fetch_credit_report",
        {
          method: "POST",
          body: JSON.stringify({
            full_name: form.getValues("full_name"),
            PAN: form.getValues("panNumber"),
            mobile_number: form.getValues("mobile_number"),
          }),
        },
      )
    },
    onSuccess: (data) => {
      if (verificationType === "online") {
        if (data?.calling_number) {
          toast.success("Successfully Submitted")
          if (user) {
            trackCompleteVerification("Pan Verification", user, "Pan", true)
          }
        }
      } else {
        toast.success("PAN Details Submitted Successfully")
        if (user) {
          trackCompleteVerification("Pan Verification", user, "Pan", true)
        }
      }
      setOpen(true)
      // onClose(false)
      // queryClient.invalidateQueries({ queryKey: ["user_verification"] })
    },
    onError: (error) => {
      if (user) {
        trackCompleteVerification("Pan Verification", user, "Pan", false)
      }

      if (error.message?.toLowerCase().includes("consumer not found")) {
        toast.error("Thankyou for submitting the details")
      } else {
        toast.error(error.message ?? "Something Went Wrong")
      }
    },
  })

  const hanldeSubmit = () => {
    if (verificationType == "online") onSubmit()
    if (verificationType == "offline") handleFileUpload("frontFile")
  }

  useEffect(() => {
    if (!isOpen) {
      form.reset()
      setPanFetch(false)
    }
  }, [form, isOpen])

  return (
    <>
      <VerificationReceivedModal
        onClose={onClose}
        messages={{
          buttonText: isStatusRequested(userVerification?.identity_status ?? "")
            ? "Verify Identity"
            : isStatusRequested(userVerification?.occupation_status ?? "")
              ? "Verify Occupation"
              : "Continue",
        }}
        open={open}
        setOpen={setOpen}
      />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(() => hanldeSubmit())}
          className='space-y-6'
        >
          <div className='min-h-[50vh] space-y-6 max-md:p-4 md:min-h-max'>
            <FormField
              control={form.control}
              name='verificationType'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Card className='shadow-none border-0'>
                      <CardContent className='p-0'>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          className='space-y-4'
                        >
                          <Card className='shadow-none border-0'>
                            <CardContent className='!p-0'>
                              <div className='rounded-lg bg-neutral-150'>
                                <div className='flex items-center space-x-2 p-4'>
                                  <RadioGroupItem value='online' id='online' />

                                  <Label
                                    htmlFor='online'
                                    className='flex items-center gap-2'
                                  >
                                    <Typography
                                      as={"p"}
                                      className='text-center text-sh5'
                                    >
                                      Pan Verification - Online
                                    </Typography>
                                    <Badge
                                      variant='secondary'
                                      className='ml-2 min-w-max rounded-full bg-primary-100 text-b6 text-primary-500 hover:bg-primary-100'
                                    >
                                      Safer & Faster
                                    </Badge>
                                  </Label>
                                </div>

                                <AnimatePresence>
                                  {/* Online */}
                                  {field.value === "online" && (
                                    <motion.div
                                      key='online'
                                      initial={{ height: 0, opacity: 0 }}
                                      animate={{ height: "auto", opacity: 1 }}
                                      exit={{ height: 0, opacity: 0 }}
                                      transition={{ duration: 0.2 }}
                                      className='overflow-hidden'
                                    >
                                      <div className='ml-2 space-y-4 p-4'>
                                        <FormField
                                          control={form.control}
                                          name='panNumber'
                                          render={({ field }) => (
                                            <FormItem>
                                              <FormLabel>
                                                Enter your PAN
                                                <span className='text-destructive-500'>
                                                  *
                                                </span>
                                              </FormLabel>
                                              <FormControl>
                                                <Input
                                                  placeholder='Enter your PAN number (e.g., **********)'
                                                  {...field}
                                                  onChange={(e) =>
                                                    field.onChange(
                                                      e.target.value.toUpperCase(),
                                                    )
                                                  }
                                                />
                                              </FormControl>
                                              <FormMessage />
                                            </FormItem>
                                          )}
                                        />
                                        {panFetch && (
                                          <>
                                            <FormField
                                              control={form.control}
                                              name='full_name'
                                              disabled={true}
                                              render={({ field }) => (
                                                <FormItem>
                                                  <FormLabel>
                                                    Your Full Name
                                                    <span className='text-destructive-500'>
                                                      *
                                                    </span>
                                                  </FormLabel>
                                                  <FormControl>
                                                    <Input
                                                      placeholder='Enter your full name'
                                                      {...field}
                                                    />
                                                  </FormControl>
                                                  <FormMessage />
                                                </FormItem>
                                              )}
                                            />
                                            <FormField
                                              control={form.control}
                                              name='mobile_number'
                                              render={({ field }) => (
                                                <FormItem>
                                                  <FormLabel>
                                                    Associated Phone Number
                                                    <span className='text-destructive-500'>
                                                      *
                                                    </span>
                                                  </FormLabel>
                                                  <FormControl>
                                                    <Input
                                                      placeholder='Enter your 10-digit phone number'
                                                      type='tel'
                                                      maxLength={10}
                                                      {...field}
                                                      onChange={(e) =>
                                                        field.onChange(
                                                          e.target.value.replace(
                                                            /\D/g,
                                                            "",
                                                          ),
                                                        )
                                                      }
                                                    />
                                                  </FormControl>
                                                  <FormMessage />
                                                </FormItem>
                                              )}
                                            />
                                          </>
                                        )}
                                      </div>
                                    </motion.div>
                                  )}
                                </AnimatePresence>
                              </div>
                              <div className='rounded-lg bg-neutral-150'>
                                <div className='mt-4 flex items-center space-x-2 p-4'>
                                  <RadioGroupItem
                                    value='offline'
                                    id='offline'
                                  />
                                  <Label htmlFor='offline' className='flex'>
                                    <Typography as={"p"} className='text-sh5'>
                                      Upload Pan
                                    </Typography>
                                  </Label>
                                </div>

                                <AnimatePresence>
                                  {/* Offline */}
                                  {field.value === "offline" && (
                                    <motion.div
                                      key='offline'
                                      initial={{ height: 0, opacity: 0 }}
                                      animate={{ height: "auto", opacity: 1 }}
                                      exit={{ height: 0, opacity: 0 }}
                                      transition={{ duration: 0.2 }}
                                      className='overflow-hidden'
                                    >
                                      <div className='ml-2 space-y-4 p-4'>
                                        <FormField
                                          control={form.control}
                                          name='frontFile'
                                          render={({ field }) => (
                                            <FormItem>
                                              <FormLabel>
                                                Upload Your Pan
                                                <span className='text-destructive-500'>
                                                  *
                                                </span>
                                              </FormLabel>
                                              <FormControl>
                                                <FileUpload
                                                  label='Upload Your Pan'
                                                  onChange={(file) => {
                                                    field.onChange(file)
                                                    setUploadError({})
                                                  }}
                                                  value={field.value}
                                                  progress={
                                                    uploadProgress.frontFile
                                                  }
                                                  error={uploadError.frontFile}
                                                  isUploading={
                                                    isUploading.frontFile
                                                  }
                                                  onRetry={() => {
                                                    hanldeSubmit()
                                                  }}
                                                />
                                              </FormControl>
                                              <FormMessage />
                                            </FormItem>
                                          )}
                                        />
                                        {/* <FormField
                                          control={form.control}
                                          name='backFile'
                                          render={({ field }) => (
                                            <FormItem>
                                              <FormLabel>
                                                Upload Back
                                                <span className='text-destructive-500'>
                                                  *
                                                </span>
                                              </FormLabel>
                                              <FormControl>
                                                <FileUpload
                                                  label='Upload Back'
                                                  onChange={(file) => {
                                                    field.onChange(file)
                                                    if (file)
                                                      handleFileUpload(
                                                        "backFile",
                                                      )
                                                  }}
                                                  value={field.value}
                                                  progress={
                                                    uploadProgress.backFile
                                                  }
                                                  error={uploadError.backFile}
                                                  isUploading={
                                                    isUploading.backFile
                                                  }
                                                  onRetry={() =>
                                                    handleFileUpload("backFile")
                                                  }
                                                />
                                              </FormControl>
                                              <FormMessage />
                                            </FormItem>
                                          )}
                                        /> */}
                                      </div>
                                    </motion.div>
                                  )}
                                </AnimatePresence>
                              </div>
                            </CardContent>
                          </Card>
                        </RadioGroup>
                      </CardContent>
                    </Card>
                    {/* </RadioGroup> */}
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <div className='max-sm:stikcy max-sm:bottom-0 max-sm:w-full max-sm:bg-gray-100 max-sm:p-4 max-sm:shadow-sm md:space-y-3'>
            {form.getValues("verificationType") === "online" && !panFetch && (
              <Button
                type='button'
                className='w-full bg-primary-500 hover:bg-primary-600'
                disabled={
                  fetchPanDetailsLoading ||
                  !form.getValues("panNumber") ||
                  !!form.formState.errors.panNumber
                }
                onClick={() => fetchPanDetails()}
              >
                {fetchPanDetailsLoading ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Verifying...
                  </>
                ) : (
                  "Verify Pan"
                )}
              </Button>
            )}
            {(panFetch || form.getValues("verificationType") === "offline") && (
              <Button
                type={verificationType === "online" ? "submit" : "button"}
                className='w-full bg-primary-500 hover:bg-primary-600'
                onClick={() => {
                  if (verificationType === "offline") hanldeSubmit()
                }}
                disabled={
                  isSubmitting ||
                  (verificationType === "online" &&
                    (!form.formState.isValid || !panFetch)) ||
                  (verificationType === "offline" && !frontFile)
                }
              >
                {verificationType === "offline" ? (
                  isUploading["frontFile"] ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Verifying...
                    </>
                  ) : (
                    "Submit"
                  )
                ) : (
                  <>
                    {isSubmitting ? (
                      <>
                        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                        Verifying...
                      </>
                    ) : (
                      "Submit"
                    )}
                  </>
                )}
              </Button>
            )}
            <Button
              variant={"link"}
              className='w-full text-center text-primary-500'
              onClick={() => {
                unableToVerify()
              }}
            >
              <WhatsAppLogoFilledIcon /> Need Help?
            </Button>
          </div>
        </form>
      </Form>
    </>
  )
}
