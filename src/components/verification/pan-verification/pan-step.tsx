import { VERIFICATION_STATUS } from "@/types/verification"
import { IdentityCardHorizontalOutlineIcon } from "sharepal-icons"
import { PanVerification } from "."
import StepWrapper from "../verification-steps-new/verification-step-wrapper"

interface PanStepProps {
  status: VERIFICATION_STATUS
  isExpanded: boolean
  onAction: () => void
  onUnableToVerify: () => void
  isOptional?: boolean
}

const PanStep = ({
  status,
  isExpanded,
  onAction,
  onUnableToVerify,
  isOptional = false,
}: PanStepProps) => (
  <StepWrapper
    icon={
      <IdentityCardHorizontalOutlineIcon className='h-6 w-6 text-secondary-800 md:h-9 md:w-9' />
    }
    title='PAN Verification'
    description='Required to assess eligibility for credit on high-value products and ensure a smooth rental experience.'
    status={status}
    onAction={onAction}
    isExpanded={isExpanded}
    isOptional={isOptional}
  >
    <PanVerification
      isOpen={isExpanded}
      unableToVerify={onUnableToVerify}
      onClose={() => onAction()}
    />
  </StepWrapper>
)

export default PanStep
