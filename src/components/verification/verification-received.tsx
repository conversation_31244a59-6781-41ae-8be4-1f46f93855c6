import SpImage from "@/shared/SpImage/sp-image"
import { useUserStore } from "@/store/user-store"
import { useQueryClient } from "@tanstack/react-query"
import { AdaptiveWrapper } from "../modals/adaptive-wrapper"
import { Button } from "../ui/button"
import { Typography } from "../ui/typography"

interface VerificationReceivedModalProps {
  open: boolean
  setOpen: (value: boolean) => void
  onClose: (value: boolean) => void
  messages: {
    buttonText: string
    title?: string
    description?: string
  }
}

const VerificationReceivedModal = ({
  open,
  setOpen,
  onClose,
  messages,
}: VerificationReceivedModalProps) => {
  const { userVerification } = useUserStore()
  const queryClient = useQueryClient()

  const handleClose = () => {
    queryClient
      .invalidateQueries({
        queryKey: ["user_verification"],
      })
      .then(() => {
        setOpen(false)
        onClose(false)
      })
  }

  const { buttonText } = messages

  if (!userVerification) return null

  return (
    <AdaptiveWrapper
      // desktop={type:"dialog"}
      desktop={{ type: "dialog" }}
      mobile={{ type: "dialog" }}
      open={open}
      // onOpenChange={setOpen}
      onOpenChange={handleClose}
      title=''
    >
      <section className='flex flex-col items-center gap-8'>
        <div className='space-y-3'>
          <Typography as={"h1"} className='text-center text-h1 text-gray-900'>
            Verification Received ✅
          </Typography>
          <Typography as={"p"} className='text-center text-b2 text-gray-700'>
            Our team is verifying your details.
            <br />
            You’ll get the status update via WhatsApp.
          </Typography>
        </div>

        {/* <Typography as={"h3"} className='text-center text-h3 text-gray-900'>
        ETA dd/mm/yyyy | hh:mm
      </Typography> */}

        <SpImage
          width={242}
          height={232}
          src='https://images.sharepal.in/verification-images//verification-received.svg'
          alt='Verification illustration'
          className='h-[232px] w-[242px]'
        />

        <div className='space-y-3'>
          <Typography as={"p"} className='text-center text-b2 text-gray-700'>
            You can verify your other details too if you like
          </Typography>

          <div className='flex flex-col items-center gap-2'>
            <Button
              onClick={handleClose}
              variant='outline-primary'
              className='h-full w-full px-4 py-3 !text-bt2 text-primary-500'
            >
              {buttonText}
              {/* {isStatusRequested(userVerification.identity_status) ? (
                <span>Verify Identity</span>
              ) : isStatusRequested(userVerification?.occupation_status) ? (
                <span>Verify Occupation & Pan</span>
              ) : isStatusRequested(userVerification?.credit_status) ? (
                <span>Verify PAN</span>
              ) : (
                <span>Continue</span>
              )} */}
            </Button>

            <Button
              onClick={handleClose}
              variant='link'
              className='!text-bt2 text-primary-500'
            >
              <span>Maybe Later</span>
            </Button>
          </div>
        </div>
      </section>
    </AdaptiveWrapper>
  )
}

export default VerificationReceivedModal
