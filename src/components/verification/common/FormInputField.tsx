import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"

interface FormInputFieldProps<T extends FieldValues> {
  // Form and field props
  form: UseFormReturn<T>
  name: FieldPath<T>

  // Label and description
  label: string
  description?: string
  required?: boolean

  // Input specific props
  type?:
    | "text"
    | "email"
    | "password"
    | "tel"
    | "number"
    | "url"
    | "search"
    | "date"
  placeholder?: string
  disabled?: boolean
  readOnly?: boolean

  // Validation and formatting
  maxLength?: number
  pattern?: string
  min?: number | string
  max?: number | string
  step?: number | string

  // Styling
  className?: string
  inputClassName?: string
  labelClassName?: string

  // Transform functions
  transform?: {
    input?: (value: string) => string
    output?: (value: string) => string
  }
}

export function FormInputField<T extends FieldValues>({
  // Destructure all props
  form,
  name,
  label,
  description,
  required = false,
  type = "text",
  placeholder,
  disabled = false,
  readOnly = false,
  maxLength,
  pattern,
  min,
  max,
  step,
  className,
  inputClassName,
  labelClassName,
  transform,
  ...props
}: FormInputFieldProps<T>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn("w-full", className)}>
          <FormLabel
            className={cn(
              required && "after:text-destructive-500 after:content-['*']",
              labelClassName,
            )}
          >
            {label}
          </FormLabel>

          <FormControl>
            <Input
              {...field}
              type={type}
              placeholder={placeholder}
              disabled={disabled}
              readOnly={readOnly}
              maxLength={maxLength}
              pattern={pattern}
              min={min}
              max={max}
              step={step}
              className={cn(
                "h-12 rounded-xl border-neutral-200 md:text-base",
                disabled && "cursor-not-allowed opacity-50",
                readOnly && "bg-neutral-50",
                inputClassName,
              )}
              onChange={(e) => {
                let value = e.target.value

                // Apply input transformation if provided
                if (transform?.input) {
                  value = transform.input(value)
                }

                // Handle specific type transformations
                if (type === "tel") {
                  value = value.replace(/\D/g, "") // Remove non-digits
                }

                if (type === "number") {
                  value = value.replace(/[^\d.-]/g, "") // Allow digits, decimal point, and minus
                }

                field.onChange(
                  transform?.output ? transform.output(value) : value,
                )
              }}
              value={field.value || ""}
              {...props}
            />
          </FormControl>

          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
