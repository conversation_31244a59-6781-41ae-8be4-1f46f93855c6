import { Button } from "@/components/ui/button"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp"
import { motion } from "framer-motion"
import { Loader2, RefreshCcw } from "lucide-react"
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"

interface OtpInputFieldProps<T extends FieldValues> {
  form: UseFormReturn<T>
  name: FieldPath<T>
  label: string
  length?: number
  isResendEnabled: boolean
  resendTimer?: number
  onResend: () => void
  isResending?: boolean
  required?: boolean
}

export function OtpInputField<T extends FieldValues>({
  form,
  name,
  label,
  length = 6,
  isResendEnabled,
  resendTimer = 0,
  onResend,
  isResending = false,
  required = false,
}: OtpInputFieldProps<T>) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className='space-y-4'
    >
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel className='text-bt3'>
              {label}
              {required && <span className='text-destructive-500'>*</span>}
            </FormLabel>
            <FormControl>
              <InputOTP
                value={field.value || ""}
                onChange={(value) => field.onChange(value)}
                maxLength={length}
              >
                <InputOTPGroup className='w-full justify-start gap-1'>
                  {Array.from({ length }).map((_, index) => (
                    <InputOTPSlot
                      className='h-12 w-12 rounded-2xl border-2 border-neutral-200 text-sh3'
                      key={index}
                      index={index}
                    />
                  ))}
                </InputOTPGroup>
              </InputOTP>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {resendTimer > 0 ? (
        <p className='text-bt3 text-neutral-500'>
          Resend OTP in {resendTimer} sec
        </p>
      ) : (
        isResendEnabled && (
          <Button
            type='button'
            variant='link'
            className='p-0 text-bt3 text-primary-500'
            onClick={onResend}
            disabled={isResending}
          >
            {isResending ? (
              <Loader2 className='mr-1 h-4 w-4 animate-spin' />
            ) : (
              <RefreshCcw className='mr-1 h-4 w-4' />
            )}
            Resend OTP
          </Button>
        )
      )}
    </motion.div>
  )
}
