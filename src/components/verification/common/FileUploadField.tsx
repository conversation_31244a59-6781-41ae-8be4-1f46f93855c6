import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"
import { FileUpload } from "../file-upload"

interface FileUploadFieldProps<T extends FieldValues> {
  form: UseFormReturn<T>
  name: FieldPath<T>
  label: string
  handleFileUpload: (fileType: string) => void
  uploadProgress: Record<string, number>
  uploadError: Record<string, string>
  isUploading: Record<string, boolean>
  required?: boolean
}

export function FileUploadField<T extends FieldValues>({
  form,
  name,
  label,
  handleFileUpload,
  uploadProgress,
  uploadError,
  isUploading,
  required = true,
}: FileUploadFieldProps<T>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            {label}
            {required && <span className='text-destructive-500'>*</span>}
          </FormLabel>
          <FormControl>
            <FileUpload
              label={label}
              onChange={(file) => {
                field.onChange(file)
                if (file) handleFileUpload(name)
              }}
              value={field.value}
              progress={uploadProgress[name]}
              error={uploadError[name]}
              isUploading={isUploading[name]}
              onRetry={() => handleFileUpload(name)}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
