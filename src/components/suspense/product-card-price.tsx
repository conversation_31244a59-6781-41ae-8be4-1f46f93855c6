import { moneyFormatter } from "@/functions/small-functions"
import useCalculateRent from "@/hooks/use-calculate-rent"
import { RentalItem } from "@/types"

type ProductCardPrice = {
  total_days: number
  data: RentalItem
}

const ProductCardPrice = ({ data, total_days }: ProductCardPrice) => {
  const { rent } = useCalculateRent({
    product: data,
    type: "product",
  })

  return (
    <div className='flex flex-col items-baseline justify-between gap-0 md:gap-1'>
      {total_days ? (
        <div className='flex items-center gap-1 text-o3 text-gray-600 md:text-sh5'>
          Rent for
          {total_days ? (
            <span className='text-xs font-semibold leading-5 text-gray-900 md:text-sm'>
              {" "}
              {total_days}
            </span>
          ) : (
            <span className='inline-flex text-gray-900 blur-sm'>00</span>
          )}
          {total_days > 1 ? "days" : "day"}
        </div>
      ) : (
        <div className='flex items-center gap-1 text-o3 text-gray-600 md:text-sh5'>
          Select Dates to view price
        </div>
      )}
      <div
        className={`flex items-center justify-center text-sh4 text-gray-900 md:text-h6`}
      >
        {rent ? (
          <span area-label={`Rent for ${total_days} days`}>
            {moneyFormatter(rent)}{" "}
          </span>
        ) : (
          <p>
            ₹<span className='inline-flex blur-sm'>N/A</span>
          </p>
        )}
      </div>
    </div>
  )
}
export default ProductCardPrice
