import { moneyFormatter } from "@/functions/small-functions"
import useCalculateRent from "@/hooks/use-calculate-rent"
import { RentalItem } from "@/types"
import { CurrencyNoteAddOutlinedIcon } from "sharepal-icons"
import Marquee from "../ui/marquee"

type ProductCardExtraDayPriceProps = {
  total_days: number
  data: RentalItem
}

const ProductCardExtraDayPrice = ({
  data,
  total_days,
}: ProductCardExtraDayPriceProps) => {
  const { extraDayRent } = useCalculateRent({
    product: data,
    type: "product",
  })
  return (
    <div className='mt-1 line-clamp-1 flex items-center gap-1 text-sh7 text-decorative-pink md:mt-0 md:text-sh5'>
      <CurrencyNoteAddOutlinedIcon className={"min-w-4 md:min-w-5"} />{" "}
      <Marquee
        className='w-full min-w-max'
        containerClassName='p-0 !gap-0 '
        repeat={2}
        duration={5}
        onlyMobile
      >
        {total_days && extraDayRent ? (
          <p className='mr-4'>
            Additional day at {moneyFormatter(extraDayRent)} only
          </p>
        ) : (
          <p className='mr-4'> Lowest Price Guarantee </p>
        )}
      </Marquee>
      {/* Desktop version - no animation */}
      {total_days && extraDayRent ? (
        <p className='ml-1 hidden min-w-max md:inline-flex'>
          Additional day at {moneyFormatter(extraDayRent)} only
        </p>
      ) : (
        <p className='hidden md:block'> Lowest Price Guarantee </p>
      )}
    </div>
  )
}

export default ProductCardExtraDayPrice
