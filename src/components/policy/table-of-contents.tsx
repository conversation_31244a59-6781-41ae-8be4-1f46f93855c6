"use client"

import { useState } from "react"
import { motion, useScroll } from "framer-motion"
// import { ChevronRight } from 'lucide-react'
import { ScrollArea } from "@/components/ui/scroll-area"

interface TableOfContentsProps {
  sections: Array<{ id: string; title: string }>
}

export function TableOfContents({ sections }: TableOfContentsProps) {
  const [activeSection, setActiveSection] = useState<string>(sections[0].id)
  const { scrollY } = useScroll()

  scrollY.onChange((latest) => {
    const viewportHeight = window.innerHeight
    let currentSection = sections[0].id

    for (const section of sections) {
      const element = document.getElementById(section.id)
      if (element && element.offsetTop - viewportHeight / 3 <= latest) {
        currentSection = section.id
      } else {
        break
      }
    }

    setActiveSection(currentSection)
  })

  const handleNavClick = (id: string) => {
    const element = document.getElementById(id)
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 100,
        behavior: "smooth",
      })
    }
  }

  return (
    <div className='top-5 h-max w-full rounded-3xl bg-gray-100 p-4 lg:sticky lg:w-96'>
      <h2 className='mb-4 ml-2 border-b border-neutral-200 pb-2 text-[20px] font-bold text-gray-900 md:text-xl'>
        Table of Contents:
      </h2>
      {/* <ScrollArea className="h-[calc(100vh-8rem)]"> */}
      <ScrollArea className='h-max'>
        <nav className='flex flex-col space-y-3'>
          {sections.map((section, index) => (
            <motion.button
              key={section.id}
              onClick={() => handleNavClick(section.id)}
              className={`line-clamp-1 flex items-center rounded-full px-5 py-3 text-left text-base font-bold text-gray-900 transition-colors ${
                activeSection === section.id
                  ? "bg-neutral-150"
                  : "hover:bg-neutral-150"
              }`}
              // whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <p className='line-clamp-1'>
                {index + 1}. {section.title}
              </p>
              {activeSection === section.id && (
                <motion.div
                  layoutId='activeSection'
                  className='ml-2'
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                >
                  {/* <ChevronRight className="h-4 w-4" /> */}
                </motion.div>
              )}
            </motion.button>
          ))}
        </nav>
      </ScrollArea>
    </div>
  )
}
