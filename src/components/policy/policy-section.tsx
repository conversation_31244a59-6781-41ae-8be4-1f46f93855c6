"use client"

import { useRef } from "react"
import { motion, useInView } from "framer-motion"
import { Separator } from "@/components/ui/separator"

interface PolicySectionProps {
  id: string
  title: string
  content: string
  index: number
  isLast: boolean
}

export function PolicySection({
  id,
  title,
  content,
  index,
  isLast,
}: PolicySectionProps) {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  return (
    <motion.section
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      // transition={{ duration: 0.5, delay: index * 0.1 }}
      transition={{ duration: 0.5, delay: 0.5 }}
      key={id}
      id={id}
      className='mb-8'
    >
      <h2 className='mb-2 text-sm font-semibold text-gray-900 md:text-base'>
        {index + 1}. {title}:
      </h2>
      <div className='space-y-4 leading-relaxed'>
        {content.split("\n").map((paragraph, i) => (
          <p
            className='text-xs font-medium leading-relaxed text-neutral-700'
            key={i}
          >
            {paragraph}
          </p>
        ))}
      </div>
      {!isLast && <Separator className='mt-8 hidden' />}
    </motion.section>
  )
}
