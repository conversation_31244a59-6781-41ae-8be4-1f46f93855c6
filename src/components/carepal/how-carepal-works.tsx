import SpImage from "@/shared/SpImage/sp-image"
import { Typography } from "../ui/typography"

export const HowCarePalWorks = () => (
  <section className='py-16- bg-gray-100 py-12 md:py-20 lg:py-[60px]'>
    <div className='container'>
      <div className='mb-10 flex flex-col items-center justify-center text-center md:mb-12'>
        <Typography
          as='h2'
          className='flex items-center gap-2 font-ubuntu text-d7 text-primary-900 md:text-d3'
        >
          How it Works
        </Typography>
      </div>
      {/* full width image */}
      <SpImage
        src='https://images.sharepal.in/carepal/desktop-carepal.webp'
        alt='How it Works'
        width={1600}
        height={900}
        className='h-full w-full object-contain'
        containerClassName='w-full hidden md:block'
      />
      <SpImage
        src='https://images.sharepal.in/carepal/how-it-works-mobile.webp'
        alt='How it Works'
        width={800}
        height={1900}
        className='h-full w-full object-contain'
        containerClassName='w-full md:hidden block'
      />
    </div>
  </section>
)
