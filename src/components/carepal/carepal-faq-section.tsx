import { fetchFaqs } from "@/actions/product"
import SpImage from "@/shared/SpImage/sp-image"
import Link from "next/link"
import { FAQS } from "../custom/faq"
import { Button } from "../ui/button"
import { Typography } from "../ui/typography"

export const FaqSectionCarepal = async () => {
  const faqs = await fetchFaqs("carepal", "", "carepal") //!To do change this to carepal
  const creatorFaqs = faqs.filter((faq) => faq.faq_type === "carepal")
  return (
    <section className='py-16- flex w-full flex-col bg-neutral-150 py-12 md:gap-12 md:py-20 lg:py-[72px]'>
      <div className='container my-6 flex flex-wrap gap-4 rounded-3xl bg-gray-100 px-4 py-12 md:gap-8 md:py-16'>
        <div className='flex flex-[1_0_250px] items-center justify-center'>
          <SpImage
            src='https://images.sharepal.in/carepal/carepal-faq.webp'
            alt='faq'
            width={400}
            height={400}
            className='h-full w-full object-contain'
            containerClassName='w-full h-full max-w-[150px] md:max-w-[500px]'
          />
        </div>
        <div className='h-max w-full flex-[2_0_250px] bg-gray-100 px-0 md:px-4'>
          <Typography
            as={"h3"}
            className='text-navy-900 mb-6 font-ubuntu text-h4 font-bold md:text-h1'
          >
            Frequently Asked Questions (FAQs)
          </Typography>

          <FAQS faqs={creatorFaqs} />
        </div>
      </div>
      {/* Need help */}

      <div className='container w-full text-center'>
        <Button
          variant={"link"}
          className='text-primary-500 hover:underline md:text-bt2'
          asChild
        >
          <Link
            href={
              "https://api.whatsapp.com/send?phone=+917619220543&text=Hi I have a some queries related to CarePal."
            }
            target='_blank'
          >
            Need more help? Contact us
          </Link>
        </Button>
      </div>
    </section>
  )
}
