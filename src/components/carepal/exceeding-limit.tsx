"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"

export const ExceedingLimit = () => (
  <section className='container py-12 !pt-12 md:py-20 lg:py-24'>
    {/* Section Title */}
    <div className='mb-8 flex flex-col items-center justify-center text-center md:mb-12'>
      <Typography
        as='h2'
        className='flex items-center gap-2 font-ubuntu text-h4 font-bold text-primary-900 md:text-h1'
      >
        Exceeding the Limit
        <span className='inline-flex items-center justify-center rounded-full bg-carepal-light p-1'>
          <svg
            width='28'
            height='28'
            viewBox='0 0 24 24'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M3 17L9 11L13 15L21 7'
              stroke='currentColor'
              strokeWidth='2'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
            <path
              d='M17 7H21V11'
              stroke='currentColor'
              strokeWidth='2'
              strokeLinecap='round'
              strokeLinejoin='round'
            />
          </svg>
        </span>
      </Typography>

      <div className='mt-4 max-w-3xl'>
        <Typography as='p' className='text-b2 text-neutral-400 md:text-sh1'>
          In the unlikely event that damage costs exceeds your coverage
          amount,you are responsible only for the amount exceeding the cap.
        </Typography>
      </div>
    </div>

    {/* Exceeding Limit Example */}
    <div className='mx-auto max-w-[588px]'>
      <Card
        className={cn(
          "group overflow-hidden rounded-[28px] border border-destructive-100 p-4",
          "bg-gradient-to-br from-white via-[rgba(248,248,248,0.00)] to-[#FFEFEF]",
          "bg-blend-multiply transition-all duration-300",
        )}
      >
        <CardContent className='flex flex-col items-center justify-between gap-3 !py-0 px-6 md:flex-row md:gap-8 md:px-8'>
          {/* Left side - Image */}
          <div className='flex flex-1 items-center justify-center'>
            <div className='relative h-full w-48 overflow-hidden p-2 md:w-60'>
              <SpImage
                src='https://images.sharepal.in/carepal/camera2.webp'
                alt='Person holding camera'
                width={400}
                height={400}
                className='h-full w-full object-contain'
              />
            </div>
          </div>

          {/* Right side - Calculation */}
          <div className='flex flex-1 flex-col items-center text-center md:text-b4'>
            <Typography as='h3' className='text-center'>
              Assume Damage Costs = <br />
              <span>
                <Typography
                  as='span'
                  className='font-bold text-destructive-500'
                >
                  ₹23,000
                </Typography>
              </span>{" "}
              (&gt;Coverage)
              <br />
              Then you only pay
            </Typography>
            <Typography
              as='p'
              className='mt-2 text-h5 font-bold text-destructive-500 md:text-sh2'
            >
              ₹23,000 - ₹20,000 = ₹3000
            </Typography>
          </div>
        </CardContent>
      </Card>
    </div>
  </section>
)
