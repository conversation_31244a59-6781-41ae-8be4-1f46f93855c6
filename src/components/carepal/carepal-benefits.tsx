import Image from "next/image"

import CarePalCard from "../cards/carepal-card"
import SectionTitle from "../section-title"

export const CarePalBenefits = () => (
  <section className='container mt-0 space-y-5 bg-neutral-150 p-4 px-0 py-5 md:mt-16'>
    {/* CarePal Header */}
    <div className='mx-auto flex items-center justify-start gap-0 px-4 pt-5 max-lg:flex-col max-lg:items-start md:py-5 lg:flex lg:gap-5'>
      <Image
        src={`https://images.sharepal.in/carepal/carepal-badge.webp`}
        width={350}
        height={100}
        alt='CarePal Assure Logo'
        className='h-auto w-[122px] md:h-auto md:w-[244px]'
      />

      {/* CarePal Title */}
      <SectionTitle
        cText='CarePal'
        cTColor='text-carepal-dark'
        nText='Why Choose'
        className='max-w-80 md:w-full'
      />
    </div>
    {/* CarePal Cards */}
    <div className='custom-scrollbar-black flex gap-3 overflow-x-auto px-4 py-3 lg:gap-8 2xl:overscroll-none'>
      <CarePalCard
        key={"huge-cost-savings"}
        description={`In case accidental damage occurs, with CarePal's damage waiver, you avoid paying the full cost of the damage.`}
        image_url='https://images.sharepal.in/carepal/assure1.svg'
        title='Huge Cost Savings'
      />
      <CarePalCard
        key={"peace-of-mind"}
        description='Renting high-value products can be stressful if unexpected damage strikes — but with CarePal, that worry is eliminated.'
        image_url='https://images.sharepal.in/carepal/assure2.svg'
        title='Peace of Mind'
      />
      <CarePalCard
        key={"instant-protection"}
        description='Once opted in, your rental is automatically covered for accidental damage up to 50% of the product value, capped at ₹25,000.'
        image_url='https://images.sharepal.in/carepal/assure3.svg'
        title='Instant Protection'
      />
    </div>
  </section>
)
