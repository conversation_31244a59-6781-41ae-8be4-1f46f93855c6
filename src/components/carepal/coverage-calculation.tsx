"use client"

import CoverageCalculationCard from "@/components/cards/coverage-calculation-card"
import { Typography } from "@/components/ui/typography"
import { Calculator } from "lucide-react"

export const CoverageCalculation = () => (
  <section className='container'>
    <div className='my-16 w-full rounded-xl border bg-gray-100 px-6 py-8 md:my-20 md:rounded-3xl md:px-6 md:py-12 lg:my-28'>
      {/* Section Title */}
      <div className='mb-8 flex flex-col items-center justify-center text-center md:mb-10'>
        <Typography
          as='h2'
          className='flex max-w-[80%] flex-col items-center text-h4 text-neutral-900 md:flex-row md:gap-2 md:text-h1'
        >
          How Coverage is
          <span className='inline-flex items-center justify-center'>
            Calculated?
            <Calculator className='h-5 w-5 text-neutral-800 md:h-6 md:w-6' />
          </span>
        </Typography>

        <div className='mt-4 max-w-2xl'>
          <Typography as='p' className='text-b4 text-neutral-400 md:text-sh1'>
            Our damage waiver coverage is capped at{" "}
            <span className='font-bold'>50% of the product value</span>. The
            maximum damage waiver is{" "}
            <span className='font-semibold'>₹25,000</span>.
          </Typography>
        </div>
      </div>

      {/* Coverage Examples */}
      <div className='mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-3 md:gap-8'>
        {/* Example 1 */}
        <CoverageCalculationCard
          imageSrc='https://images.sharepal.in/carepal/calculate1.webp'
          imageAlt='Shopping cart with camera'
          text={
            <div className='flex flex-col items-center'>
              <Typography
                as='h3'
                className='mb-3 text-b6 font-medium text-primary-900 md:text-b4'
              >
                For example, if you rent products with a purchase value
              </Typography>
              <Typography
                as='p'
                className='text-sh4 font-bold text-secondary-700 md:text-sh2'
              >
                ₹40,000
              </Typography>
            </div>
          }
        />

        {/* Example 2 */}
        <CoverageCalculationCard
          text={
            <div className='flex flex-col items-center'>
              <Typography
                as='h3'
                className='mb-3 text-b6 font-medium text-primary-900 md:text-b4'
              >
                If you opt for CarePal, you get a damage waiver coverage of
                <br />
                <span className='font-bold'>50% of ₹40,000 =</span>
              </Typography>

              <Typography
                as='p'
                className='text-sh4 font-bold text-decorative-pink md:text-sh2'
              >
                ₹20,000
              </Typography>
            </div>
          }
          imageSrc='https://images.sharepal.in/carepal/with-heart.webp'
          imageAlt='CarePal Heart'
        />

        {/* Example 3 */}
        <CoverageCalculationCard
          text={
            <div className='flex flex-col items-center'>
              <Typography
                as='h3'
                className='mb-3 text-b6 font-medium text-primary-900 md:text-b4'
              >
                Assume Damage Costs = <br />
                <span className='font-bold text-destructive-500'>
                  ₹19,000
                </span>{" "}
                (&lt; Coverage) <br /> then you{" "}
                <span className='font-bold'>don’t pay.</span>
              </Typography>
              <Typography
                as='p'
                className='text-sh4 text-neutral-400 md:text-sh2'
              >
                ₹0
              </Typography>
            </div>
          }
          imageSrc='https://images.sharepal.in/carepal/camera1.webp'
          imageAlt='Camera'
        />
      </div>
    </div>
  </section>
)
