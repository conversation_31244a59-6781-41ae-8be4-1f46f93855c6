"use client"

import { Typography } from "@/components/ui/typography"
import SpImage from "@/shared/SpImage/sp-image"
import { CoverageCard, CoverageItem } from "../cards/coverage-card"

// Coverage items data
const coveredItems: CoverageItem[] = [
  {
    title: "Accidental Damages",
    image: "https://images.sharepal.in/carepal/conver1.webp",
    alt: "Accidental Damages",
    type: "covered",
  },
  {
    title: "Hardware/Software Issues",
    image: "https://images.sharepal.in/carepal/conver2.webp",
    alt: "Hardware/Software Issues",
    type: "covered",
  },
  {
    title: "Damages due to Improper Usage",
    image: "https://images.sharepal.in/carepal/not-cover1.webp",
    alt: "Damages due to Improper Usage",
    type: "not-covered",
  },
  {
    title: "Theft, Burglary or Lost Items",
    image: "https://images.sharepal.in/carepal/not-cover2.webp",
    alt: "Theft, Burglary or Lost Items",
    type: "not-covered",
  },
]

const CoverageCards = () => {
  const coveredList = coveredItems.filter((item) => item.type === "covered")
  const notCoveredList = coveredItems.filter(
    (item) => item.type === "not-covered",
  )

  return (
    <div className='w-full rounded-xl border bg-neutral-100 p-3 pt-12 shadow-sm sm:p-4 md:rounded-3xl md:pt-10 lg:p-6'>
      {/* Heart icon */}
      <div className='relative'>
        <div className='absolute left-1/2 top-0 -translate-x-1/2 -translate-y-16 sm:-translate-y-20 md:-translate-y-24'>
          <SpImage
            src='https://images.sharepal.in/carepal/covergae-card-heart.webp'
            width={300}
            height={300}
            alt='CarePal Heart'
            className='h-full w-full object-contain'
            containerClassName='relative w-20 sm:w-24 overflow-hidden  h-auto md:w-40'
          />
        </div>
      </div>

      {/* Section Titles - Different layout for mobile/desktop */}
      <div className='mb-3 hidden sm:mb-4 sm:block'>
        <div className='flex justify-between font-ubuntu'>
          <Typography as='h3' className='text-h6 md:text-d7'>
            What&apos;s Covered?
          </Typography>
          <Typography as='h3' className='text-h6 md:text-d7'>
            What&apos;s <span className='text-destructive-600'>NOT</span>{" "}
            Covered?
          </Typography>
        </div>
      </div>

      <div className='flex flex-col items-center justify-center gap-3 md:flex-row'>
        {/*  Covered Title - Mobile & Tablet only */}
        <Typography as='h3' className='mt-4 block text-h6 sm:hidden md:text-d7'>
          What&apos;s Covered?
        </Typography>

        {/* Covered Items - Mobile & Tablet */}
        <div className='grid flex-[1_0_0] grid-cols-2 gap-3 lg:gap-6'>
          {coveredList.map((item, index) => (
            <CoverageCard key={`mobile-covered-${index}`} item={item} />
          ))}
        </div>

        <div className='block sm:w-20'></div>
        {/* Not Covered Title - Mobile & Tablet only */}
        <Typography as='h3' className='mt-2 block text-h6 sm:hidden md:text-d7'>
          What&apos;s <span className='text-destructive-600'>NOT</span> Covered?
        </Typography>

        {/* Not Covered Items - Mobile & Tablet */}
        <div className='grid flex-[1_0_0] grid-cols-2 gap-3 lg:gap-6'>
          {notCoveredList.map((item, index) => (
            <CoverageCard key={`mobile-not-covered-${index}`} item={item} />
          ))}
        </div>
      </div>
    </div>
  )
}

export default CoverageCards
