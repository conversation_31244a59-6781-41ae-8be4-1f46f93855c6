"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { motion } from "framer-motion"
import { useForm } from "react-hook-form"

import { fetchOffers } from "@/actions/creators"
import { sendOTP } from "@/actions/login"
import { CustomFormField } from "@/components/custom/form-field"
import { PhoneInput } from "@/components/custom/phone-input"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { getCookie } from "@/functions/cookies"
import {
  CreatorOffer,
  creatorSchema,
  type CreatorFormData,
} from "@/lib/validations/creator.schema"
import { customFetch } from "@/utils/customFetch"
import { useQuery } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { OtpStep } from "../onboarding/otp-step"

const formVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
}

const niches = [
  "Travel",
  "Fashion",
  "Technology",
  "Food",
  "Lifestyle",
  "Gaming",
  "Education",
  "Other",
]

const categories = [
  "Action Cameras",
  "DSLR Cameras",
  "Trekking Gear",
  "Riding Gear",
  "Gaming Console",
  "Winter Travel Wear",
]

export function CreatorForm() {
  const router = useRouter()

  const form = useForm<CreatorFormData>({
    resolver: zodResolver(creatorSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      country_code: "91",
      phone_number: "",
      email: "",
      instagram_handle: "",
      youtube_handle: "",
      niche: "",
      following_range: "",
    },
  })

  const { data: offers } = useQuery<CreatorOffer[]>({
    queryKey: ["offers"],
    queryFn: fetchOffers,
  })

  const handleAddSpCreator = async () => {
    const formData = form.getValues()
    const userData = {
      first_name: formData.first_name,
      last_name: formData.last_name,
      email: formData.email,
      ig_handle: formData.instagram_handle,
      yt_handle: formData.youtube_handle,
      category: formData.category_interest,
      niche: formData.niche,
      following: formData.following_range,
      phone_number: formData.phone_number,
      country_code: formData.country_code,
      sharepal_creator_offer_id: formData.offer_id,
    }
    try {
      await customFetch("https://api.sharepal.in/api:EhQQ9F-L/spcreators/add", {
        method: "POST",
        body: JSON.stringify(userData),
      })
      const city_cookie = getCookie("selected_city")
      router.push(`${city_cookie}`)
    } catch (error) {
      console.error(error)
    }
  }

  async function onSubmit(data: CreatorFormData) {
    try {
      await sendOTP(data.country_code, data.phone_number)
    } catch (error) {
      console.error(error)
    }
  }

  return (
    <div className='h-max rounded-3xl border-input bg-gray-100 p-6 md:p-10'>
      <h2 className='mb-6 text-h3 font-bold md:text-h1'>
        Let&apos;s Collaborate Pals!
      </h2>
      {!form.formState.isSubmitSuccessful ? (
        <motion.div
          variants={formVariants}
          initial='hidden'
          animate='visible'
          className='w-full'
        >
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
              <div className='grid gap-4 md:grid-cols-2'>
                <CustomFormField
                  form={form}
                  name='first_name'
                  label='Your Name'
                  placeholder='First name'
                  required
                />
                <CustomFormField
                  form={form}
                  name='last_name'
                  label='.'
                  placeholder='Last name'
                />
              </div>

              <div className='grid gap-4 md:grid-cols-2'>
                <PhoneInput
                  form={form}
                  phoneFieldName='phone_number'
                  countryCodeFieldName='country_code'
                  placeholder='Enter Phone Number'
                  label='Phone Number'
                  required
                />

                <CustomFormField
                  form={form}
                  name='email'
                  label='Email Address'
                  placeholder='<EMAIL>'
                  type='email'
                />
              </div>

              <div className='grid gap-4 md:grid-cols-2'>
                <CustomFormField
                  form={form}
                  name='instagram_handle'
                  label='Your Instagram Handle'
                  placeholder='@insta_handle'
                  required
                />

                <CustomFormField
                  form={form}
                  name='youtube_handle'
                  label='Your YouTube Handle'
                  placeholder='@youtube_handle'
                />
              </div>

              <FormField
                control={form.control}
                name='niche'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>What is your Niche? *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className='flex h-12 justify-between rounded-xl border-2 border-neutral-200 bg-gray-100'>
                          <SelectValue placeholder='Select your Niche' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {niches.map((niche) => (
                          <SelectItem key={niche} value={niche.toLowerCase()}>
                            {niche}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='category_interest'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      What type of equipment are you interested in renting? *
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className='flex h-12 justify-between rounded-xl border-2 border-neutral-200 bg-gray-100'>
                          <SelectValue placeholder='Select your Interested Category' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem
                            key={category}
                            value={category.toLowerCase()}
                          >
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='offer_id'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select your following range *</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        const selectedOffer = offers?.find(
                          (o: CreatorOffer) => o.id === Number(value),
                        )
                        field.onChange(Number(value))
                        // Also update the following_range field
                        form.setValue(
                          "following_range",
                          selectedOffer?.combined_following || "",
                        )
                      }}
                      value={field.value?.toString()}
                    >
                      <SelectTrigger className='flex h-12 justify-between rounded-xl border-2 border-neutral-200 bg-gray-100'>
                        <SelectValue placeholder='Select Range' />
                      </SelectTrigger>
                      <SelectContent>
                        {offers?.map((offer) => (
                          <SelectItem
                            key={offer.id}
                            value={offer.id.toString()}
                          >
                            {offer.combined_following}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type='submit'
                variant={"blue"}
                size={"lg"}
                disabled={form.formState.isSubmitting}
                className='mt-8 w-full'
              >
                {form.formState.isSubmitting ? "Submitting..." : "Sign Up Now"}
              </Button>
            </form>
          </Form>
        </motion.div>
      ) : (
        <motion.div
          variants={formVariants}
          initial='hidden'
          animate='visible'
          className='flex min-h-[550px] w-full items-center justify-center'
        >
          <OtpStep
            phoneNumber={form.getValues("phone_number")}
            isOnboarding={false}
            onCompleteAction={handleAddSpCreator}
          />
        </motion.div>
      )}
    </div>
  )
}
