"use client"
import React from "react"
import { Typography } from "../ui/typography"
import { Button } from "../ui/button"
import { ArrowRightOutlinedIcon } from "sharepal-icons"
import dynamic from "next/dynamic"
// import { Player } from '@lottiefiles/react-lottie-player'
const Player = dynamic(
  () => import("@lottiefiles/react-lottie-player").then((mod) => mod.Player),
  { ssr: false },
)

const ReadyToCreate = () => (
  <div className='h-max w-full p-6 md:p-0'>
    <Typography
      as={"h3"}
      className='text-navy-900 mb-6 font-ubuntu text-d6 font-bold md:text-d4'
    >
      Ready to Create ✨ Together?
    </Typography>

    <Player
      src={"lottie-animations/creator-animation.json"}
      style={{ height: "100%", width: "100%" }}
      // className="max-w-96 "
      loop
      autoplay
    />

    <Button variant={"primary"} className='mt-3 w-max' size={"lg"}>
      Rent Now! <ArrowRightOutlinedIcon className='h-5 w-5' />
    </Button>
  </div>
)

export default ReadyToCreate
