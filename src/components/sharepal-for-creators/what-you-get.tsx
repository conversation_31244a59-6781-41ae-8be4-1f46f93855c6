"use client"

import { Card, CardContent } from "@/components/ui/card"
import SpImage from "@/shared/SpImage/sp-image"
import { motion, useInView } from "framer-motion"
import { useRef } from "react"

export default function WhatYouGetFeaturesSection() {
  const features = [
    {
      icon: "https://images.sharepal.in/sharepal-for-creators/original/what-you-get-icon-1.svg",
      title: "Uninterrupted Creativity",
      description:
        "From Treks to Vlogs to Gaming, Access top-tier gear without the hassle of ownership.",
    },
    {
      icon: "https://images.sharepal.in/sharepal-for-creators/original/what-you-get-icon-2.svg",
      title: "Expand your Audience",
      description:
        "You'll add value to the audience by helping them save money and the planet.",
    },
    {
      icon: "https://images.sharepal.in/sharepal-for-creators/original/what-you-get-icon-3.svg",
      title: "A Partnership, Not a Transaction",
      description:
        "We're more than just rentals cause quality content needs quality gear.",
    },
    {
      icon: "https://images.sharepal.in/sharepal-for-creators/original/what-you-get-icon-4.svg",
      title: "Dedicated Creator Support",
      description:
        "From technical support to logistics need, we're here to help you with anything.",
    },
  ]

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  return (
    <section className='w-full bg-gray-50 py-4 md:py-16'>
      <div className='container mx-auto px-4'>
        <motion.h2
          ref={ref}
          initial={{ opacity: 0, y: -20 }}
          animate={isInView ? "visible" : "hidden"}
          variants={{
            visible: { opacity: 1, y: 0 },
            hidden: { opacity: 0, y: -20 },
          }}
          transition={{ duration: 0.8 }}
          className='text-navy-900 mb-6 text-center text-h1 font-bold md:mb-16 md:text-d4'
        >
          What You Get?
        </motion.h2>

        <motion.div
          ref={ref}
          initial='hidden'
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
          className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              transition={{ duration: 1 }}
            >
              <Card className='overflow-hidden rounded-3xl border border-primary-100 bg-gradient-to-b from-gray-100 to-primary-100 pb-2'>
                <CardContent className='flex flex-col items-center p-4 text-center'>
                  <SpImage
                    src={feature.icon}
                    alt={feature.title}
                    width={100}
                    height={100}
                    className='h-20 w-20 md:h-28 md:w-28'
                    containerClassName='w-full h-full flex items-center justify-center'
                  />
                  <h3 className='text-navy-900 mb-4 text-h4 font-bold md:text-h2'>
                    {feature.title}
                  </h3>
                  <p className='text-neutral-500 md:text-b2'>
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
