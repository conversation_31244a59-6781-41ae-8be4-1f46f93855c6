"use client"

import { Typography } from "../ui/typography"
import SpImage from "@/shared/SpImage/sp-image"
import { motion, useInView } from "framer-motion"
import { useRef } from "react"

export default function WhatWeGetSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  return (
    <section className='relative flex min-h-[700px] w-full items-start justify-center overflow-hidden bg-gray-100 md:min-h-[900px]'>
      {/* Background image - this would be replaced with your actual image */}
      <div className='absolute inset-0 z-0 h-full w-full'>
        <SpImage
          src='https://images.sharepal.in/sharepal-for-creators/original/LimeLight%20Visual.png'
          alt='Background with spotlight effect'
          fill
          className='object-contain'
          containerClassName=''
        />
      </div>

      {/* Content */}
      <motion.div
        ref={ref}
        initial={{ opacity: 0, y: 20 }}
        animate={isInView ? "visible" : "hidden"}
        variants={{
          visible: { opacity: 1, y: 0 },
          hidden: { opacity: 0, y: 20 },
        }}
        transition={{ duration: 0.8 }}
        className='container relative z-10 mx-auto mt-8 max-w-3xl p-6 px-4 text-center md:mt-14'
      >
        <Typography
          as={"h3"}
          className='text-navy-900 mb-3 font-ubuntu text-d6 font-bold md:mb-6 md:text-d4'
        >
          What We Get?
        </Typography>

        <p className='mb-4 text-h4 !font-normal text-neutral-900 md:text-d5'>
          We get the limelight we deserve and people realise that
        </p>

        <Typography
          as='h4'
          className='bg-review-gradient bg-clip-text py-0 text-center text-d5 font-bold text-transparent md:text-d1'
        >
          Renting is Cool
        </Typography>

        {/* This is where the 3D character image would go */}
        {/* You mentioned this would be part of a single background image */}
        {/* If you need it as a separate element, uncomment the following: */}
        {/*
        <div className="mt-12">
          <Image
            src="/placeholder.svg?height=300&width=300"
            alt="3D character with bag"
            width={300}
            height={300}
            className="mx-auto"
          />
        </div>
        */}
      </motion.div>
    </section>
  )
}
