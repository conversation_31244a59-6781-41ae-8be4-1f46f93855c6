import { fetchFaqs } from "@/actions/product"
import { FAQS } from "../custom/faq"
import { Typography } from "../ui/typography"

import ReadyToCreate from "./ready-to-create"

const FaqSectionCreator = async () => {
  const faqs = await fetchFaqs("creators", "", "SharePal For Creators")
  const creatorFaqs = faqs.filter((faq) => faq.faq_type === "creators")
  return (
    <div className='container my-6 grid gap-4 rounded-3xl bg-gray-100 px-4 py-12 md:grid-cols-2 md:gap-8 md:py-16'>
      <div className='h-max w-full bg-gray-100 px-4'>
        <Typography
          as={"h3"}
          className='text-navy-900 mb-6 font-ubuntu text-h4 font-bold md:text-h1'
        >
          Frequently Asked Questions (FAQs)
        </Typography>

        <FAQS faqs={creatorFaqs} />
      </div>

      {/* ready to create */}
      <ReadyToCreate />
    </div>
  )
}

export default FaqSectionCreator
