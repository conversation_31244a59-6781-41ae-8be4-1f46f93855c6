"use client"
import { C<PERSON>Form } from "./creator-form"
import { StepCard } from "./step-card"
import { motion, useInView } from "framer-motion"
import { useRef } from "react"

const steps = [
  {
    number: 1,
    title: "Sign up & Get the Details",
    description:
      "Fill in a few details & sign up. You'll receive all the info on WhatsApp.",
    imageSrc:
      "https://images.sharepal.in/sharepal-for-creators/original/Form.png",
  },
  {
    number: 2,
    title: "Rent & Create",
    description:
      "Pick the gear you need from our website, rent it, and create amazing content.",
    imageSrc:
      "https://images.sharepal.in/sharepal-for-creators/original/Gear.png",
  },
  {
    number: 3,
    title: "Give Us a Shout-out",
    description: "Post 1 story and a dedicated reel on social media.",
    imageSrc:
      "https://images.sharepal.in/sharepal-for-creators/original/Megaphone.png",
  },
  {
    number: 4,
    title: "Refund & Cashback",
    description:
      "Once your content is posted, we'll refund the promised percentage of the total amount (if) you paid, as per our discussion",
    imageSrc:
      "https://images.sharepal.in/sharepal-for-creators/original/Coins.png",
  },
]

export function HowItWorksCrator() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  return (
    <section className='w-full bg-gray-50 py-16'>
      <div className='container mx-auto px-4'>
        <motion.h2
          ref={ref}
          initial={{ opacity: 0, y: -20 }}
          animate={isInView ? "visible" : "hidden"}
          variants={{
            visible: { opacity: 1, y: 0 },
            hidden: { opacity: 0, y: -20 },
          }}
          transition={{ duration: 0.8 }}
          className='mb-12 font-ubuntu text-d6 md:text-d4'
        >
          How it Works
        </motion.h2>

        <div className='grid gap-8 md:gap-14 lg:grid-cols-2'>
          <motion.div
            ref={ref}
            initial='hidden'
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
            className='h-max space-y-6'
          >
            {steps.map((step) => (
              <motion.div
                key={step.number}
                variants={cardVariants}
                transition={{ duration: 1 }}
              >
                <StepCard alternate={step.number % 2 === 0} {...step} />
              </motion.div>
            ))}
          </motion.div>
          <CreatorForm />
        </div>
      </div>
    </section>
  )
}
