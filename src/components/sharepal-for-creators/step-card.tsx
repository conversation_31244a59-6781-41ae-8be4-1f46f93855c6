import { Card, CardContent } from "../ui/card"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"

interface StepCardProps {
  number: number
  title: string
  description: string
  imageSrc: string
  alternate?: boolean
}

export function StepCard({
  number,
  title,
  description,
  imageSrc,
  alternate,
}: StepCardProps) {
  return (
    <Card className='relative h-40 max-h-40 rounded-3xl border border-primary-100 bg-gradient-to-b from-gray-100 to-primary-100 p-0'>
      <CardContent
        className={cn(
          "flex gap-4 p-0",
          alternate ? "flex-row-reverse pr-6" : "pl-6",
        )}
      >
        <div className='h-max flex-1 space-y-2 py-6'>
          <h3 className='text-h4 text-primary-900 md:text-h2'>
            {number}. {title}
          </h3>
          <p className='line-clamp-4 max-w-64 pl-6 text-neutral-500 md:max-w-sm'>
            {description}
          </p>
        </div>

        <SpImage
          src={imageSrc || "/placeholder.svg"}
          alt={title}
          // fill
          width={400}
          height={400}
          className='h-full w-full object-cover'
          containerClassName='relative  w-16 md:w-40 flex-shrink-0'
        />
      </CardContent>
    </Card>
  )
}
