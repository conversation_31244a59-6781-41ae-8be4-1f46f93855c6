"use client"

import { RentalItem } from "@/types"
import { useRef, useState } from "react"
import { ChevronLeftIcon, ChevronRightIcon } from "sharepal-icons"
import type { Swiper as SwiperType } from "swiper"
import { FreeMode, Mousewheel, Navigation } from "swiper/modules"
import { Swiper, SwiperSlide } from "swiper/react"
import ProductCard from "../cards/product-card"
import { Button } from "../ui/button"

// import ProductCardSkeleton from '../skeletons/product-card-skeleton'
import SectionTitle from "../section-title"

interface SampleProps {
  data: RentalItem[]
  city: string
  hideDateAndPrice?: boolean
  CustomTitle?: React.ReactNode
  handleFetch?: () => void
}

export function SwiperSlider({
  data,
  city,
  hideDateAndPrice = false,
  CustomTitle,
  // handleFetch,
}: SampleProps) {
  const swiperRef = useRef<SwiperType | null>(null)
  // const [isMounted, setIsMounted] = useState(false)
  const [isBeginning, setIsBeginning] = useState(true)
  const [isEnd, setIsEnd] = useState(false)

  const slidesPerView = {
    default: 2.3,
    sm: 3.5,
    md: 3.8,
    lg: 4.5,
    xl: 5.5,
    "2xl": 6.5,
  }

  const slidesPerGroup = {
    default: Math.floor(slidesPerView.default / 2), // Move ~half for smoother experience
    sm: Math.floor(slidesPerView.sm / 2),
    md: Math.floor(slidesPerView.md / 2),
    lg: slidesPerView.lg, // Move full slidesPerView on larger screens
    xl: slidesPerView.xl,
    "2xl": slidesPerView["2xl"],
  }

  const handlePrev = () => swiperRef.current?.slidePrev()
  const handleNext = () => swiperRef.current?.slideNext()

  const handleSwiperUpdate = (swiper: SwiperType) => {
    setIsBeginning(swiper.isBeginning)
    setIsEnd(swiper.isEnd)
  }

  // useEffect(() => {
  //   setIsMounted(true)
  // }, [])
  // useEffect(() => {
  //   console.log(isEnd)
  //   if (isEnd && handleFetch) handleFetch()
  // }, [isEnd])

  return (
    <div className='relative w-full px-0'>
      <div className='container relative mb-5 flex w-full items-center justify-between gap-3 md:mb-10'>
        {CustomTitle ? (
          CustomTitle
        ) : (
          <SectionTitle
            cText='people love to rent'
            nTColor='text-category-red'
            nText='Trending Items '
            className='max-w-72 pb-0 text-left md:w-full'
          />
        )}

        <div className='flex items-center justify-center gap-3'>
          <Button
            size='icon'
            onClick={handlePrev}
            variant='icon'
            className='hidden h-6 w-6 rounded-full border-gray-200 bg-gray-100 md:h-12 md:w-12 lg:flex'
            aria-label='Previous slide'
            disabled={isBeginning}
          >
            <ChevronLeftIcon className='size-4 text-gray-400 md:!size-6' />
          </Button>
          <Button
            size='icon'
            variant='icon'
            onClick={handleNext}
            className='hidden h-6 w-6 rounded-full border-gray-200 bg-gray-100 md:h-12 md:w-12 lg:flex'
            aria-label='Next slide'
            disabled={isEnd}
          >
            <ChevronRightIcon className='size-4 text-gray-400 md:!size-6' />
          </Button>
        </div>
      </div>

      {/* {isMounted ? ( */}
      <Swiper
        key={data.length}
        observer={true}
        observeParents={true}
        watchSlidesProgress={true}
        modules={[FreeMode, Mousewheel, Navigation]}
        freeMode={{
          enabled: true,
          sticky: false,
        }}
        mousewheel={{ forceToAxis: true }}
        slidesPerView={slidesPerView.default}
        slidesPerGroup={slidesPerGroup.default}
        onBeforeInit={(swiper) => {
          swiperRef.current = swiper
        }}
        onSlideChange={handleSwiperUpdate}
        onInit={(swiper) => {
          swiper.update()
          handleSwiperUpdate(swiper)
        }}
        onResize={(swiper) => {
          swiper.update()
        }}
        breakpoints={{
          640: {
            slidesPerView: slidesPerView.sm,
            slidesPerGroup: slidesPerGroup.sm,
            spaceBetween: 20,
          },
          768: {
            slidesPerView: slidesPerView.md,
            slidesPerGroup: slidesPerGroup.md,
            spaceBetween: 20,
          },
          1024: {
            slidesPerView: slidesPerView.lg,
            slidesPerGroup: slidesPerGroup.lg,
            spaceBetween: 24,
          },
          1480: {
            slidesPerView: slidesPerView.xl,
            slidesPerGroup: slidesPerGroup.xl,
            spaceBetween: 50,
          },
          1966: {
            slidesPerView: slidesPerView["2xl"],
            slidesPerGroup: slidesPerGroup["2xl"],
            spaceBetween: 70,
          },
        }}
        className='mySwiper !pl-[max(calc((100vw-1200px)/2),1rem)] !pr-4 md:!pr-6 lg:!pr-8'
        direction='horizontal'
      >
        {data.map((item, index) => (
          <SwiperSlide key={index} className='max-w-56 md:max-w-64'>
            <ProductCard
              data={item}
              city={city}
              hideDateAndPrice={hideDateAndPrice}
              className='w-full'
            />
          </SwiperSlide>
        ))}
      </Swiper>
      {/* ) : (
        <div className="flex space-x-6 overflow-hidden !pl-[max(calc((100vw-1200px)/2),1rem)]">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="w-full max-w-56 md:max-w-64">
              <ProductCardSkeleton hideDateAndPrice={hideDateAndPrice} />
            </div>
          ))}
        </div>
      )} */}
    </div>
  )
}
