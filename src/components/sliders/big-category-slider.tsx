"use client"

import { SubCategory } from "@/types"
import { ArrowRight } from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"
import BigCategoryCard from "../cards/big-category-card"
import BigCategoryCardSwipper from "../cards/big-category-swipper"

const BigCategorySlider = ({
  subCategories,
  city,
  category,
}: {
  subCategories: SubCategory[]
  city: string
  category: string
}) => {
  const [adminOnly, setAdminOnly] = useState(false)
  useEffect(() => {
    setAdminOnly(
      ((window &&
        window.sessionStorage &&
        window.sessionStorage.getItem("backend_order")) ??
        "") == "true",
    )
  }, [])
  return (
    <div className='bg-top-categoies-gradient'>
      {/* Desktop */}

      <BigCategoryCardSwipper
        adminOnly={adminOnly}
        subCategories={subCategories}
        city={city}
      />

      {/* Mobile */}
      <div className='grid grid-cols-3 items-center justify-start gap-2 overflow-x-auto px-3 py-5 md:hidden'>
        {subCategories
          .filter((data) => !data.admin_only || adminOnly == data.admin_only)
          ?.map((item) => (
            <BigCategoryCard key={item.id} data={item} city={city} />
          ))}
        <Link
          href={
            city +
            "/" +
            subCategories.filter((data) => data.category_url == category)[0]
              ?.super_category_url
          }
          className=''
        >
          <div className='group relative flex h-full min-h-[120px] flex-col items-center justify-center gap-2 overflow-hidden rounded-xl border border-primary-150 bg-gray-100 text-sh7 font-semibold leading-5 text-primary-500 transition-all duration-300 hover:border-gray-100 hover:bg-primary-100 md:min-w-52 md:p-2 md:text-sh5'>
            View All
            <div className='flex h-6 w-6 items-center justify-center rounded-full bg-primary-150 sm:h-7 sm:w-7'>
              <ArrowRight className='w-4 text-primary-500' />
            </div>
          </div>
        </Link>
      </div>
    </div>
  )
}

export default BigCategorySlider
