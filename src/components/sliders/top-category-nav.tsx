"use client"

import { Chevron<PERSON>eft, ChevronRight } from "lucide-react"
import { useParams } from "next/navigation"
import React, { useCallback, useEffect, useRef, useState } from "react"
import type { Swiper as SwiperType } from "swiper"
import { <PERSON>M<PERSON>, Mousewheel, Navigation } from "swiper/modules"
import { Swiper, SwiperSlide } from "swiper/react"

import { SubCategory } from "@/types"
import { SuperCategory } from "@/types/super-category"
import { Button } from "../ui/button"
import CategoryCarouselItem from "./category-carouse-item"

interface CategorySliderProps {
  data: SuperCategory[]
  city: string
  allSubCategories: SubCategory[]
}

const TopCategoryNav: React.FC<CategorySliderProps> = ({
  data,
  city,
  allSubCategories,
}) => {
  const swiperRef = useRef<SwiperType | null>(null)
  const [isBeginning, setIsBeginning] = useState(true)
  const [isEnd, setIsEnd] = useState(false)
  const { cat: activeCategoryUrl } = useParams<{ cat: string }>()

  const handlePrev = useCallback(() => swiperRef.current?.slidePrev(), [])
  const handleNext = useCallback(() => swiperRef.current?.slideNext(), [])

  const handleSwiperUpdate = useCallback((swiper: SwiperType) => {
    setIsBeginning(swiper.isBeginning)
    setIsEnd(swiper.isEnd)
  }, [])

  // Function to find the closest matching category URL
  const findClosestMatch = useCallback(
    (url: string) => {
      const normalizedUrl = url.replace(/-on-rent$/, "").toLowerCase()
      return data.findIndex((item) =>
        item.url
          .replace(/-on-rent$/, "")
          .toLowerCase()
          .includes(normalizedUrl),
      )
    },
    [data],
  )

  const [adminOnly, setAdminOnly] = useState(false)
  useEffect(() => {
    setAdminOnly(
      ((window &&
        window.sessionStorage &&
        window.sessionStorage.getItem("backend_order")) ??
        "") == "true",
    )
  }, [])

  // Move to the active item when the activeCategoryUrl changes
  useEffect(() => {
    if (swiperRef.current && activeCategoryUrl) {
      const activeIndex = findClosestMatch(activeCategoryUrl)
      if (activeIndex !== -1) {
        swiperRef.current.slideTo(activeIndex)
      }
    }
  }, [activeCategoryUrl, findClosestMatch])

  return (
    <div className='relative mx-auto w-full max-w-3xl px-8'>
      <Swiper
        modules={[FreeMode, Mousewheel, Navigation]}
        freeMode
        mousewheel={{ forceToAxis: true }}
        slidesPerView='auto'
        spaceBetween={10}
        onBeforeInit={(swiper) => (swiperRef.current = swiper)}
        onInit={handleSwiperUpdate}
        onSlideChange={handleSwiperUpdate}
        onResize={handleSwiperUpdate}
        className='flex w-full items-center'
      >
        {data.map((item, index) => {
          const relevantSubCategories = allSubCategories.filter(
            (subCat) =>
              subCat.super_category_url === item.url &&
              (!subCat.admin_only || adminOnly == subCat.admin_only),
          )

          return (
            <SwiperSlide
              key={item.url || index}
              className='basis-[40%] px-2 py-6 pl-2 text-center sm:basis-1/2 md:basis-1/3 md:px-4 lg:basis-1/4'
            >
              <CategoryCarouselItem
                item={item}
                city={city}
                subCategories={relevantSubCategories}
              />
            </SwiperSlide>
          )
        })}
      </Swiper>

      {/* Navigation Buttons */}
      <Button
        size='icon'
        onClick={handlePrev}
        variant='icon'
        className='absolute left-1 top-1/2 flex h-6 w-7 -translate-y-1/2 rounded-full border-none hover:border hover:border-gray-100 hover:bg-primary-900 md:h-8 md:w-8'
        disabled={isBeginning}
      >
        <ChevronLeft className='size-4 text-gray-400' />
      </Button>

      <Button
        size='icon'
        onClick={handleNext}
        variant='icon'
        className='absolute right-1 top-1/2 flex h-7 w-7 -translate-y-1/2 rounded-full border-none hover:border hover:border-gray-100 hover:bg-primary-900 md:h-8 md:w-8'
        disabled={isEnd}
      >
        <ChevronRight className='size-4 text-gray-100' />
      </Button>
    </div>
  )
}

export default React.memo(TopCategoryNav)
