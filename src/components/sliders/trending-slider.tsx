"use client"

import { RentalItem } from "@/types"
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react"
import { useInView } from "react-intersection-observer"
import { ChevronLeftIcon, ChevronRightIcon } from "sharepal-icons"
import type { Swiper as SwiperType } from "swiper"
import { FreeMode, Mousewheel, Navigation } from "swiper/modules"
import { Swiper, SwiperSlide } from "swiper/react"
import ProductCard from "../cards/product-card"
import SectionTitle from "../section-title"
import ProductCardSkeleton from "../skeletons/product-card-skeleton"
import { Button } from "../ui/button"

interface TrendingSliderProps {
  data: RentalItem[]
  city: string
  hideDateAndPrice?: boolean
  CustomTitle?: React.ReactNode
  handleFetch?: () => void
  isLoading?: boolean
  isRefetching?: boolean
}

// Memoized components to prevent unnecessary re-renders
const MemoizedProductCard = memo(ProductCard)
const MemoizedProductCardSkeleton = memo(ProductCardSkeleton)

// Constants outside component to prevent recreating on each render
const SLIDES_PER_VIEW = {
  default: 2.3,
  sm: 3.5,
  md: 3.8,
  lg: 4.5,
  xl: 5.5,
  "2xl": 6.5,
} as const

const SLIDES_PER_GROUP = {
  default: Math.floor(SLIDES_PER_VIEW.default / 2),
  sm: Math.floor(SLIDES_PER_VIEW.sm / 2),
  md: Math.floor(SLIDES_PER_VIEW.md / 2),
  lg: SLIDES_PER_VIEW.lg,
  xl: SLIDES_PER_VIEW.xl,
  "2xl": SLIDES_PER_VIEW["2xl"],
} as const

const SWIPER_BREAKPOINTS = {
  640: {
    slidesPerView: SLIDES_PER_VIEW.sm,
    slidesPerGroup: SLIDES_PER_GROUP.sm,
    spaceBetween: 20,
  },
  768: {
    slidesPerView: SLIDES_PER_VIEW.md,
    slidesPerGroup: SLIDES_PER_GROUP.md,
    spaceBetween: 20,
  },
  1024: {
    slidesPerView: SLIDES_PER_VIEW.lg,
    slidesPerGroup: SLIDES_PER_GROUP.lg,
    spaceBetween: 24,
  },
  1480: {
    slidesPerView: SLIDES_PER_VIEW.xl,
    slidesPerGroup: SLIDES_PER_GROUP.xl,
    spaceBetween: 50,
  },
  1966: {
    slidesPerView: SLIDES_PER_VIEW["2xl"],
    slidesPerGroup: SLIDES_PER_GROUP["2xl"],
    spaceBetween: 70,
  },
} as const

export function TrendingSlider({
  data,
  city,
  hideDateAndPrice = false,
  CustomTitle,
  handleFetch,
  isLoading = false,
  isRefetching = false,
}: TrendingSliderProps) {
  const swiperRef = useRef<SwiperType | null>(null)
  const [isBeginning, setIsBeginning] = useState(true)
  const [isEnd, setIsEnd] = useState(false)

  // Memoize the intersection observer options
  const observerOptions = useMemo(
    () => ({
      threshold: 0.5,
      triggerOnce: false,
    }),
    [],
  )

  const { ref: loadMoreRef, inView } = useInView(observerOptions)

  // Memoize handlers to prevent recreating on each render
  const handlePrev = useCallback(() => {
    swiperRef.current?.slidePrev()
  }, [])

  const handleNext = useCallback(() => {
    swiperRef.current?.slideNext()
  }, [])

  const handleSwiperUpdate = useCallback((swiper: SwiperType) => {
    setIsBeginning(swiper.isBeginning)
    setIsEnd(swiper.isEnd)
  }, [])

  // Handle infinite loading with debounce
  useEffect(() => {
    if (inView && handleFetch && !isRefetching && !isLoading) {
      handleFetch()
    }
  }, [inView, handleFetch, isRefetching, isLoading])

  // Memoize loading skeleton
  const LoadingSkeleton = useMemo(
    () => (
      <div className='flex overflow-hidden !pl-[max(calc((100vw-1200px)/2),1rem)]'>
        {Array.from({ length: 5 }).map((_, index) => (
          <div
            key={index}
            className='w-full max-w-56 shrink-0 px-2 md:max-w-64'
          >
            <MemoizedProductCardSkeleton hideDateAndPrice={hideDateAndPrice} />
          </div>
        ))}
      </div>
    ),
    [hideDateAndPrice],
  )

  // Memoize navigation buttons
  const NavigationButtons = useMemo(
    () => (
      <div className='flex items-center gap-2'>
        <Button
          variant='outline'
          size='icon'
          onClick={handlePrev}
          disabled={isBeginning}
          className='h-8 w-8 rounded-full border-neutral-300 md:h-10 md:w-10'
        >
          <ChevronLeftIcon className='h-4 w-4 md:h-5 md:w-5' />
        </Button>
        <Button
          variant='outline'
          size='icon'
          onClick={handleNext}
          disabled={isEnd}
          className='h-8 w-8 rounded-full border-neutral-300 md:h-10 md:w-10'
        >
          <ChevronRightIcon className='h-4 w-4 md:h-5 md:w-5' />
        </Button>
      </div>
    ),
    [handleNext, handlePrev, isBeginning, isEnd],
  )

  return (
    <div className='relative w-full px-0'>
      <div className='container relative mb-5 flex w-full items-center justify-between gap-3 md:mb-10'>
        {CustomTitle ? (
          CustomTitle
        ) : (
          <SectionTitle
            cText='people love to rent'
            nTColor='text-category-red'
            nText='Trending Items '
            className='max-w-72 pb-0 text-left md:w-full'
          />
        )}
        {NavigationButtons}
      </div>

      {isLoading ? (
        LoadingSkeleton
      ) : (
        <Swiper
          observer={true}
          observeParents={true}
          modules={[FreeMode, Mousewheel, Navigation]}
          freeMode={{
            enabled: true,
            sticky: false,
            momentumBounce: false,
          }}
          mousewheel={{ forceToAxis: true }}
          slidesPerView={SLIDES_PER_VIEW.default}
          slidesPerGroup={SLIDES_PER_GROUP.default}
          onBeforeInit={(swiper) => {
            swiperRef.current = swiper
          }}
          onSlideChange={handleSwiperUpdate}
          onInit={handleSwiperUpdate}
          breakpoints={SWIPER_BREAKPOINTS}
          className='mySwiper !pl-[max(calc((100vw-1200px)/2),1rem)] !pr-4 md:!pr-6 lg:!pr-8'
          direction='horizontal'
        >
          {data.map((item, index) => (
            <SwiperSlide
              key={item.ri_name + index}
              className='max-w-56 md:max-w-64'
            >
              <MemoizedProductCard
                data={item}
                city={city}
                hideDateAndPrice={hideDateAndPrice}
                className='w-full'
              />
            </SwiperSlide>
          ))}

          {isRefetching && (
            <>
              {Array.from({ length: 3 }).map((_, index) => (
                <SwiperSlide
                  key={`loading-${index}`}
                  className='max-w-56 md:max-w-64'
                >
                  <MemoizedProductCardSkeleton
                    hideDateAndPrice={hideDateAndPrice}
                  />
                </SwiperSlide>
              ))}
            </>
          )}

          <SwiperSlide className='!w-0'>
            <div ref={loadMoreRef} className='h-full w-1' />
          </SwiperSlide>
        </Swiper>
      )}
    </div>
  )
}

// Memoize the entire component
export default memo(TrendingSlider)
