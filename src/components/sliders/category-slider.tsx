"use client"

import React, { useRef, useState } from "react"
import { Swiper, SwiperSlide } from "swiper/react"
import { FreeMode, Mousewheel } from "swiper/modules"
import type { Swiper as SwiperType } from "swiper"
import { ChevronLeftIcon, ChevronRightIcon } from "sharepal-icons"

import SmallCategoryCard from "../cards/small-category-card"
import { Category } from "@/types"

import { Button } from "../ui/button"

interface SampleProps {
  data: Category[]
  category: string
}

export function CategorySlider({ data, category }: SampleProps) {
  const swiperRef = useRef<SwiperType | null>(null)
  const [isBeginning, setIsBeginning] = useState(true)
  const [isEnd, setIsEnd] = useState(false)

  const handlePrev = () => {
    swiperRef.current?.slidePrev()
  }

  const handleNext = () => {
    swiperRef.current?.slideNext()
  }

  const handleSwiperUpdate = (swiper: SwiperType) => {
    setIsBeginning(swiper.isBeginning)
    setIsEnd(swiper.isEnd)
  }

  return (
    <div className='container relative px-0 md:px-4'>
      <Swiper
        modules={[FreeMode, Mousewheel]}
        freeMode={{
          enabled: true,
          sticky: false,
        }}
        mousewheel={{
          forceToAxis: true,
        }}
        slidesPerView='auto'
        spaceBetween={0}
        onBeforeInit={(swiper) => {
          swiperRef.current = swiper
        }}
        onInit={handleSwiperUpdate}
        onSlideChange={handleSwiperUpdate}
        onResize={handleSwiperUpdate} // Handle responsive changes
        // className="!overflow-visible"
        className='!px-2 md:!px-0'
      >
        <div className='-ml-2 flex w-full px-4 md:-ml-4'>
          {data?.map((item, index) => (
            <SwiperSlide
              key={index}
              className='!w-auto basis-[22%] pr-2 sm:basis-1/5 md:basis-1/5 lg:basis-[15%] xl:basis-[12%]'
            >
              <SmallCategoryCard category={category} data={item} />
            </SwiperSlide>
          ))}
        </div>
      </Swiper>

      {/* Blur effect on left end when scrolling or moving next */}
      {!isBeginning && (
        <div className='absolute left-0 top-0 z-[1] h-full w-[5%] bg-gradient-to-l from-transparent to-neutral-150 md:left-1'></div>
      )}

      {/* Blur effect on right end */}
      <div className='absolute right-0 top-0 z-[1] h-full w-[5%] bg-gradient-to-r from-transparent to-neutral-150'></div>

      <Button
        size={"icon"}
        onClick={handlePrev}
        variant={"icon"}
        className='absolute -left-7 top-1/3 hidden h-8 w-8 -translate-y-1/2 rounded-full border-gray-200 bg-gray-100 lg:flex'
        aria-label='Previous slide'
        disabled={isBeginning}
      >
        <ChevronLeftIcon className='size-4 text-gray-300' />
      </Button>
      <Button
        size={"icon"}
        variant={"icon"}
        onClick={handleNext}
        className='absolute -right-12 top-1/3 hidden h-8 w-8 -translate-y-1/2 rounded-full border-gray-200 bg-gray-100 lg:flex'
        aria-label='Next slide'
        disabled={isEnd}
      >
        <ChevronRightIcon className='size-4 text-gray-300' />
      </Button>
    </div>
  )
}
