"use client"

import useMediaQuery from "@/hooks/use-media-query"
import { cn } from "@/lib/utils"
import { SubCategory } from "@/types"
import { SuperCategory } from "@/types/super-category"
import { getSuperCategoryBgColor } from "@/utils/get-bg-color"
import { AnimatePresence, motion } from "framer-motion"
import Link from "next/link"
import { useParams } from "next/navigation"
import { useEffect, useMemo, useRef, useState } from "react"
import { createPortal } from "react-dom"
import { Separator } from "../ui/separator"

interface CategoryCarouselItemProps {
  item: SuperCategory
  city: string
  subCategories: SubCategory[]
}

const CategoryCarouselItem = ({
  item,
  city,
  subCategories,
}: CategoryCarouselItemProps) => {
  const [isHovered, setIsHovered] = useState(false)
  const [isPortalMounted, setIsPortalMounted] = useState(false)
  const itemRef = useRef<HTMLDivElement>(null)
  const [popoverPosition, setPopoverPosition] = useState({ top: 20, left: 0 })
  const isDesktop = useMediaQuery("(min-width: 768px)")
  // const pathname = usePathname()
  const { cat } = useParams<{
    city: string
    cat: string
    subcat: string
  }>()
  // Filter unique subcategories
  const uniqueSubCategories = useMemo(
    () =>
      subCategories.filter(
        (subCat, index, self) =>
          self.findIndex((a) => a.sc_name_ri === subCat.sc_name_ri) === index,
      ),
    [subCategories],
  )

  // Calculate number of columns needed
  const columnCount = useMemo(
    () => Math.ceil(uniqueSubCategories.length / 4),
    [uniqueSubCategories.length],
  )

  const columnWidth = 240 // Width of each column in pixels

  // Handle client-side portal mounting
  useEffect(() => {
    setIsPortalMounted(true)
    return () => setIsPortalMounted(false)
  }, [])

  // Update popover position when hovered
  useEffect(() => {
    if (isHovered && itemRef.current && isDesktop) {
      const rect = itemRef.current.getBoundingClientRect()
      const totalWidth = columnWidth * columnCount

      setPopoverPosition({
        top: rect.bottom + window.scrollY + 10,
        left: Math.max(
          0,
          rect.left + window.scrollX - (totalWidth - rect.width) / 2,
        ),
      })
    }
  }, [isHovered, columnCount, isDesktop, columnWidth])

  // Animation variants for the popover
  const popoverVariants = {
    hidden: {
      opacity: 0,
      y: -10,
      transition: {
        duration: 0.2,
        ease: "easeInOut",
      },
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
  }

  // Organize subcategories into columns
  const renderColumns = () => {
    const columns = []
    for (let i = 0; i < columnCount; i++) {
      columns.push(
        <div key={i} className='flex flex-1 flex-col gap-4'>
          {uniqueSubCategories.slice(i * 4, (i + 1) * 4).map((subCat) => (
            <div key={subCat.id}>
              <Link
                href={`/${city}/${item.url}/${subCat.url}`}
                className={cn(
                  "line-clamp-1 block rounded-lg px-2 py-1 !text-b4 text-neutral-900 transition-colors duration-200 hover:bg-gray-150",
                )}
              >
                {subCat.sc_name}
              </Link>
            </div>
          ))}
        </div>,
      )
    }
    return columns
  }

  return (
    <div
      ref={itemRef}
      className='relative w-full cursor-pointer'
      onMouseEnter={() => isDesktop && setIsHovered(true)}
      onMouseLeave={() => isDesktop && setIsHovered(false)}
    >
      <Link
        href={`/${city}/${item.url}`}
        className='relative inline-block w-full max-w-44 px-3'
      >
        <span
          className={cn(
            "inline-block transition-transform duration-200 hover:!text-sh5 hover:text-gray-100",
            item.url.includes(cat)
              ? "!text-sh4 text-gray-100"
              : "!text-sh5 text-neutral-250",
          )}
        >
          {item.super_category_short_name}
        </span>
        {item.url.includes(cat) && (
          <Separator
            orientation='horizontal'
            className={cn(
              "absolute left-1/2 mx-auto mt-2 h-[2px] w-full -translate-x-1/2 rounded-full bg-red-50 md:w-10/12",
              getSuperCategoryBgColor(item.url),
            )}
          />
        )}
      </Link>

      {isDesktop &&
        isPortalMounted &&
        typeof window !== "undefined" &&
        createPortal(
          <AnimatePresence>
            {isHovered && (
              <motion.div
                className='fixed z-50 mx-5 flex gap-6 overflow-hidden rounded-2xl bg-gray-100 p-6 shadow-lg md:rounded-3xl'
                style={{
                  top: `${popoverPosition.top}px`,
                  left: `${popoverPosition.left}px`,
                  width: `${columnWidth * columnCount}px`,
                }}
                initial='hidden'
                animate='visible'
                exit='hidden'
                variants={popoverVariants}
              >
                {renderColumns()}
              </motion.div>
            )}
          </AnimatePresence>,
          document.body,
        )}
    </div>
  )
}

export default CategoryCarouselItem
