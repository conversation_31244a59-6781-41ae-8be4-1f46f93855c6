"use client"
import { cn } from "@/lib/utils"
import type { SubCategory } from "@/types"
import { useParams } from "next/navigation"
import { useEffect, useState, useSyncExternalStore } from "react"
import { SwiperSlide } from "swiper/react"
import SubCategoryCard from "../cards/sub-category-card"
import { SwiperWrapper } from "../custom/swiper-wrapper"

const getScrollProgress = () => {
  const currentScroll = window.scrollY // using window.scrollY to get the current Scroll as the useScroll was taking time to provided the updated value when navigating the page.
  return Math.min(Math.max((currentScroll - 400) / 100, 0), 1)
}

// Custom hook to track scroll progress
const useScrollProgress = () =>
  useSyncExternalStore(
    (callback) => {
      if (typeof window !== "undefined") {
        window.addEventListener("scroll", callback)
        return () => window.removeEventListener("scroll", callback)
      }
      return () => {}
    },
    // getSnapshot for client
    () => (typeof window !== "undefined" ? getScrollProgress() : 0),
    // getServerSnapshot for SSR
    () => 0,
  )

interface ProductSubcategorySliderProps {
  data: SubCategory[]
}

export function ProductSubcategorySlider({
  data,
}: ProductSubcategorySliderProps) {
  const uniqueData: SubCategory[] =
    Array.from(new Set(data.map((a) => a.sc_name_ri)))
      .map((sc_name_ri) => data.find((a) => a.sc_name_ri === sc_name_ri))
      .filter((item): item is SubCategory => item !== undefined) || data

  const scrollProgress = useScrollProgress()
  const [activeCardID, setActiveCardID] = useState(0)

  // Get URL params to determine active subcategory
  const { subcat } = useParams<{ subcat?: string }>()

  // Find the active card index based on URL params and update state
  useEffect(() => {
    if (!subcat) {
      // "All" is selected
      setActiveCardID(0)
    } else {
      // Find the index of the active subcategory
      const activeIndex = uniqueData.findIndex((item) => item.url === subcat)
      if (activeIndex !== -1) {
        // Add 1 because "All" card is at index 0
        setActiveCardID(activeIndex + 1)
      }
    }
  }, [subcat, uniqueData])

  const [adminOnly, setAdminOnly] = useState(false)
  useEffect(() => {
    setAdminOnly(
      ((window &&
        window.sessionStorage &&
        window.sessionStorage.getItem("backend_order")) ??
        "") == "true",
    )
  }, [])

  return (
    <div className={cn("")}>
      <div className='container sticky z-[10] w-full rounded-2xl border bg-neutral-100 p-2 shadow-sm md:rounded-3xl md:p-4 md:pt-6'>
        <SwiperWrapper
          breakpoints={{
            300: { slidesPerView: 3.5, slidesPerGroup: 3.5 },
            400: { slidesPerView: 3.5, slidesPerGroup: 4.5 },
            600: { slidesPerView: 5.5, slidesPerGroup: 5.5 },
            1024: { slidesPerView: 6.5, slidesPerGroup: 6.5 },
          }}
          enableFreeMode
          spaceBetween={20}
          moveToCard
          moveToCardIndex={activeCardID}
          showNavigation={false}
        >
          <SwiperSlide>
            <SubCategoryCard isAll scrollProgress={scrollProgress} />
          </SwiperSlide>
          {uniqueData.map((item) =>
            !item.admin_only || item.admin_only == adminOnly ? (
              <SwiperSlide key={item.sc_name_ri}>
                <SubCategoryCard
                  scrollProgress={scrollProgress}
                  setActiveCardID={setActiveCardID}
                  data={item}
                />
              </SwiperSlide>
            ) : (
              <></>
            ),
          )}
        </SwiperWrapper>
      </div>
    </div>
  )
}
