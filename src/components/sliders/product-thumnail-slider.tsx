"use client"

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import * as React from "react"

interface ProductThumbnailStripProps {
  images: string[]
  currentIndex: number
  onThumbnailClick: (index: number) => void
  orientation?: "vertical" | "horizontal"
}

const ProductThumbnailStrip = React.memo(
  ({
    images,
    currentIndex,
    onThumbnailClick,
    orientation = "vertical",
  }: ProductThumbnailStripProps) => {
    const [api, setApi] = React.useState<CarouselApi>()

    // console.log(orientation)
    React.useEffect(() => {
      if (api) {
        api.scrollTo(Math.floor(currentIndex / 3) * 3)
      }
    }, [api, currentIndex])

    return (
      <Carousel
        setApi={setApi}
        opts={{
          align: "start",
          // vertical: orientation === 'vertical',
        }}
        className='group/thumbnail w-full rounded-lg bg-neutral-150 px-1'
        orientation={orientation}
        //   className="h-full w-full"
      >
        <CarouselContent
          className={cn(
            orientation === "vertical" ? "-mt-2 h-[470px]" : "-ml-2 h-max",
            "p-2",
          )}
        >
          {images.map((img, idx) => (
            <CarouselItem
              onClick={() => onThumbnailClick(idx)}
              key={idx}
              className={cn(
                "rounded-m flex items-center justify-center rounded-lg bg-gray-100 p-1 md:p-2",
                currentIndex === idx && "border border-primary-500",
                orientation === "vertical" ? "mt-2" : "ml-2",
                orientation === "vertical"
                  ? "basis-1/4]" // ] this is working don't remove it
                  : "basis-1/6 sm:basis-1/6",
              )}
            >
              <SpImage
                src={img.trim()}
                alt={`Product thumbnail ${idx + 1}`}
                width={60}
                height={60}
                className='rounded-md object-cover'
                sizes={orientation === "vertical" ? "w-4 h-4" : "25vw"}
                // sizes={orientation === 'vertical' ? '80px' : '25vw'}
                // loading={idx < 4 ? 'eager' : 'lazy'}
              />
            </CarouselItem>
          ))}
        </CarouselContent>
        {orientation === "vertical" && (
          <>
            <CarouselPrevious
              className={
                "-top-4 left-1/2 hidden -translate-x-1/2 transition-opacity group-hover/thumbnail:flex"
                // '-top-4 left-1/2 -translate-x-1/2 transition-opacity'
              }
            />
            <CarouselNext
              className='-bottom-4 left-1/2 hidden -translate-x-1/2 transition-opacity group-hover/thumbnail:flex'
              // className="-bottom-4 left-1/2 -translate-x-1/2 transition-opacity"
            />
          </>
        )}
      </Carousel>
    )
  },
)

ProductThumbnailStrip.displayName = "ProductThumbnailStrip"

export default ProductThumbnailStrip
