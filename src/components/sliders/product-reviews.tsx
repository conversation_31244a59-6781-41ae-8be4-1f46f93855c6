"use client"

import * as React from "react"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import { Card, CardContent } from "@/components/ui/card"
// import Autoplay from 'embla-carousel-autoplay'
interface Review {
  category_rented: string
  customer_occupation: string
  full_review: string
  period: string
  review_platform: string
  review_summary: string
  reviewer_city: string
  reviewer_first_name: string
  reviewer_last_name: string
  reviewer_rating: number
  reviewer_image: string
}

type ProductReviewProps = {
  reviews: Review[]
}

export default function ProductReviews({ reviews }: ProductReviewProps) {
  // const plugin = React.useRef(
  //   Autoplay({ delay: 2000, stopOnInteraction: true }),
  // )

  return (
    <div className='mx-auto w-full max-w-5xl'>
      <Carousel
        // plugins={[plugin.current]}
        opts={{
          align: "center",
          loop: true,
        }}
        className='w-full'
        // onMouseEnter={plugin.current.stop}
        // onMouseLeave={plugin.current.reset}
      >
        <CarouselContent>
          {reviews.map((review, index) => (
            <CarouselItem key={index}>
              <Card className='border-none'>
                <CardContent className='flex flex-col items-center justify-center p-6'>
                  <blockquote className='text-center'>
                    <h5 className='mb-6 line-clamp-3 text-pretty font-ubuntu text-2xl font-bold sm:text-3xl md:text-4xl lg:text-5xl'>
                      &quot;{review.full_review}&quot;
                    </h5>
                    <footer className='text-center'>
                      <cite className='not-italic'>
                        <div className='text-lg font-semibold'>
                          {review.reviewer_first_name}{" "}
                          {review.reviewer_last_name}
                        </div>
                        <div className='text-muted-foreground'>
                          {review.reviewer_city} • {review.reviewer_rating}
                        </div>
                      </cite>
                    </footer>
                  </blockquote>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className='-left-12 hidden sm:-left-16 sm:flex' />
        <CarouselNext className='-right-12 hidden sm:-right-16 sm:flex' />
      </Carousel>
    </div>
  )
}
