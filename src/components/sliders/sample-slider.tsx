"use client"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import ProductCard from "../cards/product-card"
import { RentalItem } from "@/types"
import useSurgeProduct from "@/hooks/use-prodcut-surge"

interface SampleProps {
  data: RentalItem[]
  city: string
  hideDateAndPrice?: boolean
}

export function SampleSlider({
  data,
  city,
  hideDateAndPrice = true,
}: SampleProps) {
  useSurgeProduct({
    type: "ri_names",
    ri_names: data.map((item) => item.ri_name),
  })

  return (
    <Carousel
      opts={{
        align: "start",
        dragFree: true,
      }}
      className='container w-full'
      draggable={true}
    >
      {/* <CarouselPrevious className="right-10 top-0" />
      <CarouselNext className="right-10 top-0" /> */}

      <CarouselContent className='w-full'>
        {/* {Array.from({ length: 5 }).map((_, index) => (
          <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3">
            <div className="p-1">
              <Card>
                <CardContent className="flex aspect-square items-center justify-center p-6">
                  <span className="text-3xl font-semibold">{index + 1}</span>
                </CardContent>
              </Card>
            </div>
          </CarouselItem>
        ))} */}
        {data?.map((item, index) => (
          <CarouselItem
            key={index}
            className='basis-1/2 md:basis-1/3 lg:basis-[22%]'
            // pl- or ml- for spacing
          >
            {/* <div className="p-1"> */}
            {/* <Card>
                <CardContent className="flex aspect-square items-center justify-center p-6"> */}
            {/* <span className="text-3xl font-semibold">{index + 1}</span> */}
            <ProductCard
              data={item}
              city={city}
              hideDateAndPrice={hideDateAndPrice}
            />
            {/* </CardContent>
              </Card> */}
            {/* </div> */}
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious className='-top-16 right-20 hidden h-12 w-12 md:flex [&_svg]:size-5' />
      <CarouselNext className='-top-16 right-4 hidden h-12 w-12 md:flex [&_svg]:size-5' />
    </Carousel>
  )
}
