"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel"
import SpImage from "@/shared/SpImage/sp-image"
import { AnimatePresence, motion } from "framer-motion"
import { Share2 } from "lucide-react"
import * as React from "react"
import FullscreenImageDialog from "./fullscreen-image-dialog"
import ProductThumbnailStrip from "./product-thumnail-slider"

type ProductSliderProps = {
  images: string[]
}
const ProductSlider = ({ images }: ProductSliderProps) => {
  const [api, setApi] = React.useState<CarouselApi>()
  const [current, setCurrent] = React.useState(0)
  const [count, setCount] = React.useState(0)
  const [isFullscreen, setIsFullscreen] = React.useState(false)

  React.useEffect(() => {
    if (!api) {
      return
    }

    setCount(api.scrollSnapList().length)
    setCurrent(api.selectedScrollSnap())

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap())
    })
  }, [api])

  const handleShare = React.useCallback(() => {
    if (navigator.share) {
      navigator.share({
        title: "Check out this product",
        url: window.location.href,
      })
    } else {
      // Fallback - copy to clipboard
      navigator.clipboard.writeText(window.location.href)
    }
  }, [])

  const openFullscreen = React.useCallback((index: number) => {
    setCurrent(index)
    setIsFullscreen(true)
  }, [])

  return (
    <>
      <div className='group flex min-h-[250px] w-full flex-col items-center gap-4 rounded-2xl bg-gray-100 p-3 md:min-h-[400px] md:flex-row md:gap-6 md:rounded-3xl md:p-6'>
        {/* Desktop Thumbnails - Left Side */}
        <div className='relative hidden md:block md:w-24'>
          <ProductThumbnailStrip
            images={images}
            currentIndex={current}
            onThumbnailClick={(index) => {
              api?.scrollTo(index)
              // Don't open fullscreen on thumbnail click for desktop
            }}
            orientation='vertical'
          />
        </div>

        {/* Main Image Carousel */}
        <div className='relative w-full md:flex-1'>
          <Button
            size='icon'
            variant='ghost'
            className='absolute right-3 top-0 z-10 md:-top-2'
            onClick={handleShare}
          >
            <Share2 className='h-5 w-5' />
          </Button>

          <Carousel
            setApi={setApi}
            className='relative w-full'
            opts={{
              dragFree: false,
            }}
          >
            <CarouselContent>
              <AnimatePresence mode='wait'>
                {images.map((img, idx) => (
                  <CarouselItem key={idx}>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                      className='relative aspect-[4/3] w-full overflow-hidden rounded-lg md:aspect-[4/3]'
                    >
                      <div
                        className='absolute inset-0 z-10 cursor-zoom-in'
                        onClick={() => openFullscreen(idx)}
                      />
                      <SpImage
                        src={img.trim()}
                        alt={`Product image ${idx + 1}`}
                        fill
                        className='scale-95 rounded-md object-contain'
                        priority={idx === 0}
                        sizes='(min-width: 768px) calc(100vw - 144px), 100vw'
                        placeholder='blur'
                        blurDataURL='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mN8/+F9PQAI8wNPvd7POQAAAABJRU5ErkJggg=='
                      />
                    </motion.div>
                  </CarouselItem>
                ))}
              </AnimatePresence>
            </CarouselContent>
            <CarouselPrevious className='-left-4 hidden h-8 w-8 transition-opacity group-hover:flex md:left-0 md:h-10 md:w-10' />
            <CarouselNext className='-right-4 hidden h-8 w-8 transition-opacity group-hover:flex md:right-0 md:h-10 md:w-10' />
          </Carousel>

          {/* Image Counter */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className='absolute -bottom-4 left-1/2 -translate-x-1/2 rounded-full px-4 py-1.5 text-xs text-primary-900 md:text-sm'
          >
            {current + 1} of {count} Images
          </motion.div>
        </div>

        {/* Mobile Thumbnails - Bottom */}
        <div className='w-full md:hidden'>
          <ProductThumbnailStrip
            images={images}
            currentIndex={current}
            onThumbnailClick={(index) => {
              api?.scrollTo(index)
              // Don't open fullscreen on thumbnail click for mobile
            }}
            orientation='horizontal'
          />
        </div>
      </div>

      {/* Fullscreen Image Dialog Component */}
      <FullscreenImageDialog
        images={images}
        isOpen={isFullscreen}
        onOpenChange={setIsFullscreen}
        initialIndex={current}
      />
    </>
  )
}

export default ProductSlider
