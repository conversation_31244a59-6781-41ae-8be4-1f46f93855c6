"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import SpImage from "@/shared/SpImage/sp-image"
import { ChevronLeft, ChevronRight } from "lucide-react"
import * as React from "react"
import type { Swiper as SwiperType } from "swiper"
import { <PERSON><PERSON> } from "swiper/modules"
import { Swiper, SwiperSlide } from "swiper/react"
import { AdaptiveWrapper } from "../modals/adaptive-wrapper"

interface FullscreenImageDialogProps {
  images: string[]
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  initialIndex: number
}

const FullscreenImageDialog: React.FC<FullscreenImageDialogProps> = ({
  images,
  isOpen,
  onOpenChange,
  initialIndex,
}) => {
  const [current, setCurrent] = React.useState(initialIndex)
  const [swiperInstance, setSwiperInstance] = React.useState<SwiperType | null>(
    null,
  )
  const [isBeginning, setIsBeginning] = React.useState(true)
  const [isEnd, setIsEnd] = React.useState(false)

  React.useEffect(() => {
    if (swiperInstance && isOpen) {
      swiperInstance.slideTo(initialIndex, 0)
    }
  }, [swiperInstance, initialIndex, isOpen])

  const handlePrev = () => {
    if (swiperInstance) {
      swiperInstance.slidePrev()
    }
  }

  const handleNext = () => {
    if (swiperInstance) {
      swiperInstance.slideNext()
    }
  }

  const handleSwiperUpdate = (swiper: SwiperType) => {
    setIsBeginning(swiper.isBeginning)
    setIsEnd(swiper.isEnd)
    setCurrent(swiper.activeIndex)
  }

  return (
    <AdaptiveWrapper
      open={isOpen}
      onOpenChange={onOpenChange}
      title='Product Images'
      className='pb-10 md:pb-14'
    >
      <div className='relative flex aspect-square h-max w-full items-center justify-center p-6 md:p-12'>
        <Swiper
          modules={[Mousewheel]}
          // freeMode={{
          //   enabled: true,
          //   sticky: false,
          // }}
          mousewheel={{
            forceToAxis: true,
          }}
          slidesPerView='auto'
          spaceBetween={0}
          initialSlide={initialIndex}
          onBeforeInit={(swiper) => {
            setSwiperInstance(swiper)
          }}
          onInit={handleSwiperUpdate}
          onSlideChange={handleSwiperUpdate}
          onResize={handleSwiperUpdate}
          className='h-full w-full'
        >
          {images.map((img, idx) => (
            <SwiperSlide
              key={idx}
              className='flex aspect-square h-full items-center justify-center rounded-2xl'
            >
              <SpImage
                src={img.trim()}
                alt={`Product image ${idx + 1}`}
                fill
                className='aspect-square rounded-2xl object-contain'
                sizes='100vw'
                priority={idx === initialIndex}
                placeholder='blur'
                blurDataURL='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mN8/+F9PQAI8wNPvd7POQAAAABJRU5ErkJggg=='
                containerClassName='relative aspect-square flex rounded-2xl overflow-hidden h-full w-full items-center justify-center'
              />
            </SwiperSlide>
          ))}
        </Swiper>

        <Button
          size='icon'
          onClick={handlePrev}
          variant='icon'
          className='absolute left-4 top-1/2 z-[1] hidden h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-black/40 hover:bg-black/60 lg:flex'
          aria-label='Previous slide'
          disabled={isBeginning}
        >
          <ChevronLeft className='h-6 w-6 text-white' />
        </Button>
        <Button
          size='icon'
          variant='icon'
          onClick={handleNext}
          className='absolute right-4 top-1/2 z-[1] hidden h-12 w-12 -translate-y-1/2 items-center justify-center rounded-full bg-black/40 hover:bg-black/60 lg:flex'
          aria-label='Next slide'
          disabled={isEnd}
        >
          <ChevronRight className='h-6 w-6 text-white' />
        </Button>
      </div>

      <div className='absolute bottom-4 left-1/2 -translate-x-1/2 rounded-full bg-black/50 px-4 py-2 text-sh5 text-white md:text-sh3'>
        {current + 1} of {images.length}
      </div>
    </AdaptiveWrapper>
  )
}

export default FullscreenImageDialog
