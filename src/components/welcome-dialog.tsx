"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Typography } from "@/components/ui/typography"
import SpImage from "@/shared/SpImage/sp-image"
import { useEffect, useState } from "react"

export const WelcomeDialog = () => {
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    if (typeof window === "undefined") return
    // Check if dialog has been shown before
    const hasSeenWelcome = localStorage.getItem("hasSeenWelcome")

    if (!hasSeenWelcome) {
      setIsOpen(true)
      // Mark as seen for future visits
      localStorage.setItem("hasSeenWelcome", "true")
    }
  }, [])

  const handleClose = () => {
    setIsOpen(false)
  }

  if (!isOpen) return null

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        // Only allow closing via the button, not by clicking overlay
        if (!open) return
        setIsOpen(open)
      }}
    >
      <DialogContent className='w-[95%] rounded-2xl bg-gray-100 px-6 pt-12 sm:max-w-[688px] md:rounded-3xl md:px-10 xl:px-14'>
        <div className='flex flex-col items-center gap-8'>
          <div className='space-y-3 text-center'>
            <Typography
              as='h3'
              className='font-ubuntu text-d7 text-gray-900 md:text-d3'
            >
              Old Pals, New{" "}
              <span className='bg-review-gradient bg-clip-text text-transparent'>
                Vibes
              </span>
            </Typography>
            <Typography as='p' className='text-h7 text-gray-900 md:text-h3'>
              SharePal just got a Glow-up! ✨
            </Typography>
          </div>

          <SpImage
            src='https://images.sharepal.in/misc/hard-coded/sharepal/welcom-sharepal.gif'
            alt='Welcome to SharePal'
            width={1600}
            height={900}
            className='w-full max-w-[300px] md:max-w-[450px]'
            containerClassName='w-full flex justify-center'
          />

          <Button
            onClick={handleClose}
            variant={"primary"}
            size={"extra-lg"}
            className='w-full'
          >
            Dive Right In!
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
