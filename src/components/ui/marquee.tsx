import { cn } from "@/lib/utils"
import type React from "react"

interface MarqueeProps {
  className?: string
  containerClassName?: string
  reverse?: boolean
  pauseOnHover?: boolean
  children?: React.ReactNode
  vertical?: boolean
  repeat?: number
  onlyMobile?: boolean
  onlyDesktop?: boolean
  duration?: number // New prop for animation duration in seconds
  [key: string]: unknown
}

export default function Marquee({
  className,
  reverse,
  pauseOnHover = false,
  children,
  vertical = false,
  onlyDesktop,
  onlyMobile,
  containerClassName,
  duration = 40,
  ...props
}: MarqueeProps) {
  return (
    <div
      {...props}
      className={cn(
        "group overflow-hidden p-2",
        {
          "hidden md:block": onlyDesktop,
          "block md:hidden": onlyMobile,
        },
        containerClassName,
      )}
      style={
        {
          "--duration": `${duration}s`,
        } as React.CSSProperties
      }
    >
      <div
        className={cn(
          "flex w-max",
          {
            "animate-marquee-horizontal flex-row": !vertical,
            "animate-marquee-vertical flex-col": vertical,
            "group-hover:[animation-play-state:paused]": pauseOnHover,
            "[animation-direction:reverse]": reverse,
          },
          className,
        )}
      >
        <div className='flex shrink-0 [gap:1rem]'>{children}</div>
        <div className='flex shrink-0 [gap:1rem]'>{children}</div>
      </div>
    </div>
  )
}
