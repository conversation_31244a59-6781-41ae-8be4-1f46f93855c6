import React from "react"
import clsx from "clsx"

// Define the props for the Typography component
interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  as: React.ElementType
  className?: string
}

// Define the Typography component
export const Typography: React.FC<TypographyProps> = ({
  as: Component = "p",
  className,
  ...props
}) => <Component className={clsx(className)} {...props} />
