import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { Button } from "./button"

interface SlidingAlertProps {
  show: boolean
  title: string
  description: string
  onConfirm: () => void
  onCancel: () => void
  confirmText?: string
  cancelText?: string
  variant?: "default" | "destructive"
}

export function SlidingAlert({
  show,
  title,
  description,
  onConfirm,
  onCancel,
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "default",
}: SlidingAlertProps) {
  return (
    <div className='fixed bottom-4 left-0 right-0 z-[100] flex justify-center md:bottom-auto md:top-4'>
      <motion.div
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: show ? 0 : -100, opacity: show ? 1 : 0 }}
        className={cn(
          "w-[95%] max-w-md rounded-xl bg-white p-4 shadow-lg",
          show ? "pointer-events-auto" : "pointer-events-none",
        )}
      >
        <h3 className='text-lg font-semibold'>{title}</h3>
        <p className='mt-1 text-sm text-gray-500'>{description}</p>
        <div className='mt-4 flex w-full items-center justify-center gap-2'>
          <Button
            className='flex-1 rounded-xl'
            onClick={onCancel}
            variant={"outline-primary"}
          >
            {cancelText}
          </Button>
          <Button
            className='flex-1 rounded-xl'
            onClick={onConfirm}
            variant={variant === "destructive" ? "destructive" : "primary"}
          >
            {confirmText}
          </Button>
        </div>
      </motion.div>
    </div>
  )
}
