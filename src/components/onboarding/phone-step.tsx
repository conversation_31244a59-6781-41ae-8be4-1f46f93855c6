"use client"

import { getOfferCoupon } from "@/actions/coupon"
import { sendOTP } from "@/actions/login"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { trackLoginOtpSent } from "@/lib/gtag-event"
import { cn } from "@/lib/utils"
import { phoneSchema, type PhoneFormData } from "@/lib/validations/auth"
import { useRentalStore } from "@/store/rental-store"
import { zodResolver } from "@hookform/resolvers/zod"
import { useQuery } from "@tanstack/react-query"
import Link from "next/link"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { IconArrowRight } from "../Icons"
import PartyPopperIcon from "../Icons/party-popper-icon"
import { CustomNumberInput } from "../custom/custom-numberinput"

interface PhoneStepProps {
  onComplete: (calling_number: string) => void
}

export function PhoneStep({ onComplete }: PhoneStepProps) {
  const { data: offerCoupon } = useQuery({
    queryKey: ["offer_coupon"],
    queryFn: getOfferCoupon,
    staleTime: 1000 * 60 * 10, // 10 minutes
  })

  const form = useForm<PhoneFormData>({
    resolver: zodResolver(phoneSchema),
    defaultValues: {
      calling_number: "",
    },
  })
  const { selectedCity } = useRentalStore()

  const onSubmit = async (data: PhoneFormData) => {
    try {
      onComplete(data.calling_number)
      await sendOTP("91", data.calling_number)

      // if (!response) {
      //   return
      // }
      trackLoginOtpSent("91", data.calling_number, selectedCity.city_url)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to send OTP")
    }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className='flex h-full flex-col justify-between gap-10'
      >
        <div className='flex flex-col gap-6'>
          <div className='space-y-2 text-center font-inter leading-7'>
            <h2 className='text-xl font-semibold tracking-tight'>
              Login/Signup to Your Account
            </h2>
            <p className='text-netural-500 text-sm'>
              Enter your Calling number to continue
            </p>
          </div>
          <FormField
            control={form.control}
            name='calling_number'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className='flex overflow-hidden rounded-xl border-2 border-neutral-200 focus-within:border-primary-500'>
                    <Select defaultValue='+91'>
                      <SelectTrigger className='m-0 h-12 w-max rounded-none rounded-l-xl bg-neutral-200 px-3 py-2 font-semibold text-neutral-950 shadow-transparent focus:border-0 focus:ring-0 focus:ring-transparent focus-visible:ring-transparent'>
                        <SelectValue placeholder='+91' className='px-2' />
                      </SelectTrigger>
                      <SelectContent className='text-neutral-950 focus-visible:ring-transparent'>
                        <SelectItem value='+91'>+91</SelectItem>
                      </SelectContent>
                    </Select>
                    <CustomNumberInput
                      {...field}
                      placeholder='Enter your number'
                      className='flex-1 rounded-none border-none outline-none focus:outline-none focus-visible:ring-transparent'
                      maxLength={10}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Alert className='border-none p-0'>
            <AlertDescription className='text-green-900'>
              <div className='flex h-24 items-center gap-4 overflow-hidden rounded-3xl bg-gradient-to-r from-[#EDF4FF] to-[#F7FFEA] p-3'>
                <PartyPopperIcon />
                <div className='flex flex-col gap-2'>
                  <div>
                    <span className='text-base font-bold text-pink-500'>
                      {offerCoupon?.coupon_description?.split("off")[0]}
                    </span>
                    <span className='text-base font-bold text-zinc-950'>
                      {offerCoupon?.coupon_description?.split("off")[1]}
                    </span>
                  </div>
                  <div className='text-sm font-semibold text-zinc-700'>
                    Use Coupon - {offerCoupon?.coupon_code}
                  </div>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        </div>
        <div>
          <Button
            type='submit'
            disabled={!form.formState.isValid}
            className={cn(
              "flex h-12 w-full items-center justify-center gap-2 rounded-full bg-primary-500 text-base font-semibold leading-tight text-white hover:bg-primary-600 disabled:bg-neutral-200 disabled:text-neutral-500",
            )}
          >
            <span>Get OTP</span>
            <span className='scale-[1.5]'>
              <IconArrowRight />
            </span>
          </Button>
          <p className='pt-5 text-center text-b6 text-muted-foreground'>
            By continuing, you agree to the{" "}
            <Button asChild variant='link' className='h-auto p-0 text-xs'>
              <Link href={"/terms-and-conditions"} target='_blank'>
                Terms of Service
              </Link>
            </Button>{" "}
            and acknowledge the{" "}
            <Button asChild variant='link' className='h-auto p-0 text-xs'>
              <Link href={"/privacy-policy"} target='_blank'>
                Privacy Policy
              </Link>
            </Button>
            .
          </p>
        </div>
      </form>
    </Form>
  )
}
