"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
} from "@/components/ui/dialog"
import { trackLoginInitiated } from "@/lib/gtag-event"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useRentalStore } from "@/store/rental-store"
import { AnimatePresence, motion } from "framer-motion"
import { X } from "lucide-react"
import { useEffect } from "react"
import { IconLogoLeft, IconLogoRight } from "../Icons"
import { OtpStep } from "./otp-step"
import { PhoneStep } from "./phone-step"
import { ProfileStep } from "./profile-step"

const stepVariants = {
  enter: {
    opacity: 0,
    x: 20,
  },
  center: {
    opacity: 1,
    x: 0,
  },
  exit: {
    opacity: 0,
    x: -20,
  },
}

export function OnboardingFlow() {
  const {
    isOpen,
    currentStep,
    phoneNumber,
    closeModal,
    setStep,
    setPhoneNumber,
    isNonClosable,
  } = useOnboardingStore()

  const { selectedCity } = useRentalStore()

  const handleStepComplete = (
    nextStep: "phone" | "otp" | "profile",
    phone?: string,
  ) => {
    if (phone) {
      setPhoneNumber(phone)
    }
    setStep(nextStep)
  }
  useEffect(() => {
    trackLoginInitiated(selectedCity.city_url)
  }, [selectedCity.city_url])

  return (
    <Dialog open={isOpen} onOpenChange={closeModal}>
      <DialogContent className='rounded-3xl p-0 max-md:h-[95%] max-md:w-[95%] sm:max-w-[400px] sm:rounded-[48px] md:min-w-[512px]'>
        {!isNonClosable && (
          <button
            onClick={() => closeModal()}
            className='absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity data-[state=open]:bg-accent data-[state=open]:text-muted-foreground hover:opacity-100 disabled:pointer-events-none md:right-6 md:top-6'
          >
            <X className='h-4 w-4 md:h-6 md:w-6' />
            <span className='sr-only'>Close</span>
          </button>
        )}
        <DialogHeader className='hidden'>
          <DialogTitle className='hidden'></DialogTitle>
        </DialogHeader>
        <AnimatePresence mode='wait'>
          <motion.div
            key={currentStep}
            variants={stepVariants}
            initial='enter'
            animate='center'
            exit='exit'
            transition={{
              x: { type: "spring", stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 },
            }}
            className='h-full p-6 md:px-[72px]'
          >
            <div className='flex h-full flex-col space-y-8'>
              <span className='mt-10 flex w-full justify-center font-bold'>
                <span className='[&>svg>path]:fill-primary-500'>
                  <IconLogoLeft />
                </span>
                <IconLogoRight color='#81CD05' />
              </span>
              {currentStep === "phone" && (
                <PhoneStep
                  onComplete={(phone) => {
                    handleStepComplete("otp", phone)
                  }}
                />
              )}
              {currentStep === "otp" && <OtpStep phoneNumber={phoneNumber} />}
              {currentStep === "profile" && (
                <ProfileStep
                  onComplete={() => {
                    closeModal()
                  }}
                />
              )}
            </div>
          </motion.div>
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  )
}
