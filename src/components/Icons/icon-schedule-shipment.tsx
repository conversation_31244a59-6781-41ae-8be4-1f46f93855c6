import { cn } from "@/lib/utils"
import * as React from "react"

export const IconSchedlueShipment = ({ className }: { className: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='558'
    height='128'
    fill='none'
    viewBox='0 0 558 128'
    className={cn(className)}
  >
    <path fill='url(#paint0_linear_6882_57601)' d='M0 0h558v128H0z'></path>
    <g clipPath='url(#clip0_6882_57601)'>
      <g clipPath='url(#clip1_6882_57601)'>
        <path
          fill='#9EFF00'
          d='m315.5 76.666 3.087-26.889h-8.127L299.25 96.75l39.868-2.274 3.266-12.19z'
        ></path>
        <path
          fill='#91EA00'
          d='M309.877 82.285h-8.127l-4.355 16.254h36.571c2.244 0 4.551-1.82 5.152-4.063h-28.444c-2.244 0-3.576-1.82-2.975-4.064z'
        ></path>
        <path
          fill='#D8FF99'
          d='m341.818 77.038-6.87-4.911-7.822-5.096 7.754-9.127-.024-5.132c-.009-1.768-1.286-2.995-3.118-2.995h-13.151l-8.71 32.508h32.508l.472-1.762c.382-1.428-.012-2.75-1.039-3.485'
        ></path>
        <path
          fill='#476AED'
          d='M295.988 45.715h17.591c1.122 0 1.788.91 1.488 2.032l-12.522 46.73-58.629 3.44c-1.122 0-1.551-.544-1.25-1.666l11.208-42.125z'
        ></path>
        <path
          fill='#1437BA'
          d='M247.688 94.477c-1.122 0-1.788-.91-1.487-2.032l2.968-11.077c.31-1.156 1.554-1.816 2.666-1.373a32.1 32.1 0 0 0 11.926 2.291c17.954 0 32.508-14.554 32.508-32.508 0-1.379-.114-2.73-.281-4.063h-39.297c-1.122 0-2.276.91-2.576 2.032l-13.066 48.762c-.301 1.122.365 2.031 1.487 2.031h58.921l1.088-4.063z'
        ></path>
        <path
          fill='#030D31'
          d='M328.587 103.879c3.863-3.381 4.896-8.519 2.308-11.476s-7.817-2.614-11.68.767-4.896 8.518-2.308 11.475c2.588 2.958 7.817 2.614 11.68-.766'
        ></path>
        <path
          fill='#E8ECFD'
          d='M326.244 101.202c1.931-1.69 2.448-4.259 1.154-5.738-1.294-1.478-3.909-1.307-5.84.384s-2.448 4.259-1.154 5.737c1.294 1.479 3.908 1.308 5.84-.383'
        ></path>
        <path
          fill='#030D31'
          d='M265.577 103.912c3.863-3.381 4.896-8.519 2.308-11.476s-7.817-2.614-11.68.767-4.896 8.518-2.308 11.476c2.588 2.957 7.817 2.613 11.68-.767'
        ></path>
        <path
          fill='#E8ECFD'
          d='M263.236 101.233c1.931-1.69 2.448-4.259 1.154-5.737-1.294-1.479-3.909-1.307-5.84.383s-2.448 4.259-1.154 5.738c1.294 1.478 3.909 1.307 5.84-.384M334.88 57.904h-10.344l-3.598 4.064 1.406 8.844 12.604-2.749z'
        ></path>
        <path
          fill='#D1DAFA'
          d='M325.877 68.063c-2.244 0-3.576-1.82-2.975-4.064l1.633-6.095c-2.244 0-4.551 1.82-5.152 4.064l-1.633 6.095c-.601 2.244.73 4.063 2.975 4.063h14.222l-.02-4.063z'
        ></path>
        <path
          fill='#BAC7F8'
          d='M263.763 78.223c15.709 0 28.444-12.735 28.444-28.444 0-15.71-12.735-28.445-28.444-28.445-15.71 0-28.445 12.735-28.445 28.445 0 15.709 12.735 28.444 28.445 28.444'
        ></path>
        <path
          fill='#fff'
          d='M263.762 74.159c13.465 0 24.381-10.916 24.381-24.382 0-13.465-10.916-24.38-24.381-24.38s-24.381 10.915-24.381 24.38 10.916 24.382 24.381 24.382'
        ></path>
        <path
          fill='#BAC7F8'
          d='M263.762 33.523a2.03 2.03 0 0 1-2.032-2.032V29.46a2.031 2.031 0 1 1 4.064 0v2.032a2.03 2.03 0 0 1-2.032 2.032M263.762 72.127a2.03 2.03 0 0 1-2.032-2.032v-2.032a2.031 2.031 0 1 1 4.064 0v2.032a2.03 2.03 0 0 1-2.032 2.031M284.079 51.81h-2.032a2.03 2.03 0 0 1-2.031-2.032 2.03 2.03 0 0 1 2.031-2.032h2.032a2.031 2.031 0 1 1 0 4.064M245.476 51.81h-2.032a2.031 2.031 0 1 1 0-4.064h2.032a2.03 2.03 0 0 1 2.031 2.032 2.03 2.03 0 0 1-2.031 2.032'
        ></path>
        <path
          fill='#C5FF66'
          d='M237.349 78.222h-20.317a2.031 2.031 0 1 1 0-4.064h20.317a2.031 2.031 0 1 1 0 4.064M235.318 86.349h-12.191a2.03 2.03 0 0 1-2.031-2.032 2.03 2.03 0 0 1 2.031-2.032h12.191a2.031 2.031 0 1 1 0 4.064M233.287 94.476h-4.064a2.031 2.031 0 1 1 0-4.064h4.064a2.03 2.03 0 0 1 2.031 2.032 2.03 2.03 0 0 1-2.031 2.032'
        ></path>
        <path
          fill='#91EA00'
          d='M257.667 57.904a2.03 2.03 0 0 1-1.437-3.468l19.302-19.302a2.03 2.03 0 1 1 2.873 2.873L259.103 57.31a2.03 2.03 0 0 1-1.436.595'
        ></path>
        <path
          fill='#030D31'
          d='M263.763 53.842a4.063 4.063 0 1 0 0-8.126 4.063 4.063 0 0 0 0 8.126'
        ></path>
      </g>
    </g>
    <defs>
      <clipPath id='clip0_6882_57601'>
        <path fill='#fff' d='M215 0h128v128H215z'></path>
      </clipPath>
      <clipPath id='clip1_6882_57601'>
        <path fill='#fff' d='M215 0h128v128H215z'></path>
      </clipPath>
      <linearGradient
        id='paint0_linear_6882_57601'
        x1='0'
        x2='558'
        y1='64'
        y2='64'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#C7D3FF' stopOpacity='0'></stop>
        <stop offset='0.5' stopColor='#C7D3FF'></stop>
        <stop offset='1' stopColor='#C7D3FF' stopOpacity='0'></stop>
      </linearGradient>
    </defs>
  </svg>
)
