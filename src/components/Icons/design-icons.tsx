import { FC } from "react"

interface IconProps {
  className?: string
}

export const IconOneStopText: FC<IconProps> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='248'
    height='74'
    fill='none'
    viewBox='0 0 248 74'
    className={className}
  >
    <path
      stroke='#9EFF00'
      strokeLinecap='round'
      strokeWidth='3'
      d='M2 15.227c292.62-48.21 304.712 50.415 116.813 56.542C-69.087 77.895-29.116-40.271 246 20.259'
    ></path>
  </svg>
)

export const IconSolutionText: FC<IconProps> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='214'
    height='8'
    fill='none'
    viewBox='0 0 214 8'
    className={className}
  >
    <path
      stroke='#1945E8'
      strokeLinecap='round'
      strokeWidth='3'
      d='M2 4.825c3.26-.666 12.539-2.16 21.618-1.997 7.617.138 9.883 2.136 17.5 1.997 5.916-.109 7.455-1.716 13.382-1.644 5.405.065 6.428 1.68 11.838 1.644 6.82-.045 5.561-2.672 12.353-2.819 8.207-.176 6.683 3.406 14.927 3.406s6.687-3.343 14.926-3.406c8.807-.067 7.147 3.693 15.956 3.64 7.033-.041 7.384-2.54 14.412-2.465 7.104.075 6.276 2.883 13.382 2.818 6.817-.062 5.555-2.687 12.353-2.818 7.01-.135 7.422 2.283 14.412 2.466 8.807.23 10.297-3.147 19.044-2.819 7.573.285 5.147 3.17 13.897 3.17'
    ></path>
  </svg>
)

export const IconRentalNeedsText: FC<IconProps> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='326'
    height='15'
    fill='none'
    viewBox='0 0 326 15'
    className={className}
  >
    <path
      stroke='#9EFF00'
      strokeLinecap='round'
      strokeWidth='3'
      d='M2 7.885C53.227 2.113 417.917.343 164.22 10.22S173.977.343 324 2.113'
    ></path>
  </svg>
)

export const IconHeroDeposit: FC<IconProps> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    fill='none'
    viewBox='0 0 24 24'
    className={className}
  >
    <path
      fill='#1437BA'
      d='M3 4.9v.05l.05-.01L4.11 6H3v12h13.11l4.73 4.73 1.27-1.27L4.57 3.91 2.39 1.73 1.11 3zm3.41 4.51q.3-.285.45-.66l2.3 2.3c-.11.3-.16.62-.16.95 0 .8.32 1.56.88 2.12S11.2 15 12 15c.33 0 .65-.05.95-.16L14.11 16H7c0-.53-.21-1.04-.59-1.41C6.04 14.21 5.53 14 5 14v-4c.53 0 1.04-.21 1.41-.59m11.28 5.09L21 17.8V6H9.2l2 2H17c0 .53.21 1.04.59 1.41.37.38.88.59 1.41.59v4c-.5 0-.95.18-1.31.5'
    ></path>
  </svg>
)

export const IconHeroFlexible: FC<IconProps> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    fill='none'
    viewBox='0 0 24 24'
    className={className}
  >
    <path
      fill='#1437BA'
      d='M8.982 19.477q-2.392-1.104-3.887-3.335Q3.6 13.91 3.6 11.105q0-.598.058-1.173t.195-1.127l-1.058.62-.92-1.586 4.393-2.53 2.53 4.37-1.61.92-1.242-2.162a6.7 6.7 0 0 0-.38 1.288 7.5 7.5 0 0 0-.126 1.38q0 2.23 1.219 4.06a7.3 7.3 0 0 0 3.243 2.702zM16.02 8.322v-1.84h2.507a7.4 7.4 0 0 0-2.553-2.035 7.2 7.2 0 0 0-3.174-.725q-1.264 0-2.392.39a7.5 7.5 0 0 0-2.07 1.105l-.92-1.61a9.9 9.9 0 0 1 2.507-1.265 8.9 8.9 0 0 1 2.875-.46q1.817 0 3.473.679a9.2 9.2 0 0 1 2.967 1.965V3.262h1.84v5.06zm-.598 13.8-4.393-2.53 2.53-4.37 1.587.92-1.311 2.254q2.714-.39 4.52-2.461t1.805-4.853q0-.253-.011-.472a3 3 0 0 0-.058-.448h1.863q.023.23.035.449.012.22.01.47 0 3.105-1.851 5.556-1.852 2.45-4.818 3.3l1.012.598z'
    ></path>
  </svg>
)

export const IconHeroExhaustive: FC<IconProps> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    fill='none'
    viewBox='0 0 24 24'
    className={className}
  >
    <path
      fill='#1437BA'
      d='m12.729 18.501 2.275-1.375 2.275 1.375-.6-2.6 2-1.725-2.625-.225-1.05-2.45-1.05 2.45-2.625.225 2 1.725zm-6.725-4.5v2h-2q-.824 0-1.412-.587a1.93 1.93 0 0 1-.588-1.413v-10q0-.825.588-1.412a1.93 1.93 0 0 1 1.412-.588h10q.824 0 1.413.588.588.588.587 1.412v2h-2v-2h-10v10zm4 8q-.824 0-1.412-.587a1.93 1.93 0 0 1-.588-1.413v-10q0-.825.588-1.412a1.93 1.93 0 0 1 1.412-.588h10q.824 0 1.413.588.588.588.587 1.412v10q0 .825-.587 1.413a1.92 1.92 0 0 1-1.413.587zm0-2h10v-10h-10z'
    ></path>
  </svg>
)

export const IconStar: FC<IconProps> = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
  >
    <path
      fill='#E8AE19'
      d='M6.354 5.333 10 .604l3.646 4.73 5.708 1.916-3.604 5.104.146 5.688L10 16.396l-5.896 1.646.146-5.709L.667 7.25z'
    ></path>
  </svg>
)

export const IconGoogle: FC<IconProps> = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    fill='none'
    viewBox='0 0 24 24'
  >
    <path
      fill='#4285F4'
      d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09'
    ></path>
    <path
      fill='#34A853'
      d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23'
    ></path>
    <path
      fill='#FBBC05'
      d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22z'
    ></path>
    <path
      fill='#EA4335'
      d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53'
    ></path>
  </svg>
)

export const IconApplyOffer = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
  >
    <g id='Offer - Filled'>
      <path
        id='Subtract'
        fill='currentColor'
        fillRule='evenodd'
        d='M12.729 17.907a.833.833 0 0 1-1.138.304l-1.592-.92-1.59.92a.834.834 0 0 1-1.14-.304l-.92-1.592H4.517a.833.833 0 0 1-.833-.833v-1.833l-1.592-.92a.837.837 0 0 1-.303-1.139l.92-1.59-.92-1.59a.833.833 0 0 1 .304-1.14l1.592-.92V4.519a.833.833 0 0 1 .833-.833H6.35l.92-1.592a.833.833 0 0 1 1.138-.305L10 2.71l1.591-.92a.834.834 0 0 1 1.139.305l.919 1.591h1.832a.833.833 0 0 1 .834.833v1.834l1.591.92a.833.833 0 0 1 .305 1.138L17.29 10l.919 1.59a.834.834 0 0 1-.305 1.14l-1.591.92v1.832a.833.833 0 0 1-.834.833h-1.832zm-4.563-4.092-1.334-1 5-6.666 1.333 1zM7.5 6.665a1.25 1.25 0 1 1 0 2.502 1.25 1.25 0 0 1 0-2.501m4.521 6.573a1.25 1.25 0 1 0 .958-2.311 1.25 1.25 0 0 0-.958 2.311'
        clipRule='evenodd'
      ></path>
    </g>
  </svg>
)

export const IconExperience = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='337'
    height='74'
    fill='none'
    viewBox='0 0 337 74'
    {...props}
  >
    <path
      stroke='#9EFF00'
      strokeLinecap='round'
      strokeWidth='3'
      d='M2 15.227c399.354-48.21 415.858 50.415 159.421 56.542C-95.016 77.895-40.466-40.271 335 20.259'
    ></path>
  </svg>
)

export const IconRent = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='213'
    height='15'
    fill='none'
    viewBox='0 0 213 15'
    {...props}
  >
    <path
      stroke='#1945E8'
      strokeLinecap='round'
      strokeWidth='3'
      d='M2 7.885C35.25 2.113 271.958.343 107.292 10.22S113.625.343 211 2.113'
    ></path>
  </svg>
)

export const IconWallet = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='21'
    height='21'
    fill='none'
    viewBox='0 0 21 21'
    {...props}
  >
    <g clipPath='url(#clip0_6146_248004)'>
      <path
        fill='#1437BA'
        fillRule='evenodd'
        d='M16.227 14.636h3.818v-1.272h-3.818zM18.772 7H.954v12.727c0 .703.57 1.273 1.273 1.273h16.545c.703 0 1.273-.57 1.273-1.273V15.91H15.59a.636.636 0 0 1-.636-.636v-2.546c0-.351.285-.636.636-.636h4.455V8.273c0-.703-.57-1.273-1.273-1.273m0-5.727c0-.352-.15-.547-.477-.676-.252-.1-.497-.04-.795.04L.954 5.726h17.818z'
        clipRule='evenodd'
      ></path>
    </g>
    <defs>
      <clipPath id='clip0_6146_248004'>
        <path fill='#fff' d='M0 0h21v21H0z'></path>
      </clipPath>
    </defs>
  </svg>
)

export const IconWorld = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='21'
    height='22'
    fill='none'
    viewBox='0 0 21 22'
    {...props}
  >
    <g clipPath='url(#clip0_6146_247997)'>
      <g fill='#1437BA' clipPath='url(#clip1_6146_247997)'>
        <path d='m14.099 14.43-.574.015-.023-.**************-.022-.024-.287-.62.007-.171-.225.01-.117-.8-.72-.194.155 1.14 1.395.162.055v.078h-.217l.055.171 1.404.062zM14.757 14.162l.087-.221.154-.14V13.7l.096-.064v-.766l-.096-.04-.046-.239h-.14l-.09.242-.107.097-.812.439v.435l.27.358zM15.094 13.888v.566h.21l-.031-.361.163.058v.256l.17-.015-.005-.756-.41.16.004.092zM17.31 14.27l.287.152.007.14h-.186l.007.286.621-.038.225-.242.24-.022v.442h.42v-.178h-.21l-.04-.54-.999-.453-.28.147-.26.007.02-.27-.248-.148-.353.353.237.291zM9.957 12.677l.098.263.352-.132.039-.363-.284-.057zM5.816 15.748l-.48-.062v.372l.17.202-.132.201-.194.225v.411l.265.256.24-.386.472-.692.226-1.085h-.288zM15.545 14.686l.122.04.035-.124h-.157zM14.999 14.725h-.186v.103h.186zM15.205 14.725l-.11.103v.09l.17-.018v-.072h.117v-.09l.084-.013v-.088h-.186zM15.562 14.828v.163h.181v-.082h.186v-.095l.13.014-.017-.165-.212.03-.019.108-.237.027z'></path>
        <path d='M10.5 0C4.71 0 0 4.71 0 10.5 0 16.289 4.71 21 10.5 21 16.289 21 21 16.289 21 10.5 21 4.71 16.288 0 10.5 0m0 .862c3.38 0 6.356 1.75 8.077 4.389l-.96-.407-3.607-.186-.337-.17-.536-.08-.402-.014-.366.369-.154-.012-.485-.36H10.3l-.664-.396-1.329.084-1.01.451.009.524-.311.093-.07-.524-.116-.182-.35-.086-.08.314.104.256-1.24-.08-.593.274-.09-.26-.147.04-.109.286-.31.242-.757-.021-.034-.167.79-.035.163-.139-.953-.442-.343.01A9.63 9.63 0 0 1 10.5.862M5.51 8.02l-.015-.36.078-.179.313-.027.268.112.012.186-.38-.021-.02.172.477.318v.233l.206.151v.163l-.105.101-.244-.02-.047-.073-.384-.028.027-.124.202-.175v-.146zM3.25 9.648l-.454-.337-.477.046v.291h-.152l-.162-.116-.826-.21v-.534l-.165.012q.067-.379.163-.747l.649.438-.09.12-.328-.018.127.179.202.044.237-.098-.004-.285.105-.053-.085-.09-.486-.27-.128-.36h.404l.13.127.348.3.014.362.361.385.134-.527.25-.136.047.431.244.267.736-.012.217.164v.384l-.267.244zm-1.374-3.36v-.086q.172-.345.37-.673v.179l.262.122.016.25-.369.55-.478-.005q.068-.155.14-.309zm.684-1.235h.148v.187l-.456.278q.141-.232.295-.456zm1.57 3.42-.603-.024-.18.024v-.412l.135-.152.146-.045.32-.037.07-.11.131.065.019.375.35.145.031.17zm-.354 8.923.094-.097.248-.442.318-.07v-.372l.21-.248.441-.605-.093-.922-.194-.342.87-.832.96-1.371-.186-.086-.997.21-.256-.128-1.047-1.972.074-.085 1.037 1.26.026.39.24.241.488-.032 1.02-.739.32-.208-.006-.256-.34-.045s-1.195-.563-1.195-.588l.008-.483h.263l.039.334.554.318.632.03 1.374.225.321.203v.314l-.041.427.703 1.157.227.053.226-.33.126-.936.234-.504.378-.141.668.064.836.745v1.07l1.106 1.057.058-.185-.896-1.28.093-.373.475.537.262.027.348-.23-.057-.35-.574-.46-.024-.42.164-.221.291.093-.024.198.167.256.147-.104-.08-.303.814-.291.357-.298-.055-.54-.709-.744-.011-.605h.314l.977.745.276-.074-.183-.24-.523-.318.03-.473h.62l.051-.585-.236-.311.139-.152.302.303.097-.128-.387-.442-.965-.116-.128-.256.279-.396.22.163.663.058.838-.558.14.174-.188.291v.291l.768.628.245-.128-.094-.42-.545-.51.72-.046v-.21l.35-.069-.268-.184.012-.142.732.118.127-.107a9.58 9.58 0 0 1 1.546 5.226 9.57 9.57 0 0 1-1.632 5.36l-.013-.288-.276-.472-.247-.012-.102.857-.474-.036-.132-.08-.017-.339.252.051.211-.206-.45-.271h-.53l-.38.54h-.155v-.17l-.456.034-.721.821-.383.006-.426.294-.03 1.272-.257.248v.225l1.24-.427 1.007-.04.144.315a9.6 9.6 0 0 1-5.811 1.955 9.6 9.6 0 0 1-6.723-2.74'></path>
      </g>
    </g>
    <defs>
      <clipPath id='clip0_6146_247997'>
        <path fill='#fff' d='M0 .5h21v21H0z'></path>
      </clipPath>
      <clipPath id='clip1_6146_247997'>
        <path fill='#fff' d='M0 0h21v21H0z'></path>
      </clipPath>
    </defs>
  </svg>
)
