import { cn } from "@/lib/utils"
import * as React from "react"

const SmileIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='54'
    height='54'
    fill='none'
    viewBox='0 0 54 54'
    className={cn(className)}
  >
    <path
      fill='#E8ECFD'
      fillRule='evenodd'
      d='M12.754 49.885a26.8 26.8 0 0 0 9.395 3.633 27 27 0 0 0 9.95-.046 26.8 26.8 0 0 0 8.316-3.09 26.95 26.95 0 0 0 9.103-8.565A26.83 26.83 0 0 0 53.953 27C53.953 12.114 41.886.047 27 .047S.047 12.114.047 27a26.83 26.83 0 0 0 5.132 15.825 27 27 0 0 0 7.575 7.06'
      clipRule='evenodd'
    ></path>
    <path
      fill='#1945E8'
      fillRule='evenodd'
      d='M50.438 27c0 12.944-10.494 23.438-23.438 23.438S3.563 39.944 3.563 27 14.056 3.563 27 3.563 50.438 14.056 50.438 27M27 53.953c14.886 0 26.953-12.067 26.953-26.953S41.886.047 27 .047.047 12.114.047 27 12.114 53.953 27 53.953M16.781 12.938a1.5 1.5 0 0 0-1.5 1.5v6.374a1.5 1.5 0 0 0 1.5 1.5h4.032a1.5 1.5 0 0 0 1.5-1.5v-6.375a1.5 1.5 0 0 0-1.5-1.5zm24.202 19.075c.088-.824-.592-1.497-1.42-1.497H14.436c-.828 0-1.508.673-1.42 1.497a14.063 14.063 0 0 0 27.966 0m-9.295-17.575a1.5 1.5 0 0 1 1.5-1.5h4.03a1.5 1.5 0 0 1 1.5 1.5v6.374a1.5 1.5 0 0 1-1.5 1.5h-4.03a1.5 1.5 0 0 1-1.5-1.5z'
      clipRule='evenodd'
    ></path>
  </svg>
)

export default SmileIcon
