import * as React from "react"

const PartyPopperIcon: React.FC = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='72'
    height='72'
    fill='none'
    viewBox='0 0 72 72'
  >
    <circle
      cx='36.001'
      cy='35.999'
      r='14.087'
      fill='#758FF1'
      opacity='0.2'
      style={{ mixBlendMode: "multiply" }}
    ></circle>
    <circle
      cx='35.999'
      cy='35.999'
      r='19.565'
      fill='#758FF1'
      opacity='0.2'
      style={{ mixBlendMode: "multiply" }}
    ></circle>
    <circle
      cx='36'
      cy='35.998'
      r='23.478'
      fill='#758FF1'
      opacity='0.2'
      style={{ mixBlendMode: "multiply" }}
    ></circle>
    <circle
      cx='36'
      cy='35.998'
      r='28.174'
      fill='#758FF1'
      opacity='0.2'
      style={{ mixBlendMode: "multiply" }}
    ></circle>
    <g clipPath='url(#clip0_4546_82757)'>
      <path
        fill='#476AED'
        d='M36.389 31.231c-7.277-7.276-15.255-11.093-17.822-8.528a2.6 2.6 0 0 0-.604 1.044l-.042-.041-2.45 10.045-2.441 10.014-2.894 11.859-1.831 7.51 10.843-4.103 13.793-5.219 11.003-4.163-.013-.014a2.56 2.56 0 0 0 .986-.582c2.566-2.567-1.252-10.546-8.528-17.822'
      ></path>
      <path
        fill='#030D31'
        d='M36.39 31.232c-7.276-7.277-15.255-11.095-17.821-8.528-2.566 2.566 1.252 10.545 8.528 17.82 7.276 7.277 15.255 11.095 17.821 8.529s-1.252-10.545-8.528-17.821'
      ></path>
      <path
        fill='#1945E8'
        d='M42.945 49.975a9.2 9.2 0 0 1-3.822-.758l-8.994 3.403-13.797 5.214-7.398 2.8-.63 2.578 10.844-4.103 13.793-5.219 10.098-3.82z'
      ></path>
      <path
        fill='#9EFF00'
        d='M54.305 42.558a1 1 0 0 1-.33-.06 29 29 0 0 0-14.937-1.45l-3.353.558a.939.939 0 0 1-.31-1.851l3.354-.564a30.9 30.9 0 0 1 15.906 1.545.939.939 0 0 1-.33 1.822M37.406 36.925a.939.939 0 0 1-.404-1.784l19.714-9.387a.939.939 0 0 1 .808 1.695L37.81 36.837a.94.94 0 0 1-.404.088M26.428 35.048a.939.939 0 0 1-.866-1.3l1.366-3.275a26.26 26.26 0 0 0 1.401-15.874.939.939 0 1 1 1.833-.408 28.13 28.13 0 0 1-1.502 17.004l-1.366 3.276a.94.94 0 0 1-.866.577'
      ></path>
      <path
        fill='#13BB88'
        d='M58.997 46.314a1.878 1.878 0 1 0 0-3.755 1.878 1.878 0 0 0 0 3.755'
      ></path>
      <path
        fill='#E86419'
        d='M36.471 20.03a1.877 1.877 0 1 0 0-3.755 1.877 1.877 0 0 0 0 3.755'
      ></path>
      <path
        fill='#13BB88'
        d='M28.02 11.581a1.877 1.877 0 1 1 0-3.755 1.877 1.877 0 0 1 0 3.755'
      ></path>
      <path
        fill='#E819AE'
        d='M54.304 15.334a.94.94 0 0 1-.939-.939v-.939a.939.939 0 0 1 1.878 0v.94c0 .518-.42.938-.939.938M54.304 10.64a.94.94 0 0 1-.939-.938v-.939a.939.939 0 0 1 1.878 0v.939c0 .518-.42.938-.939.938M57.12 12.518h-.939a.939.939 0 0 1 0-1.877h.939a.939.939 0 0 1 0 1.877M52.426 12.518h-.938a.939.939 0 0 1 0-1.877h.938a.939.939 0 0 1 0 1.877'
      ></path>
      <path
        fill='#BAC7F8'
        d='M15.756 35.049a26 26 0 0 1-.286-1.22l-2.44 10.014.187 2.29a21.08 21.08 0 0 0 5.93 12.97l13.794-5.214c-.076-.023-.154-.04-.229-.065a27.52 27.52 0 0 1-16.956-18.775'
      ></path>
      <path
        fill='#758FF1'
        d='M32.942 53.89c-.076-.023-.154-.04-.229-.065a28 28 0 0 1-2.65-1.167l-12.424 4.7q.706.917 1.51 1.751z'
      ></path>
      <path fill='#6D42F9' d='M47.733 19.07v4.714H43.02V19.07z'></path>
      <path
        fill='#9EFF00'
        d='M30.839 35.988a.939.939 0 0 1-.628-1.636l9.388-8.45a.939.939 0 0 1 1.256 1.396l-9.388 8.449a.94.94 0 0 1-.628.24'
      ></path>
      <path
        fill='#0079BC'
        d='M58.062 32.236a.94.94 0 0 1-.939-.938v-.94a.939.939 0 0 1 1.878 0v.94c0 .518-.42.938-.94.938M61.38 30.862a.93.93 0 0 1-.664-.275l-.663-.664a.939.939 0 0 1 1.327-1.327l.664.663a.939.939 0 0 1-.664 1.603M62.758 27.542h-.938a.939.939 0 0 1 0-1.878h.938a.939.939 0 0 1 0 1.878M60.72 24.89a.939.939 0 0 1-.664-1.603l.664-.664a.939.939 0 0 1 1.327 1.327l-.663.664a.94.94 0 0 1-.664.275M58.062 23.789a.94.94 0 0 1-.939-.939v-.939a.939.939 0 0 1 1.878 0v.94c0 .518-.42.938-.94.938M55.407 24.89a.94.94 0 0 1-.663-.276l-.664-.664a.939.939 0 0 1 1.327-1.327l.664.664a.939.939 0 0 1-.664 1.602'
      ></path>
      <path
        fill='#6D42F9'
        d='m13.002 8.768 1.45 2.939 3.244.471-2.347 2.288.554 3.23-2.9-1.525-2.901 1.525.553-3.23-2.346-2.288 3.243-.471z'
      ></path>
      <path
        fill='#E819AE'
        d='M63.396 58.674a6.52 6.52 0 0 0-2.879-3.794l-.387-.58a14.17 14.17 0 0 0-9.426-6.095.939.939 0 0 0-.31 1.851 12.28 12.28 0 0 1 7.164 3.994 5.54 5.54 0 0 0-3.755 2.578 3.755 3.755 0 1 0 6.557 3.662c.387-.746.595-1.57.61-2.41a4.59 4.59 0 0 1 .205 4.875.939.939 0 1 0 1.64.914 6.53 6.53 0 0 0 .58-4.995m-4.673.7a1.877 1.877 0 1 1-3.287-1.815l.008-.015a3.52 3.52 0 0 1 2.542-1.654.95.95 0 0 1 .47.108c.792.443.834 2.36.267 3.375'
      ></path>
    </g>
    <defs>
      <clipPath id='clip0_4546_82757'>
        <path fill='#fff' d='M7.826 7.826h56.348v56.348H7.826z'></path>
      </clipPath>
    </defs>
  </svg>
)

export default PartyPopperIcon
