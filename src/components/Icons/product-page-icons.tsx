import React, { FC } from "react"

interface IconProps {
  className?: string
}

export const C<PERSON>rencyNoteAddIcon: FC<IconProps> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
    className={className}
  >
    <path
      fill='#E819AE'
      fillRule='evenodd'
      d='M14.4 2.504q.388-.01.777.007c1.18.053 2.345.287 3.455.696l.535.197v6.68a5.9 5.9 0 0 0-1.627-1.17V5.434a1.76 1.76 0 0 1-1.1-.273 1.8 1.8 0 0 1-.74-.973 9 9 0 0 0-.594-.047 10 10 0 0 0-.662-.002c-1.495.047-2.673.455-3.934.899l-.01.003c-1.429.502-2.928 1.028-4.987 1.05-.396.006-.791-.014-1.185-.052a1.8 1.8 0 0 1-.708 1.03 1.76 1.76 0 0 1-1.158.313v5.512a1.76 1.76 0 0 1 1.097.272c.353.227.615.57.74.97q.3.035.602.051c1.689.08 2.986-.293 4.311-.747a5.9 5.9 0 0 0 .038 1.71c-1.143.371-2.38.682-3.889.682q-.264 0-.539-.013a12 12 0 0 1-3.446-.694l-.542-.195V3.474l1.08.377c1.182.413 2.383.623 3.58.603 1.794-.018 3.087-.47 4.482-.96l.028-.01c1.295-.455 2.65-.931 4.396-.98M9.084 7.76a.155.155 0 0 1-.153.157H8q.113.19.181.416h.749c.084 0 .153.07.153.157v.52a.155.155 0 0 1-.153.157h-.674c-.092.97-.789 1.635-1.765 1.665l1.925 1.814c.**************-.104.27H7.26a.15.15 0 0 1-.104-.04L5.05 10.89a.16.16 0 0 1-.05-.114v-.69c0-.087.07-.157.154-.157h1.072c.533 0 .874-.293.954-.763H5.154A.155.155 0 0 1 5 9.01v-.52c0-.087.068-.157.153-.157H7.02c-.162-.225-.43-.355-.778-.355H5.154A.155.155 0 0 1 5 7.822V7.24c0-.087.068-.157.153-.157H8.93c.084 0 .153.07.153.157zm2.97 9.353a4.167 4.167 0 1 0 5.893-5.892 4.167 4.167 0 0 0-5.893 5.892m2.447-2.446v2h1v-2h2v-1h-2v-2h-1v2h-2v1z'
      clipRule='evenodd'
    ></path>
  </svg>
)
