import { cn } from "@/lib/utils"
import React, { FC } from "react"

interface IconProps {
  className?: string
}

export const IconLogoLeft = (
  props?: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='86'
    height='27'
    fill='none'
    viewBox='0 0 86 27'
    {...props}
  >
    <path
      fill='#fff'
      fillRule='evenodd'
      d='M2.3 18.159h5.787c.173 1.14 1.544 1.962 3.262 1.962 1.775 0 2.886-.707 2.886-1.746 0-.765-.39-1.212-2.352-1.818l-2.18-.664c-3.478-1.054-5.325-2.93-5.325-5.773 0-4.243 3.666-7.014 8.717-7.014 5.368 0 8.644 2.454 8.673 6.581h-5.585c-.043-1.241-1.212-2.064-2.944-2.064-1.602 0-2.64.722-2.64 1.703 0 .837.576 1.415 2.25 1.905l2.28.664c3.738 1.082 5.355 2.641 5.355 5.643 0 4.387-3.753 7.115-9.251 7.115-5.614 0-8.919-2.381-8.933-6.494m17.816 6.133 4.43-20.825h5.687L28.66 10.77h.115c1.184-1.732 2.973-2.728 5.181-2.728 2.916 0 4.85 1.775 4.85 4.416a9.7 9.7 0 0 1-.217 1.948l-2.078 9.886h-5.672l1.89-9.005c.073-.376.102-.679.102-.982 0-1.097-.91-1.905-2.165-1.905-1.371 0-2.555 1.04-2.872 2.54l-1.977 9.352zm28.73 0h5.714l3.392-15.933h-5.585l-.549 2.57h-.274c-.505-1.704-2.294-2.8-4.59-2.8-4.574 0-7.965 4.416-7.965 10.347 0 3.738 2.034 6.047 5.31 6.047 2.006 0 3.594-.823 4.763-2.482h.26zm1.702-8.948c0 2.57-1.587 4.835-3.406 4.835-1.342 0-2.236-1.068-2.236-2.684 0-2.7 1.53-4.878 3.434-4.878 1.342 0 2.208 1.068 2.208 2.727m5.056 8.948 3.42-15.933h5.586l-.462 2.31h.115c.722-1.487 2.136-2.54 3.854-2.54.909 0 1.558.13 2.193.418l-1.082 4.95c-.736-.346-1.429-.592-2.396-.592-1.89 0-3.261 1.054-3.738 3.248l-1.76 8.139zm13.466-6.898c0 4.589 2.973 7.288 7.533 7.288 3.037 0 5.725-1.882 7.18-4.844-6.297.382-9.34-2.32-9.34-2.32s5.225.941 10.253-.404q.123-.617.182-1.267c.47-5.161-2.502-7.892-6.774-7.892-5.426 0-9.034 3.983-9.034 9.439m5.657-2.944h5.397c.03-.073.044-.303.044-.462 0-1.213-.953-2.078-2.324-2.078-1.486 0-2.742 1.024-3.117 2.54'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconLogoLeftBlue = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='115'
    height='36'
    fill='none'
    viewBox='0 0 115 36'
    className={className}
  >
    <path
      fill='#1B4BFF'
      fillRule='evenodd'
      d='M2.222 23.92h7.83c.234 1.543 2.089 2.656 4.413 2.656 2.401 0 3.905-.957 3.905-2.363 0-1.034-.528-1.64-3.183-2.46l-2.948-.898c-4.706-1.425-7.205-3.964-7.205-7.81 0-5.74 4.96-9.49 11.793-9.49 7.263 0 11.696 3.32 11.735 8.904h-7.556c-.06-1.679-1.64-2.792-3.984-2.792-2.167 0-3.573.976-3.573 2.304 0 1.133.781 1.913 3.046 2.577l3.085.899c5.057 1.464 7.244 3.573 7.244 7.634 0 5.936-5.077 9.626-12.516 9.626-7.595 0-12.066-3.222-12.086-8.786m24.105 8.299L32.32 4.044h7.693l-2.128 9.88h.156c1.601-2.343 4.022-3.69 7.01-3.69 3.944 0 6.56 2.4 6.56 5.974 0 .8-.098 1.699-.293 2.636l-2.811 13.375h-7.674l2.558-12.184a7 7 0 0 0 .137-1.328c0-1.484-1.23-2.577-2.93-2.577-1.854 0-3.455 1.406-3.885 3.436L34.04 32.22zm38.869 0h7.732l4.588-21.556H69.96l-.742 3.475h-.371c-.683-2.304-3.105-3.787-6.21-3.787-6.189 0-10.777 5.974-10.777 14 0 5.056 2.753 8.18 7.185 8.18 2.714 0 4.862-1.113 6.444-3.358h.351zM67.5 20.113c0 3.476-2.148 6.541-4.608 6.541-1.816 0-3.027-1.445-3.027-3.632 0-3.65 2.07-6.6 4.647-6.6 1.816 0 2.988 1.446 2.988 3.691m6.84 12.106 4.627-21.556h7.557l-.625 3.124h.156c.976-2.011 2.89-3.437 5.213-3.437 1.23 0 2.11.176 2.968.567l-1.464 6.697c-.996-.469-1.933-.8-3.241-.8-2.558 0-4.413 1.425-5.057 4.392L82.092 32.22zm18.218-9.333c0 6.209 4.022 9.86 10.192 9.86 4.108 0 7.746-2.546 9.714-6.553-8.519.517-12.637-3.14-12.637-3.14s7.07 1.274 13.872-.546a16 16 0 0 0 .247-1.714c.635-6.983-3.386-10.677-9.165-10.677-7.342 0-12.223 5.39-12.223 12.77m7.654-3.983h7.302c.039-.098.059-.41.059-.625 0-1.64-1.289-2.812-3.144-2.812-2.011 0-3.71 1.386-4.217 3.437'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconLogoRight = ({
  color,
  className,
}: {
  color?: string
  className?: string
}) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='51'
    height='27'
    fill='none'
    viewBox='0 0 51 27'
    className={cn(className)}
  >
    <path
      fill={color ?? "#9EFF00"}
      fillRule='evenodd'
      d='M4.786 3.106h8.14c5.075 0 7.917 2.679 7.917 6.682 0 5.477-3.84 8.93-10 8.93H7.791l-1.25 5.863H.247l1.07-5.06c6.5-1.309 10.317-6.29 10.317-6.29l2.168 1.84 1.601-8.78-8.404 3.004 2.04 1.731s-2.377 3.315-7.047 5.296zm31.4 21.475h-5.892l.49-2.322h-.267c-1.206 1.712-2.843 2.56-4.911 2.56-3.378 0-5.477-2.381-5.477-6.236 0-6.116 3.498-10.67 8.215-10.67 2.366 0 4.212 1.131 4.733 2.887h.282l.566-2.649h5.76zm-7.648-4.242c1.875 0 3.512-2.336 3.512-4.985 0-1.711-.893-2.813-2.277-2.813-1.965 0-3.542 2.248-3.542 5.03 0 1.667.923 2.768 2.307 2.768M42.825 3.106l-4.569 21.475h5.893l4.57-21.475z'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconMap: React.FC<IconProps> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
    className={cn("fill-[#030D31]", className)}
  >
    <path
      fill='inherit'
      d='M10 5.417a2.083 2.083 0 1 1 0 4.166 2.083 2.083 0 0 1 0-4.166m0-3.75A5.833 5.833 0 0 1 15.833 7.5c0 4.375-5.833 10.833-5.833 10.833S4.167 11.875 4.167 7.5A5.833 5.833 0 0 1 10 1.667m0 1.666A4.167 4.167 0 0 0 5.833 7.5c0 .833 0 2.5 4.167 8.092C14.166 10 14.166 8.333 14.166 7.5A4.167 4.167 0 0 0 10 3.333'
    ></path>
  </svg>
)

export const IconDeliveryDate = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
    className={className}
  >
    <path
      fill='currentColor'
      fillRule='evenodd'
      d='M15 2.5h.833A1.666 1.666 0 0 1 17.5 4.167V7.5h-1.667v-.833H4.167v9.166h4.166V17.5H4.167A1.667 1.667 0 0 1 2.5 15.833V4.167A1.667 1.667 0 0 1 4.167 2.5H5V.833h1.667V2.5h6.666V.833H15zm-4.369 14.369a5 5 0 1 0 7.071-7.071 5 5 0 0 0-7.07 7.07m5.827-3.536-3.75 2.25v-4.5z'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconPickupDate = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
    className={className}
  >
    <path
      fill='currentColor'
      fillRule='evenodd'
      d='M15 2.5h.833A1.666 1.666 0 0 1 17.5 4.167V7.5h-1.667v-.833H4.167v9.166h4.166V17.5H4.167A1.667 1.667 0 0 1 2.5 15.833V4.167A1.667 1.667 0 0 1 4.167 2.5H5V.833h1.667V2.5h6.666V.833H15zm-4.369 14.369a5 5 0 1 0 7.071-7.071 5 5 0 0 0-7.07 7.07m1.869-5.202h3.333V15H12.5z'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconSearch = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    fill='none'
    viewBox='0 0 24 24'
    className={cn("fill-inherit", className)}
  >
    <path
      // fill="#090A0B"
      d='M15.5 14h-.79l-.28-.27A6.47 6.47 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14'
    ></path>
  </svg>
)

export const IconCart: FC<IconProps> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    fill='none'
    viewBox='0 0 24 24'
    className={cn("fill-inherit", className)}
  >
    <path
      // fill="#090A0B"
      d='M17 18a2 2 0 1 1-2 2c0-1.11.89-2 2-2M1 2h3.27l.94 2H20a1 1 0 0 1 1 1c0 .17-.05.34-.12.5l-3.58 6.47c-.34.61-1 1.03-1.75 1.03H8.1l-.9 1.63-.03.12a.25.25 0 0 0 .25.25H19v2H7a2 2 0 0 1-2-2c0-.35.09-.68.24-.96l1.36-2.45L3 4H1zm6 16a2 2 0 1 1-2 2c0-1.11.89-2 2-2m9-7 2.78-5H6.14l2.36 5z'
    ></path>
  </svg>
)

export const IconProfile = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    fill='none'
    viewBox='0 0 24 24'
    className='fill-inherit'
    {...props}
  >
    <path
      // fill="#fff"
      fillRule='evenodd'
      d='M9.828 14.621c.478 0 .935.189 1.272.524l.9.894.9-.894a1.8 1.8 0 0 1 1.272-.524h3.62c.602 0 1.186.2 1.66.568.475.368.812.883.958 1.463l.56 2.222a.89.89 0 0 1-.331.948.902.902 0 0 1-1.415-.514l-.559-2.221a.89.89 0 0 0-.872-.678h-3.621l-.9.894a1.805 1.805 0 0 1-2.544 0l-.9-.894h-3.62a.9.9 0 0 0-.872.678l-.56 2.221a.89.89 0 0 1-.74.682.9.9 0 0 1-.912-.43.9.9 0 0 1-.093-.686l.558-2.222a2.68 2.68 0 0 1 .958-1.463 2.7 2.7 0 0 1 1.66-.568zM12 3c.705 0 1.494.17 2.141.35 1.496.413 2.357 1.804 2.357 3.223v3.581c0 1.42-.86 2.81-2.357 3.224-.647.178-1.436.35-2.141.35s-1.494-.17-2.141-.35c-1.496-.414-2.357-1.804-2.357-3.224V6.573c0-1.419.86-2.81 2.357-3.223C10.506 3.17 11.295 3 12 3m0 1.788c-.458 0-1.059.118-1.658.284-.601.166-1.04.757-1.04 1.501v3.581c0 .744.439 1.335 1.04 1.502.6.165 1.2.283 1.658.283s1.059-.118 1.658-.284c.601-.166 1.04-.757 1.04-1.5V6.572c0-.744-.439-1.335-1.04-1.502-.6-.164-1.2-.283-1.658-.283'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconVerified = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    width='72'
    height='72'
    viewBox='0 0 72 72'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_1496_1449)'>
      <rect width='72' height='72' rx='36' fill='#1437BA' />
      <path
        d='M36 17.25L51.75 23.25V38.25C51.75 45 45.75 51.75 36 54.75C26.25 51.75 20.25 45.75 20.25 38.25V23.25L36 17.25Z'
        stroke='#9EFF00'
        strokeWidth='4.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M29.25 36.25L33.75 40.75L42.75 30.25'
        stroke='#9EFF00'
        strokeWidth='4.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </g>
    <defs>
      <clipPath id='clip0_1496_1449'>
        <rect width='72' height='72' fill='white' />
      </clipPath>
    </defs>
  </svg>
)

export const IconDelivery = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    width='73'
    height='72'
    viewBox='0 0 73 72'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_1602_1451)'>
      <rect x='0.333496' width='72' height='72' rx='36' fill='#1437BA' />
      <path
        d='M16.3335 26V24C15.8031 24 15.2944 24.2107 14.9193 24.5858C14.5442 24.9609 14.3335 25.4696 14.3335 26H16.3335ZM38.3335 26H40.3335C40.3335 25.4696 40.1228 24.9609 39.7477 24.5858C39.3726 24.2107 38.8639 24 38.3335 24V26ZM38.3335 30V28C37.8031 28 37.2944 28.2107 36.9193 28.5858C36.5442 28.9609 36.3335 29.4696 36.3335 30H38.3335ZM16.3335 28H38.3335V24H16.3335V28ZM36.3335 26V50H40.3335V26H36.3335ZM18.3335 46V26H14.3335V46H18.3335ZM38.3335 32H48.3335V28H38.3335V32ZM54.3335 38V46H58.3335V38H54.3335ZM40.3335 50V30H36.3335V50H40.3335ZM49.7475 51.414C49.3724 51.7889 48.8638 51.9996 48.3335 51.9996C47.8032 51.9996 47.2946 51.7889 46.9195 51.414L44.0915 54.242C45.2167 55.3668 46.7425 55.9987 48.3335 55.9987C49.9245 55.9987 51.4503 55.3668 52.5755 54.242L49.7475 51.414ZM46.9195 48.586C47.2946 48.2111 47.8032 48.0004 48.3335 48.0004C48.8638 48.0004 49.3724 48.2111 49.7475 48.586L52.5755 45.758C51.4503 44.6332 49.9245 44.0013 48.3335 44.0013C46.7425 44.0013 45.2167 44.6332 44.0915 45.758L46.9195 48.586ZM25.7475 51.414C25.3724 51.7889 24.8638 51.9996 24.3335 51.9996C23.8032 51.9996 23.2946 51.7889 22.9195 51.414L20.0915 54.242C21.2167 55.3668 22.7425 55.9987 24.3335 55.9987C25.9245 55.9987 27.4503 55.3668 28.5755 54.242L25.7475 51.414ZM22.9195 48.586C23.2946 48.2111 23.8032 48.0004 24.3335 48.0004C24.8638 48.0004 25.3724 48.2111 25.7475 48.586L28.5755 45.758C27.4503 44.6332 25.9245 44.0013 24.3335 44.0013C22.7425 44.0013 21.2167 44.6332 20.0915 45.758L22.9195 48.586ZM49.7475 48.586C50.1395 48.976 50.3335 49.486 50.3335 50H54.3335C54.3335 48.468 53.7475 46.928 52.5755 45.758L49.7475 48.586ZM50.3335 50C50.3334 50.5304 50.1226 51.039 49.7475 51.414L52.5755 54.242C53.7008 53.117 54.3332 51.5912 54.3335 50H50.3335ZM44.3335 48H38.3335V52H44.3335V48ZM46.9195 51.414C46.5444 51.039 46.3336 50.5304 46.3335 50H42.3335C42.3335 51.532 42.9195 53.072 44.0915 54.242L46.9195 51.414ZM46.3335 50C46.3336 49.4696 46.5444 48.961 46.9195 48.586L44.0915 45.758C42.9662 46.883 42.3338 48.4088 42.3335 50H46.3335ZM22.9195 51.414C22.5444 51.039 22.3336 50.5304 22.3335 50H18.3335C18.3335 51.532 18.9195 53.072 20.0915 54.242L22.9195 51.414ZM22.3335 50C22.3336 49.4696 22.5444 48.961 22.9195 48.586L20.0915 45.758C18.9662 46.883 18.3338 48.4088 18.3335 50H22.3335ZM38.3335 48H28.3335V52H38.3335V48ZM25.7475 48.586C26.1395 48.976 26.3335 49.486 26.3335 50H30.3335C30.3335 48.468 29.7475 46.928 28.5755 45.758L25.7475 48.586ZM26.3335 50C26.3334 50.5304 26.1226 51.039 25.7475 51.414L28.5755 54.242C29.7008 53.117 30.3332 51.5912 30.3335 50H26.3335ZM54.3335 46C54.3335 46.5304 54.1228 47.0391 53.7477 47.4142C53.3726 47.7893 52.8639 48 52.3335 48V52C53.9248 52 55.4509 51.3679 56.5761 50.2426C57.7014 49.1174 58.3335 47.5913 58.3335 46H54.3335ZM48.3335 32C49.9248 32 51.4509 32.6321 52.5761 33.7574C53.7014 34.8826 54.3335 36.4087 54.3335 38H58.3335C58.3335 36.6868 58.0748 35.3864 57.5723 34.1732C57.0697 32.9599 56.3331 31.8575 55.4046 30.9289C54.476 30.0003 53.3736 29.2638 52.1603 28.7612C50.9471 28.2587 49.6467 28 48.3335 28V32ZM14.3335 46C14.3335 47.5913 14.9656 49.1174 16.0909 50.2426C17.2161 51.3679 18.7422 52 20.3335 52V48C19.8031 48 19.2944 47.7893 18.9193 47.4142C18.5442 47.0391 18.3335 46.5304 18.3335 46H14.3335Z'
        fill='#9EFF00'
      />
      <path
        d='M27.3335 24C27.3335 22.4087 26.7014 20.8826 25.5761 19.7574C24.4509 18.6321 22.9248 18 21.3335 18C20.8031 18 20.2944 18.2107 19.9193 18.5858C19.5442 18.9609 19.3335 19.4696 19.3335 20C19.3335 21.5913 19.9656 23.1174 21.0909 24.2426C22.2161 25.3679 23.7422 26 25.3335 26H27.3335M27.3335 24V26M27.3335 24C27.3335 22.4087 27.9656 20.8826 29.0909 19.7574C30.2161 18.6321 31.7422 18 33.3335 18C33.8639 18 34.3726 18.2107 34.7477 18.5858C35.1228 18.9609 35.3335 19.4696 35.3335 20C35.3335 21.5913 34.7014 23.1174 33.5761 24.2426C32.4509 25.3679 30.9248 26 29.3335 26H27.3335'
        stroke='#9EFF00'
        strokeWidth='4'
        strokeLinejoin='round'
      />
    </g>
    <defs>
      <clipPath id='clip0_1602_1451'>
        <rect
          width='72'
          height='72'
          fill='white'
          transform='translate(0.333496)'
        />
      </clipPath>
    </defs>
  </svg>
)

export const IconSupport = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    width='73'
    height='72'
    viewBox='0 0 73 72'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <g clipPath='url(#clip0_1496_1469)'>
      <rect x='0.666504' width='72' height='72' rx='36' fill='#1437BA' />
      <g clipPath='url(#clip1_1496_1469)'>
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M36.6328 21.7273C35.5482 21.7077 34.4703 21.9022 33.461 22.2996C32.4516 22.6971 31.5305 23.2896 30.7504 24.0435C29.9703 24.7973 29.3465 25.6976 28.9147 26.6927C28.483 27.6879 28.2517 28.7585 28.2341 29.8431V41.1867C28.2341 41.8748 27.9608 42.5348 27.4742 43.0214C26.9876 43.508 26.3277 43.7813 25.6395 43.7813H22.3963C21.3641 43.7813 20.3742 43.3713 19.6443 42.6414C18.9144 41.9115 18.5044 40.9216 18.5044 39.8894V34.7002C18.5044 33.668 18.9144 32.6781 19.6443 31.9482C20.3742 31.2184 21.3641 30.8083 22.3963 30.8083H24.3422V29.7964C24.3659 28.2018 24.7035 26.6274 25.3357 25.1632C25.9679 23.6991 26.8823 22.3738 28.0268 21.263C29.1712 20.1523 30.5233 19.2779 32.0057 18.6898C33.4881 18.1017 35.0719 17.8113 36.6666 17.8354C38.2612 17.8113 39.845 18.1017 41.3274 18.6898C42.8098 19.2779 44.1619 20.1523 45.3063 21.263C46.4508 22.3738 47.3652 23.6991 47.9974 25.1632C48.6296 26.6274 48.9672 28.2018 48.9909 29.7964V30.8083H50.9368C51.969 30.8083 52.9589 31.2184 53.6888 31.9482C54.4187 32.6781 54.8287 33.668 54.8287 34.7002V39.8894C54.8287 40.9216 54.4187 41.9115 53.6888 42.6414C52.9589 43.3713 51.969 43.7813 50.9368 43.7813H48.9909V45.0786C48.9912 46.8319 48.346 48.524 47.1783 49.8319C46.0106 51.1398 44.4022 51.972 42.6601 52.1696C42.3217 52.7737 41.8284 53.2766 41.2309 53.6264C40.6335 53.9762 39.9535 54.1603 39.2612 54.1597H35.3693C34.3371 54.1597 33.3471 53.7497 32.6173 53.0198C31.8874 52.2899 31.4774 51.3 31.4774 50.2678C31.4774 49.2356 31.8874 48.2457 32.6173 47.5158C33.3471 46.7859 34.3371 46.3759 35.3693 46.3759H39.2612C40.6674 46.3759 41.8999 47.1206 42.5822 48.2388C43.2974 48.0745 43.9356 47.6724 44.3928 47.0984C44.8499 46.5244 45.0989 45.8124 45.099 45.0786V29.8406C45.0812 28.7503 44.8475 27.6744 44.4113 26.675C43.9752 25.6757 43.3453 24.7726 42.558 24.0182C41.7708 23.2638 40.8418 22.6728 39.8247 22.2796C38.8077 21.8864 37.7228 21.6986 36.6328 21.7273Z'
          fill='#9EFF00'
        />
      </g>
    </g>
    <defs>
      <clipPath id='clip0_1496_1469'>
        <rect
          width='72'
          height='72'
          fill='white'
          transform='translate(0.666504)'
        />
      </clipPath>
      <clipPath id='clip1_1496_1469'>
        <rect
          width='36.3243'
          height='36.3243'
          fill='white'
          transform='translate(18.5044 17.8379)'
        />
      </clipPath>
    </defs>
  </svg>
)

export const IconChevonRight = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    width='20'
    height='20'
    viewBox='0 0 20 20'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}
  >
    <path
      d='M7.1582 13.8167L10.9749 10L7.1582 6.175L8.3332 5L13.3332 10L8.3332 15L7.1582 13.8167Z'
      fill='#E2E7EA'
    />
  </svg>
)

export const IconCall = () => (
  <svg
    width='25'
    height='24'
    viewBox='0 0 25 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <g clipPath='url(#clip0_1640_4537)'>
      <path
        d='M7.37 10.79C8.81 13.62 11.13 15.93 13.96 17.38L16.16 15.18C16.43 14.91 16.83 14.82 17.18 14.94C18.3 15.31 19.51 15.51 20.75 15.51C21.3 15.51 21.75 15.96 21.75 16.51V20C21.75 20.55 21.3 21 20.75 21C11.36 21 3.75 13.39 3.75 4C3.75 3.45 4.2 3 4.75 3H8.25C8.8 3 9.25 3.45 9.25 4C9.25 5.25 9.45 6.45 9.82 7.57C9.93 7.92 9.85 8.31 9.57 8.59L7.37 10.79Z'
        fill='#9C9FA8'
      />
    </g>
    <defs>
      <clipPath id='clip0_1640_4537'>
        <rect width='24' height='24' fill='white' transform='translate(0.75)' />
      </clipPath>
    </defs>
  </svg>
)

export const IconMail = () => (
  <svg
    width='25'
    height='24'
    viewBox='0 0 25 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      d='M4.75 20C4.2 20 3.72933 19.8043 3.338 19.413C2.94667 19.0217 2.75067 18.5507 2.75 18V6C2.75 5.45 2.946 4.97933 3.338 4.588C3.73 4.19667 4.20067 4.00067 4.75 4H20.75C21.3 4 21.771 4.196 22.163 4.588C22.555 4.98 22.7507 5.45067 22.75 6V18C22.75 18.55 22.5543 19.021 22.163 19.413C21.7717 19.805 21.3007 20.0007 20.75 20H4.75ZM12.75 13L4.75 8V18H20.75V8L12.75 13ZM12.75 11L20.75 6H4.75L12.75 11ZM4.75 8V6V18V8Z'
      fill='#9C9FA8'
    />
  </svg>
)

export const IconFacebook = () => (
  <svg
    width='33'
    height='32'
    viewBox='0 0 33 32'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M8.75 4C6.54086 4 4.75 5.79086 4.75 8V24C4.75 26.2091 6.54086 28 8.75 28H15.6867V19.1634H13.1501V16.039H15.6867V13.451C15.6867 10.7192 17.2239 8.80031 20.4633 8.80031L22.7501 8.80284V12.0914H21.2319C19.9712 12.0914 19.4917 13.0332 19.4917 13.9068V16.0402H22.7488L22.0284 19.1634H19.4917V28H24.75C26.9591 28 28.75 26.2091 28.75 24V8C28.75 5.79086 26.9591 4 24.75 4H8.75Z'
      fill='#9C9FA8'
    />
  </svg>
)

export const IconInstagram = () => (
  <svg
    width='33'
    height='32'
    viewBox='0 0 33 32'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M8.75 4C6.54086 4 4.75 5.79086 4.75 8V24C4.75 26.2091 6.54086 28 8.75 28H24.75C26.9591 28 28.75 26.2091 28.75 24V8C28.75 5.79086 26.9591 4 24.75 4H8.75ZM26.3511 8.22583C26.3511 8.71014 26.1587 9.17461 25.8162 9.51707C25.4738 9.85952 25.0093 10.0519 24.525 10.0519C24.0407 10.0519 23.5762 9.85952 23.2337 9.51707C22.8913 9.17461 22.6989 8.71014 22.6989 8.22583C22.6989 7.74152 22.8913 7.27705 23.2337 6.93459C23.5762 6.59213 24.0407 6.39974 24.525 6.39974C25.0093 6.39974 25.4738 6.59213 25.8162 6.93459C26.1587 7.27705 26.3511 7.74152 26.3511 8.22583ZM16.8548 11.7212C15.6925 11.7212 14.5777 12.183 13.7558 13.0049C12.9339 13.8268 12.4722 14.9415 12.4722 16.1038C12.4722 17.2662 12.9339 18.3809 13.7558 19.2028C14.5777 20.0247 15.6925 20.4864 16.8548 20.4864C18.0171 20.4864 19.1319 20.0247 19.9538 19.2028C20.7757 18.3809 21.2374 17.2662 21.2374 16.1038C21.2374 14.9415 20.7757 13.8268 19.9538 13.0049C19.1319 12.183 18.0171 11.7212 16.8548 11.7212ZM16.8548 8.79948C18.792 8.79948 20.6499 9.56904 22.0198 10.9389C23.3896 12.3087 24.1592 14.1666 24.1592 16.1038C24.1592 18.0411 23.3896 19.899 22.0198 21.2688C20.6499 22.6386 18.792 23.4082 16.8548 23.4082C14.9176 23.4082 13.0597 22.6386 11.6899 21.2688C10.32 19.899 9.55046 18.0411 9.55046 16.1038C9.55046 14.1666 10.32 12.3087 11.6899 10.9389C13.0597 9.56904 14.9176 8.79948 16.8548 8.79948Z'
      fill='#9C9FA8'
    />
  </svg>
)

export const IconLinkedIn = () => (
  <svg
    width='33'
    height='32'
    viewBox='0 0 33 32'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M8.75 4C6.54086 4 4.75 5.79086 4.75 8V24C4.75 26.2091 6.54086 28 8.75 28H24.75C26.9591 28 28.75 26.2091 28.75 24V8C28.75 5.79086 26.9591 4 24.75 4H8.75ZM10.3782 11.6536C10.9162 11.6536 11.4321 11.44 11.8124 11.06C12.1928 10.6799 12.4065 10.1644 12.4065 9.62691C12.4065 9.08941 12.1928 8.57392 11.8124 8.19385C11.4321 7.81378 10.9162 7.60026 10.3782 7.60026C9.84029 7.60026 9.32438 7.81378 8.94401 8.19385C8.56363 8.57392 8.34994 9.08941 8.34994 9.62691C8.34994 10.1644 8.56363 10.6799 8.94401 11.06C9.32438 11.44 9.84029 11.6536 10.3782 11.6536ZM12.0755 24.4003V13.2427H8.68091V24.4003H12.0755ZM17.7242 13.2427H14.3296V24.3986H17.7242V19.3345C17.7666 18.0049 17.9211 15.9731 19.7525 15.9731C21.5822 15.9731 21.7553 17.5894 21.7553 18.4119V24.3986H25.1499V18.4102C25.1499 18.4102 25.1364 17.7369 25.1364 17.5894C25.1364 14.5146 23.8108 12.8984 21.37 12.8984C19.3248 12.8984 18.2249 13.9533 17.7242 14.7368V13.2427Z'
      fill='#9C9FA8'
    />
  </svg>
)

export const IconClose = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='12'
    height='12'
    fill='none'
    viewBox='0 0 12 12'
  >
    <path
      fill='#000'
      d='M1.286.107.107 1.286 4.821 6 .107 10.714l1.179 1.179L6 7.179l4.714 4.714 1.178-1.179L7.179 6l4.715-4.714L10.714.107 6 4.821z'
    ></path>
  </svg>
)

export const IconDatePicker = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
  >
    <g clipPath='url(#clip0_1764_1193)'>
      <path
        fill='currentColor'
        fillRule='evenodd'
        d='M15 2.5h.833A1.666 1.666 0 0 1 17.5 4.167V7.5h-1.667v-.833H4.167v9.166h4.166V17.5H4.167A1.667 1.667 0 0 1 2.5 15.833V4.167A1.667 1.667 0 0 1 4.167 2.5H5V.833h1.667V2.5h6.666V.833H15zm-4.369 14.369a5 5 0 1 0 7.071-7.071 5 5 0 0 0-7.07 7.07m3.036-3.036v2h1v-2h2v-1h-2v-2h-1v2h-2v1z'
        clipRule='evenodd'
      ></path>
    </g>
    <defs>
      <clipPath id='clip0_1764_1193'>
        <path fill='#fff' d='M0 0h20v20H0z'></path>
      </clipPath>
    </defs>
  </svg>
)

export const IconOffer = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='36'
    height='36'
    fill='none'
    viewBox='0 0 36 36'
  >
    <path
      fill='#D4DF5B'
      fillRule='evenodd'
      d='m29.163 14.5 2.692-4.652-4.652-2.69V1.798h-5.36l-2.688-4.652-4.656 2.69-4.655-2.692-2.69 4.654h-5.36v5.36l-4.653 2.689L-.166 14.5l-2.694 4.655 4.652 2.688v5.36h5.362l2.69 4.653 4.652-2.692 4.657 2.692 2.687-4.653h5.36v-5.36l4.654-2.69zM21.46 34.661a2.125 2.125 0 0 1-2.903.776l-4.059-2.346-4.054 2.346c-.327.189-.695.286-1.065.286a2.15 2.15 0 0 1-1.84-1.062l-2.346-4.059H.517a2.125 2.125 0 0 1-2.125-2.125v-4.673l-4.059-2.346a2.133 2.133 0 0 1-.773-2.904l2.346-4.055-2.346-4.055a2.125 2.125 0 0 1 .775-2.904l4.059-2.346V.522A2.125 2.125 0 0 1 .519-1.603h4.673l2.346-4.059a2.125 2.125 0 0 1 2.903-.778l4.058 2.348 4.06-2.346a2.125 2.125 0 0 1 2.902.778l2.344 4.057h4.673A2.125 2.125 0 0 1 30.603.522v4.675l4.058 2.346a2.125 2.125 0 0 1 .776 2.902L33.091 14.5l2.344 4.055a2.125 2.125 0 0 1-.776 2.904L30.6 23.805v4.673a2.125 2.125 0 0 1-2.125 2.125h-4.672z'
      clipRule='evenodd'
    ></path>
    <path
      fill='#D4DF5B'
      d='M8.126 5.998a3.189 3.189 0 1 1-.002 6.377 3.189 3.189 0 0 1 .002-6.377M9.822 24.228l-3.4-2.548 12.75-17 3.4 2.548zM20.874 23a3.19 3.19 0 1 1 .003-6.378A3.19 3.19 0 0 1 20.874 23'
    ></path>
  </svg>
)

export const IconSupportWhatsapp = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
  >
    <g clipPath='url(#clip0_1812_23815)'>
      <path
        fill='currentColor'
        d='M10.001 1.667a8.333 8.333 0 1 1 0 16.666 8.3 8.3 0 0 1-4.191-1.129l-4.139 1.13 1.127-4.14a8.333 8.333 0 0 1 7.203-12.527m-2.84 4.416-.166.007a.8.8 0 0 0-.31.083q-.136.078-.245.19c-.1.094-.157.176-.218.255-.308.401-.474.893-.472 1.399.002.408.109.806.275 1.177.341.752.902 1.548 1.642 2.285.178.178.353.356.542.522a7.9 7.9 0 0 0 3.2 1.705l.473.072c.154.009.309-.003.463-.01.243-.013.48-.079.695-.193q.163-.085.319-.183 0 0 .104-.075a2 2 0 0 0 .275-.24 1 1 0 0 0 .175-.252c.065-.136.13-.395.157-.61.02-.166.014-.256.011-.312-.003-.089-.077-.181-.158-.22l-.485-.218s-.725-.316-1.168-.518a.4.4 0 0 0-.147-.034.4.4 0 0 0-.315.106c-.004-.002-.06.046-.662.776a.29.29 0 0 1-.307.108 1 1 0 0 1-.16-.055c-.103-.043-.139-.06-.21-.09a5 5 0 0 1-1.312-.835c-.105-.092-.202-.192-.302-.289a5.3 5.3 0 0 1-.85-1.056l-.05-.08a1 1 0 0 1-.085-.17.27.27 0 0 1 .051-.221s.203-.222.297-.342c.092-.117.17-.23.22-.31.097-.16.128-.322.077-.447a68 68 0 0 0-.724-1.701c-.049-.112-.195-.192-.327-.208l-.135-.013a3 3 0 0 0-.336.003z'
      ></path>
    </g>
    <defs>
      <clipPath id='clip0_1812_23815'>
        <path fill='#fff' d='M0 0h20v20H0z'></path>
      </clipPath>
    </defs>
  </svg>
)

export const IconWhatsapp = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='30'
    height='30'
    className={cn(className)}
    fill='none'
    viewBox='0 0 30 30'
  >
    <path
      fill='#000'
      d='m7.881 24.741 1.086.634A11.9 11.9 0 0 0 15.002 27a12 12 0 1 0-12-12 11.9 11.9 0 0 0 1.626 6.036l.633 1.086-.98 3.602zM.008 30l2.028-7.452A14.9 14.9 0 0 1 .002 15c0-8.285 6.715-15 15-15s15 6.716 15 15c0 8.285-6.715 15-15 15a14.9 14.9 0 0 1-7.545-2.032zm9.58-22.038q.303-.021.605-.***************.024c.239.027.501.172.59.373q.67 1.523 1.302 3.06c.093.229.037.521-.14.806-.09.146-.231.35-.395.558-.169.218-.534.617-.534.617s-.148.176-.091.397c.**************.153.307l.088.143c.384.64.9 1.29 1.53 1.902.18.174.356.352.545.519a9 9 0 0 0 2.355 1.5l.008.003c.***************.377.165q.14.058.287.**************.017a.52.52 0 0 0 .442-.213c1.085-1.314 1.185-1.4 1.193-1.4v.003a.72.72 0 0 1 .567-.19.8.8 0 0 1 .265.06c.796.364 2.1.933 2.1.933l.873.391c.147.07.28.237.285.397.006.101.015.263-.02.56-.047.389-.165.855-.282 1.1q-.122.25-.314.453a3.6 3.6 0 0 1-.495.431 3 3 0 0 1-.188.136 8 8 0 0 1-.575.33 3 3 0 0 1-1.249.345c-.277.015-.555.035-.834.02-.012 0-.852-.13-.852-.13a14.2 14.2 0 0 1-5.76-3.069c-.339-.299-.654-.62-.975-.939-1.332-1.328-2.342-2.76-2.955-4.113a5.25 5.25 0 0 1-.495-2.12 4.1 4.1 0 0 1 .847-2.52c.11-.14.213-.287.392-.457.189-.18.31-.276.441-.342a1.5 1.5 0 0 1 .556-.15'
    ></path>
  </svg>
)

export const IconPerson = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='18'
    height='17'
    fill='none'
    className={cn(className)}
    viewBox='0 0 18 17'
  >
    <path
      fill='#4A4E5A'
      fillRule='evenodd'
      d='M6.828 11.621c.477 0 .935.189 1.272.524l.9.894.9-.894a1.8 1.8 0 0 1 1.272-.524h3.62c.602 0 1.186.2 1.66.568.475.368.812.883.958 1.463l.56 2.222a.89.89 0 0 1-.331.948.902.902 0 0 1-1.415-.514l-.559-2.221a.89.89 0 0 0-.872-.678h-3.621l-.9.894a1.805 1.805 0 0 1-2.544 0l-.9-.894h-3.62a.9.9 0 0 0-.872.678l-.56 2.221a.89.89 0 0 1-.74.682.9.9 0 0 1-.912-.43.9.9 0 0 1-.093-.686l.558-2.222a2.68 2.68 0 0 1 .958-1.463 2.7 2.7 0 0 1 1.66-.568zM9 0c.705 0 1.494.17 2.141.35 1.496.413 2.357 1.804 2.357 3.223v3.581c0 1.42-.86 2.81-2.357 3.224-.647.178-1.436.35-2.141.35s-1.494-.17-2.141-.35c-1.496-.414-2.357-1.804-2.357-3.224V3.573c0-1.419.86-2.81 2.357-3.223C7.506.17 8.295 0 9 0m0 1.788c-.458 0-1.059.118-1.658.284-.601.166-1.04.757-1.04 1.501v3.581c0 .744.439 1.335 1.04 1.502.6.165 1.2.283 1.658.283s1.059-.118 1.658-.284c.601-.166 1.04-.757 1.04-1.5V3.572c0-.744-.439-1.335-1.04-1.502-.6-.164-1.2-.283-1.658-.283'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconCategory = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='17'
    height='17'
    fill='none'
    viewBox='0 0 17 17'
  >
    <path
      fill='#4A4E5A'
      fillRule='evenodd'
      d='M7.292 1.06 4.188 6.105a.84.84 0 0 0-.********* 0 0 0 .728.415h6.208a.812.812 0 0 0 .833-.83.83.83 0 0 0-.124-.435L8.708 1.06a.76.76 0 0 0-.312-.3.89.89 0 0 0-.792-.001.77.77 0 0 0-.312.3m-.125 9.215a.8.8 0 0 0-.24-.591.8.8 0 0 0-.594-.24h-5a.81.81 0 0 0-.593.24.8.8 0 0 0-.24.59v4.98q0 .354.24.592.24.24.593.239h5a.8.8 0 0 0 .594-.238q.24-.24.24-.592zm-4.908 4.05h3.148v-3.121H2.26zm6.667-2.428a4 4 0 0 0-.093.868q0 .452.093.865.225 1.009 1.002 1.78 1.093 1.09 2.655 1.09 1.566.004 2.657-1.09 1.091-1.091 1.093-2.645t-1.093-2.646-2.657-1.09q-1.56.003-2.655 1.09a3.57 3.57 0 0 0-1.002 1.778m.517-6.287L8 3.266 6.557 5.61zm3.14 9.13h.004c.583.002 1.017-.18 1.408-.572.396-.397.578-.832.58-1.406 0-.57-.18-1.001-.576-1.396-.398-.396-.835-.578-1.414-.577-.582 0-1.02.184-1.417.578-.395.392-.576.823-.575 1.397 0 .575.182 1.008.576 1.4.395.393.832.577 1.414.577'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconStockIncrease = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
  >
    <g clipPath='url(#clip0_1842_36448)'>
      <path
        fill='#10BE56'
        d='m2.833 15-1.167-1.167 6.167-6.208 3.333 3.333 4.333-4.291h-2.166V5h5v5h-1.667V7.833l-5.5 5.5L7.833 10z'
      ></path>
    </g>
    <defs>
      <clipPath id='clip0_1842_36448'>
        <path fill='#fff' d='M0 0h20v20H0z'></path>
      </clipPath>
    </defs>
  </svg>
)

export const IconHeart = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='18'
    fill='none'
    viewBox='0 0 20 18'
  >
    <path
      fill='#A6A6A6'
      fillRule='evenodd'
      d='M6.458 2.592a4 4 0 0 0-2.825 6.84l.003.004 5.657 5.657a1 1 0 0 0 1.414 0l5.652-5.653a4 4 0 0 0-5.66-5.652 1 1 0 0 1-1.409-.012 4 4 0 0 0-2.832-1.184M4.159 1.046a6 6 0 0 1 5.852.718 6 6 0 0 1 7.776 9.076l-.009.01-5.657 5.657a3 3 0 0 1-4.242 0l-5.654-5.654-.001-.001a6 6 0 0 1 1.935-9.806'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconRate = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='16'
    height='16'
    fill='none'
    viewBox='0 0 16 16'
  >
    <path
      fill='#000'
      d='M13.833.5H2.167C1.25.5.5 1.25.5 2.167v11.666c0 .917.75 1.667 1.667 1.667h11.666c.917 0 1.667-.75 1.667-1.667V2.167C15.5 1.25 14.75.5 13.833.5m-5 3.417L9.75 3l1.167 1.167L12.083 3l.917.917-1.167 1.166L13 6.25l-.917.917L10.917 6 9.75 7.167l-.917-.917L10 5.083zm-5.666.5h4.166v1.25H3.167zm4.416 6.916H5.917V13h-1.25v-1.667H3v-1.25h1.667V8.417h1.25v1.666h1.666zm5.417 1H8.833v-1.25H13zm0-2H8.833v-1.25H13z'
    ></path>
  </svg>
)

export const IconAddToCart = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
  >
    <path
      fill='#fff'
      d='M16 16a2 2 0 1 1-2 2c0-1.11.89-2 2-2M0 0h3.27l.94 2H19a1 1 0 0 1 1 1c0 .17-.05.34-.12.5L16.3 9.97c-.34.61-1 1.03-1.75 1.03H7.1l-.9 1.63-.03.12a.25.25 0 0 0 .25.25H18v2H6a2 2 0 0 1-2-2c0-.35.09-.68.24-.96L5.6 9.59 2 2H0zm6 16a2 2 0 1 1-2 2c0-1.11.89-2 2-2m9-7 2.78-5H5.14L7.5 9z'
    ></path>
    <path fill='#fff' d='m15 9 2.78-5H5.14L7.5 9z'></path>
  </svg>
)

export const IconCalenderEdit = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='25'
    height='24'
    fill='none'
    viewBox='0 0 25 24'
    className={cn(className)}
  >
    <path
      fill='currentColor'
      fillRule='evenodd'
      d='M5.5 19h5.07a7 7 0 0 0 0 2H5.5a2 2 0 0 1-2-2V5c0-1.1.9-2 2-2h1V1h2v2h8V1h2v2h1a2 2 0 0 1 2 2v5.07a7 7 0 0 0-2 0V9h-14zm15.7-3.65 1-1c.19-.2.19-.52 0-.72l-1.28-1.28a.54.54 0 0 0-.77 0l-1 1zm-2.63-1.47-6.07 6.06V22h2.06l6.06-6.12z'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconSparkle = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='16'
    height='16'
    fill='none'
    viewBox='0 0 16 16'
  >
    <path
      fill='currentColor'
      fillRule='evenodd'
      d='M1.334 4v1.333A2.667 2.667 0 0 1 4.001 8h1.333a2.667 2.667 0 0 1 2.667-2.667V4a2.667 2.667 0 0 1-2.667-2.667H4.001A2.667 2.667 0 0 1 1.334 4m4 5.333v1.334a4 4 0 0 1 4 4h1.333a4 4 0 0 1 4-4V9.333a4 4 0 0 1-4-4H9.334a4 4 0 0 1-4 4'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconMinus = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='16'
    height='16'
    fill='none'
    viewBox='0 0 16 16'
  >
    <path fill='currentColor' d='M12.667 8.665H3.334V7.332h9.333z'></path>
  </svg>
)

export const IconPlus = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='16'
    height='16'
    fill='none'
    viewBox='0 0 16 16'
  >
    <path
      fill='currentColor'
      d='M12.667 8.665h-4v4H7.334v-4h-4V7.332h4v-4h1.333v4h4z'
    ></path>
  </svg>
)

export const IconTick = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='32'
    height='32'
    fill='none'
    viewBox='0 0 32 32'
  >
    <g clipPath='url(#clip0_4354_30487)'>
      <path
        fill='#3ECB77'
        fillRule='evenodd'
        d='M16 29.333c7.364 0 13.333-5.97 13.333-13.333S23.363 2.667 16 2.667 2.667 8.637 2.667 16 8.636 29.333 16 29.333m-1.837-7.483 9.418-8.88-1.83-1.94-7.55 7.12-3.934-3.863L8.4 16.189z'
        clipRule='evenodd'
      ></path>
    </g>
    <defs>
      <clipPath id='clip0_4354_30487'>
        <path fill='#fff' d='M0 0h32v32H0z'></path>
      </clipPath>
    </defs>
  </svg>
)

export const IconCouponDiscount = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='16'
    height='16'
    fill='none'
    viewBox='0 0 16 16'
    className={cn("fill-[#4A4E5A]", className)}
  >
    <g id='Offer - Outlined'>
      <g id='Vector' fill=''>
        <path
          fillRule='evenodd'
          d='m12.6 8 .845-1.46-1.46-.843V4.015h-1.681l-.844-1.46L8 3.4l-1.46-.845-.845 1.46H4.014v1.681l-1.46.844L3.4 8l-.845 1.46 1.46.844v1.681h1.681l.844 1.46L8 12.601l1.46.844.844-1.46h1.682v-1.681l1.46-.844zm-2.417 6.325a.667.667 0 0 1-.91.244l-1.274-.736-1.272.736a.67.67 0 0 1-.911-.244l-.736-1.273H3.613a.667.667 0 0 1-.666-.667V10.92l-1.274-.736a.67.67 0 0 1-.242-.911L2.167 8 1.43 6.728a.667.667 0 0 1 .243-.911l1.273-.736V3.615a.667.667 0 0 1 .667-.667H5.08l.736-1.273a.667.667 0 0 1 .91-.244L8 2.167l1.273-.736a.667.667 0 0 1 .91.244l.736 1.273h1.466a.667.667 0 0 1 .667.667V5.08l1.273.736a.667.667 0 0 1 .244.911L13.833 8l.735 1.272a.666.666 0 0 1-.243.911l-1.274.736v1.466a.667.667 0 0 1-.666.667h-1.466z'
          clipRule='evenodd'
        ></path>
        <path d='M6 5.333a1 1 0 1 1 0 2 1 1 0 0 1 0-2M6.533 11.052l-1.067-.8 4-5.333 1.067.8zM10 10.667a1.002 1.002 0 0 1-.382-1.925A1 1 0 1 1 10 10.667'></path>
      </g>
    </g>
  </svg>
)

export const IconWalletCash = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='16'
    height='16'
    fill='none'
    viewBox='0 0 16 16'
  >
    <g id='Currency Note - Outlined'>
      <path
        id='Union'
        fill='#7D808D'
        fillRule='evenodd'
        d='M11.52 2.67q.31-.009.621.005c.944.043 1.876.23 2.764.557l.428.158v9.17l-.868-.31a8.3 8.3 0 0 0-2.384-.483c-1.53-.073-2.651.318-3.863.742l-.018.006c-1.144.4-2.346.818-3.912.818q-.21 0-.43-.01a9.6 9.6 0 0 1-2.758-.556l-.433-.155V3.446l.863.301c.946.331 1.907.499 2.865.483 1.435-.015 2.47-.377 3.585-.768l.023-.008c1.036-.364 2.12-.745 3.517-.784M8.408 4.698c1.008-.355 1.95-.682 3.147-.72q.257-.**************.01.474.038c.1.32.31.597.592.778.263.17.573.245.88.219v4.409a1.4 1.4 0 0 0-.92.246 1.43 1.43 0 0 0-.57.816 9 9 0 0 0-.399-.027c-1.783-.08-3.1.375-4.351.814-1.214.425-2.341.817-3.871.744a8 8 0 0 1-.48-.04c-.102-.32-.31-.595-.593-.776a1.4 1.4 0 0 0-.878-.219V6.574a1.4 1.4 0 0 0 .926-.25 1.43 1.43 0 0 0 .567-.824q.473.047.948.041c1.647-.017 2.847-.438 3.99-.84zM9.6 6.208a.124.124 0 0 1-.122.125h-.745q.091.153.145.334h.6c.067 0 .122.056.122.125v.416a.124.124 0 0 1-.122.125h-.54c-.073.777-.63 1.308-1.412 1.333l1.54 1.45c.**************-.082.217H8.14a.12.12 0 0 1-.083-.033L6.373 8.713a.13.13 0 0 1-.04-.092V8.07c0-.069.055-.125.123-.125h.857c.427 0 .7-.235.764-.61H6.456a.124.124 0 0 1-.123-.126v-.416c0-.07.055-.125.123-.125h1.493c-.13-.18-.344-.284-.623-.284h-.87a.124.124 0 0 1-.123-.125v-.466c0-.07.055-.125.123-.125h3.022c.067 0 .122.056.122.125z'
        clipRule='evenodd'
      ></path>
    </g>
  </svg>
)

export const IconArrowRight = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='25'
    height='24'
    fill='none'
    viewBox='0 0 25 24'
  >
    <g id='Arrow - Right - Outlined'>
      <path
        id='Vector'
        fill='currentColor'
        d='M4.5 11v2h12L11 18.5l1.42 1.42L20.34 12l-7.92-7.92L11 5.5l5.5 5.5z'
      ></path>
    </g>
  </svg>
)

export const IconEdit = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
  >
    <g id='Edit - Filled'>
      <path
        id='Union'
        fill='#030D31'
        fillRule='evenodd'
        d='m18.323 4.177-2.5-2.5-1.905 1.906 2.5 2.5zm-9.156 9.156h-2.5v-2.5l6.072-6.072 2.5 2.5zm-2.369 2.5h9.035v-5.556L17.5 8.61v7.223a1.666 1.666 0 0 1-1.667 1.667H4.167c-.92 0-1.667-.747-1.667-1.667V4.167c0-.92.748-1.667 1.667-1.667h7.372L9.873 4.167H4.167v11.666h2.482q.*************.***************.017 0 .033-.004t.033-.005'
        clipRule='evenodd'
      ></path>
    </g>
  </svg>
)

export const IconRefresh = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='16'
    height='16'
    fill='none'
    viewBox='0 0 16 16'
  >
    <g clipPath='url(#clip0_4596_77581)'>
      <path
        fill='#1945E8'
        d='M4.72 11.09c-1.656-1.779-1.627-4.568.093-6.3A4.44 4.44 0 0 1 7.31 3.52L7.26 2a5.93 5.93 0 0 0-3.51 1.72c-2.305 2.32-2.333 6.065-.09 8.438L2.385 13.44l4.032.22-.01-4.269zm4.863-8.75.01 4.269 1.689-1.699c1.655 1.781 1.626 4.57-.094 6.3a4.44 4.44 0 0 1-2.498 1.271L8.74 14a5.94 5.94 0 0 0 3.511-1.72c2.304-2.32 2.332-6.066.09-8.437l1.274-1.283z'
      ></path>
    </g>
    <defs>
      <clipPath id='clip0_4596_77581'>
        <path fill='#fff' d='M0 0h16v16H0z'></path>
      </clipPath>
    </defs>
  </svg>
)
