import * as React from "react"

const OfferIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='40'
    height='40'
    fill='none'
    viewBox='0 0 40 40'
    className={className}
  >
    <path
      fill='#D4DF5B'
      fillRule='evenodd'
      d='m29.163 14.5 2.692-4.652-4.652-2.69V1.798h-5.36l-2.688-4.652-4.656 2.69-4.655-2.692-2.69 4.654h-5.36v5.36l-4.653 2.689L-.166 14.5l-2.694 4.655 4.652 2.688v5.36h5.362l2.69 4.653 4.652-2.692 4.657 2.692 2.687-4.653h5.36v-5.36l4.654-2.69zM21.46 34.661a2.125 2.125 0 0 1-2.903.776l-4.059-2.346-4.054 2.346c-.327.189-.695.286-1.065.286a2.15 2.15 0 0 1-1.84-1.062l-2.346-4.059H.517a2.125 2.125 0 0 1-2.125-2.125v-4.673l-4.059-2.346a2.133 2.133 0 0 1-.773-2.904l2.346-4.055-2.346-4.055a2.125 2.125 0 0 1 .775-2.904l4.059-2.346V.522A2.125 2.125 0 0 1 .519-1.603h4.673l2.346-4.06a2.125 2.125 0 0 1 2.903-.777l4.058 2.348 4.06-2.346a2.124 2.124 0 0 1 2.902.778l2.344 4.057h4.673A2.125 2.125 0 0 1 30.603.522v4.675l4.058 2.346a2.125 2.125 0 0 1 .776 2.902L33.091 14.5l2.344 4.055a2.124 2.124 0 0 1-.776 2.904L30.6 23.805v4.673a2.125 2.125 0 0 1-2.125 2.125h-4.672z'
      clipRule='evenodd'
    ></path>
    <path
      fill='#D4DF5B'
      d='M8.126 5.998a3.189 3.189 0 1 1-.002 6.377 3.189 3.189 0 0 1 .002-6.377M9.822 24.228l-3.4-2.548 12.75-17 3.4 2.548zM20.874 23a3.19 3.19 0 1 1 .003-6.378A3.19 3.19 0 0 1 20.874 23'
    ></path>
  </svg>
)

export default OfferIcon
