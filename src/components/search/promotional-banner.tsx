import { getOfferCoupon } from "@/actions/coupon"
import { cn } from "@/lib/utils"
import { useQuery } from "@tanstack/react-query"
import { JSX, memo } from "react"
import PartyPopperIcon from "../Icons/party-popper-icon"
import { Alert, AlertDescription } from "../ui/alert"

const PromotionalBanner = ({
  showColor = true,
}: {
  showColor?: boolean
}): JSX.Element => {
  const { data: offerCoupon } = useQuery({
    queryKey: ["offer_coupon"],
    queryFn: getOfferCoupon,
    staleTime: 1000 * 60 * 10, // 10 minutes
  })

  return (
    <Alert
      className={cn(
        "rounded-3xl border border-gray-100 p-0",
        !showColor && "border-0 bg-transparent",
      )}
    >
      <AlertDescription className='text-green-900'>
        <div
          className={cn(
            "flex h-24 items-center gap-4 overflow-hidden rounded-3xl p-3",
            showColor && "bg-gradient-to-r from-[#EDF4FF] to-[#F7FFEA]",
          )}
        >
          <PartyPopperIcon />
          <div className='flex flex-col gap-2 text-start'>
            <div>
              <span className='text-base font-bold text-pink-500'>
                {offerCoupon?.coupon_description?.split("off")[0]}
              </span>
              <span className='text-base font-bold text-zinc-950'>
                {" "}
                {offerCoupon?.coupon_description?.split("off")[1]}
              </span>
            </div>
            <div className='text-start text-sm font-semibold text-zinc-700'>
              Use Coupon - {offerCoupon?.coupon_code}
            </div>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  )
}

export default memo(PromotionalBanner)
