import { Skeleton } from "@/components/ui/skeleton"

export const SearchDropdownSkeleton = () => (
  <div className='flex flex-col gap-4'>
    {[1, 2, 3].map((index) => (
      <div
        key={index}
        className='group flex w-full items-center gap-4 rounded-lg p-2'
      >
        <Skeleton className='h-[46px] w-[46px] rounded-lg' />
        <div className='flex flex-col gap-2'>
          <Skeleton className='h-4 w-32' />
          <Skeleton className='h-3 w-48' />
        </div>
      </div>
    ))}
  </div>
)
