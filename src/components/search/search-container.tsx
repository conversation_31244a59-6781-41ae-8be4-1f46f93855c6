"use client"
import { cn } from "@/lib/utils"
import { useRentalStore } from "@/store/rental-store"
import { SearchContainerProps } from "@/types/search"
import { motion } from "framer-motion"
import { XIcon } from "lucide-react"
import { useCallback, useEffect, useRef, useState } from "react"
import { ArrowRightOutlinedIcon, SearchOutlinedIcon } from "sharepal-icons"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { SearchDropdown } from "./search-dropdown"

export const SearchContainer = ({
  searchResults,
  isLoading,
  isFetching,
  onClearSearch,
  searchText,
  setSearchText,
}: SearchContainerProps) => {
  const { selectedCity } = useRentalStore()
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        closeSearchDropdown()
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleSearchTextChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchText(e.target.value)
      setIsDropdownOpen(true)
      setIsFocused(true)
    },
    [setSearchText],
  )

  const closeSearchDropdown = useCallback(() => {
    setIsDropdownOpen(false)
    setIsFocused(false)
    if (inputRef.current) {
      inputRef.current.blur()
    }
  }, [])

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if ((e.key === "Enter" || e.key === "ArrowRight") && searchText) {
        closeSearchDropdown()
      }
    },
    [searchText, closeSearchDropdown],
  )

  const handleSearchClick = useCallback(() => {
    if (searchText) {
      closeSearchDropdown()
    }
  }, [searchText, closeSearchDropdown])

  return (
    <motion.div
      ref={containerRef}
      className={cn(
        "group relative flex w-full items-center gap-2 rounded-2xl border-2 border-neutral-200 bg-gray-100 px-3 py-1.5 transition-all duration-200",
        isDropdownOpen &&
          isFocused &&
          "rounded-b-none border-b-0 border-primary-500",
      )}
    >
      <SearchOutlinedIcon className='h-5 w-5 min-w-5 text-neutral-600' />
      <Input
        ref={inputRef}
        type='text'
        placeholder='Search for products'
        value={searchText}
        onChange={handleSearchTextChange}
        onKeyDown={handleKeyDown}
        onFocus={() => {
          setIsFocused(true)
          setIsDropdownOpen(true)
        }}
        className='h-8 w-full border-0 bg-transparent p-0 text-base placeholder:text-neutral-400 focus-visible:border-0 focus-visible:ring-0 focus-visible:ring-offset-0 md:h-9 md:text-b2'
      />
      <div className='flex items-center gap-1'>
        {searchText && (
          <Button
            size='icon'
            variant='ghost'
            onClick={onClearSearch}
            className='h-8 w-8 rounded-full p-1.5 hover:bg-neutral-100'
          >
            <XIcon className='h-4 w-4 text-neutral-500' />
          </Button>
        )}
        <Button
          size='icon'
          variant='ghost'
          className='h-8 w-8 rounded-full hover:bg-neutral-100 md:h-9 md:w-9'
          disabled={!searchText || isLoading}
          onClick={handleSearchClick}
        >
          {isFetching ? (
            <div className='h-5 w-5 animate-spin rounded-full border-2 border-neutral-200 border-t-neutral-600' />
          ) : (
            <ArrowRightOutlinedIcon className='h-5 w-5 text-neutral-600' />
          )}
        </Button>
      </div>

      {isDropdownOpen && isFocused && (
        <SearchDropdown
          data={searchResults}
          setSearchText={setSearchText}
          selectedCity={selectedCity}
          isLoading={isLoading}
          searchText={searchText}
        />
      )}
    </motion.div>
  )
}
