"use client"
import { filterAndSortRentalItems } from "@/functions/products"
import { useRentalStore } from "@/store/rental-store"
import { RentalItem } from "@/types"
import { AnimatePresence, motion } from "framer-motion"
import SearchProductCard from "../cards/search-product-card"
import { SectionHeader } from "./section-header"

interface SearchResultsProps {
  results?: {
    items: RentalItem[]
  }
}

export const SearchResults = ({ results }: SearchResultsProps) => {
  const { selectedCity } = useRentalStore()

  const filteredResults = results?.items
    ? filterAndSortRentalItems(results.items)
    : []

  if (!filteredResults.length) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className='space-y-4'
      >
        <SectionHeader title='Search Results' />
        <div className='grid grid-cols-2 gap-2 sm:grid-cols-3 md:gap-3'>
          {filteredResults.map((item, index) => (
            <SearchProductCard
              key={item.id || index}
              data={item}
              city={selectedCity.city_name}
              className='w-full'
            />
          ))}
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
