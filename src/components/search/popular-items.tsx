import { fetchTrending } from "@/actions/trending"
import { filterAndSortRentalItems } from "@/functions/products"
import { useRentalStore } from "@/store/rental-store"
import { RentalItem } from "@/types"
import { useQuery } from "@tanstack/react-query"
import { memo, useMemo } from "react"
import SearchProductCard from "../cards/search-product-card"

const PopularItems = () => {
  const { selectedCity } = useRentalStore()
  const city = selectedCity.city_name.toLowerCase()

  const { data: trendings } = useQuery({
    queryKey: ["trendings", city],
    queryFn: () => fetchTrending(city),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: false,
    enabled: Boolean(city),
  })

  const filteredItems = useMemo(() => {
    if (!trendings?.items) return []
    return filterAndSortRentalItems(trendings.items)
  }, [trendings?.items])

  if (!filteredItems.length) return null

  return (
    <div className='custom-scrollbar-black flex-1 space-y-4 overflow-y-auto bg-neutral-150 p-4'>
      <div className='space-y-2'>
        <div className='flex'>
          {filteredItems.map((item: RentalItem) => (
            <div
              key={item.id}
              className='mr-5 max-w-56 md:max-w-64 lg:mr-6 xl:mr-7 2xl:mr-8'
            >
              <SearchProductCard
                data={item}
                city={city}
                hideDateAndPrice={false}
                className='w-full'
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Memoize the entire component
export default memo(PopularItems)
