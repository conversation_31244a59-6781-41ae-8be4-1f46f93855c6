import { Separator } from "../ui/separator"
import { Typography } from "../ui/typography"

interface SectionHeaderProps {
  title: string
}

export const SectionHeader = ({ title }: SectionHeaderProps) => (
  <div className='flex w-full items-center justify-start gap-2 overflow-hidden'>
    <Typography as='p' className='min-w-max text-o3 text-neutral-300'>
      {title}
    </Typography>
    <Separator className='h-[2px] w-full rounded-full md:max-w-[400px]' />
  </div>
)
