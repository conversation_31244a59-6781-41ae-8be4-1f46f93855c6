import { filterAndSortRentalItems } from "@/functions/products"
import { cn } from "@/lib/utils"
import { SearchDropdownProps } from "@/types/search"
import { motion } from "framer-motion"
import { SearchDropdownSkeleton } from "./search-dropdown-skeleton"
import { SearchResultItem } from "./search-result-item"
import { SearchSuggestions } from "./search-suggestions"

// const MAX_RESULTS = 3

const ResultList = ({
  data,
  selectedCity,
}: {
  data: SearchDropdownProps["data"]
  selectedCity: SearchDropdownProps["selectedCity"]
}) => {
  const items = data?.items

  if (!items?.length) {
    return null
  }

  const sortedItems = filterAndSortRentalItems(items)

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className='flex flex-col space-y-2'
    >
      {sortedItems.map((item, index) => (
        <SearchResultItem
          key={item.id || index}
          item={item}
          selectedCity={selectedCity}
        />
      ))}
    </motion.div>
  )
}

// const ViewAllButton = ({ itemCount }: { itemCount: number }) => (
//   <div className='sticky bottom-0 flex items-center justify-center border-t border-neutral-200 bg-gray-100 p-2'>
//     <button
//       onClick={() => {
//         /* Implement view all logic */
//       }}
//       className='text-sm font-medium text-primary-600 hover:text-primary-700'
//     >
//       View all {itemCount} results
//     </button>
//   </div>
// )

export const SearchDropdown = ({
  data,
  setSearchText,
  selectedCity,
  isLoading,
}: SearchDropdownProps) => (
  <motion.div
    className={cn(
      "ring-t-0 absolute left-0 right-0 top-12 z-[100] hidden w-full overflow-hidden rounded-b-xl bg-gray-100 ring-2 ring-primary-500 group-focus-within:block",
    )}
    initial={{ opacity: 0, y: -10 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -10 }}
  >
    <div className='max-h-[400px] overflow-y-auto p-3 scrollbar-thin scrollbar-track-transparent scrollbar-thumb-neutral-300 md:p-4'>
      {isLoading ? (
        <SearchDropdownSkeleton />
      ) : data?.items?.length ? (
        <ResultList data={data} selectedCity={selectedCity} />
      ) : (
        <SearchSuggestions setSearchText={setSearchText} />
      )}
    </div>
    {/* {data && data?.items?.length > MAX_RESULTS && (
      <ViewAllButton itemCount={data.items.length} />
    )} */}
  </motion.div>
)
