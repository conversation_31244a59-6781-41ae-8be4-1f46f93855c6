import { generateLinkPath } from "@/functions/generate-link-path"
import { getImage } from "@/functions/small-functions"
import SpImage from "@/shared/SpImage/sp-image"
import { SearchResultItemProps } from "@/types/search"
import Link from "next/link"
import { Typography } from "../ui/typography"

export const SearchResultItem = ({
  item,
  selectedCity,
}: SearchResultItemProps) => (
  <Link
    href={generateLinkPath(
      item.ri_name,
      selectedCity.city_name.toLowerCase(),
      item.category_short_name,
      item.sc_name,
    )}
    className='group flex w-full items-center gap-4 rounded-lg bg-neutral-100 p-3 transition-colors hover:bg-neutral-150'
  >
    <SpImage
      src={getImage(item.ri_image)}
      alt={item.ri_image_alt_text}
      className='h-10 w-10 object-contain'
      width={40}
      height={40}
      containerClassName='p-2 bg-neutral-200 rounded-md'
    />
    <div className='flex-grow'>
      <Typography
        as='h4'
        className='mb-1 text-sh4 font-medium text-primary-700 group-hover:text-primary-600'
      >
        {item.ri_short_name}
      </Typography>
      <Typography as='p' className='text-b6 text-neutral-500'>
        {item.ri_name}
      </Typography>
    </div>
  </Link>
)
