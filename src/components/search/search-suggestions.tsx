"use client"
import { useCallback, useEffect, useState } from "react"
import { SearchOutlinedIcon } from "sharepal-icons"
import { Typography } from "../ui/typography"

interface SearchSuggestionsProps {
  setSearchText: (text: string) => void
}

const MAX_RECENT_SEARCHES = 5
const LOCAL_STORAGE_KEY = "recentSearches"

export const SearchSuggestions = ({
  setSearchText,
}: SearchSuggestionsProps) => {
  const popularSearches = ["Trekking Gear", "Camping", "Photography", "Gaming"]
  const [recentSearches, setRecentSearches] = useState<string[]>([])

  // Load recent searches from localStorage on component mount
  useEffect(() => {
    const storedSearches = localStorage.getItem(LOCAL_STORAGE_KEY)
    if (storedSearches) {
      setRecentSearches(JSON.parse(storedSearches))
    }
  }, [])

  // Function to add a search term to recent searches
  const addRecentSearch = useCallback(
    (term: string) => {
      setSearchText(term) // Update the search input immediately

      setRecentSearches((prevSearches) => {
        const newSearches = [term, ...prevSearches.filter((s) => s !== term)]

        // Trim the array if it exceeds the maximum number of recent searches
        const trimmedSearches = newSearches.slice(0, MAX_RECENT_SEARCHES)

        // Update localStorage with the new recent searches
        localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(trimmedSearches))

        return trimmedSearches
      })
    },
    [setSearchText],
  )

  return (
    <div className='flex flex-col gap-6 p-4'>
      {/* Popular Searches */}
      <div className='space-y-3'>
        <Typography as='h4' className='text-lg font-semibold text-neutral-700'>
          Popular Searches
        </Typography>
        <div className='flex flex-wrap gap-2'>
          {popularSearches.map((term) => (
            <button
              key={term}
              onClick={() => addRecentSearch(term)}
              className='rounded-full bg-neutral-100 px-4 py-2 text-sm text-neutral-700 transition-colors hover:bg-primary-100 hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500'
            >
              {term}
            </button>
          ))}
        </div>
      </div>

      {/* Recent Searches */}
      {recentSearches.length > 0 && (
        <div className='space-y-3'>
          <Typography
            as='h4'
            className='text-lg font-semibold text-neutral-700'
          >
            Recent Searches
          </Typography>
          <div className='flex flex-col gap-2'>
            {recentSearches.map((term) => (
              <button
                key={term}
                onClick={() => addRecentSearch(term)}
                className='flex items-center gap-3 rounded-md p-3 text-sm text-neutral-700 transition-colors hover:bg-neutral-100 hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500'
              >
                <SearchOutlinedIcon className='h-5 w-5 text-neutral-500' />
                <span>{term}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
