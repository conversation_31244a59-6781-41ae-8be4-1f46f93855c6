/* eslint-disable @typescript-eslint/no-explicit-any */

import type React from "react"
import Link from "next/link"
import { capitalizeFirstLetter } from "@/functions/small-functions"
import { SeoDetails } from "@/types/super-category"

interface CommonPageDescriptionProps {
  seoDesc: SeoDetails | null
  city: string
}

const CategorySeoContent: React.FC<CommonPageDescriptionProps> = ({
  seoDesc,
  city,
}) => {
  // Early return if seoDesc is null
  if (!seoDesc) {
    return (
      <div className='bg-light-color flex items-center justify-center p-5'>
        <div className='flex w-full flex-col gap-5'>
          <p className='text-justify text-sm font-light'>
            No SEO description available for this page.
          </p>
        </div>
      </div>
    )
  }

  // Safely extract values with fallbacks
  const pageTitle = seoDesc?.url
    ? capitalizeFirstLetter(seoDesc.url.split("-").join(" "))
    : ""
  const firstBlock = seoDesc?.first_block || ""
  const secondBlock = seoDesc?.second_block || ""
  const thirdBlock = seoDesc?.third_block || ""
  const products = seoDesc?.products || []

  return (
    <div className='flex w-full flex-col gap-5 text-neutral-300'>
      {/* Page Title */}
      <h2 className='text-lg font-semibold'>
        {pageTitle} in {capitalizeFirstLetter(city)}
      </h2>

      {/* First Block */}
      <p className='text-justify text-sm font-light'>{firstBlock}</p>

      {/* Second Block */}
      <p className='text-justify text-sm font-light'>
        <b>
          Rent {pageTitle.slice(0, -8)} in {capitalizeFirstLetter(city)}{" "}
        </b>
        {secondBlock} {city}.
      </p>

      {/* Products Section */}
      {products.map((product: any) => (
        <p key={product.id} className='text-justify text-sm font-light'>
          <Link
            className='font-medium text-gray-150 underline'
            href={`/${city}/${product.url}`}
          >
            {product.product_name} on rent in {city}:{" "}
          </Link>
          {product.desc}
        </p>
      ))}

      {/* Why Rent from SharePal? */}
      <p className='text-justify text-sm font-light'>
        Renting is the latest trend, and SharePal is one of the leading
        companies that offer rental services across India in various locations.
      </p>

      {/* Why Rent from SharePal? */}
      <p className='text-justify text-sm font-light'>
        <b>Why rent from SharePal?</b>
        SharePal provides top-notch branded trekking gear rentals at the lowest
        cost. Our renting process is easy and customer-friendly and doesn&apos;t
        take more than 5 minutes to place an order.
      </p>

      {/* Hygiene & Cleanliness */}
      <p className='text-justify text-sm font-light'>
        <b>Hygiene & Cleanliness: </b>
        Maintaining hygiene & cleanliness is our top priority. All the products
        you rent are washed and disinfected after every use. Our 12 point strict
        quality check process ensures that you will receive excellent quality
        gear.
      </p>

      {/* Wide Range of Products */}
      <p className='text-justify text-sm font-light'>
        <b>Wide range of Products:</b>
        {thirdBlock}
      </p>

      {/* Best Pricing */}
      <p className='text-justify text-sm font-light'>
        <b>Best Pricing: </b>
        Our pricing is easy to understand & transparent. You pay what you see!
        All rentals are inclusive of GST. Rent per day x No. of Days. Our
        deposit rates are also the lowest in the market. We also offer weekly &
        monthly rentals to suit your long-term rental needs. The longer you
        rent, the less you pay.
      </p>

      {/* Timely Doorstep Delivery & Pick-up */}
      <p className='text-justify text-sm font-light'>
        <b>Timely doorstep delivery & pick-up: </b>
        We will deliver your order as per your chosen delivery date between 3 pm
        to 7 pm. The delivery date is not chargeable.
        <br />
        Similarly, we will pick up your order the next day after your rental
        period ends. The order is picked back up between 10 am to 1 pm. The
        pick-up date is not chargeable.
      </p>

      {/* Customer-Friendly Support */}
      <p className='text-justify text-sm font-light'>
        <b>Customer-friendly support:</b>
        Be it product selection or timely status updates. Our super-friendly
        customer support team will ensure a smooth experience for you.
        <br />
        SharePal is on a mission to make your lifestyle affordable, safer, and
        sustainable. By renting, you are not just saving your precious money but
        also saving the planet.
      </p>
    </div>
  )
}

export default CategorySeoContent
