import type React from "react"

interface SeoContentProps {
  seoContent: {
    content: string
  }
}

const SeoContent: React.FC<SeoContentProps> = ({ seoContent }) => (
  <div className='flex w-full flex-col gap-5'>
    <div
      className='prose prose-sm max-w-none [&_a]:text-sh4 [&_a]:font-semibold [&_a]:text-primary-100 [&_a]:underline [&_b]:font-medium [&_h2]:my-1 [&_h2]:text-lg [&_h2]:font-medium [&_h2]:text-gray-100 [&_h3]:my-2 [&_h3]:text-base [&_h3]:font-medium [&_h4]:my-1 [&_h4]:text-sm [&_h4]:font-medium [&_li]:mt-1 [&_li]:whitespace-pre-line [&_li]:text-start [&_li]:text-sm [&_li]:font-light [&_ol]:list-inside [&_p]:mt-1 [&_p]:whitespace-pre-line [&_p]:text-start [&_p]:text-sm [&_p]:font-light [&_strong]:text-b4 [&_strong]:font-bold [&_strong]:text-gray-150 [&_ul]:list-inside'
      dangerouslySetInnerHTML={{ __html: seoContent.content }}
    />
  </div>
)

export default SeoContent
