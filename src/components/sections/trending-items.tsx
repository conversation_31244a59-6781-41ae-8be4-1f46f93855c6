"use client"

import { fetchTrending } from "@/actions/trending"
import { filterAndSortRentalItems } from "@/functions/products"
import { RentalItem, RentalItemProduct } from "@/types"
import { useQuery } from "@tanstack/react-query"
import { useEffect, useRef, useState } from "react"
import { TrendingSlider } from "../sliders/trending-slider"

interface TrendingItemsSectionProps {
  city: string
}

interface TrendingResponse {
  curPage: number
  items: RentalItemProduct[]
  itemsReceived: number
  nextPage: number | null
}

export default function TrendingItemsSection({
  city,
}: TrendingItemsSectionProps) {
  const loadingRef = useRef(false)
  const [nextPage, setNextPage] = useState<number | null>(null)
  const [sortedItems, setSortedItems] = useState<RentalItem[]>([])

  const { data, status, refetch, isRefetching } = useQuery({
    queryKey: ["trending-items", city],
    queryFn: async () => {
      const response = await fetchTrending(city, "rent", nextPage ?? 1)
      return response as TrendingResponse
    },
    staleTime: 1000 * 60 * 5, // Cache for 5 minutes
    refetchOnWindowFocus: false,
    retry: false,
  })

  const handleFetch = async () => {
    if (!loadingRef.current && data?.nextPage) {
      loadingRef.current = true
      setNextPage(data.nextPage)
      await refetch()
      loadingRef.current = false
    }
  }

  useEffect(() => {
    if (data?.items) {
      if (nextPage === 1) {
        setSortedItems(data.items)
      } else {
        // setSortedItems((prevItems) => [...prevItems, ...data.items])
        setSortedItems((prevItems) => {
          const newProducts = data.items.filter(
            (newItem) =>
              !prevItems.some((existingItem) => existingItem.id === newItem.id),
          )
          return filterAndSortRentalItems([...prevItems, ...newProducts])
        })
      }
    }
  }, [data?.items, nextPage])

  if (status === "error") {
    return null
  }

  return (
    <section className='relative mx-auto mt-12 w-full items-center justify-center bg-neutral-150 md:mt-24'>
      <TrendingSlider
        handleFetch={handleFetch}
        data={sortedItems}
        city={city}
        hideDateAndPrice={true}
        isLoading={status === "pending"}
        isRefetching={isRefetching}
      />
    </section>
  )
}
