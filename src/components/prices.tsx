import React, { FC } from "react"

export interface PricesProps {
  className?: string
  price?: number
  contentClass?: string
}

const Prices: FC<PricesProps> = ({
  className = "",
  price = 33,
  contentClass = "py-1 px-2 md:py-1.5 md:px-2.5 text-sm font-medium",
}) => (
  <div className={`${className}`}>
    <div
      className={`flex items-center rounded-radius border-2 border-green-500 ${contentClass}`}
    >
      {/* <span className="text-green-500 !leading-none">${String(price)}</span> */}
      <span className='!leading-none text-green-500'>
        &#8377;{String(price)}
      </span>
    </div>
  </div>
)

export default Prices
