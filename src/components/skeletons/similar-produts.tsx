"use client"

import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"

export function SimilarProductsSkeleton() {
  return (
    <div className='w-full p-6'>
      <div className='mb-6 flex items-center justify-between'>
        <div className='space-y-2'>
          <Skeleton className='h-8 w-64' />
          <Skeleton className='h-4 w-24' />
        </div>
        <div className='flex gap-2'>
          <Skeleton className='h-8 w-8 rounded-full' />
          <Skeleton className='h-8 w-8 rounded-full' />
        </div>
      </div>

      <div className='grid grid-cols-1 gap-4 md:grid-cols-4'>
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className='p-4'>
              <Skeleton className='mb-4 aspect-square w-full rounded-lg' />
              <div className='space-y-2'>
                <Skeleton className='h-4 w-3/4' />
                <Skeleton className='h-3 w-1/2' />
                <div className='mt-4 flex items-center gap-2'>
                  <Skeleton className='h-4 w-24' />
                  <Skeleton className='h-4 w-16' />
                </div>
                <div className='flex items-center gap-1'>
                  {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className='h-4 w-4' />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
