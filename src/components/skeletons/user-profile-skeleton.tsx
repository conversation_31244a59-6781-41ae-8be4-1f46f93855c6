import { Skeleton } from "@/components/ui/skeleton"

export default function UserProfileSkeleton() {
  return (
    <div className='flex h-full flex-col'>
      {/* Header Section */}
      <div className='bg-gray-100 p-4 pt-10 md:p-6 md:pt-16'>
        <div className='space-y-4'>
          {/* User greeting and logout button */}
          <div className='flex items-center justify-between'>
            <Skeleton className='h-8 w-32 md:h-10 md:w-40' />
            <Skeleton className='h-9 w-24 rounded-lg' />
          </div>

          {/* Verification Banner */}
          <div className='mt-4 flex h-[88px] items-center gap-3 rounded-3xl bg-gray-50/50 p-4'>
            <Skeleton className='h-12 w-12 shrink-0 rounded-full' />
            <div className='flex-1 space-y-2'>
              <Skeleton className='h-5 w-3/4' />
              <Skeleton className='h-4 w-full' />
            </div>
            <Skeleton className='h-8 w-8 shrink-0 rounded-full' />
          </div>
        </div>
      </div>

      {/* Menu Items */}
      <div className='flex-1 space-y-4 p-4'>
        <div className='space-y-2'>
          {/* My Account */}
          <div className='flex items-center justify-between rounded-xl border p-4'>
            <div className='flex items-center gap-3'>
              <Skeleton className='h-5 w-5 rounded-md' />
              <Skeleton className='h-5 w-24' />
            </div>
            <Skeleton className='h-4 w-4' />
          </div>

          {/* My Orders with Badge */}
          <div className='flex items-center justify-between rounded-xl border p-4'>
            <div className='flex items-center gap-3'>
              <Skeleton className='h-5 w-5 rounded-md' />
              <Skeleton className='h-5 w-24' />
              <Skeleton className='h-6 w-28 rounded-full' />
            </div>
            <Skeleton className='h-4 w-4' />
          </div>

          {/* My Wishlist */}
          <div className='flex items-center justify-between rounded-xl border p-4'>
            <div className='flex items-center gap-3'>
              <Skeleton className='h-5 w-5 rounded-md' />
              <Skeleton className='h-5 w-24' />
            </div>
            <Skeleton className='h-4 w-4' />
          </div>

          {/* Pal Wallet with Amount */}
          <div className='flex items-center justify-between rounded-xl border p-4'>
            <div className='flex items-center gap-3'>
              <Skeleton className='h-5 w-5 rounded-md' />
              <Skeleton className='h-5 w-24' />
              <Skeleton className='h-6 w-16 rounded-full' />
            </div>
            <Skeleton className='h-4 w-4' />
          </div>

          {/* Referral Program with Badge */}
          <div className='flex items-center justify-between rounded-xl border p-4'>
            <div className='flex items-center gap-3'>
              <Skeleton className='h-5 w-5 rounded-md' />
              <Skeleton className='h-5 w-24' />
              <Skeleton className='h-6 w-20 rounded-full' />
            </div>
            <Skeleton className='h-4 w-4' />
          </div>

          {/* Help & Support */}
          <div className='flex items-center justify-between rounded-xl border p-4'>
            <div className='flex items-center gap-3'>
              <Skeleton className='h-5 w-5 rounded-md' />
              <Skeleton className='h-5 w-24' />
            </div>
            <Skeleton className='h-4 w-4' />
          </div>
        </div>

        <div className='h-px bg-neutral-200' />

        {/* Business Section Cards */}
        <div className='space-y-2'>
          {/* SharePal for Creators */}
          <div className='flex items-center justify-between rounded-xl bg-gradient-to-r from-gray-50 to-neutral-100 p-4'>
            <div className='flex items-center gap-3'>
              <Skeleton className='h-10 w-10 rounded-full' />
              <div className='space-y-1'>
                <Skeleton className='h-5 w-40' />
                <Skeleton className='h-4 w-32' />
              </div>
            </div>
            <Skeleton className='h-4 w-4' />
          </div>

          {/* SharePal for Business */}
          <div className='flex items-center justify-between rounded-xl bg-gradient-to-r from-gray-50 to-neutral-100 p-4'>
            <div className='flex items-center gap-3'>
              <Skeleton className='h-10 w-10 rounded-full' />
              <div className='space-y-1'>
                <Skeleton className='h-5 w-40' />
                <Skeleton className='h-4 w-32' />
              </div>
            </div>
            <Skeleton className='h-4 w-4' />
          </div>
        </div>
      </div>
    </div>
  )
}
