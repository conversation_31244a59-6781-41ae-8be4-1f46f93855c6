import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { ArrowLeft } from "lucide-react"

export function OrderDetailsSkeleton() {
  return (
    <div className='grid gap-6 md:grid-cols-[1fr_380px]'>
      <div className='space-y-6 rounded-3xl bg-gray-100 p-6'>
        {/* Header Skeleton */}
        <div className='space-y-4'>
          <div className='flex items-center gap-2'>
            <ArrowLeft className='h-4 w-full' />
            <Skeleton className='h-6 w-full' />
          </div>
          <div className='flex items-start gap-4'>
            <Skeleton className='h-12 w-full rounded-lg' />
            <div className='space-y-2'>
              <Skeleton className='h-5 w-full' />
              <Skeleton className='h-4 w-full' />
            </div>
            <Button variant='outline' size='sm' className='ml-auto' disabled>
              <Skeleton className='h-4 w-32' />
            </Button>
          </div>
        </div>

        {/* Delivery Details Skeleton */}
        <Card>
          <CardContent className='p-6'>
            <div className='mb-4 flex items-center justify-between gap-2'>
              <Skeleton className='h-5 w-full' />
              <Button variant='outline' size='sm' disabled>
                <Skeleton className='h-4 w-32' />
              </Button>
            </div>
            <div className='grid gap-8 md:grid-cols-2'>
              <div className='space-y-2'>
                <Skeleton className='h-4 w-full' />
                <Skeleton className='h-5 w-full' />
                <div className='space-y-1'>
                  <Skeleton className='h-4 w-full' />
                  <Skeleton className='h-4 w-1/4' />
                </div>
              </div>
              <div className='grid gap-4'>
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-full' />
                  <div className='space-y-1'>
                    <Skeleton className='h-4 w-full' />
                    <Skeleton className='h-4 w-full' />
                  </div>
                </div>
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-full' />
                  <div className='space-y-1'>
                    <Skeleton className='h-4 w-full' />
                    <Skeleton className='h-4 w-full' />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Items Skeleton */}
        <Card>
          <CardContent className='p-6'>
            <div className='mb-6 flex items-center justify-between gap-2'>
              <Skeleton className='h-5 w-full' />
              <Button variant='outline' size='sm' disabled>
                <Skeleton className='h-4 w-32' />
              </Button>
            </div>
            <div className='space-y-6'>
              {[1, 2, 3].map((i) => (
                <div key={i} className='flex gap-4'>
                  <Skeleton className='h-24 w-full rounded-md' />
                  <div className='flex flex-1 justify-between'>
                    <div className='space-y-2'>
                      <Skeleton className='h-5 w-full' />
                      <Skeleton className='h-4 w-full' />
                    </div>
                    <div className='text-right'>
                      <Skeleton className='h-5 w-full' />
                      <Skeleton className='mt-1 h-4 w-full' />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Summary Skeleton */}
        <Card>
          <CardContent className='p-6'>
            <Skeleton className='mb-4 h-5 w-full' />
            <div className='space-y-4'>
              <div className='flex justify-between'>
                <Skeleton className='h-4 w-full' />
                <Skeleton className='h-4 w-full' />
              </div>
              <div className='flex justify-between'>
                <Skeleton className='h-4 w-full' />
                <Skeleton className='h-4 w-full' />
              </div>
              <div className='flex justify-between'>
                <div className='flex items-center gap-2'>
                  <Skeleton className='h-4 w-full' />
                  <Skeleton className='h-4 w-full' />
                </div>
                <Skeleton className='h-4 w-full' />
              </div>
              <div className='flex justify-between'>
                <div className='flex items-center gap-2'>
                  <Skeleton className='h-4 w-full' />
                  <Skeleton className='h-4 w-full' />
                </div>
                <Skeleton className='h-4 w-full line-through' />
              </div>
              <div className='flex justify-between border-t pt-4'>
                <Skeleton className='h-5 w-full' />
                <Skeleton className='h-5 w-full' />
              </div>
            </div>
            <div className='mt-6 space-y-4'>
              <Skeleton className='h-4 w-full' />
              <div className='flex items-center justify-between gap-4'>
                <div className='flex-1 space-y-2'>
                  <Skeleton className='h-5 w-full' />
                  <Skeleton className='h-4 w-full' />
                </div>
                <Button className='w-full' disabled>
                  <Skeleton className='h-4 w-32' />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Sidebar Skeleton */}
      <div className='space-y-6 rounded-3xl bg-gray-100 p-6'>
        <Card>
          <CardContent className='p-6'>
            <div className='mb-6 space-y-2'>
              <Skeleton className='h-6 w-full' />
              <Skeleton className='h-4 w-full' />
            </div>
            <div className='space-y-4'>
              <Button className='w-full' size='lg' disabled>
                <Skeleton className='h-4 w-32' />
              </Button>
              <Button variant='outline' className='w-full' size='lg' disabled>
                <Skeleton className='h-4 w-32' />
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-6'>
            <Skeleton className='mb-6 h-5 w-full' />
            <div className='space-y-6'>
              {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                <div key={i} className='flex gap-4'>
                  <div className='relative'>
                    <Skeleton className='h-5 w-full rounded-full' />
                    {i !== 8 && (
                      <div className='absolute left-2.5 top-5 h-[calc(100%+8px)] w-full bg-gray-200' />
                    )}
                  </div>
                  <div className='flex-1 space-y-2'>
                    <div className='flex items-center justify-between'>
                      <Skeleton className='h-4 w-full' />
                      <Skeleton className='h-4 w-full' />
                    </div>
                    <Skeleton className='w-full/4 h-4' />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
