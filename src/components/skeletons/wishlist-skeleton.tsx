import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export const WishlistItemSkeleton = () => (
  <div className='relative overflow-hidden rounded-2xl border border-neutral-200 bg-gray-100 p-4'>
    <div className='flex items-start gap-4 md:gap-8'>
      <Skeleton className='h-24 w-24 rounded-2xl md:h-32 md:w-32' />
      <div className='flex flex-1 flex-col gap-2'>
        <div>
          <Skeleton className='h-6 w-3/4' />
          <Skeleton className='mt-1 h-4 w-1/2' />
        </div>
        <div className='space-y-2'>
          <Skeleton className='h-5 w-36' />
          <Skeleton className='h-4 w-32' />
        </div>
      </div>
      <div className='flex min-w-[200px] flex-col items-end gap-2'>
        <Skeleton className='h-10 w-full rounded-md' />
        <Skeleton className='h-10 w-full rounded-md' />
        <Skeleton className='h-10 w-full rounded-md' />
      </div>
    </div>
  </div>
)

export const WishlistSkeleton = () => (
  <Card className='rounded-3xl bg-gray-100'>
    <CardHeader className='p-6'>
      <CardTitle className='text-2xl font-semibold text-neutral-900'>
        <Skeleton className='h-8 w-32' />
      </CardTitle>
    </CardHeader>
    <CardContent className='space-y-4 p-6 pt-0'>
      {[1, 2, 3, 4, 5].map((item) => (
        <WishlistItemSkeleton key={item} />
      ))}
    </CardContent>
  </Card>
)
