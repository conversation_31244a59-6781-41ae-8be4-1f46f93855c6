import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export default function SkeletonCard() {
  return (
    <Card className='flex w-full min-w-80 max-w-md overflow-hidden rounded-xl md:rounded-2xl'>
      {/* Left accent bar */}
      <div className='flex w-10 items-center justify-center bg-neutral-150 p-2'>
        <Skeleton className='h-20 w-full' />
      </div>

      {/* Main content */}
      <CardContent className='flex-1 space-y-4 p-4'>
        {/* Heading skeleton */}
        <Skeleton className='h-4 w-3/4' />

        {/* Description skeleton - two lines */}
        <div className='space-y-2'>
          <Skeleton className='h-4 w-full' />
          <Skeleton className='h-4 w-5/6' />
        </div>
      </CardContent>
    </Card>
  )
}
