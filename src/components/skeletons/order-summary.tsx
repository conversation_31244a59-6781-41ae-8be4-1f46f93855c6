"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { motion } from "framer-motion"
import { Calendar, Tag } from "lucide-react"

export default function OrderSummarySkeleton() {
  return (
    <div className='mx-auto w-full max-w-2xl space-y-6 rounded-3xl bg-gray-100 p-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className='space-y-4'
        >
          <Skeleton className='h-8 w-48' />
        </motion.div>
      </div>

      {/* Rental Period */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className='flex items-center justify-between rounded-full border py-0.5 pl-4'
      >
        <div className='flex items-center gap-2'>
          <Calendar className='h-5 w-5 text-muted-foreground' />
          <Skeleton className='h-4 w-32' />
        </div>
        <Button
          variant='default'
          disabled
          className='opacity-50 drop-shadow-none'
        >
          Edit
        </Button>
      </motion.div>

      {/* GST Checkbox */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className='flex items-center gap-2'
      >
        <Skeleton className='h-5 w-5' />
        <Skeleton className='h-4 w-64' />
      </motion.div>

      {/* Coupon Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className='space-y-2'
      >
        <div className='flex items-center justify-between rounded-full border py-0.5 pl-4'>
          <Tag className='mr-2 h-5 w-5 text-muted-foreground' />
          <Skeleton className='h-4 w-32 flex-1' />
          <Button variant='default' disabled className='opacity-50'>
            Apply
          </Button>
        </div>
        <Skeleton className='h-4 w-24' />
      </motion.div>

      {/* Charges Breakdown */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className='space-y-4 border-t pt-4'
      >
        {/* Total Rental Charges */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-4 w-40' />
            <Skeleton className='h-6 w-24 rounded-full bg-muted/50' />
          </div>
          <Skeleton className='h-6 w-24' />
        </div>

        {/* Deposit Charges */}
        <div className='flex items-center justify-between'>
          <Skeleton className='h-4 w-32' />
          <Skeleton className='h-6 w-16' />
        </div>

        {/* Delivery Charges */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-4 w-32' />
            <Skeleton className='h-6 w-32 rounded-full bg-primary/10' />
          </div>
          <Skeleton className='h-6 w-16' />
        </div>

        {/* Pickup Charges */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-4 w-32' />
            <Skeleton className='h-6 w-32 rounded-full bg-primary/10' />
          </div>
          <Skeleton className='h-6 w-16' />
        </div>
      </motion.div>

      {/* Order Total */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className='space-y-2 border-t pt-4'
      >
        <div className='flex items-center justify-between'>
          <Skeleton className='h-6 w-32' />
          <Skeleton className='h-8 w-32' />
        </div>
        <Skeleton className='h-4 w-48' />
      </motion.div>

      {/* Payment Mode */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className='space-y-4 pt-4'
      >
        <div className='flex items-center justify-between'>
          <Skeleton className='h-5 w-32' />
          <Skeleton className='h-5 w-48' />
        </div>

        {/* Razorpay Section */}
        <div className='space-y-2 rounded-lg bg-muted/20 p-4'>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-8 w-8' />
            <Skeleton className='h-5 w-32' />
          </div>
          <Skeleton className='h-4 w-64' />
        </div>
      </motion.div>

      {/* Place Order Button */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.7 }}
        className='space-y-4'
      >
        <Button className='h-12 w-full' disabled>
          Place Your Order & Pay
        </Button>
        <Skeleton className='mx-auto h-4 w-full max-w-md' />
      </motion.div>
    </div>
  )
}
