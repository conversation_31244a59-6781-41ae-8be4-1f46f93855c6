"use client"

import { motion } from "framer-motion"
import { Skeleton } from "@/components/ui/skeleton"

export function ProductGallerySkeleton() {
  return (
    <div className='grid grid-cols-[100px_1fr] gap-4 p-4'>
      {/* Thumbnails */}
      <div className='space-y-2'>
        {[...Array(6)].map((_, i) => (
          <Skeleton key={i} className='aspect-square w-full rounded-lg' />
        ))}
      </div>

      {/* Main Image */}
      <motion.div
        initial={{ opacity: 0.6 }}
        animate={{ opacity: 1 }}
        transition={{ repeat: Infinity, duration: 1.5 }}
      >
        <Skeleton className='aspect-square w-full rounded-lg' />
      </motion.div>
    </div>
  )
}
