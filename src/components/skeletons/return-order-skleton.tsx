"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { ChevronLeft, Package } from "lucide-react"

export default function ReturnOrderSkeleton() {
  return (
    <div className='min-h-screen w-full'>
      {/* Mobile Step Indicator */}
      <div className='flex items-center justify-center gap-2 pb-4 lg:hidden'>
        <div className='flex h-8 w-8 items-center justify-center rounded-full bg-primary-500'>
          <span className='text-sm font-medium text-white'>1</span>
        </div>
        <div className='h-0.5 w-8 bg-gray-200'></div>
        <div className='h-8 w-8 rounded-full bg-gray-200'></div>
        <div className='h-0.5 w-8 bg-gray-200'></div>
        <div className='h-8 w-8 rounded-full bg-gray-200'></div>
      </div>

      <div className='grid grid-cols-1 gap-4 xl:grid-cols-3 xl:gap-6'>
        {/* Main Content */}
        <div className='rounded-2xl bg-gray-100 p-4 lg:rounded-3xl lg:p-8 xl:col-span-2'>
          {/* Header */}
          <div className='mb-4 flex items-start justify-between'>
            <div className='flex min-w-0 flex-1 items-center gap-2 lg:gap-4'>
              <ChevronLeft className='h-5 w-5 flex-shrink-0 lg:h-6 lg:w-6' />
              <div className='min-w-0 flex-1'>
                <Skeleton className='mb-1 h-6 w-full max-w-xs lg:mb-2 lg:h-8 lg:max-w-md' />
              </div>
            </div>

            {/* Desktop Order Info */}
            <div className='ml-4 hidden flex-shrink-0 xl:block'>
              <Skeleton className='mb-1 h-5 w-48 lg:h-6' />
            </div>
          </div>

          {/* Products Section */}
          <Card className='mb-4 lg:mb-6'>
            <CardContent className='p-4 lg:p-6'>
              <div className='mb-4 flex items-center justify-between lg:mb-6'>
                <div className='flex items-center gap-2 lg:gap-3'>
                  <Package className='h-4 w-4 flex-shrink-0 lg:h-5 lg:w-5' />
                  <Skeleton className='h-5 w-32 lg:h-6 lg:w-48' />
                </div>
                <Skeleton className='h-8 w-20 lg:h-9 lg:w-32' />
              </div>

              {/* Desktop Table Header */}
              <div className='mb-4 hidden grid-cols-12 gap-4 border-b pb-2 lg:grid'>
                <div className='col-span-6'>
                  <Skeleton className='h-4 w-32' />
                </div>
                <div className='col-span-2 text-center'>
                  <Skeleton className='mx-auto h-4 w-16' />
                </div>
                <div className='col-span-2 text-center'>
                  <Skeleton className='mx-auto h-4 w-20' />
                </div>
                <div className='col-span-2 text-center'>
                  <Skeleton className='mx-auto h-4 w-16' />
                </div>
              </div>

              {/* Product List */}
              <div className='space-y-4 lg:space-y-6'>
                {[1, 2, 3].map((item) => (
                  <div
                    key={item}
                    className='border-b border-gray-100 pb-4 last:border-b-0 lg:pb-6'
                  >
                    {/* Desktop Layout */}
                    <div className='hidden grid-cols-12 items-center gap-4 lg:grid'>
                      <div className='col-span-6 flex items-center gap-3'>
                        <Skeleton className='h-12 w-12 flex-shrink-0 rounded' />
                        <div className='min-w-0'>
                          <Skeleton className='mb-2 h-5 w-28' />
                          <Skeleton className='h-4 w-20' />
                        </div>
                      </div>
                      <div className='col-span-2 flex justify-center'>
                        <Skeleton className='h-5 w-5 rounded-full' />
                      </div>
                      <div className='col-span-2 flex justify-center'>
                        <Skeleton className='h-5 w-5 rounded-full' />
                      </div>
                      <div className='col-span-2 flex justify-center'>
                        <Skeleton className='h-5 w-5 rounded-full' />
                      </div>
                    </div>

                    {/* Mobile Layout */}
                    <div className='lg:hidden'>
                      <div className='mb-4 flex items-center gap-3'>
                        <Skeleton className='h-12 w-12 flex-shrink-0 rounded' />
                        <div className='min-w-0 flex-1'>
                          <Skeleton className='mb-2 h-4 w-32' />
                          <Skeleton className='h-3 w-40' />
                        </div>
                      </div>

                      {/* Mobile Radio Options with Text */}
                      <div className='space-y-3'>
                        <div className='flex items-center gap-3 rounded-lg border p-3'>
                          <Skeleton className='h-4 w-4 flex-shrink-0 rounded-full' />
                          <Skeleton className='h-4 w-16' />
                        </div>
                        <div className='flex items-center gap-3 rounded-lg border p-3'>
                          <Skeleton className='h-4 w-4 flex-shrink-0 rounded-full' />
                          <Skeleton className='h-4 w-28' />
                        </div>
                        <div className='flex items-center gap-3 rounded-lg border bg-primary-100 p-3'>
                          <Skeleton className='h-4 w-4 flex-shrink-0 rounded-full' />
                          <Skeleton className='h-4 w-20' />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Packaging Section */}
          <Card>
            <CardContent className='p-4 lg:p-6'>
              <div className='flex items-center justify-between'>
                <div className='flex min-w-0 flex-1 items-center gap-2 lg:gap-3'>
                  <Package className='h-4 w-4 flex-shrink-0 lg:h-5 lg:w-5' />
                  <Skeleton className='h-5 w-40 lg:h-6 lg:w-56' />
                </div>
                <Skeleton className='h-8 w-20 flex-shrink-0 lg:h-9 lg:w-32' />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Desktop Sidebar */}
        <div className='hidden rounded-2xl bg-gray-100 p-2 lg:rounded-3xl lg:p-4 xl:block'>
          <Card className='sticky top-6'>
            <CardContent className='p-6'>
              <div className='space-y-6'>
                <div>
                  <Skeleton className='mb-4 h-5 w-48' />
                  <Skeleton className='mb-2 h-4 w-full' />
                  <Skeleton className='mb-2 h-4 w-3/4' />
                  <Skeleton className='h-4 w-5/6' />
                </div>

                <div className='space-y-4'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <Skeleton className='mb-1 h-4 w-24' />
                      <Skeleton className='h-3 w-20' />
                    </div>
                    <Skeleton className='h-4 w-12' />
                  </div>

                  <div className='flex items-center justify-between'>
                    <div>
                      <Skeleton className='mb-1 h-4 w-20' />
                      <Skeleton className='h-3 w-24' />
                    </div>
                    <Skeleton className='h-4 w-16' />
                  </div>
                </div>

                <div className='border-t pt-4'>
                  <div className='mb-2 flex items-center justify-between'>
                    <Skeleton className='h-5 w-24' />
                    <Skeleton className='h-6 w-20' />
                  </div>
                  <Skeleton className='h-3 w-32' />
                </div>

                <Skeleton className='h-12 w-full' />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Mobile Next Button */}
      <div className='mt-4 lg:mt-6 xl:hidden'>
        <Skeleton className='h-12 w-full' />
      </div>
    </div>
  )
}
