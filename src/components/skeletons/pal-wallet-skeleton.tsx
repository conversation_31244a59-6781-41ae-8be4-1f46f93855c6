import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

const PalWalletLoading = () => (
  <Card className='bg-gray-100'>
    <CardHeader>
      <CardTitle className='text-2xl font-semibold text-neutral-900'>
        Pal Wallet
      </CardTitle>
    </CardHeader>
    <CardContent className='space-y-6'>
      {/* Wallet balance card skeleton */}
      <div className='relative rounded-xl bg-neutral-100 p-6'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-4'>
            <Skeleton className='h-12 w-12 rounded-lg' />
            <div className='space-y-1'>
              <Skeleton className='h-4 w-32' />
              <Skeleton className='h-8 w-40' />
            </div>
          </div>
          <Skeleton className='h-10 w-28 rounded-full' />
        </div>
      </div>

      {/* Transactions list skeleton */}
      <div className='space-y-4'>
        {/* Transactions header */}
        <div className='grid grid-cols-3 px-4'>
          <Skeleton className='h-4 w-24' />
          <Skeleton className='h-4 w-28' />
          <Skeleton className='h-4 w-16 justify-self-end' />
        </div>

        {/* Transaction items */}
        {Array.from({ length: 5 }).map((_, index) => (
          <div
            key={index}
            className='rounded-xl border border-neutral-200 bg-gray-100 p-4'
          >
            <div className='grid grid-cols-3 items-center gap-4'>
              <div className='flex items-start gap-3'>
                <Skeleton className='h-8 w-8 rounded-full' />
                <div className='flex-1 space-y-1'>
                  <Skeleton className='h-4 w-3/4' />
                  <Skeleton className='h-3 w-1/2' />
                </div>
              </div>
              <div className='space-y-1'>
                <Skeleton className='h-4 w-full' />
                <Skeleton className='h-3 w-3/4' />
              </div>
              <Skeleton className='h-4 w-20 justify-self-end' />
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
)

export default PalWalletLoading
