"use client"

import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export default function ProductCardSkeleton({
  hideDateAndPrice = false,
}: {
  hideDateAndPrice?: boolean
}) {
  return (
    <Card className='group relative h-max overflow-hidden rounded-2xl border-none bg-transparent p-2.5 leading-5 transition-all duration-300 md:rounded-3xl md:p-3'>
      <Skeleton className='relative aspect-square rounded-2xl bg-neutral-100 p-1.5 md:rounded-3xl md:p-3'>
        {/* Trending Tag skeleton */}
        {/* <Badge
          variant={'outline'}
          className="absolute left-2 top-2 rounded-lg border border-gray-200 px-1.5 py-0 md:left-3 md:top-3 md:border-2 md:px-2.5 md:py-0.5"
        >
          <Skeleton className="h-[10px] w-14 md:h-3 md:w-16 bg-neutral-200" />
        </Badge> */}

        {/* Image skeleton */}
        <div className='flex h-full w-full items-center justify-center p-3 md:p-5'>
          <Skeleton className='h-full w-full scale-90 rounded-2xl bg-neutral-100 md:rounded-3xl' />
        </div>
      </Skeleton>

      <CardContent className='flex flex-col items-start justify-center gap-1 p-0 pt-2.5 md:gap-2.5 md:p-3'>
        <div className='w-full space-y-1.5'>
          {/* Title skeleton */}
          <Skeleton className='h-[14px] w-3/4 bg-neutral-200 md:h-[18px]' />
          {/* Subtitle skeleton */}
          <Skeleton className='h-[12px] w-full bg-neutral-200 md:h-[16px]' />
        </div>

        {!hideDateAndPrice && (
          <div className='mt-1 flex w-full flex-col items-baseline justify-between gap-0.5 md:mt-2 md:gap-1'>
            {/* Rent duration skeleton */}
            <div className='flex items-center gap-1'>
              <Skeleton className='h-[12px] w-16 bg-neutral-200 md:h-[14px] md:w-20' />
              <Skeleton className='h-[12px] w-8 bg-neutral-200 md:h-[14px] md:w-10' />
            </div>
            {/* Price skeleton */}
            <Skeleton className='h-[16px] w-20 bg-neutral-200 md:h-[22px] md:w-28' />
          </div>
        )}
      </CardContent>

      <CardFooter className='flex w-full flex-col items-start justify-between gap-1.5 px-0 py-0 md:gap-2 md:px-3'>
        {/* Extra day price skeleton */}
        {!hideDateAndPrice && (
          <div className='flex items-center gap-1'>
            <Skeleton className='h-4 w-4 bg-neutral-200 md:h-5 md:w-5' />{" "}
            {/* Icon */}
            <Skeleton className='h-[14px] w-48 bg-neutral-200 md:h-[16px]' />
          </div>
        )}

        {/* Booking info skeleton */}
        <div className='flex items-center gap-1'>
          <Skeleton className='h-4 w-4 bg-neutral-200 md:h-5 md:w-5' />{" "}
          {/* Icon */}
          <Skeleton className='h-[12px] w-32 bg-neutral-200 md:h-[14px]' />
        </div>

        {/* Rating skeleton */}
        <div className='flex items-center gap-2'>
          <div className='flex gap-0.5'>
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton
                key={i}
                className='h-[11px] w-[11px] bg-neutral-200 md:h-[15px] md:w-[15px]'
              />
            ))}
          </div>
          <Skeleton className='h-[11px] w-8 bg-neutral-200 md:h-[14px] md:w-10' />
        </div>
      </CardFooter>
    </Card>
  )
}
