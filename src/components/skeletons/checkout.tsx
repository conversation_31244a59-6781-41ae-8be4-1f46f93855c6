"use client"

import { Skeleton } from "@/components/ui/skeleton"
import { motion } from "framer-motion"
import OrderSummarySkeleton from "./order-summary"

export default function CheckoutSkeleton() {
  return (
    <div className='w-full py-28 md:py-24'>
      <div className='container grid grid-cols-1 gap-3 lg:grid-cols-[1fr,400px] lg:gap-6'>
        {/* Main Checkout Form Area */}
        <div className='space-y-8 rounded-3xl bg-gray-100 p-6 lg:p-10'>
          {/* Contact Details Section */}
          <motion.section
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className='space-y-6'
          >
            <div className='flex items-center gap-4'>
              <div className='flex items-center gap-2'>
                <div className='flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground'>
                  1
                </div>
                <h2 className='text-xl font-semibold'>Contact Details</h2>
              </div>
              <Skeleton className='ml-auto h-6 w-16' />
            </div>

            <div className='space-y-6'>
              {/* Name Fields */}
              <div className='grid gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-24' />
                  <Skeleton className='h-10 w-full' />
                </div>
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-24' />
                  <Skeleton className='h-10 w-full' />
                </div>
              </div>

              {/* Email Field */}
              <div className='space-y-2'>
                <Skeleton className='h-4 w-32' />
                <Skeleton className='h-10 w-full' />
              </div>

              {/* Phone Number Field */}
              <div className='space-y-2'>
                <Skeleton className='h-4 w-32' />
                <div className='flex gap-2'>
                  <Skeleton className='h-10 w-20' />
                  <Skeleton className='h-10 flex-1' />
                </div>
              </div>

              {/* WhatsApp Toggle */}
              <div className='space-y-2'>
                <Skeleton className='h-4 w-64' />
                <div className='flex gap-4'>
                  <Skeleton className='h-10 w-20' />
                  <Skeleton className='h-10 w-20' />
                </div>
              </div>

              {/* Save Button */}
              <Skeleton className='h-10 w-32' />
            </div>
          </motion.section>

          {/* Delivery Address Section */}
          <motion.section
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className='space-y-6'
          >
            <div className='flex items-center gap-4 py-6'>
              <div className='flex items-center gap-2'>
                <div className='flex h-8 w-8 items-center justify-center rounded-full border-2 border-muted text-muted-foreground'>
                  2
                </div>
                <h2 className='text-xl font-semibold text-muted-foreground'>
                  Delivery Address
                </h2>
              </div>
              <Skeleton className='ml-auto h-6 w-16' />
            </div>
          </motion.section>

          {/* Delivery Address Section */}
          <motion.section
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className='space-y-6'
          >
            <div className='flex items-center gap-4 py-6'>
              <div className='flex items-center gap-2'>
                <div className='flex h-8 w-8 items-center justify-center rounded-full border-2 border-muted text-muted-foreground'>
                  3
                </div>
                <h2 className='text-xl font-semibold text-muted-foreground'>
                  CarePal Secure
                </h2>
              </div>
              <Skeleton className='ml-auto h-6 w-16' />
            </div>
          </motion.section>

          {/* Review & Pay Section */}
          <motion.section
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className='space-y-4'
          >
            <div className='flex items-center gap-4 py-6'>
              <div className='flex items-center gap-2'>
                <div className='flex h-8 w-8 items-center justify-center rounded-full border-2 border-muted text-muted-foreground'>
                  4
                </div>
                <h2 className='text-xl font-semibold text-muted-foreground'>
                  Review & Pay
                </h2>
              </div>
              <Skeleton className='ml-auto h-6 w-16' />
            </div>
          </motion.section>
        </div>

        {/* Order Summary Sidebar */}
        <div className='lg:sticky lg:top-8'>
          <OrderSummarySkeleton />
        </div>
      </div>
    </div>
  )
}
