"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import { Calendar<PERSON>heckIcon, ChevronRight, HelpCircle } from "lucide-react"

export function OrderCardSkeleton() {
  return (
    <Card className='overflow-hidden rounded-3xl border-2 border-neutral-200'>
      <CardHeader className='flex flex-row items-center justify-between gap-2 space-y-0 bg-neutral-150 p-4 px-3 py-2 md:gap-6'>
        {/* left  */}
        <div className='hidden items-center justify-between gap-3 md:flex md:gap-6'>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              ORDER PLACED ON:
            </p>
            <Skeleton className='h-5 w-24' />
          </div>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              SHIPPING TO:
            </p>
            <div className='flex items-center gap-1'>
              <Skeleton className='h-5 w-32' />
              <HelpCircle className='h-3.5 w-3.5 text-neutral-400' />
            </div>
          </div>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              RENTAL PERIOD:
            </p>
            <Skeleton className='h-5 w-20' />
          </div>
        </div>

        <div className='flex w-full items-center justify-between gap-2 md:w-auto'>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              ORDER NO:
            </p>
            <Skeleton className='h-5 w-32' />
          </div>
          <Button
            size={"icon"}
            variant={"outline"}
            className='border-neutral-200 bg-gray-100'
            disabled
          >
            <ChevronRight className='h-5 w-5 text-neutral-400' />
          </Button>
        </div>
      </CardHeader>
      <CardContent className='bg-gray-100 p-2 md:space-y-6 md:px-4 md:py-3'>
        {/* desktop */}
        <div className='hidden w-full md:block'>
          <Skeleton className='h-8 w-48 md:h-9' />
          <Skeleton className='mt-1 h-5 w-72' />
        </div>

        <div className='grid grid-cols-1 items-start gap-y-2 md:grid-cols-3 md:gap-3'>
          <div className='col-span-2 flex w-full items-start justify-start gap-4 rounded-xl bg-neutral-150 p-2 md:rounded-2xl md:p-3'>
            <div className='flex -space-x-4 lg:max-w-56 lg:-space-x-14'>
              <Skeleton className='h-14 w-14 rounded-lg md:h-20 md:w-20 md:rounded-xl' />
              <Skeleton className='h-14 w-14 rounded-lg md:h-20 md:w-20 md:rounded-xl' />
              <Skeleton className='h-14 w-14 rounded-lg md:h-20 md:w-20 md:rounded-xl' />
            </div>

            {/* order details */}
            <div className='flex-1'>
              <p className='mb-0 text-[10px] font-medium uppercase tracking-wide text-neutral-500 md:mb-1'>
                ORDER ITEMS:
              </p>
              <div className='space-y-0.5'>
                <Skeleton className='h-3 w-full max-w-md' />
                <Skeleton className='h-3 w-full max-w-sm' />
                <Skeleton className='h-3 w-full max-w-xs' />
              </div>
              <div className='mt-1 flex items-center gap-1 md:mt-4'>
                <Skeleton className='h-5 w-20' />
                <Skeleton className='h-4 w-40' />
              </div>
            </div>
          </div>

          <div className='flex h-full w-full flex-row items-center justify-evenly gap-1 rounded-xl bg-neutral-150 p-2 md:w-auto md:flex-col md:items-start md:justify-evenly md:gap-3 md:rounded-2xl md:p-4'>
            <div className='w-max'>
              <div className='flex w-max items-center text-[10px] text-neutral-500 md:gap-1 md:text-sm'>
                <CalendarCheckIcon className='mr-1 h-2.5 w-2.5 md:h-4 md:w-4' />
                Delivery Date:
                <Skeleton className='ml-0.5 h-3 w-16 md:h-4 md:w-24' />
              </div>
            </div>

            <Separator orientation='vertical' className='flex md:hidden' />
            <Separator className='hidden md:flex' />

            <div className='w-max'>
              <div className='flex items-center text-[10px] text-neutral-500 md:gap-1 md:text-sm'>
                <CalendarCheckIcon className='mr-1 h-2.5 w-2.5 md:h-4 md:w-4' />
                Pickup Date:
                <Skeleton className='ml-0.5 h-3 w-16 md:h-4 md:w-24' />
              </div>
            </div>
          </div>
        </div>

        {/* mobile */}
        <div className='mt-3 block w-full md:hidden'>
          <Skeleton className='h-6 w-36' />
          <Skeleton className='mt-1 h-4 w-48' />
        </div>
      </CardContent>
    </Card>
  )
}
