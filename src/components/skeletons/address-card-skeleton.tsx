import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export function AddressSkeleton() {
  return (
    <Card>
      <CardContent className='p-4'>
        <div className='space-y-3'>
          <div className='flex items-start justify-between'>
            <div className='w-full space-y-2'>
              <Skeleton className='h-5 w-3/4' />
              <Skeleton className='h-4 w-full' />
            </div>
            <Skeleton className='h-5 w-5 rounded-full' />
          </div>
          <Skeleton className='h-4 w-24' />
        </div>
      </CardContent>
    </Card>
  )
}
