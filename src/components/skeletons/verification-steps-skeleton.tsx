"use client"

import { Card } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export const VerificationStepsSkeleton = () => (
  <div className='mx-auto w-full max-w-3xl rounded-3xl bg-white'>
    {/* Header section */}
    <div className='px-4 pb-4 pt-6 md:px-16 md:pb-6 md:pt-8'>
      {/* Profile and greeting - horizontal on mobile, centered on desktop */}
      <div className='mb-4 flex flex-row items-center gap-4 md:flex-col md:items-center'>
        {/* Profile image with checkmark */}
        <div className='relative flex-shrink-0'>
          <Skeleton className='h-16 w-16 rounded-2xl md:h-20 md:w-20' />
          <div className='absolute bottom-0 right-0'>
            <Skeleton className='h-6 w-6 rounded-full bg-green-500 md:h-8 md:w-8' />
          </div>
        </div>

        {/* Greeting and heading - left aligned on mobile, centered on desktop */}
        <div className='flex flex-col items-start justify-center space-y-1 md:items-center md:space-y-4'>
          <Skeleton className='h-5 w-32' />
          <Skeleton className='h-7 w-64 md:h-9 md:w-80' />
        </div>
      </div>

      {/* Description text - centered on both mobile and desktop */}
      <div className='mb-4 text-center'>
        <Skeleton className='mx-auto h-4 w-full max-w-md' />
      </div>

      {/* Learn more button - centered on both */}
      <div className='flex justify-center pb-4 pt-2'>
        <Skeleton className='h-12 w-72 rounded-full' />
      </div>
    </div>

    {/* Verification steps */}
    <div className='space-y-4 px-4 pb-6 md:px-16 md:pb-8'>
      {/* Step 1: Identity Verification */}
      <Card className='overflow-hidden rounded-2xl'>
        <div className='p-4 md:p-5'>
          <div className='flex items-start gap-3'>
            <Skeleton className='h-8 w-8 flex-shrink-0 rounded-md bg-green-100' />
            <div className='flex-1 space-y-2'>
              <Skeleton className='h-6 w-48' />
              <Skeleton className='h-4 w-full' />
            </div>
          </div>
        </div>
        <Skeleton className='h-9 w-full bg-primary-100 md:h-10' />
      </Card>

      {/* Step 2: Occupation or PAN Verification */}
      <Card className='overflow-hidden rounded-2xl'>
        <div className='p-4 md:p-5'>
          <div className='flex items-start gap-3'>
            <Skeleton className='h-8 w-8 flex-shrink-0 rounded-md bg-green-100' />
            <div className='flex-1 space-y-2'>
              <Skeleton className='h-6 w-64' />
              <Skeleton className='h-4 w-full' />
            </div>
          </div>
        </div>
        <Skeleton className='h-9 w-full bg-primary-100 md:h-10' />
      </Card>

      {/* Step 3: PAN Verification */}
      <Card className='overflow-hidden rounded-2xl'>
        <div className='p-4 md:p-5'>
          <div className='flex items-start gap-3'>
            <Skeleton className='h-8 w-8 flex-shrink-0 rounded-md bg-green-100' />
            <div className='flex-1 space-y-2'>
              <div className='mb-2 flex items-center justify-between'>
                <Skeleton className='h-6 w-48' />
              </div>
              <Skeleton className='h-4 w-full' />
            </div>
          </div>
        </div>
        <Skeleton className='h-9 w-full bg-primary-100 md:h-10' />
      </Card>
    </div>
  </div>
)
