import HeroSectionHome from "@/components/hero/hero-section-home"
import { CategorySlider } from "@/components/sliders/category-slider"
// import { SampleSlider } from '@/components/sliders/sample-slider'
import { fetchCategories, fetchSubCategories } from "@/actions/category"
import { fetchCities, fetchCityHeader } from "@/actions/city"
import { fetchCitySeoDescription } from "@/actions/seo-data"
import { fetchSuperCategories } from "@/actions/super-category"
import NotFound from "@/app/not-found"
import FooterSeoPortal from "@/components/layout/footer/footer-seo-portal"
import SharePalPromise from "@/components/main/sharepal-promise"
import WhyRentingisBetter from "@/components/main/why-renting-is-better"
import ZeroPolicy from "@/components/main/zero-policy"
import Reviews from "@/components/reviews"
import SectionTitle from "@/components/section-title"
import FooterSeoContent from "@/components/seocontent/city-seo"
import TrendingItemsSection from "../sections/trending-items"
import BigCategorySlider from "../sliders/big-category-slider"

interface CityHomePageProps {
  city: string
  searchParams?: { [key: string]: string | string[] | undefined }
  showSeoContent?: boolean // to differentiate between main page and city page
}
export default async function Page({ city, searchParams }: CityHomePageProps) {
  const cities = await fetchCities()
  //find city in cities and if not found then redirect to 404
  const cityFound = cities.find((item) => item.city_url === city)
  if (!cityFound) {
    return NotFound()
  }

  const searchParamsResolved = await searchParams
  const category = Array.isArray(searchParamsResolved?.category)
    ? searchParamsResolved?.category[0]
    : searchParamsResolved?.category || "trekking-gear-on-rent"

  const [categories, superCategories, seoDesc, subCategories, cityHeader] =
    await Promise.all([
      // fetchTrending(city).catch((error) => {
      //   console.error("Error fetching trendings:", error)
      //   return { items: [] }
      // }),
      fetchCategories().catch((error) => {
        console.error("Error fetching categories:", error)
        return []
      }),
      fetchSuperCategories().catch((error) => {
        console.error("Error fetching superCategories:", error)
        return []
      }),
      fetchCitySeoDescription(city).catch((error) => {
        console.error("Error fetching city SEO description:", error)
        return { desc: "" }
      }),
      fetchSubCategories(category).catch((error) => {
        console.error("Error fetching subCategories:", error)
        return []
      }),
      fetchCityHeader(city).catch((error) => {
        console.error("Error fetching city header:", error)
        return null
      }),
    ])

  // console.log('trendings', trendings)

  return (
    // <div className="min-h-screen">
    <div className='relative'>
      <HeroSectionHome
        super_categories={superCategories}
        cityHeader={cityHeader}
        city={city}
      />
      {/* top categories section */}

      <section id='categories' className='mt-5 md:mt-10'>
        <div className='mx-auto mb-5 w-56 md:mb-10 md:w-full'>
          <SectionTitle
            cText='to choose from'
            nTColor='text-category-orange'
            nText='Top Categories '
            className='text-center md:text-d6'
          />
        </div>
        <CategorySlider category={category} data={categories} />
        <BigCategorySlider
          subCategories={subCategories}
          city={city}
          category={category}
        />
      </section>

      {/* Trending Items Section */}
      {/* <section className='relative mx-auto mt-12 w-full items-center justify-center bg-neutral-150 md:mt-24'>
        <SwiperSlider
          data={filterAndSortRentalItems(trendings.items)}
          city={city}
          hideDateAndPrice={true}
        />
      </section> */}

      <TrendingItemsSection city={city} />

      {/* <SwiperSlider
        data={filterAndSortRentalItems(trendings.items)}
        city={city}
      /> */}

      {/* Zero Policy  */}
      <ZeroPolicy />

      {/* Sharepal Promise */}
      <SharePalPromise />

      {/* Why Renting Better */}
      <WhyRentingisBetter city={city} />

      {/* Reviews */}
      <Reviews />

      {/* seo content  */}
      <FooterSeoPortal>
        {/* <h2>{seoDesc?.desc}</h2> */}
        <FooterSeoContent city={city} seoDesc={seoDesc?.desc} />
      </FooterSeoPortal>
    </div>
  )
}
