"use client"

import * as React from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import { X } from "lucide-react"

interface DialogWrapperProps {
  isOpen: boolean
  onClose: () => void
  title: string
  description?: string
  children: React.ReactNode
}

export function DialogWrapper({
  isOpen,
  onClose,
  title,
  description,
  children,
}: DialogWrapperProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='w-[95%] rounded-2xl bg-gray-100 px-6 pt-12 sm:max-w-[550px] md:rounded-3xl md:px-10 xl:px-14'>
        <div className='absolute right-3 top-3 flex items-center justify-end'>
          <button
            onClick={onClose}
            className='rounded-full p-1.5 hover:bg-neutral-100'
          >
            <X className='h-5 w-5 md:h-6 md:w-6' />
          </button>
        </div>
        <DialogHeader>
          <DialogTitle className='w-full text-center text-2xl font-bold'>
            {title}
          </DialogTitle>
          {description && (
            <p className='mt-2 text-center text-sm text-neutral-600 md:text-base'>
              {description}
            </p>
          )}
        </DialogHeader>
        {children}
      </DialogContent>
    </Dialog>
  )
}
