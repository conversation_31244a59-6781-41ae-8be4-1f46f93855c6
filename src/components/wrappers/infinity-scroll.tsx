"use client"
import React, { ReactNode } from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

const InfinityScroll = ({
  children,
  className,
  duration,
}: {
  children: ReactNode
  className: string
  duration: number
}) => {
  const marqueeVariants = {
    animate: {
      x: [0, -1000], // Adjust the distance to match the width of your text
      transition: {
        x: {
          repeat: Infinity,
          repeatType: "loop",
          duration: duration, // Adjust the duration to control speed
          ease: "linear",
        },
      },
    },
  }
  return (
    <div>
      <motion.div
        className={cn("inline-block", className)}
        variants={marqueeVariants}
        animate='animate'
      >
        {children}{" "}
      </motion.div>
    </div>
  )
}

export default InfinityScroll
