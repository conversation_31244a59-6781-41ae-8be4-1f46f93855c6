"use client"
import { <PERSON>hare<PERSON><PERSON><PERSON><PERSON> } from "@/components/main/logo"
import CartItems from "@/components/modals/cart"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import { City } from "@/types/address"
import { User } from "@/types/user"
import { formatDateWithOrdinal } from "@/utils/date-logics"
import {
  getHeaderBorderColor,
  getSuperCategoryBgColor,
} from "@/utils/get-bg-color"
import Link from "next/link"
import React from "react"

import useScrollState from "@/hooks/use-scroll-state"
import { usePathname } from "next/navigation"
import {
  CalendarAddFilledIcon,
  CalendarEditFilledIcon,
  ChevronDownIcon,
  LocationPointerOutlinedIcon,
  UserOutlinedIcon,
} from "sharepal-icons"

interface HeaderMobileProps {
  selectedCity: City
  setCityModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  setIsProfileOpen: React.Dispatch<React.SetStateAction<boolean>>
  delivery_date: Date | null
  openCalendar: () => void
  openModal: () => void
  pickup_date: Date | null
  isLoggedIn: boolean
  setIsLoggedIn: (isTrue: boolean) => void
  isCartOpen: boolean
  setCartOpen: React.Dispatch<React.SetStateAction<boolean>>
  user: User | null
  specialRoute?: boolean
  category?: string
}

export const HeaderMobile: React.FC<HeaderMobileProps> = ({
  selectedCity,
  delivery_date,
  openCalendar,
  setCityModalOpen,
  // openModal,
  pickup_date,
  // isLoggedIn,
  // setIsLoggedIn,
  isCartOpen,
  setCartOpen,
  // user,
  specialRoute,
  setIsProfileOpen,
  category,
}) => {
  // const { scrollY } = useScroll()
  const isScrolled = useScrollState(80) // Now
  const pathname = usePathname()

  // console.log("mobile header re render")
  return (
    <div className='mobile container flex w-full flex-col items-center justify-center gap-3 lg:hidden'>
      <div className='flex h-full w-full items-center justify-between gap-1'>
        <Link
          href={selectedCity ? `/${selectedCity.city_url}` : "/"}
          className='logo flex h-10 flex-col items-center justify-end gap-1 rounded-bl-xl rounded-br-xl bg-primary-500 px-3 pb-1 pt-3'
          prefetch={true}
        >
          <div className='flex w-full max-w-28 items-center justify-center'>
            <SharePalLogo />
          </div>
        </Link>
        <div
          className={cn(
            "flex w-full items-center justify-end gap-1.5 pt-1.5 md:gap-4",
            specialRoute && !isScrolled
              ? "fill-gray-100 text-gray-900"
              : "fill-gray-900 text-gray-100",
          )}
        >
          <button
            type='button'
            onClick={(e) => {
              e.preventDefault()
              setCityModalOpen(true)
            }}
            className={cn(
              "city flex items-center justify-center gap-1 rounded-l-full border border-input bg-neutral-200 px-2 py-0.5 text-xs text-primary-900 max-lg:rounded-full",
              specialRoute && !isScrolled && getSuperCategoryBgColor(category),

              !isScrolled && getHeaderBorderColor(pathname),
              specialRoute &&
                !isScrolled &&
                "fill-gray-100 font-medium text-gray-100 shadow-md", // Additional styles if needed
            )}
          >
            <LocationPointerOutlinedIcon
              className={cn(
                "w-4 md:w-5",
                specialRoute && !isScrolled && "fill-gray-100",
              )}
            />

            {selectedCity.city_name ? (
              <Typography as={"p"} className='min-w-4 text-bt4'>
                {selectedCity.city_name}{" "}
              </Typography>
            ) : (
              <Skeleton className='h-4 w-16 bg-gray-200' />
            )}
            <ChevronDownIcon className='w-3 font-bold md:w-4' />
          </button>

          <CartItems setShowCart={setCartOpen} showCart={isCartOpen} />
          <div className='profile items-center justify-center gap-1 p-0 sm:flex'>
            <Button
              onClick={() => setIsProfileOpen(true)}
              variant='normal'
              className={cn(
                "h-8 min-h-8 w-8 min-w-8 cursor-pointer rounded-full border-2 border-solid border-neutral-200 p-0.5",
                specialRoute && !isScrolled
                  ? // ? 'bg-gray-100 fill-neutral-900 hover:bg-gray-200'
                    "bg-neutral-900 text-gray-100 hover:bg-neutral-950"
                  : "bg-neutral-900 text-gray-100 hover:bg-neutral-950",
              )}
            >
              <UserOutlinedIcon />
            </Button>
          </div>
        </div>
      </div>

      {/* date select  */}

      {/* don't remove the bottom comment out the below span as it solves tailwind color loading issue */}

      {/* <span className='border-category-orange border-category-green border-category-purple border-category-red'></span> */}
      {/* {'DONT REMOVE THE ABOVE SPAN'} */}
      <div
        className={cn(
          "flex h-[34px] w-full items-center justify-between gap-1 rounded-full border-2 border-neutral-200 bg-gray-100",
          !isScrolled && getHeaderBorderColor(pathname),
        )}
      >
        <div className='flex w-max items-center justify-center gap-2 rounded-radius px-2 text-sm'>
          <div className='delivery-date flex items-center justify-center gap-1 px-0 text-xs font-semibold lg:text-sm'>
            <CalendarAddFilledIcon className='mx-1 w-4' />

            {delivery_date && pickup_date ? (
              <Typography as={"p"} className='text-sh5 text-neutral-700'>
                <span className='text-neutral-400'>Rent For: </span>
                {formatDateWithOrdinal(delivery_date)} •{" "}
                {formatDateWithOrdinal(pickup_date)}
              </Typography>
            ) : (
              <Typography as={"span"} className='text-sh5 text-neutral-700'>
                {/* Select Delivery & Pickup Dates */}
                Select Rental Dates
              </Typography>
            )}
          </div>
        </div>

        <Button
          onClick={openCalendar}
          className='add-date h-full gap-1 px-2 py-[6px] pr-3 hover:bg-primary-900 max-lg:text-xs'
          // className="add-date h-full border-2 border-gray-100 px-2 py-[5px] max-lg:text-xs"
        >
          {/* <Calendar1Icon className="w-5" /> */}
          {delivery_date && pickup_date ? (
            <>
              <CalendarEditFilledIcon className='min-h-3 min-w-3' />
              Edit
            </>
          ) : (
            <>
              <CalendarAddFilledIcon className='min-h-3 min-w-3' />
              Select
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
