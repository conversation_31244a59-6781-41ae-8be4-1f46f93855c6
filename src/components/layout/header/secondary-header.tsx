"use client"
import Link from "next/link"

// import type { LucideIcon } from 'lucide-react'
import { SharePalLogo } from "@/components/main/logo"
import { useHeaderLogic } from "@/hooks/use-header"
import HeaderPartnerDetails from "./header-partner"
import { Suspense } from "react"
import { OnboardingFlow } from "@/components/onboarding"

interface CheckoutHeaderProps {
  text?: string
  //   icon?: LucideIcon
}

export const SecondaryHeader = ({ text }: CheckoutHeaderProps) => {
  const { selectedCity } = useHeaderLogic()
  return (
    <>
      <Suspense>
        <HeaderPartnerDetails />
      </Suspense>
      <OnboardingFlow />
      <header className='container absolute left-0 right-0 top-0 z-50 flex w-full flex-row items-center justify-center gap-1 overflow-hidden'>
        {/* Desktop Header */}
        <div className='desktop hidden w-full items-end justify-between gap-1 md:flex'>
          {/* Logo - Left */}
          <div className='flex items-end justify-start'>
            <Link
              href={selectedCity ? `/${selectedCity.city_url}` : "/"}
              className='left h-full flex-[1]'
            >
              <div className='logo flex h-[68px] w-40 flex-col items-center justify-end gap-1 rounded-bl-2xl rounded-br-2xl bg-primary-500 p-3 pt-[18px]'>
                <div className='flex items-center justify-center'>
                  <SharePalLogo />
                </div>
              </div>
            </Link>
          </div>

          {/* Text - Center */}
          <div className='flex-1 text-center'>
            <h1 className='text-2xl font-semibold'>{text}</h1>
          </div>

          {/* Icon - Right */}
          <div className='flex w-40 justify-end pb-3'>
            {/* {Icon && <Icon className="h-6 w-6 text-gray-600" />} */}
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='32'
              height='32'
              fill='none'
              viewBox='0 0 32 32'
            >
              <path
                fill='#5D6171'
                d='M15.999 29.333q-4.635-1.167-7.651-5.317Q5.33 19.865 5.332 14.8V6.667l10.667-4 10.666 4V14.8q0 5.067-3.016 9.217T16 29.333m-2.667-8h5.333q.567 0 .951-.384t.383-.95v-4q0-.565-.384-.949a1.3 1.3 0 0 0-.95-.384v-1.333q0-1.099-.782-1.883a2.57 2.57 0 0 0-1.884-.784q-1.102-.001-1.883.784a2.58 2.58 0 0 0-.784 1.883v1.333q-.567 0-.95.384A1.3 1.3 0 0 0 12 16v4q0 .567.384.95.384.384.949.383m1.333-6.666v-1.334q0-.567.384-.95.384-.381.95-.383.565-.002.95.384.386.385.383.95v1.332z'
              ></path>
            </svg>
          </div>
        </div>

        {/* Mobile Header */}
        <div className='flex w-full items-end justify-between md:hidden'>
          {/* Logo */}
          <Link
            href={selectedCity ? `/${selectedCity.city_url}` : "/"}
            className='logo flex h-10 flex-col items-center justify-end gap-1 rounded-bl-xl rounded-br-xl bg-primary-500 px-3 pb-1 pt-3 shadow-sm'
          >
            <div className='flex w-full max-w-28 items-center justify-center'>
              <SharePalLogo />
            </div>
          </Link>

          {/* Text and Icon */}
          <div className='flex items-center gap-3'>
            <span className='text-lg font-semibold'>{text}</span>
            {/* {Icon && <Icon className="h-5 w-5 text-gray-600" />} */}
            <svg
              xmlns='http://www.w3.org/2000/svg'
              width='32'
              height='32'
              fill='none'
              viewBox='0 0 32 32'
            >
              <path
                fill='#5D6171'
                d='M15.999 29.333q-4.635-1.167-7.651-5.317Q5.33 19.865 5.332 14.8V6.667l10.667-4 10.666 4V14.8q0 5.067-3.016 9.217T16 29.333m-2.667-8h5.333q.567 0 .951-.384t.383-.95v-4q0-.565-.384-.949a1.3 1.3 0 0 0-.95-.384v-1.333q0-1.099-.782-1.883a2.57 2.57 0 0 0-1.884-.784q-1.102-.001-1.883.784a2.58 2.58 0 0 0-.784 1.883v1.333q-.567 0-.95.384A1.3 1.3 0 0 0 12 16v4q0 .567.384.95.384.384.949.383m1.333-6.666v-1.334q0-.567.384-.95.384-.381.95-.383.565-.002.95.384.386.385.383.95v1.332z'
              ></path>
            </svg>
          </div>
        </div>
      </header>
    </>
  )
}
