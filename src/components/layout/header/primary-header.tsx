"use client"

import { IconWhatsapp } from "@/components/Icons"
import CityDialog from "@/components/main/select-city"
import { UserProfileSheet } from "@/components/modals/user-profile-sheet"
import WhatsappSupportModal from "@/components/modals/whatsapp-support"
import { OnboardingFlow } from "@/components/onboarding"
import { Button } from "@/components/ui/button"
import { useHeaderLogic } from "@/hooks/use-header"
import useScrollState from "@/hooks/use-scroll-state"
import { cn } from "@/lib/utils"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useCalendarStore } from "@/store/rental-store"
import { City } from "@/types/address"
import { AnimatePresence, motion, useScroll, useTransform } from "framer-motion"
import React, { Suspense } from "react"
import CartButton from "../cart-button"
import { HeaderDesktop } from "./header-desktop"
import { HeaderMobile } from "./header-mobile"
import HeaderPartnerDetails from "./header-partner"

interface IHeaderProps {
  sticky?: boolean
  stickyThreshold?: number
  cities: City[]
}

const Header = ({
  sticky = false,
  stickyThreshold = 200,
  cities,
}: IHeaderProps) => {
  const {
    isWhatsappSupportModalOpen,
    setIsWhatsappSupportModalOpen,
    isCityModalOpen,
    setCityModalOpen,
    isProfileOpen,
    setIsProfileOpen,
    delivery_date,
    pickup_date,
    selectedCity,
    isCartOpen,
    setCartOpen,
    user,
    isLoggedIn,
    setIsLoggedIn,
    items_count,
    cat,

    specialRoute,
  } = useHeaderLogic()

  const { openModal } = useOnboardingStore()
  const { openCalendar } = useCalendarStore()

  const { scrollY } = useScroll()

  // Transform the header's background opacity based on scroll
  // const headerBackgroundOpacity = useTransform(
  //   scrollY,
  //   [0, stickyThreshold],
  //   [0, 1],
  // )

  // Transform the header's backdrop blur based on scroll
  const headerBackdropBlur = useTransform(scrollY, [0, stickyThreshold], [0, 8])

  const isScrolled = useScrollState(600) // Now
  const isHeaderScrolled = useScrollState(80) // Now

  // console.log('isScrolled:', isScrolled)
  // console.log('primary header re render')

  return (
    <>
      <CityDialog
        isOpen={isCityModalOpen}
        onOpenChange={setCityModalOpen}
        cities={cities}
      />
      <Suspense>
        <HeaderPartnerDetails />
      </Suspense>
      <CartButton setCartOpen={setCartOpen} />

      <UserProfileSheet
        isOpen={isProfileOpen}
        setIsProfileOpen={setIsProfileOpen}
      />

      <AnimatePresence>
        <motion.header
          className={cn(
            "absolute left-0 right-0 top-0 z-50 flex h-max w-full flex-col items-center justify-center gap-1 overflow-hidden pb-3 opacity-100 transition-all duration-500 md:pb-4 lg:flex-row",
            sticky ? "sticky top-0 bg-gray-100 shadow-md" : "fixed top-0 z-50",
            // !product && 'fixed top-0 z-50',
            specialRoute &&
              isScrolled &&
              "-translate-y-[100%] opacity-0 md:p-0",
            isHeaderScrolled && "bg-gray-50",
          )}
          transition={{ duration: 0.2 }}
          style={{
            // backgroundColor: useTransform(
            //   headerBackgroundOpacity,
            //   (opacity) => `hsla(210, 17%, 96%, ${opacity})`,
            //   // (opacity) => `rgba(255, 255, 255, ${opacity})`,
            // ),
            backdropFilter: useTransform(
              headerBackdropBlur,
              (blur) => `blur(${blur}px)`,
            ),
            // boxShadow: useTransform(
            //   headerShadowOpacity,
            //   (opacity) => `0 4px 6px rgba(0, 0, 0, ${opacity})`,
            // ),
          }}
        >
          <HeaderDesktop
            selectedCity={selectedCity}
            delivery_date={delivery_date}
            pickup_date={pickup_date}
            openCalendar={openCalendar}
            openModal={openModal}
            isLoggedIn={isLoggedIn}
            setIsLoggedIn={setIsLoggedIn}
            isCartOpen={isCartOpen}
            setCartOpen={setCartOpen}
            user={user}
            setCityModalOpen={setCityModalOpen}
            setIsProfileOpen={setIsProfileOpen}
            specialRoute={specialRoute}
            category={cat}
            items_count={items_count}
          />
          <HeaderMobile
            selectedCity={selectedCity}
            delivery_date={delivery_date}
            pickup_date={pickup_date}
            openCalendar={openCalendar}
            openModal={openModal}
            isLoggedIn={isLoggedIn}
            setIsLoggedIn={setIsLoggedIn}
            isCartOpen={isCartOpen}
            setCartOpen={
              setCartOpen as React.Dispatch<React.SetStateAction<boolean>>
            }
            user={user}
            setCityModalOpen={setCityModalOpen}
            specialRoute={specialRoute}
            setIsProfileOpen={setIsProfileOpen}
            category={cat}
          />
          {/* onboarding is currenty in primary header when needed we can move this to layout itself */}
          <OnboardingFlow />
        </motion.header>
      </AnimatePresence>

      <Button
        onClick={() => setIsWhatsappSupportModalOpen(true)}
        className='fixed bottom-20 right-3 z-50 h-12 w-12 rounded-full bg-secondary-600 p-2 hover:bg-secondary-500 md:bottom-8 md:right-5 md:min-h-16 md:min-w-16'
      >
        <IconWhatsapp className='scale-125 md:scale-150' />
      </Button>

      <WhatsappSupportModal
        openView={isWhatsappSupportModalOpen}
        setOpenView={setIsWhatsappSupportModalOpen}
      />
    </>
  )
}

export default Header
