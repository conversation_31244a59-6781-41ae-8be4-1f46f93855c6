"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import React from "react"

import CartItems from "@/components/modals/cart"
import { Typography } from "@/components/ui/typography"
import useScrollState from "@/hooks/use-scroll-state"
import { cn } from "@/lib/utils"
import { useSearchStore } from "@/store/search-store"
import { City } from "@/types/address"
import { User } from "@/types/user"
import { formatDateWithOrdinal } from "@/utils/date-logics"
import { getHeaderBorderColor } from "@/utils/get-bg-color"
import { usePathname } from "next/navigation"
import {
  CalendarAddFilledIcon,
  CalendarEditFilledIcon,
  CalendarEndFilledIcon,
  CalendarStartFilledIcon,
  ChevronDownIcon,
  LocationPointerOutlinedIcon,
  SearchOutlinedIcon,
  ShoppingCartOutlinedIcon,
  UserOutlinedIcon,
} from "sharepal-icons"
import { SharePalLogo } from "../../main/logo"
import { Skeleton } from "../../ui/skeleton"

interface HeaderDesktopProps {
  selectedCity: City
  delivery_date: Date | null
  openModal: () => void
  pickup_date: Date | null
  isLoggedIn: boolean
  setIsLoggedIn: (isTrue: boolean) => void
  isCartOpen: boolean
  setCartOpen: (isOpen: boolean) => void
  openCalendar: () => void
  setCityModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  setIsProfileOpen: React.Dispatch<React.SetStateAction<boolean>>

  user: User | null
  specialRoute?: boolean
  items_count: number
  category?: string
}

export const HeaderDesktop: React.FC<HeaderDesktopProps> = ({
  selectedCity,
  delivery_date,
  // openModal,
  pickup_date,
  isLoggedIn,
  // setIsLoggedIn,
  isCartOpen,
  setCartOpen,
  setCityModalOpen,
  user,
  openCalendar,
  specialRoute,
  setIsProfileOpen,
  items_count,
}) => {
  const isScrolled = useScrollState(200) // Now
  const { setIsSearchOpen } = useSearchStore()
  const pathname = usePathname()

  // const { scrollY } = useScroll()
  // const [isScrolled, setIsScrolled] = useState(false)

  // useEffect(() => {
  //   const unsubscribe = scrollY.on('change', (latest) => {
  //     setIsScrolled(latest > 200) // Change threshold as needed
  //   })

  //   return () => unsubscribe() // Cleanup on unmount
  // }, [scrollY]) // Run effect when `scrollY` changes

  // console.log("desktop header re render")

  return (
    <div
      className={cn(
        "desktop container hidden w-full items-end justify-between gap-1 transition-all lg:flex",
      )}
    >
      {/* left */}
      <div className='flex items-end justify-start gap-28'>
        <Link
          href={selectedCity ? `/${selectedCity.city_url}` : "/"}
          className='left h-full flex-[1]'
          prefetch={true}
        >
          <div className='logo flex h-[68px] w-40 flex-col items-center justify-end gap-1 rounded-bl-2xl rounded-br-2xl bg-primary-500 p-3 pt-[18px] shadow-sm'>
            <div className='flex items-center justify-center'>
              <SharePalLogo />
            </div>
          </div>
        </Link>
      </div>

      {/* don't remove the bottom comment out the below span as it solves tailwind color loading issue */}

      {/* <span className='border-category-orange border-carepal-dark  border-category-green border-category-purple border-category-red'></span> */}
      {/* {'DONT REMOVE THE ABOVE SPAN'} */}

      {/* middle part */}
      <div
        className={cn(
          "middle relative flex items-center justify-center gap-2 rounded-full border-2 border-neutral-200 bg-gray-100",
          !isScrolled && getHeaderBorderColor(pathname),
        )}
      >
        <button
          onClick={(e) => {
            e.preventDefault()
            setCityModalOpen(true)
          }}
          type='button'
          className={cn(
            "city flex items-center justify-center gap-1 rounded-l-full bg-neutral-200 p-1.5 px-[10px] py-[6px] text-sm font-semibold text-primary-900 hover:bg-neutral-250 max-lg:translate-x-5 max-lg:rounded-full max-lg:text-xs",
          )}
        >
          {" "}
          {/* <IconMap className={'w-5'} /> {selectedCity.city_name ?? "Bangalore"}{' '} */}
          <LocationPointerOutlinedIcon className={"w-5"} />
          {selectedCity.city_name ? (
            <Typography as={"p"} className='min-w-16 text-bt3'>
              {selectedCity.city_name}{" "}
            </Typography>
          ) : (
            <Skeleton className='h-5 w-24' />
          )}
          <ChevronDownIcon className='h-4 w-4 font-bold' />
        </button>

        <div
          onClick={openCalendar}
          aria-label='Edit Dates'
          className='flex w-max cursor-pointer items-center justify-center gap-2 bg-gray-100 text-neutral-700'
        >
          <div className='delivery-date flex items-center justify-center gap-2 text-sh5'>
            <CalendarStartFilledIcon className='h-4 w-4' />{" "}
            {delivery_date ? (
              <p>
                <span className='font-medium'> Delivery Date: </span>{" "}
                {formatDateWithOrdinal(delivery_date)}
              </p>
            ) : (
              "Delivery Date"
            )}
          </div>
          <span className='h-5 w-[2] bg-neutral-200'></span>
          <div className='pickup-date flex items-center justify-center gap-2 text-sh5'>
            <CalendarEndFilledIcon className='h-4 w-4' />{" "}
            {/* {pickup_date ? formatDate(pickup_date) : 'Add Pickup Date'} */}
            {pickup_date ? (
              <p>
                <span className='font-medium'> Pickup Date: </span>{" "}
                {formatDateWithOrdinal(pickup_date)}
              </p>
            ) : (
              "Pickup Date"
            )}
          </div>
        </div>
        <Button
          onClick={openCalendar}
          // className="add-date h-full border-2 border-gray-100 px-3"
          className='h-full gap-1 px-3 py-[8px] !text-bt3 hover:bg-primary-900'
        >
          {delivery_date && pickup_date ? (
            <>
              <CalendarEditFilledIcon className='h-5 w-5' />
              <p className='pr-1 font-semibold leading-5 tracking-wide'>Edit</p>
            </>
          ) : (
            <>
              {/* <CalendarPlus className="h-4 w-4" /> */}
              <CalendarAddFilledIcon className='size-4' />
              <p className='pr-1 font-semibold leading-5 tracking-wide'>
                Select
              </p>
            </>
          )}{" "}
        </Button>
      </div>

      {/* right  */}
      <div
        className={cn(
          "right flex items-end justify-end gap-3 transition-colors duration-300",
          specialRoute && !isScrolled
            ? "fill-gray-100 text-gray-100"
            : "fill-gray-900 text-gray-900",
        )}
      >
        <Button
          onClick={() => {
            setIsSearchOpen(true)
          }}
          variant='ghost'
          size={"icon"}
          className={cn(
            "search relative h-11 w-11 p-2",
            specialRoute && !isScrolled
              ? "text-gray-100 hover:bg-neutral-150 hover:text-neutral-900"
              : "text-neutral-900",
          )}
        >
          <SearchOutlinedIcon className='min-h-7 lg:min-w-7' />
        </Button>
        <Button
          onClick={() => setCartOpen(true)}
          variant='ghost'
          className={cn(
            "cart relative h-11 w-11 p-2",
            specialRoute && !isScrolled
              ? "text-gray-100 hover:bg-neutral-150 hover:text-neutral-900"
              : "text-neutral-900",
          )}
          size={"icon"}
        >
          <ShoppingCartOutlinedIcon className='min-h-7 lg:min-w-7' />
          {items_count > 0 && (
            <span className='absolute right-1 top-1 flex h-3 w-3 items-center justify-center rounded-full border-neutral-150 bg-red-500 text-[8px] font-bold text-gray-100 md:h-4 md:w-4'>
              {items_count}
            </span>
          )}
        </Button>
        <CartItems
          setShowCart={
            setCartOpen as React.Dispatch<React.SetStateAction<boolean>>
          }
          showCart={isCartOpen}
        />
        <div
          onClick={() => setIsProfileOpen(true)}
          className='profile flex cursor-pointer items-center justify-end gap-3'
        >
          <Button
            className={cn(
              "flex h-11 w-11 cursor-pointer items-center justify-center rounded-full border-2 border-solid border-neutral-200 p-0.5 transition-colors duration-300",
              specialRoute && !isScrolled
                ? "bg-gray-100 text-neutral-900 hover:bg-gray-200"
                : "bg-neutral-900 text-gray-100 hover:bg-neutral-950",
              !isScrolled && getHeaderBorderColor(pathname),
            )}
          >
            <UserOutlinedIcon className='min-h-6 min-w-6 fill-inherit' />
          </Button>

          {isLoggedIn ? (
            <span className='!text-bt2 font-medium'>
              Hi, {user?.first_name}
            </span>
          ) : (
            <span className='!text-bt2 font-medium'>Hi, Login</span>
          )}
        </div>
      </div>
    </div>
  )
}
