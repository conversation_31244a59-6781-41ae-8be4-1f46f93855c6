"use client"
import { fetchPartner } from "@/actions/surge"
import { getCookie, setCookie } from "@/functions/cookies"
import { useRentalStore } from "@/store/rental-store"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import { useEffect } from "react"

const COOKIE_EXPIRY_DAYS = 5

const partner_cookie_name = "partner" as const
const utm_cookie_name = "utm_source" as const
const utm_campaign_cookie_name = "utm_campaign" as const
const utm_medium_cookie_name = "utm_medium" as const
const selected_city_cookie_name = "selected_city" as const

const HeaderPartnerDetails = () => {
  const { setPartner, partner, selectedCity } = useRentalStore()
  const pathname = usePathname()
  const params = useSearchParams()
  const router = useRouter()

  useEffect(() => {
    const city_cookie = getCookie(selected_city_cookie_name)

    if (
      pathname === "/" &&
      city_cookie != undefined &&
      city_cookie != "undefined" &&
      (city_cookie || selectedCity?.city_url)
    ) {
      router.replace(`/${city_cookie || selectedCity?.city_url}`, {
        scroll: false,
      })
    }
    if (selectedCity && !city_cookie) {
      setCookie("selected_city", selectedCity?.city_url, { expires: 365 })
    }
  }, [pathname, router, selectedCity]) // Only runs when pathname or router changes

  useEffect(() => {
    const partner_cookie =
      params.get(partner_cookie_name) || getCookie(partner_cookie_name)
    const utm_source = params.get(utm_cookie_name) || getCookie(utm_cookie_name)
    const utm_campaign =
      params.get(utm_campaign_cookie_name) ||
      getCookie(utm_campaign_cookie_name)
    const utm_medium =
      params.get(utm_medium_cookie_name) || getCookie(utm_medium_cookie_name)

    if (partner_cookie)
      setCookie(partner_cookie_name, partner_cookie, {
        expires: COOKIE_EXPIRY_DAYS,
      })
    if (utm_source)
      setCookie(utm_cookie_name, utm_source, { expires: COOKIE_EXPIRY_DAYS })
    if (utm_campaign)
      setCookie(utm_campaign_cookie_name, utm_campaign, {
        expires: COOKIE_EXPIRY_DAYS,
      })
    if (utm_medium)
      setCookie(utm_medium_cookie_name, utm_medium, {
        expires: COOKIE_EXPIRY_DAYS,
      })
  }, [params]) // Runs when query params change

  useEffect(() => {
    const partner_cookie =
      params.get(partner_cookie_name) || getCookie(partner_cookie_name)

    const fetchData = async () => {
      if (partner_cookie && !partner) {
        const response = await fetchPartner(partner_cookie)
        setPartner(response)
      }
    }
    fetchData()
  }, [params, partner, setPartner])
  return null
}

export default HeaderPartnerDetails
