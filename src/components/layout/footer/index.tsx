import {
  IconCall,
  IconFacebook,
  IconInstagram,
  IconLinkedIn,
  IconMail,
} from "@/components/Icons"

import { fetchFooterLinks } from "@/actions/category"
import FooterFixSeo from "./footer-fix-seo"
import FooterInfo from "./footer-info"
import { DeskTopFooterLinks, MobileFooterLinks } from "./footer-links"

export const icons = {
  call: <IconCall />,
  mail: <IconMail />,
  facebook: <IconFacebook />,
  instagram: <IconInstagram />,
  linkedin: <IconLinkedIn />,
}

const Footer = async () => {
  // const categories = await fetchCategories()
  // //fetch subcategories for each category
  // const subcategories = await Promise.all(
  //   categories.map((category) => fetchSubCategoriesFooter(category.url)),
  // )
  const footerLinks = await fetchFooterLinks()
  // console.log(subcategories)
  //create a new array of objects with combined caategory and subcateogy object for each category
  // const combinedCategories = categories.map((category, index) => ({
  //   ...category,
  //   subcategories: subcategories[index],
  // }))

  return (
    <>
      {/* Mobile */}
      <footer className='bg-primary-900 py-5 pb-16 md:py-[72px] md:pb-10'>
        <div className='container mx-auto flex w-full flex-col gap-5 md:gap-12'>
          {/* Categoires */}

          {/* Categories Desktop */}
          <DeskTopFooterLinks combinedCategories={footerLinks} />

          {/* Categories Mobile */}
          <MobileFooterLinks combinedCategories={footerLinks} />
          {/* Seo Content */}
          <FooterFixSeo showPortal={true} />
          {/* Information */}
          <FooterInfo />
        </div>
      </footer>
    </>
  )
}

export default Footer
