"use client"

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { useRentalStore } from "@/store/rental-store"
import { Category, SubCategory } from "@/types"
import Link from "next/link"

type Props = {
  combinedCategories: {
    category: Category
    sub_categories: SubCategory[]
  }[]
}

export const DeskTopFooterLinks = ({ combinedCategories }: Props) => {
  const { selectedCity } = useRentalStore()
  return (
    <div className='grid gap-6 max-md:hidden md:grid-cols-4 md:gap-10 lg:grid-cols-5'>
      {combinedCategories.map((category, index) => (
        <div
          key={index}
          // className="flex  w-[180px] max-w-[180px] flex-col gap-4"
          className='flex w-full flex-col gap-4'
        >
          <h1 className='line-clamp-2 h-max text-lg font-[600] text-gray-100'>
            {category.category.category_short_name}
          </h1>
          {category.sub_categories.slice(0, 6).map((subcategory, index) => (
            <Link
              href={`/${selectedCity.city_url}/${subcategory.super_category_url}/${subcategory.url}`}
              key={index}
              className='text-b6 text-neutral-300 transition-all duration-75 hover:text-neutral-200 hover:underline md:text-b4'
            >
              {subcategory.sc_name}
            </Link>
          ))}
        </div>
      ))}
    </div>
  )
}
export const MobileFooterLinks = ({ combinedCategories }: Props) => {
  const { selectedCity } = useRentalStore()

  return (
    <div className='flex gap-6 md:hidden'>
      <Accordion
        type='single'
        collapsible
        className='flex w-full flex-col gap-3 md:gap-5'
      >
        {combinedCategories.map((category, index) => (
          <AccordionItem
            className='border-0'
            key={index}
            value={`index-${index}`}
          >
            <AccordionTrigger className='rounded-[8px] bg-primary-800 px-3 py-3 text-xs font-[600] text-gray-100 hover:no-underline'>
              {category?.category?.category_short_name}
            </AccordionTrigger>
            <AccordionContent className='flex flex-col gap-3 rounded-bl-[8px] rounded-br-[8px] bg-primary-900/50 p-2'>
              {category?.sub_categories.map((subcategory, index) => (
                <Link
                  href={`/${selectedCity.city_url}/${subcategory.super_category_url}`}
                  key={index}
                  className='text-xs text-neutral-300'
                >
                  {subcategory.sc_name}
                </Link>
              ))}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  )
}
