// 'use client'

// import React, { useState } from 'react'
// import { motion, AnimatePresence } from 'framer-motion'
// import useCommonDataStore from '@/store/use-common-data'
// import { IconChevonRight } from '../Icons'

// const Footerseo = () => {
//   const { seo } = useCommonDataStore()
//   const [isExpanded, setIsExpanded] = useState(false)

//   return (
//     <div className="text-neutral-300">
//       {/* Toggle Button */}

//       {/* Animated SEO Content */}
//       <AnimatePresence>
//         {isExpanded && (
//           <motion.div
//             className="text-justif mb-3 mt-2 whitespace-pre-line text-pretty text-sm"
//             initial={{ height: 0, opacity: 0 }}
//             animate={{ height: 'auto', opacity: 1 }}
//             exit={{ height: 0, opacity: 0 }}
//             transition={{ duration: 0.5, ease: 'easeInOut' }}
//           >
//             {seo.desc}
//             {/* {seo.desc.split('\n').map((line, index) => (
//               <p key={index} className="leading-relaxed">
//                 {line.includes('<b>') ? (
//                   <span className="font-bold">
//                     {line.replace(/<\/?b>/g, '')}
//                   </span>
//                 ) : line.includes('<a href=') ? (
//                   <a
//                     href={line.match(/href="([^"]*)"/)?.[1] || '#'}
//                     className="underline"
//                     target="_blank"
//                     rel="noopener noreferrer"
//                   >
//                     {line.replace(/<\/?a[^>]*>/g, '')}
//                   </a>
//                 ) : (
//                   line
//                 )}
//               </p>
//             ))} */}
//           </motion.div>
//         )}
//       </AnimatePresence>

//       <h2
//         className="flex cursor-pointer items-center text-sm text-neutral-200"
//         onClick={() => setIsExpanded(!isExpanded)}
//       >
//         {isExpanded ? 'Read Less' : 'Read More'} <IconChevonRight />
//       </h2>
//     </div>
//   )
// }

// export default Footerseo
