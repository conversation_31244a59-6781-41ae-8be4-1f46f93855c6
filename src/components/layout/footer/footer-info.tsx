import {
  IconChevonRight,
  IconLogoLeftBlue,
  IconLogoRight,
} from "@/components/Icons"
import { But<PERSON> } from "@/components/ui/button"
import { Typography } from "@/components/ui/typography"
import { contactInfo, footerurls } from "@/constants/footer-data"
import Link from "next/link"
import ScrollToTop from "./scroll-to-top"

const FooterInfo = () => (
  <div className='flex w-full flex-col gap-8'>
    {/*  */}
    <div className='flex h-10 w-full items-center bg-footer-logo'>
      <IconLogoLeftBlue className='w-24' />
      <IconLogoRight />
    </div>
    {/*  */}
    <div className='flex flex-col justify-between gap-6 md:flex-row md:gap-4'>
      <div className='flex w-full max-w-80 flex-col items-start justify-start gap-4'>
        <Typography as={"h6"} className='text-h7 text-gray-100'>
          About US
        </Typography>
        <Typography as={"p"} className='text-b7 text-neutral-300 md:text-b4'>
          SharePal is a rental startup for all your lifestyle needs. Be it
          travel, photography, entertainment or fitness, you can rent all the
          latest products.
        </Typography>
        <Button
          asChild
          className='h-9 gap-1 text-sm md:h-11'
          variant={"primary"}
        >
          <Link href='/about-us'>
            Know More <IconChevonRight />
          </Link>
        </Button>
      </div>
      <div className='grid w-full grid-cols-2 gap-x-3 gap-y-6 md:justify-between md:gap-3 lg:grid-cols-4'>
        {/* Regular footer sections */}
        {footerurls.slice(0, -1).map((footer, index) => (
          <div key={index}>
            <h2 className='mb-3 min-w-max text-sh4 text-gray-100 md:mb-6'>
              {footer.title}
            </h2>
            <div className='flex min-w-max flex-col gap-1 text-xs font-[500] text-neutral-300'>
              {footer.urls.map((url, index) => (
                <Link
                  key={index}
                  href={"/" + url.url}
                  className='decora w-full min-w-max py-1.5 text-b6 decoration-transparent transition-all duration-300 hover:decoration-white md:py-3 md:text-b4'
                >
                  {url.title}
                </Link>
              ))}
            </div>
          </div>
        ))}

        {/* Need Help section */}
        <div>
          <h2 className='mb-3 min-w-max text-sh4 text-gray-100 md:mb-6'>
            Need Help
          </h2>
          <div className='flex min-w-max flex-col gap-2 text-xs font-[500] text-neutral-300'>
            {/* Phone */}
            <Link
              href={`tel:${contactInfo.phone.number}`}
              className='flex items-center gap-2 py-1.5 transition-colors duration-300 hover:text-white md:py-3'
            >
              {contactInfo.phone.icon}
              <span className='text-b6 md:text-b4'>
                {contactInfo.phone.formattedNumber}
              </span>
            </Link>

            {/* Email */}
            <Link
              href={`mailto:${contactInfo.email.address}`}
              className='flex items-center gap-2 py-1.5 transition-colors duration-300 hover:text-white md:py-3'
            >
              {contactInfo.email.icon}
              <span className='text-b6 md:text-b4'>
                {contactInfo.email.address}
              </span>
            </Link>

            {/* Social Media */}
            <div className='flex items-center gap-3 py-1.5 md:py-3'>
              {contactInfo.social.map((platform, index) => (
                <Link
                  key={index}
                  href={platform.url}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='transition-opacity duration-300 hover:opacity-80'
                  aria-label={`Visit SharePal on ${platform.platform}`}
                >
                  {platform.icon}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
    {/*  */}
    <div className='flex w-full items-center justify-between gap-4 border-t border-primary-700 py-6 text-sm font-[500] text-primary-300 max-md:flex-col'>
      <ScrollToTop />
      <p>© {new Date().getFullYear()}. SWNAC E-Kiraya Services Pvt Ltd</p>
      <p>Made with ♥️ for India</p>
    </div>
  </div>
)

export default FooterInfo
