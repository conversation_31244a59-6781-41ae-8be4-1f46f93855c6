"use client"
import {
  IconCall,
  IconFacebook,
  IconInstagram,
  IconLinkedIn,
  IconMail,
} from "@/components/Icons"
import { cn } from "@/lib/utils"
import { usePathname } from "next/navigation"

import FooterFixSeo from "./footer-fix-seo"
import FooterInfo from "./footer-info"

export const icons = {
  call: <IconCall />,
  mail: <IconMail />,
  facebook: <IconFacebook />,
  instagram: <IconInstagram />,
  linkedin: <IconLinkedIn />,
}

const SecondaryFooter = () => {
  const pathname = usePathname()
  const isCheckoutPage = pathname.includes("/checkout")
  const isReturnOrderPage = pathname.includes("/return-order")
  const isOrderDetailsPage = pathname.match(/\/orders\/[^/]+$/)

  const shouldHideOnMobile =
    isCheckoutPage || isOrderDetailsPage || isReturnOrderPage

  return (
    <footer
      className={cn(
        "bg-primary-900 px-1 py-5 md:py-[72px]",
        // Hide on mobile for checkout and order details pages
        shouldHideOnMobile ? "hidden md:block" : "",
      )}
    >
      <div className='container mx-auto flex w-full flex-col gap-12'>
        {/* SEO */}
        <FooterFixSeo showPortal={false} />
        {/* Information */}
        <FooterInfo />
      </div>
    </footer>
  )
}

export default SecondaryFooter
