"use client"

import { IconChevonRight } from "@/components/Icons"
import { AnimatePresence, motion } from "framer-motion"
import { useEffect, useState } from "react"
import { createPortal } from "react-dom"

interface FooterSeoPortalProps {
  children: React.ReactNode
}

const FooterSeoPortal: React.FC<FooterSeoPortalProps> = ({ children }) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [portalElement, setPortalElement] = useState<HTMLElement | null>(null)

  useEffect(() => {
    setPortalElement(document.getElementById("footer-seo-portal"))
  }, [])

  const toggleExpansion = () => setIsExpanded((prev) => !prev)

  const content = (
    <div className='flex flex-col gap-2'>
      <AnimatePresence initial={false}>
        <motion.div
          className='overflow-hidden'
          initial={{ height: 260 }}
          animate={{
            height: isExpanded ? "auto" : 260,
          }}
          exit={{ height: 260 }}
          transition={{ duration: 0.3, ease: "easeIn" }}
        >
          <div className='text-pretty text-sm text-neutral-300'>{children}</div>
        </motion.div>
      </AnimatePresence>

      <button
        onClick={toggleExpansion}
        className='flex items-center gap-1 text-bt4 text-neutral-200 transition-colors hover:text-neutral-100'
        aria-expanded={isExpanded}
        aria-controls='seo-content'
      >
        {isExpanded ? "Read Less" : "Read More"}
        <motion.span
          animate={{ rotate: isExpanded ? -90 : 90 }}
          transition={{ duration: 0.3 }}
        >
          <IconChevonRight className='h-4 w-4' />
        </motion.span>
      </button>
    </div>
  )

  if (!portalElement) return null

  return createPortal(content, portalElement)
}

export default FooterSeoPortal
