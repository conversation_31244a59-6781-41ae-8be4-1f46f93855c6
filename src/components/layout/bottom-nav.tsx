"use client"

import { cn } from "@/lib/utils"
import { AnimatePresence, motion } from "framer-motion"
import { Home, LayoutGrid, Search } from "lucide-react"
import { usePathname } from "next/navigation"
import { memo, useState } from "react"

import { useRentalStore } from "@/store/rental-store"
import { useSearchStore } from "@/store/search-store"
import dynamic from "next/dynamic"
import { ShoppingCartOutlinedIcon } from "sharepal-icons"

const MobileCategoryWrapper = dynamic(
  () => import("@/components/modals/mobile-category-wrapper"),
  { ssr: false },
)

// Reusable Nav Button Component
const NavButton = ({
  icon: Icon,
  label,
  isActive,
  onClick,
}: {
  icon: React.ElementType
  label: string
  isActive?: boolean
  onClick?: () => void
}) => (
  <button
    onClick={onClick}
    className={cn(
      "flex flex-1 flex-col items-center justify-center rounded-lg px-1 py-1.5 text-primary-900 transition-colors hover:text-primary-800",
      isActive && "text-primary-500",
    )}
  >
    <motion.div
      initial={{ scale: 1 }}
      animate={{ scale: isActive ? 1.1 : 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 15 }}
    >
      <Icon className='h-5 w-5' />
    </motion.div>
    <motion.span
      className='mt-0.5 text-[10px]'
      initial={{ opacity: 0.5 }}
      animate={{ opacity: isActive ? 1 : 0.5 }}
      transition={{ duration: 0.2 }}
    >
      {label}
    </motion.span>
  </button>
)

function BottomNav() {
  const pathname = usePathname()
  const [isCategoryOpen, setIsCategoryOpen] = useState(false)
  const { setIsSearchOpen } = useSearchStore()

  const { setCartOpen } = useRentalStore()

  return (
    <>
      <AnimatePresence>
        {isCategoryOpen && (
          <MobileCategoryWrapper
            isOpen={isCategoryOpen}
            onClose={() => setIsCategoryOpen(false)}
          />
        )}
      </AnimatePresence>

      <nav className='fixed bottom-0 left-0 right-0 z-20 flex border-t bg-background bg-gray-100 p-1.5 lg:hidden'>
        <div className='mx-auto flex w-full max-w-sm gap-0.5'>
          <NavButton
            icon={Home}
            label='Home'
            isActive={pathname === "/"}
            onClick={() => (window.location.href = "/")}
          />
          <NavButton
            icon={LayoutGrid}
            label='Category'
            isActive={isCategoryOpen}
            onClick={() => setIsCategoryOpen((prev) => !prev)}
          />
          <NavButton
            icon={Search}
            label='Search'
            onClick={() => setIsSearchOpen(true)}
          />
          <NavButton
            icon={ShoppingCartOutlinedIcon}
            label='Cart'
            onClick={() => setCartOpen(true)}
          />
        </div>
      </nav>
    </>
  )
}

export default memo(BottomNav)
