"use client"

import SpImage from "@/shared/SpImage/sp-image"
import { useCheckoutStore } from "@/store/checkout-store"
import { useCalendarStore, useRentalStore } from "@/store/rental-store"
import { formatDateWithOrdinal } from "@/utils/date-logics"
import { motion, useScroll, useTransform } from "framer-motion"
import { ArrowRight } from "lucide-react"
import { memo } from "react"
import {
  CalendarAddFilledIcon,
  CalendarEditFilledIcon,
  CalendarEndFilledIcon,
  CalendarStartFilledIcon,
  ThumbsUpFilledIcon,
} from "sharepal-icons"
import { Button } from "../ui/button"

interface CartButtonProps {
  setCartOpen: (open: boolean) => void
}

function CartButton({ setCartOpen }: CartButtonProps) {
  const { cart_items, items_count } = useCheckoutStore()
  const { total_days, delivery_date, pickup_date } = useRentalStore()
  const { scrollY } = useScroll()
  const { isOpen, openCalendar } = useCalendarStore()

  // Transform the opacity and y position based on scroll
  const opacity = useTransform(
    scrollY,
    // Start showing at 200px scroll, fully visible by 300px
    [0, 20, 30],
    [0, 0, 1],
  )

  const yPos = useTransform(
    scrollY,
    // Start moving up at 200px scroll, in position by 300px
    [0, 20, 30],
    [100, 100, 0],
  )

  if (!total_days)
    return (
      <motion.div
        style={{
          opacity,
          y: yPos,
        }}
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 100, opacity: 0 }}
        className='fixed bottom-20 left-4 right-4 z-[49] mx-auto flex max-w-max justify-center md:bottom-10'
      >
        <motion.button
          whileTap={{ scale: 0.95 }}
          // onClick={() => router.push('/cart')}
          // onClick={() => setCartOpen(true)}
          className='relative rounded-full border-2 border-secondary-500 bg-primary-900'
        >
          {isOpen ? (
            <div className='flex items-center justify-center gap-2 px-4 py-2'>
              <div className='flex w-max items-center justify-center gap-5 text-gray-100'>
                <div className='flex items-center justify-center gap-2 text-sh5'>
                  <CalendarStartFilledIcon className='h-4 w-4' />{" "}
                  <span className='font-medium'> Delivery Date: </span>{" "}
                  {delivery_date ? (
                    <p>{formatDateWithOrdinal(delivery_date)}</p>
                  ) : (
                    <p>--</p>
                  )}
                </div>
                <span className='h-5 w-[2] bg-neutral-200'></span>
                <div className='flex items-center justify-center gap-2 text-sh5'>
                  <CalendarEndFilledIcon className='h-4 w-4' />{" "}
                  <span className='font-medium'> Pickup Date: </span>{" "}
                  {pickup_date ? (
                    <p>{formatDateWithOrdinal(pickup_date)}</p>
                  ) : (
                    <p>--</p>
                  )}
                </div>
              </div>
              <Button
                onClick={openCalendar}
                // className="add-date h-full border-2 border-gray-100 px-3"
                className='bg-primary-800 !text-bt3 text-primary-300 max-md:hidden'
              >
                {delivery_date && pickup_date ? (
                  <>
                    <p className=''>Edit</p>
                    <CalendarEditFilledIcon className='h-5 w-5' />
                  </>
                ) : (
                  <>
                    {/* <CalendarPlus className="h-4 w-4" /> */}
                    <p className=''>Done</p>
                    <ThumbsUpFilledIcon className='size-4' />
                  </>
                )}{" "}
              </Button>
            </div>
          ) : (
            <div
              onClick={openCalendar}
              className='delivery-date flex items-center justify-center gap-2 px-[18px] py-[14px] text-sh5 text-gray-100'
            >
              <CalendarAddFilledIcon className='h-4 w-4' /> Select rental dates
              to view prices{" "}
            </div>
          )}
        </motion.button>
      </motion.div>
    )

  if (!items_count || items_count === 0) return null

  return (
    <motion.div
      // style={{
      //   opacity,
      //   y: yPos,
      // }}
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: 100, opacity: 0 }}
      className='fixed bottom-20 left-4 right-4 z-[49] mx-auto flex max-w-max justify-center md:bottom-10'
    >
      <motion.button
        whileTap={{ scale: 0.95 }}
        // onClick={() => router.push('/cart')}
        onClick={() => setCartOpen(true)}
        className='mx-auto flex w-max min-w-max max-w-48 items-center justify-between rounded-full border-2 border-secondary-600 bg-secondary-500 px-4 py-3 shadow-lg md:max-w-64'
        style={{
          boxShadow: "0 0 20px rgba(158, 255, 0, 0.3)",
        }}
      >
        <div className='flex items-center gap-3'>
          <div className='flex items-start gap-4'>
            {/* change space x for stacing effect */}
            <div className='flex -space-x-5 md:-space-x-4'>
              {cart_items.slice(0, 3).map((cart, index) => (
                <div
                  key={cart.id}
                  className='relative h-6 w-6 overflow-hidden rounded-md border-2 border-secondary-600 bg-gray-100 p-0.5 md:h-8 md:w-8 md:rounded-lg'
                  style={{ zIndex: items_count + index }}
                >
                  <SpImage
                    src={cart.cart_image}
                    alt={cart.cat_sname}
                    width={32}
                    height={32}
                    className='h-full w-full object-contain'
                  />
                </div>
              ))}

              {cart_items.length > 3 && (
                <div
                  className='relative flex h-6 w-6 items-center justify-center overflow-hidden rounded-md border-2 border-secondary-600 bg-gray-100 p-0.5 text-xs font-semibold text-secondary-700 md:h-8 md:w-8 md:rounded-lg md:text-sm'
                  style={{ zIndex: cart_items.length + 3 }}
                >
                  +{cart_items.length - 3}
                </div>
              )}
            </div>
          </div>
          {/* <div className="overflow-hidden rounded-md bg-gray-100 p-1">
              <Image
                src={thumbnailUrl || '/placeholder.svg'}
                alt="Cart item"
                width={24}
                height={24}
                className="h-6 w-6 object-cover"
              />
            </div> */}
          <span className='min-w-max text-base font-semibold text-black'>
            Go to Cart
          </span>
        </div>
        <ArrowRight className='ml-2 h-5 w-5 text-black' />
      </motion.button>
    </motion.div>
  )
}

export default memo(CartButton)
