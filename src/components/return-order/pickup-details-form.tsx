"use client"

import { PhoneInput } from "@/components/custom/phone-input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Typography } from "@/components/ui/typography"
import { useReturnOrderStore } from "@/store/return-order-store"
import { formVariants } from "@/utils/animation-variants"
import { formatDate } from "@/utils/date-logics"
import { generateTimeSlots } from "@/utils/time-slots"
import { zodResolver } from "@hookform/resolvers/zod"
import { motion } from "framer-motion"

import { HARD_CODED_IMAGE_URL } from "@/constants"
import { getDateDifference } from "@/functions/date"
import { moneyFormatter } from "@/functions/small-functions"
import SpImage from "@/shared/SpImage/sp-image"
import { getExpectedDateScheduleDate } from "@/utils/is-time-valid"
import {
  CalendarIcon,
  Loader2,
  MapPin,
  RefreshCwIcon,
  TruckIcon,
} from "lucide-react"
import { useEffect, useMemo } from "react"
import { useForm } from "react-hook-form"
import { InfoCircleOutlinedIcon, UserOutlinedIcon } from "sharepal-icons"
import { z } from "zod"
import ReturnOrderCharges from "./return-order-charges"

// Pickup time options with associated costs

const pickupDetailsSchema = z.object({
  pickup_time: z.string().min(1, "Please select a pickup time"),
  contact_person: z.string().min(1, "Contact person name is required"),
  phone_number: z.string().min(10, "Valid phone number is required"),
  country_code: z.number().default(91),
  address_id: z.string().optional(),
})

type PickupDetailsFormData = z.infer<typeof pickupDetailsSchema>

interface PickupDetailsFormProps {
  onConfirmAction: () => void
  onPrevAction: () => void
  isLoading?: boolean
  setOpenDeliveryAddress: (open: boolean) => void
  order: {
    id: string
    customerName: string
    phoneNumber: string
    deliveryAddress: string
    deliveryDate: Date
    pickupDate: Date
    total_rent: number
    main_order_id: number
    address_id: number
    total_perday_rent: number
  }
}

export function PickupDetailsForm({
  onConfirmAction,
  // onPrevAction,
  order,
  isLoading = false,
  setOpenDeliveryAddress,
}: PickupDetailsFormProps) {
  const {
    pickupData,
    setPickupData,
    setTimeSlotCharges,
    calculateTotals,
    totalCharges,
    paymentOption,
    setPaymentOption,
  } = useReturnOrderStore()

  const form = useForm<PickupDetailsFormData>({
    resolver: zodResolver(pickupDetailsSchema),
    defaultValues: {
      pickup_time: pickupData?.pickup_time || "",
      contact_person: pickupData?.contact_person || "",
      phone_number: pickupData?.phone_number || "",
      country_code: pickupData?.country_code || 91,
      address_id: pickupData?.address_id || "",
    },
  })

  const pickupTimeOptions = useMemo(
    () =>
      generateTimeSlots(
        true,
        new Date(
          (getDateDifference(new Date(order.pickupDate), new Date()) || 0) < 0
            ? order.pickupDate
            : new Date(),
        ),
        true,
        order.total_perday_rent,
      ),
    [order.pickupDate],
  )

  // const pickupTimeOptions = useMemo(
  //   () =>
  //     generateTimeSlots(
  //       true,
  //       new Date(order.pickupDate ?? Date.now()),
  //       true,
  //       order.total_perday_rent,
  //     ),
  //   [order.pickupDate],
  // )

  const expectedDate = getExpectedDateScheduleDate(
    Number(order.pickupDate),
    "pickup",
  )

  // const calculateDate = useCallback(() => {
  //   const dateDifference =
  //     getDateDifference(new Date(order.pickupDate), new Date()) || 0

  //   return new Date(dateDifference < 0 ? order.pickupDate : new Date())
  // }, [order.pickupDate]) // Dependencies array

  // Update form when pickupData changes
  // useEffect(() => {
  //   if (pickupData) {
  //     form.reset(pickupData)
  //   }
  // }, [pickupData, form])

  const selectedTimeOption = pickupTimeOptions.find(
    (option) => option.value === form.watch("pickup_time"),
  )

  const onSubmit = (_: PickupDetailsFormData) => {
    // Calculate pickup charges based on selected time
    // const selectedOption = pickupTimeOptions.find(
    //   (option) => option.value === data.pickup_time,
    // )
    // const pickup_charges = selectedOption?.cost || 0
    // const submissionData = {
    //   ...data,
    // }
    // console.log("Pickup Details Submitted:", submissionData)
    // setPickupData(submissionData)
    // setTimeSlotCharges(pickup_charges)
    // onConfirmAction()
  }

  //watch
  const pickupTimeChange = form.watch("pickup_time")
  const phoneNumberChange = form.watch("phone_number")

  //set pickup charges
  useEffect(() => {
    if (pickupData && form.getValues("phone_number").length == 10) {
      const selectedOption = pickupTimeOptions.find(
        (option) => option.value === form.getValues("pickup_time"),
      )
      const pickup_charges = selectedOption?.cost || 0

      setPickupData({
        contact_person: form.getValues("contact_person"),
        country_code: form.getValues("country_code"),
        phone_number: form.getValues("phone_number"),
        pickup_time: form.getValues("pickup_time"),
      })
      setTimeSlotCharges(pickup_charges)
      calculateTotals()
    }
  }, [pickupTimeChange, phoneNumberChange])

  return (
    <motion.div
      variants={formVariants}
      initial='hidden'
      animate='visible'
      className='space-y-4 px-2 pb-3 md:px-0'
    >
      <div className='mb-4 flex flex-col items-center justify-center gap-1.5 p-2 md:gap-3 lg:hidden'>
        <Typography
          as='h2'
          className='text-center text-sh2 font-semibold md:text-left'
        >
          Schedule Return
        </Typography>
        <Typography as='p' className='text-center text-b6 text-gray-500'>
          Select the Pickup Time and also Confirm the Pickup Details
        </Typography>
      </div>

      <Typography
        as='h2'
        className='flex items-center justify-start gap-2 text-sh4 font-semibold md:text-h6'
      >
        <TruckIcon className='size-4 text-primary-900 md:size-6' />
        Pickup Details:
      </Typography>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className='space-y-4 md:space-y-6'
        >
          {/* Pickup Time Selection */}
          <FormField
            control={form.control}
            name='pickup_time'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Select Pickup Time <span className='text-red-500'>*</span>
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className='rounded-xl py-5'>
                      <SelectValue placeholder='Select pickup time' />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {pickupTimeOptions.map((option) => (
                      <SelectItem
                        disabled={option.disabled}
                        key={option.value}
                        value={option.value}
                      >
                        <div className='flex w-full items-center justify-between'>
                          <span>{option.label}</span>
                          {option.cost > 0 && (
                            <span className='ml-2 text-sm font-medium text-destructive-500'>
                              +₹{option.cost}
                            </span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {typeof selectedTimeOption?.cost === "number" &&
                  selectedTimeOption.cost > 0 && (
                    <p className='mt-1 text-sm text-primary-600'>
                      Additional charge: ₹{selectedTimeOption.cost}
                    </p>
                  )}
                <FormMessage />

                <FormDescription className='flex items-start justify-start gap-1'>
                  <InfoCircleOutlinedIcon className='size-3 min-w-3 text-neutral-400' />
                  <p className='w-max text-b6 text-neutral-400'>
                    Delay in returns will incur a cost equivalent to the order
                    rent for a day.
                  </p>
                </FormDescription>
              </FormItem>
            )}
          />

          {/* Address Section */}
          {/* Content Grid */}
          <div className='grid gap-4 md:grid-cols-3'>
            <div className='flex h-full w-full flex-col gap-3 md:col-span-2'>
              {/* Delivery Address Card */}
              <Card className='w-full overflow-hidden !rounded-2xl md:min-h-[130px]'>
                <CardContent className='p-0'>
                  <div className='w-full'>
                    <div className='flex items-center gap-1 bg-neutral-150 px-4 py-3'>
                      <MapPin className='h-4 w-4 text-gray-600' />
                      <Typography as={"h5"} className='text-sh7 md:text-sh6'>
                        Pickup Address:
                      </Typography>
                    </div>
                    <div className='px-4 py-3'>
                      <Typography
                        as={"h4"}
                        className='text-sh6 text-neutral-850 md:text-sh5'
                      >
                        {order.customerName} | {order.phoneNumber}{" "}
                        {/* {form.watch("contact_person")} |{" "}
                        {form.watch("phone_number")} */}
                      </Typography>
                      <Typography
                        as={"p"}
                        className='mt-1 max-w-max text-wrap text-b6 text-gray-700'
                      >
                        {order.deliveryAddress}
                        {/* {form.watch("address_id")
                          ? `Address ID: ${form.watch("address_id")}`
                          : "No address selected"} */}
                      </Typography>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Button
                variant='link'
                size={"sm"}
                className='flex h-auto items-center justify-start gap-2 p-0 text-blue-600 hover:text-blue-700'
                asChild
              >
                <Button
                  onClick={() => {
                    setOpenDeliveryAddress(true)
                  }}
                  variant={"link"}
                  className='!text-bt4 md:!text-bt3'
                >
                  <RefreshCwIcon className='h-5 w-5 text-blue-600' />
                  Change Pickup Address
                </Button>
              </Button>
            </div>

            <div className='flex h-full w-full flex-col gap-3'>
              <Card className='w-full overflow-hidden !rounded-2xl md:min-h-[120px]'>
                <CardContent className='h-full w-full p-0'>
                  <div className='flex items-center gap-1 bg-neutral-150 px-4 py-3'>
                    <MapPin className='h-4 w-4 text-gray-600' />
                    <Typography as={"h5"} className='text-sh7 md:text-sh6'>
                      Pickup Date:
                    </Typography>
                  </div>
                  <div className='flex h-max items-center justify-start gap-1 px-4 py-3 md:min-h-[85px] md:justify-center'>
                    <CalendarIcon className='size-4' />

                    <Typography
                      as={"h4"}
                      className='text-sh6 text-neutral-850 md:text-sh5'
                    >
                      {formatDate(new Date(expectedDate ?? Date.now()))}
                      {/* {order.customerName} | {order.phoneNumber}{" "} */}
                      {/* {form.watch("contact_person")} |{" "}
                        {form.watch("phone_number")} */}
                    </Typography>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Contact Details */}
          <div className='mt-3 flex flex-col gap-2 md:mt-0'>
            <div className='flex items-center gap-2'>
              <UserOutlinedIcon className='size-4 md:size-6' />
              <Typography as={"p"} className='text-sh4 md:text-h6'>
                Someone else returning on your behalf?
              </Typography>
            </div>
            <div className='flex w-full flex-wrap items-center justify-between gap-4'>
              {/* name input */}
              {/* <div className='w-full flex-[1_0_250px]'>
                <CustomFormField
                  form={form}
                  name='contact_person'
                  placeholder='Name of Person Returning the parcel.'
                  label='Enter Name of Person Returning the parcel.'
                  required
                />
              </div> */}

              {/* phone input */}

              <div className='w-full max-w-[250px] flex-[1_0_250px]'>
                <PhoneInput
                  form={form}
                  phoneFieldName='phone_number'
                  countryCodeFieldName='country_code'
                  placeholder='Enter their phone number here.'
                  label='Phone Number'
                  required
                />
              </div>
            </div>
            <div className='flex items-center gap-2 text-neutral-400'>
              <InfoCircleOutlinedIcon className='h-4 w-4' />
              <Typography as={"p"} className='text-b6'>
                By default your number appears here.
              </Typography>
            </div>
          </div>

          {/* Only show summary if there are charges */}

          {typeof totalCharges === "number" && totalCharges > 0 && (
            <div className='block md:hidden'>
              <ReturnOrderCharges />

              <div className='mt-6 space-y-3 border-t pt-4'>
                <Typography as='h4' className='font-semibold text-neutral-900'>
                  How would you like to pay?
                </Typography>

                <RadioGroup
                  value={paymentOption}
                  onValueChange={setPaymentOption}
                  className='space-y-1'
                >
                  <div className='relative flex w-full items-center gap-2 rounded-2xl border-2 border-input bg-neutral-150 p-4 has-[[data-state=checked]]:border-secondary-600 has-[[data-state=checked]]:bg-transparent'>
                    <RadioGroupItem
                      value='pay_now'
                      id='pay_now'
                      className='after:absolute after:inset-0'
                    />
                    <label
                      htmlFor='pay_now'
                      className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                    >
                      Pay Now
                    </label>
                  </div>

                  <div className='relative flex w-full items-center gap-2 rounded-2xl border-2 border-input bg-neutral-150 p-4 has-[[data-state=checked]]:border-secondary-600 has-[[data-state=checked]]:bg-transparent'>
                    <RadioGroupItem
                      value='pay_later'
                      id='pay_later'
                      className='after:absolute after:inset-0'
                    />
                    <label
                      htmlFor='pay_later'
                      className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                    >
                      Pay Later
                    </label>
                  </div>
                </RadioGroup>

                <div className='flex w-full flex-wrap items-center justify-between gap-4'>
                  <Typography as={"p"} className='text-b6'>
                    Payment Mode :
                  </Typography>
                  <Typography as={"p"} className='text-sh7'>
                    {paymentOption === "pay_now"
                      ? "Pay NOW (Online)"
                      : "Pay Later"}
                  </Typography>
                </div>

                {paymentOption === "pay_now" && (
                  <div className='flex w-full items-center justify-start gap-[5px] rounded-lg bg-neutral-150 px-2 py-[6px] md:w-auto'>
                    <SpImage
                      src={`${HARD_CODED_IMAGE_URL}/razorpay.webp`}
                      alt='Razorpay'
                      width={20}
                      height={20}
                      className='rounded'
                    />
                    <div className='flex flex-col items-start justify-start'>
                      <Typography
                        as={"span"}
                        className='!text-sh5 !text-neutral-900'
                      >
                        Pay via Razorpay
                      </Typography>
                      <Typography
                        as={"span"}
                        className='text-b7 text-neutral-500'
                      >
                        Cards, Netbanking, Wallet & UPI
                      </Typography>
                    </div>
                  </div>
                )}

                {paymentOption === "pay_later" && (
                  <div className='rounded-lg border border-yellow-200 bg-yellow-50 p-2'>
                    <Typography as='p' className='text-xs text-yellow-800'>
                      Note: Late payment charges may apply if payment is not
                      made within the specified time.
                    </Typography>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className='fixed bottom-0 left-0 right-0 flex w-full flex-col gap-3 bg-gray-100 p-3 pt-6 sm:flex-row sm:justify-between md:relative md:hidden'>
            {totalCharges > 0 && paymentOption === "pay_now" && (
              <div className='flex flex-col items-start justify-start gap-0.5'>
                <Typography as={"p"}>
                  <span className='mr-1 text-sh2'>Total Charges :</span>
                  <span className='text-sh4'>
                    {moneyFormatter(totalCharges)}
                  </span>
                </Typography>
                <Typography as={"p"} className='text-o4'>
                  Price incl. of all taxes
                </Typography>
              </div>
            )}
            <Button
              type='submit'
              size={"lg"}
              variant={"primary"}
              className='order-1 sm:order-2'
              disabled={isLoading || !pickupData?.pickup_time}
              onClick={onConfirmAction}
            >
              {isLoading ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Scheduling...
                </>
              ) : (
                "Schedule Return"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </motion.div>
  )
}
