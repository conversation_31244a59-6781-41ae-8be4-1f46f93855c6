"use client"

import { Typography } from "@/components/ui/typography"
import { ImageUploadType } from "@/hooks/use-image-upload"
import { cn } from "@/lib/utils"
import { AlertCircleIcon, PlusIcon, Upload, X } from "lucide-react"
import Image from "next/image"

interface UploadSectionProps {
  showNavigationButtons?: boolean
  onUploadSuccess?: () => void
  onBack?: () => void
  imageUpload: ImageUploadType
}

const UploadSection = ({ imageUpload }: UploadSectionProps) => {
  const {
    files,
    imageLoadErrors,
    getInputProps,
    getRootProps,
    isDragActive,
    removeFile,
    uploadError,
    uploadProgress,
    setImageLoadErrors,
  } = imageUpload

  const imageUploaded = files.length > 0

  return (
    <div className='mt-4 space-y-6'>
      {/* Uploaded Files Preview and Upload Box */}
      <div
        className={cn(
          "grid gap-4",
          imageUploaded ? "grid-cols-1 md:grid-cols-3" : "grid-cols-1",
        )}
      >
        {imageUploaded && (
          <div className='custom-scrollbar-black grid max-h-[280px] grid-cols-3 gap-4 overflow-y-auto scrollbar-thin md:col-span-2 md:max-h-[230px] lg:grid-cols-3 xl:grid-cols-4'>
            {files.map((file, index) => (
              <div key={index} className='group relative h-max w-full'>
                <div className='aspect-square h-max overflow-hidden rounded-lg bg-neutral-200'>
                  {!imageLoadErrors.has(index) ? (
                    <Image
                      src={file.preview}
                      alt={`Upload preview ${index + 1}`}
                      width={200}
                      height={200}
                      className='h-full w-full object-cover'
                      onError={(_e) => {
                        console.error(
                          "Error loading image preview:",
                          file.preview,
                        )
                        setImageLoadErrors((prev) => new Set(prev).add(index))
                      }}
                      unoptimized
                    />
                  ) : (
                    <div className='flex h-full w-full items-center justify-center text-neutral-400'>
                      <div className='text-center'>
                        <Upload className='mx-auto mb-2 h-8 w-8' />
                        <Typography as='p' className='text-xs'>
                          Preview
                        </Typography>
                      </div>
                    </div>
                  )}
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className='absolute right-0 top-0 rounded-full bg-red-500 p-1 text-white opacity-0 transition-opacity group-hover:opacity-100 hover:bg-red-600'
                  aria-label='Remove image'
                >
                  <X className='h-4 w-4' />
                </button>
                {file.size && (
                  <div className='absolute bottom-2 left-2 flex items-center justify-center rounded bg-black/50 px-1 py-0 text-white'>
                    <Typography as='span' className='text-[10px]'>
                      {(file.size / 1024 / 1024).toFixed(1)}MB
                    </Typography>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Upload Box */}
        <div
          {...getRootProps()}
          className={cn(
            "relative flex cursor-pointer items-center justify-center rounded-xl border border-dashed px-4 py-6 text-center transition-colors md:p-8",
            isDragActive
              ? "bg-primary-50 border-primary-400"
              : "border-primary-150 hover:border-primary-400 hover:bg-neutral-50",
            uploadError && "border-red-300 bg-red-50",
          )}
        >
          <input {...getInputProps()} />
          <div className='space-y-4'>
            <div
              className={cn(
                "mx-auto flex h-14 w-14 items-center justify-center rounded-full md:h-16 md:w-16",
                isDragActive ? "bg-primary-150" : "bg-primary-100",
              )}
            >
              {imageUploaded ? (
                <PlusIcon
                  className={cn(
                    "size-6 md:size-8",
                    isDragActive ? "text-primary-500" : "text-neutral-400",
                  )}
                />
              ) : (
                <Upload className={cn("h-8 w-8 text-primary-500")} />
              )}
            </div>
            <div className='flex items-center justify-center'>
              {isDragActive ? (
                <Typography as='p' className='font-medium text-primary-600'>
                  Drop your images here
                </Typography>
              ) : (
                <>
                  {imageUploaded ? (
                    <Typography as='p' className='font-medium text-neutral-700'>
                      Add More
                    </Typography>
                  ) : (
                    <div>
                      <Typography as='p' className='font-bold text-neutral-800'>
                        Upload Images of Locked Packages{" "}
                        <em className='hidden md:inline-block'>Here</em>
                      </Typography>
                      <Typography
                        as='p'
                        className='mt-1 text-sh5 text-neutral-500'
                      >
                        Files supported: PNG, JPEG, JPG
                      </Typography>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* show at least one image is required */}
      {files.length <= 0 && (
        <div className='flex items-center justify-start space-x-2 text-start'>
          <AlertCircleIcon className='size-4 text-destructive-500' />
          <Typography as='p' className='text-o2 text-destructive-500'>
            At least one image is required.
          </Typography>
        </div>
      )}

      {/* Error Message */}
      {uploadError && (
        <div className='text-center'>
          <Typography as='p' className='text-sm text-red-600'>
            {uploadError}
          </Typography>
        </div>
      )}

      {/* Upload Progress */}
      {uploadProgress > 0 && uploadProgress < 100 && (
        <div className='space-y-2'>
          <div className='flex items-center justify-between text-sm'>
            <span>Uploading...</span>
            <span>{uploadProgress}%</span>
          </div>
          <div className='h-2 w-full overflow-hidden rounded-full bg-neutral-200'>
            <div
              className='h-full bg-primary-500 transition-all duration-300'
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default UploadSection
