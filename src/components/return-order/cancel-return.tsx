import { AlreadyBookedResponse } from "@/types/schedule"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useMutation, useQuery } from "@tanstack/react-query"
import { TriangleAlertIcon } from "lucide-react"
import { useRouter } from "next/navigation"
import { Dispatch, SetStateAction } from "react"
import { toast } from "sonner"
import { AdaptiveWrapper } from "../modals/adaptive-wrapper"
import { Button } from "../ui/button"
import { Skeleton } from "../ui/skeleton"
import { Typography } from "../ui/typography"

interface CancelReturnProps {
  order_id: string

  isOpen: boolean
  handleOpenChange: Dispatch<SetStateAction<boolean>>
  handleScheduleAgainModal: Dispatch<SetStateAction<boolean>>
}

const CancelReturn = ({
  order_id,
  isOpen,
  handleOpenChange,
  handleScheduleAgainModal,
}: CancelReturnProps) => {
  const router = useRouter()

  const { mutate: cancelAndReschduleShipment } = useMutation({
    mutationFn: () =>
      fetchWithAuthPost(
        "https://api.sharepal.in/api:AIoqxnqr/return/cancel-and-reschedule",
        {
          order_id,
        },
      ),
    onSuccess: () => {
      // queryClient.invalidateQueries({ queryKey: ["orderStatus"] })
      router.push("/return-order/" + order_id)
    },
    onError: () => {
      toast.error("Unable to cancel and reschedule shipment")
    },
  })

  const { data: alreadyBooked, isPending } = useQuery({
    queryKey: ["alreadyBooked", order_id],
    queryFn: async () => {
      const data = await fetchWithAuthPost<AlreadyBookedResponse | null>(
        "https://api.sharepal.in/api:AIoqxnqr/shipment/schedule-check",
        {
          order_id: order_id,
        },
      )
      return data
    },
    enabled: isOpen,
  })

  return (
    <AdaptiveWrapper
      open={isOpen}
      onOpenChange={handleOpenChange}
      title='Sure you want to Cancel Return?'
      desktop={{ type: "dialog" }}
      mobile={{ type: "drawer", side: "bottom" }}
      tablet={{ type: "drawer", side: "bottom" }}
      className='lg:max-w-[650px]'
    >
      <div className='space-y-4 p-4 md:px-4 lg:px-8 lg:pt-0'>
        <Typography as={"p"} className='text-start text-sh6 lg:text-center'>
          Order No: <span>{order_id}</span>
        </Typography>
        <Typography
          as='div'
          className='flex flex-col items-center justify-start gap-1 text-start text-b4 text-gray-700 lg:justify-center lg:text-center'
        >
          Please confirm if you wish to cancel your return scheduled for
          <div className='flex w-max items-center justify-start gap-2 lg:justify-center'>
            <div className='w-full min-w-max'>
              {isPending ? (
                <Skeleton className='mx-auto h-6 w-32' />
              ) : (
                <b>{alreadyBooked?.schedule_date}</b>
              )}
            </div>
            at
            <div className='w-full min-w-max'>
              {isPending ? (
                <Skeleton className='mx-auto h-6 w-32' />
              ) : (
                <b>{alreadyBooked?.schedule_time}</b>
              )}
            </div>
          </div>
        </Typography>

        <Typography
          as='p'
          className='text-start text-b4 text-gray-700 md:px-4 lg:text-center'
        >
          Any payment made by you will be automatically refunded to your payment
          source within 3 to 7 days.
        </Typography>
        <div className='flex items-start justify-start gap-1 rounded-xl bg-warning-100 px-3 py-2 md:rounded-full'>
          <TriangleAlertIcon className='size-4 min-w-4 text-warning-500 md:size-6 md:min-w-6' />
          <Typography as='p' className='text-start text-b6 text-warning-700'>
            If you proceed with cancellation, your order will return to the
            Return Due stage. Delay charges will be applicable for any delay in
            return.
          </Typography>
        </div>

        {/* action buttons */}
        <div className='mx-auto flex flex-col gap-3 pt-4 lg:max-w-[400px]'>
          <Button
            onClick={() => {
              cancelAndReschduleShipment()
              handleOpenChange(false)
            }}
            variant={"destructive"}
            size={"lg"}
          >
            Confirm Return Cancellation
          </Button>
          <Button
            onClick={() => {
              handleScheduleAgainModal(true)
              handleOpenChange(false)
            }}
            variant={"outline-primary"}
            size={"lg"}
          >
            Change Pickup Time Instead
          </Button>
        </div>
      </div>
    </AdaptiveWrapper>
  )
}

export default CancelReturn
