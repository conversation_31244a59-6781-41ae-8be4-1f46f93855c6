/* eslint-disable @next/next/no-img-element */
"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { useReturnOrderStore } from "@/store/return-order-store"
import {
  ChecklistItem as ChecklistItemType,
  ChecklistOption,
} from "@/types/return-order"
import { containerVariants } from "@/utils/animation-variants"
import { AnimatePresence, motion } from "framer-motion"
import { AlertCircleIcon, Package } from "lucide-react"
import { useMemo, useState } from "react"
import { ChevronDownIcon } from "sharepal-icons"

export function ProductChecklistForm() {
  const {
    products,
    packaging,
    productSelections,
    packagingSelections,
    updateProductSelection,
    updatePackagingSelection,
    toggleReviewModal,
  } = useReturnOrderStore()

  const [isProductsOpen, setIsProductsOpen] = useState(true)
  const [isPackagingOpen, setIsPackagingOpen] = useState(false)

  const {
    totalProductsCount,
    checkedProductsCount,
    totalPackagingCount,
    checkedPackagingCount,
    isNextButtonDisabled,
  } = useMemo(() => {
    const totalProductsCount = products.length
    const checkedProductsCount = Object.keys(productSelections).length

    const totalPackagingCount = packaging.length
    const checkedPackagingCount = Object.keys(packagingSelections).length

    // Determine if the Next button should be disabled
    const hasPackaging = totalPackagingCount > 0
    const onlyHasPackgingItems =
      totalPackagingCount > 0 && totalProductsCount <= 0
    const allProductsSelected =
      checkedProductsCount === totalProductsCount && onlyHasPackgingItems
        ? totalProductsCount <= 0
        : totalProductsCount > 0
    const allPackagingSelected = hasPackaging
      ? checkedPackagingCount === totalPackagingCount
      : true

    const isNextButtonDisabled = !(allProductsSelected && allPackagingSelected)

    return {
      totalProductsCount,
      checkedProductsCount,
      totalPackagingCount,
      checkedPackagingCount,
      isNextButtonDisabled,
    }
  }, [products, packaging, productSelections, packagingSelections])

  return (
    <div className='mx-auto'>
      <div className='mb-4 flex flex-col items-center justify-center gap-1.5 p-2 md:gap-3 lg:hidden'>
        <Typography
          as='h2'
          className='text-center text-sh2 font-semibold md:text-left'
        >
          Order Return Checklist
        </Typography>
        <Typography as='p' className='text-center text-b6 text-gray-500'>
          Here’s a list of everything we sent in your order so you can return
          hassle-free. Complete checklist to schedule return.
        </Typography>
        <div className='mt-2 flex items-start justify-start gap-2 rounded-2xl bg-neutral-150 px-3 py-3'>
          <AlertCircleIcon className='size-4 min-w-4 text-neutral-700' />
          <Typography as='p' className='text-b6 text-neutral-700'>
            Damage charges will be calculated once return is received at
            warehouse and quality check is complete.
          </Typography>
        </div>
      </div>

      {/* Products Section */}
      {products.length > 0 && (
        <div className='mb-4'>
          <ChecklistSectionHeader
            title='Products in Order'
            checkedCount={checkedProductsCount}
            totalCount={totalProductsCount}
            isOpen={isProductsOpen}
            onToggle={() => setIsProductsOpen(!isProductsOpen)}
          />
          <AnimatePresence>
            {isProductsOpen && (
              <motion.div
                variants={containerVariants}
                initial='hidden'
                animate='visible'
                exit='exit'
                className='rounded-b-2xl border border-t-0 border-neutral-200 md:rounded-b-3xl'
              >
                <div className='hidden grid-cols-[1fr,auto,auto,auto] items-center gap-4 border-b px-4 py-2 md:grid md:py-3'>
                  <Typography as='p' className='text-sh3'>
                    Product Name & ID
                  </Typography>
                  <Typography
                    as='p'
                    className='w-24 text-center text-sh6 text-primary-500'
                  >
                    RETURN
                  </Typography>
                  <Typography
                    as='p'
                    className='w-24 text-center text-sh6 text-primary-500'
                  >
                    RETURNING DAMAGED
                  </Typography>
                  <Typography
                    as='p'
                    className='w-24 text-center text-sh6 text-primary-500'
                  >
                    BUY/LOST
                  </Typography>
                </div>
                <div className='w-full space-y-2 px-2 py-2 md:space-y-4 md:px-4'>
                  {products.map((item) => (
                    <ChecklistItem
                      key={item.product_id}
                      item={item}
                      selections={productSelections}
                      updateSelection={updateProductSelection}
                      type='product'
                    />
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Packaging Section */}
      {packaging.length > 0 && (
        <div className='mb-4'>
          <ChecklistSectionHeader
            title='Packaging Items in Order'
            checkedCount={checkedPackagingCount}
            totalCount={totalPackagingCount}
            isOpen={isPackagingOpen}
            onToggle={() => setIsPackagingOpen(!isPackagingOpen)}
          />
          <AnimatePresence>
            {isPackagingOpen && (
              <motion.div
                variants={containerVariants}
                initial='hidden'
                animate='visible'
                exit='exit'
                className='rounded-b-2xl border border-t-0 border-neutral-200 md:rounded-b-3xl'
              >
                <div className='hidden grid-cols-[1fr,auto,auto,auto] items-center gap-4 border-b px-4 py-2 md:grid md:py-3'>
                  <Typography as='p' className='text-sh3'>
                    Packaging Name & ID
                  </Typography>
                  <Typography
                    as='p'
                    className='w-24 text-center text-sh6 text-primary-500'
                  >
                    RETURN
                  </Typography>
                  <Typography
                    as='p'
                    className='w-24 text-center text-sh6 text-primary-500'
                  >
                    RETURNING DAMAGED
                  </Typography>
                  <Typography
                    as='p'
                    className='w-24 text-center text-sh6 text-primary-500'
                  >
                    BUY/LOST
                  </Typography>
                </div>
                <div className='w-full space-y-2 px-4 py-2 md:space-y-4'>
                  {packaging.map((item) => (
                    <ChecklistItem
                      key={item.product_id}
                      item={item}
                      selections={packagingSelections}
                      updateSelection={updatePackagingSelection}
                      type='packaging'
                    />
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}

      <div className='fixed bottom-0 left-0 right-0 flex w-full flex-col gap-3 bg-gray-100 p-3 pt-6 sm:flex-row sm:justify-between md:relative md:hidden'>
        <Button
          type='submit'
          size='lg'
          variant='primary'
          className='order-1 sm:order-2'
          disabled={isNextButtonDisabled}
          onClick={() => {
            toggleReviewModal(true)
          }}
        >
          Next
        </Button>
      </div>
    </div>
  )
}

interface RadioOptionProps {
  label: string
  isSelected: boolean
  onChange: () => void
}

// Radio option component for mobile view
const RadioOption = ({ label, isSelected, onChange }: RadioOptionProps) => (
  <div
    className={cn(
      "flex w-full items-center gap-2 rounded-full border-2 border-neutral-150 px-3 py-1.5 text-neutral-900 md:px-4 md:py-2",
      isSelected ? "border-primary-500" : "bg-neutral-150",
    )}
    onClick={onChange}
  >
    <div
      className={cn(
        "flex h-4 w-4 items-center justify-center rounded-full border md:h-5 md:w-5",
        isSelected
          ? "border-white bg-primary-500"
          : "border-neutral-300 bg-white",
      )}
    >
      {isSelected && (
        <div className='h-2 w-2 rounded-full bg-white md:h-2.5 md:w-2.5'></div>
      )}
    </div>
    <span className='ml-1 text-o4 text-gray-900 md:text-o2'>{label}</span>
  </div>
)

interface ChecklistItemProps {
  item: ChecklistItemType
  selections: Record<string, ChecklistOption>
  updateSelection: (product_id: string, selection: ChecklistOption) => void
  type: "product" | "packaging"
}

// Checklist item component for both desktop and mobile views
const ChecklistItem = ({
  item,
  selections,
  updateSelection,
  // type,
}: ChecklistItemProps) => {
  // const isProduct = type === "product"
  const isProduct = true

  return (
    <div className='space-y-4'>
      {/* Desktop layout */}
      <div
        className={cn(
          "hidden items-center gap-4 md:grid",
          isProduct
            ? "grid-cols-[1fr,auto,auto,auto]"
            : "grid-cols-[1fr,auto,auto]",
        )}
      >
        <div className='flex items-center gap-3'>
          {isProduct && (
            <SpImage
              src={item.product_image ?? ""}
              alt={item.product_name}
              width={100}
              height={100}
              className='h-full w-full object-cover'
              containerClassName='flex h-12 w-12 p-1.5 items-center justify-center overflow-hidden rounded-md bg-gray-100'
            />
          )}
          <div>
            <Typography as='p' className='font-medium'>
              {item.product_name}
            </Typography>
            <Typography as='p' className='text-xs text-gray-500'>
              ID: #{item.product_id}
              {/* | Code: {item.product_code} */}
            </Typography>
          </div>
        </div>

        {/* RETURN option */}
        <div className='flex w-24 justify-center'>
          <div
            className={cn(
              "flex h-5 w-5 items-center justify-center rounded-full border",
              selections[item.product_id] === "return"
                ? "border-primary-500 bg-primary-500"
                : "border-neutral-300 bg-white",
            )}
            onClick={() => updateSelection(item.product_id, "return")}
          >
            {selections[item.product_id] === "return" && (
              <div className='h-2.5 w-2.5 rounded-full bg-white'></div>
            )}
          </div>
        </div>

        {/* DAMAGED option */}
        <div className='flex w-24 justify-center'>
          <div
            className={cn(
              "flex h-5 w-5 items-center justify-center rounded-full border",
              selections[item.product_id] === "damaged"
                ? "border-primary-500 bg-primary-500"
                : "border-neutral-300 bg-white",
            )}
            onClick={() => updateSelection(item.product_id, "damaged")}
          >
            {selections[item.product_id] === "damaged" && (
              <div className='h-2.5 w-2.5 rounded-full bg-white'></div>
            )}
          </div>
        </div>

        {/* BUY/LOST option for products only */}
        {isProduct && (
          <div className='flex w-24 justify-center'>
            <div
              className={cn(
                "flex h-5 w-5 items-center justify-center rounded-full border",
                selections[item.product_id] === "buy_lost"
                  ? "border-primary-500 bg-primary-500"
                  : "border-neutral-300 bg-white",
              )}
              onClick={() => updateSelection(item.product_id, "buy_lost")}
            >
              {selections[item.product_id] === "buy_lost" && (
                <div className='h-2.5 w-2.5 rounded-full bg-white'></div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Mobile layout */}
      <div className='space-y-2 rounded-2xl border border-neutral-150 px-3 pb-4 pt-2 md:hidden md:space-y-3'>
        <div className='flex items-center gap-3'>
          {isProduct && (
            <SpImage
              src={item.product_image ?? ""}
              alt={item.product_name}
              width={100}
              height={100}
              className='h-full w-full object-cover'
              containerClassName='flex p-1.5 md:p-2 h-16 w-16 items-center justify-center overflow-hidden rounded-md bg-gray-100 '
            />
          )}
          <div>
            <Typography as='p' className='font-medium'>
              {item.product_name}
            </Typography>
            <Typography as='p' className='text-xs text-gray-500'>
              ID: #{item.product_id}
            </Typography>
          </div>
        </div>

        <div className='space-y-2'>
          <RadioOption
            label='RETURN'
            isSelected={selections[item.product_id] === "return"}
            onChange={() => updateSelection(item.product_id, "return")}
          />
          <RadioOption
            label='RETURN DAMAGED'
            isSelected={selections[item.product_id] === "damaged"}
            onChange={() => updateSelection(item.product_id, "damaged")}
          />
          {/* {isProduct && ( */}
          <RadioOption
            label='BUY/LOST'
            isSelected={selections[item.product_id] === "buy_lost"}
            onChange={() => updateSelection(item.product_id, "buy_lost")}
          />
          {/* )} */}
        </div>
      </div>
    </div>
  )
}

interface ChecklistSectionHeaderProps {
  title: string
  checkedCount: number
  totalCount: number
  isOpen: boolean
  onToggle: () => void
}

const ChecklistSectionHeader = ({
  title,
  checkedCount,
  totalCount,
  isOpen,
  onToggle,
}: ChecklistSectionHeaderProps) => (
  <motion.div
    className={cn(
      "flex items-center justify-between rounded-t-2xl bg-primary-100 px-3 py-3 md:rounded-t-3xl md:p-4",
      !isOpen &&
        "rounded-2xl border border-neutral-200 bg-gray-100 md:rounded-3xl",
    )}
  >
    <div className='flex items-center gap-2'>
      <Package className='size-5 min-w-5 text-primary-500 md:size-7 md:min-w-7' />
      <Typography
        as='h3'
        className='flex flex-col items-start gap-0 text-sh4 font-semibold md:flex-row md:items-center lg:justify-center lg:gap-1 lg:text-h6'
      >
        {title}
        <span className='text-sh7 text-neutral-600 lg:text-h6'>
          ({checkedCount}/{totalCount} Checked)
        </span>
      </Typography>
    </div>
    <Button
      variant={!isOpen ? "primary" : "outline-primary"}
      size='sm'
      className='h-auto rounded-full text-xs'
      onClick={onToggle}
    >
      <span className='hidden md:block'>
        {isOpen ? "Close Checklist" : "Open Checklist"}
      </span>

      <span className='block md:hidden'>
        <span>
          <ChevronDownIcon
            className={cn("h-4 w-4 transition-transform", {
              "rotate-180": isOpen,
            })}
          />
        </span>
      </span>
    </Button>
  </motion.div>
)
