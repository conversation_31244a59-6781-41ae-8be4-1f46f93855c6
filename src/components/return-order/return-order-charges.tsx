import { Separator } from "@/components/ui/separator"
import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import { useReturnOrderStore } from "@/store/return-order-store"
import { ChargeItem } from "../checkout/order-summary-new/charge-item"

const ReturnOrderCharges = () => {
  const {
    buyLostTotal,
    buyLostItemsCount,
    delayRentalCharges,
    timeSlotCharges,
    totalDelayDays,
    totalCharges,
    previousPayment,
  } = useReturnOrderStore()
  return (
    <div className='mt-4 space-y-4 border-t pt-4'>
      {buyLostTotal > 0 && (
        <ChargeItem
          item={{
            label: "Lost Items",
            items_count: buyLostItemsCount,
            amount: buyLostTotal,
            items_count_text: ` item${buyLostItemsCount > 1 ? "s" : ""} lost`,
          }}
        />
      )}

      {delayRentalCharges > 0 && (
        <ChargeItem
          item={{
            label: "Delay Rental Charges",
            amount: delayRentalCharges,
            items_count: totalDelayDays,
            items_count_text: "day delayed",
          }}
        />
      )}
      {timeSlotCharges > 0 && (
        <ChargeItem
          item={{
            label: "Time Slot Charges",
            amount: timeSlotCharges,
          }}
        />
      )}
      {previousPayment > 0 && (
        <ChargeItem
          item={{
            label: "Already Paid",
            amount: previousPayment,
          }}
          type='discount'
        />
      )}
      <Separator className='mt-4' />
      <div className='flex items-center justify-between'>
        <div>
          <Typography as='p' className='text-lg font-bold text-neutral-900'>
            Total Charges
          </Typography>
          <div className='text-xs text-gray-500'>Price incl. of all taxes</div>
        </div>

        <Typography as='p' className='text-lg font-bold'>
          {moneyFormatter(totalCharges)}
        </Typography>
      </div>
    </div>
  )
}

export default ReturnOrderCharges
