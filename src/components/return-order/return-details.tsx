"use client"
import {
  fetchAdditionalOrderSummary,
  fetchReturnItems,
} from "@/actions/return-order"
import { moneyFormatter } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { RentOrder } from "@/types"
import { OrderSummary } from "@/types/order"
import { ChecklistItem as ChecklistItemType } from "@/types/return-order"
import { AlreadyBookedResponse } from "@/types/schedule"
import { containerVariants } from "@/utils/animation-variants"
import { formatDate } from "@/utils/date-logics"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useQuery } from "@tanstack/react-query"
import { AnimatePresence, motion } from "framer-motion"
import { ChevronDownIcon, ChevronUp, Package } from "lucide-react"
import { Dispatch, SetStateAction, useState } from "react"
import { ChargeItem } from "../checkout/order-summary-new/charge-item"
import { AdaptiveWrapper } from "../modals/adaptive-wrapper"
import { Button } from "../ui/button"
import { Separator } from "../ui/separator"
import { Typography } from "../ui/typography"

interface ReturnDetailsProps {
  order_id: string
  order: RentOrder
  orderSummary: OrderSummary
  open: boolean
  handleOpenChange: Dispatch<SetStateAction<boolean>>
}

const ReturnDetails = ({
  order_id,
  open,
  handleOpenChange,
  orderSummary,
}: ReturnDetailsProps) => {
  const [isProductsOpen, setIsProductsOpen] = useState(false)
  const [isPackagingOpen, setIsPackagingOpen] = useState(false)
  //
  const { data: returnItems } = useQuery<ChecklistItemType[] | []>({
    queryKey: ["return-items", order_id],
    queryFn: () => fetchReturnItems(order_id),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: open, // Only fetch when the modal is open
  })

  //additional order summmay
  const { data: additionalOrderSummary } = useQuery({
    queryKey: ["additoinal-order-summary", order_id],
    queryFn: () => fetchAdditionalOrderSummary(order_id),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: open, // Only fetch when the modal is open
  })

  const { data: alreadyBooked } = useQuery({
    queryKey: ["alreadyBooked", order_id],
    queryFn: async () => {
      const data = await fetchWithAuthPost<AlreadyBookedResponse | null>(
        "https://api.sharepal.in/api:AIoqxnqr/shipment/schedule-check",
        {
          order_id: order_id,
        },
      )
      return data
    },
    enabled: open,
  })

  const totalLostCount = returnItems?.filter(
    (item) => item.status == "buy_lost",
  ).length
  const totalProductCount = returnItems?.filter(
    (item) => item.type == "product",
  ).length
  const totalPackagingCount = returnItems?.filter(
    (item) => item.type == "packaging",
  ).length

  if (!additionalOrderSummary) return <></>

  return (
    <AdaptiveWrapper
      open={open}
      onOpenChange={handleOpenChange}
      title='Return Details'
      desktop={{ type: "dialog" }}
      mobile={{ type: "drawer", side: "bottom" }}
      tablet={{ type: "drawer", side: "bottom" }}
      className='lg:max-w-[600px]'
    >
      <div className='space-y-4 p-4 lg:mt-0 lg:p-3'>
        {/* pickup details */}
        <div className='flex w-full flex-col gap-1 rounded-2xl border border-neutral-200 p-3 text-left'>
          <div className='flex w-full flex-col gap-1'>
            <Typography
              as='p'
              className='text-sh4 text-gray-900 dark:text-gray-100'
            >
              Pickup Details
            </Typography>
            <div>
              <Typography
                as='p'
                className='text-sh4 text-neutral-500 dark:text-gray-100'
              >
                Pickup Date:{" "}
                <span>
                  {formatDate(
                    new Date(alreadyBooked?.schedule_date || new Date()),
                  )}
                </span>
              </Typography>

              <Typography
                as='p'
                className='text-sh4 text-neutral-500 dark:text-gray-100'
              >
                Pickup Time:{" "}
                <span>
                  {alreadyBooked && <span>{alreadyBooked?.schedule_time}</span>}
                </span>
              </Typography>
            </div>
          </div>

          {/* pickup address */}
          <div className='mt-3 flex w-full flex-col gap-1'>
            <Typography
              as='p'
              className='text-sh4 text-gray-900 dark:text-gray-100'
            >
              Pickup Address
            </Typography>
            <div>
              <Typography
                as='p'
                className='text-b6 text-neutral-500 dark:text-gray-100 md:text-b4'
              >
                {orderSummary.deal_cf_delivery_address}
              </Typography>
            </div>
          </div>
        </div>

        {/* Products Section */}

        {(totalProductCount || 0) > 0 && (
          <div className='mb-4'>
            <ChecklistSectionHeader
              title='Products in Order'
              totalCount={totalProductCount || 0}
              isOpen={isProductsOpen}
              onToggle={() => {
                setIsProductsOpen(!isProductsOpen)
                setIsPackagingOpen(false)
              }}
            />

            <AnimatePresence>
              {isProductsOpen && (
                <motion.div
                  variants={containerVariants}
                  initial='hidden'
                  animate='visible'
                  exit='exit'
                  className='max-h-[200px] overflow-y-scroll rounded-b-2xl border border-t-0 border-neutral-200 md:rounded-b-3xl'
                >
                  <div className='w-full space-y-2 px-4 py-2 md:space-y-4'>
                    {/* Products */}
                    {returnItems
                      ?.filter((item) => item.type == "product")
                      ?.map((item) => (
                        <ChecklistItem
                          key={item.product_id}
                          item={item}
                          type='product'
                        />
                      ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}

        {/* Packaging Section */}
        {(totalPackagingCount || 0) > 0 && (
          <div className='mb-4'>
            <ChecklistSectionHeader
              title='Packaging Items in Order'
              totalCount={totalPackagingCount || 0}
              isOpen={isPackagingOpen}
              onToggle={() => {
                setIsPackagingOpen(!isPackagingOpen)
                setIsProductsOpen(false)
              }}
            />

            <AnimatePresence>
              {isPackagingOpen && (
                <motion.div
                  variants={containerVariants}
                  initial='hidden'
                  animate='visible'
                  exit='exit'
                  className='max-h-[200px] overflow-y-scroll rounded-b-2xl border border-t-0 border-neutral-200 md:rounded-b-3xl'
                >
                  <div className='w-full space-y-2 px-4 py-2 md:space-y-4'>
                    {/* Packaging Items */}
                    {returnItems
                      ?.filter((item) => item.type == "packaging")
                      ?.map((item) => (
                        <ChecklistItem
                          key={item.product_id}
                          item={item}
                          type='packaging'
                        />
                      ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}

        {/*  */}

        {additionalOrderSummary?.total_return_charges > 0 && (
          <div className='space-y-2 rounded-lg border border-neutral-200 bg-neutral-50 p-4 md:rounded-2xl'>
            {additionalOrderSummary.total_lost_charges > 0 && (
              <ChargeItem
                item={{
                  label: "Lost Items",
                  items_count: totalLostCount,
                  amount: additionalOrderSummary.total_lost_charges,
                  items_count_text: `item${(totalLostCount || 0) > 1 ? "s" : ""} lost`,
                }}
              />
            )}

            {additionalOrderSummary.delay_rental_charge > 0 && (
              <ChargeItem
                item={{
                  label: "Delay Rental Charge",
                  amount: additionalOrderSummary.delay_rental_charge,
                  items_count_text: `Late return fee`,
                }}
              />
            )}
            {additionalOrderSummary.timeslot_charges > 0 && (
              <ChargeItem
                item={{
                  label: "TimeSlot Charges",
                  amount: additionalOrderSummary.timeslot_charges,
                }}
              />
            )}

            <Separator className='my-2' />

            <div className='flex items-center justify-between'>
              <div>
                <Typography
                  as='p'
                  className='text-lg font-bold text-neutral-900'
                >
                  Total Charges
                </Typography>
                <div className='text-xs text-gray-500'>
                  Price incl. of all taxes
                </div>
              </div>

              <Typography as='p' className='text-lg font-bold'>
                {moneyFormatter(
                  additionalOrderSummary.total_return_charges ?? 0,
                )}
              </Typography>
            </div>
          </div>
        )}

        <div className='flex flex-col gap-3 pt-4'>
          <Button
            onClick={() => {
              handleOpenChange(false)
            }}
            className='w-full'
            size={"lg"}
            variant={"primary"}
          >
            Done
          </Button>
        </div>
      </div>
    </AdaptiveWrapper>
  )
}

interface ChecklistItemProps {
  item: ChecklistItemType
  type: string
}

// Checklist item component for both desktop and mobile views
const ChecklistItem = ({ item, type }: ChecklistItemProps) => {
  const isProduct = type === "product"

  return (
    <div className='space-y-4'>
      {/* Desktop layout */}
      <div
        className={cn(
          "items-center gap-4 md:grid",
          isProduct
            ? "grid-cols-[1fr,auto,auto,auto]"
            : "grid-cols-[1fr,auto,auto]",
        )}
      >
        <div className='flex items-center justify-between gap-3'>
          <div className='flex items-center gap-3'>
            <SpImage
              src={item.product_image ?? ""}
              alt={item.product_name}
              width={100}
              height={100}
              className='h-full w-full object-cover'
              containerClassName='flex h-12 w-12 p-1.5 items-center justify-center overflow-hidden rounded-md bg-gray-100'
            />

            <div>
              <Typography as='p' className='text-sh6 md:text-sh3'>
                {item.product_name}
              </Typography>
              <Typography as='p' className='text-o2 text-gray-500'>
                ID: #{item.product_id}
                {/* | Code: {item.product_code} */}
              </Typography>
            </div>
          </div>

          <div className='flex w-auto items-center justify-end'>
            {item.status === "return" && (
              <Typography
                as='p'
                className='text-o4 text-success-500 md:text-o1'
              >
                Return
              </Typography>
            )}
            {item.status === "damaged" && (
              <Typography
                as='p'
                className='text-o4 text-warning-500 md:text-o1'
              >
                Return Damaged
              </Typography>
            )}
            {item.status === "buy_lost" && (
              <Typography
                as='p'
                className='text-o4 text-destructive-500 md:text-o1'
              >
                Buy/Lost
              </Typography>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

interface ChecklistSectionHeaderProps {
  title: string
  totalCount: number
  isOpen: boolean
  onToggle: () => void
}

const ChecklistSectionHeader = ({
  title,
  totalCount,
  isOpen,
  onToggle,
}: ChecklistSectionHeaderProps) => (
  <motion.div
    className={cn(
      "flex items-center justify-between border border-neutral-200 px-4 py-2.5",
      isOpen ? "rounded-t-2xl md:rounded-t-3xl" : "rounded-2xl md:rounded-3xl",
    )}
  >
    <div className='flex items-center gap-2'>
      <Package className='h-5 w-5 text-primary-500' />
      <Typography
        as='h3'
        className='flex flex-row items-center gap-0 text-sh3 font-semibold'
      >
        {title}
        <span className='text-sm text-neutral-600'>({totalCount})</span>
      </Typography>
    </div>
    <Button
      variant={!isOpen ? "primary" : "outline-primary"}
      size='sm'
      className='h-auto rounded-full text-xs'
      onClick={onToggle}
    >
      <div className='hidden md:block'>
        {isOpen ? (
          <span className='flex items-center justify-center gap-2'>
            <ChevronUp className='size-4' />
            Close
          </span>
        ) : (
          <span className='flex items-center justify-center gap-2'>
            <ChevronDownIcon className='size-4' />
            View All
          </span>
        )}
      </div>

      <span className='block md:hidden'>
        <span>
          <ChevronDownIcon
            className={cn("h-4 w-4 transition-transform", {
              "rotate-180": isOpen,
            })}
          />
        </span>
      </span>
    </Button>
  </motion.div>
)

export default ReturnDetails
