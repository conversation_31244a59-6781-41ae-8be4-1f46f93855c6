import { fetchOrderDetails, fetchOrderWithoutCart } from "@/actions/orders"
import {
  fetchAdditionalOrderSummary,
  fetchProductsAndPackagingItems,
} from "@/actions/return-order"
import DeliveryAddressSelect from "@/components/modals/delivery-address"
import { ShipmentDetailsResponse } from "@/components/modals/schedule-modal"
import { Separator } from "@/components/ui/separator"
import { Typography } from "@/components/ui/typography"
import { getDateDifference } from "@/functions/date"
import { useReturnOrderStore } from "@/store/return-order-store"
import { useUserStore } from "@/store/user-store"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { getCombinedTimestamp } from "@/utils/is-time-valid"
import { useMutation, useQuery } from "@tanstack/react-query"
import { Address } from "cluster"
import { motion } from "framer-motion"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"

import CheckoutRedirectLoading from "@/components/loadings/checkout-redirect-loading"
import ReturnOrderSkeleton from "@/components/skeletons/return-order-skleton"
import { useImageUpload } from "@/hooks/use-image-upload"
import usePlaceOrder from "@/hooks/use-place-order"
import { differenceInCalendarDays, format } from "date-fns"
import { useEffect, useState } from "react"
import { ChevronLeftIcon } from "sharepal-icons"
import { toast } from "sonner"
import {
  ReturnAlreadyBooked,
  ReturnNotAvailable,
} from "../checkout/order-return"

import { cn } from "@/lib/utils"
import { PackingInstructionsForm } from "./packing-instructions-form"
import { PickupDetailsForm } from "./pickup-details-form"
import { ProductChecklistForm } from "./product-checklist-form"
import { ReturnOrderSummary } from "./return-order-summary"
import { ReturnStepper } from "./return-stepper"
import { ReviewModal } from "./review-modal"

const ReturnOrder = () => {
  const { order_id } = useParams<{ order_id: string }>()

  const {
    activeStep,
    orderId,
    showReviewModal,
    productSelections,
    packagingSelections,
    pickupData,
    isSubmitting,
    setOrderId,
    goToNextStep,
    setPickupData,
    goToPrevStep,
    toggleReviewModal,
    setProducts,
    setPackaging,
    setDelayRentalCharges,
    seTotalDelayDays,
    tsaLockApplied,
    zipLockApplied,
    paymentOption,
    delayRentalCharges,
    timeSlotCharges,
    totalCharges,
    buyLostTotal,
    setActiveStep,
    setPreviousPayment,
    calculateTotals,
  } = useReturnOrderStore()
  const { user } = useUserStore()

  const { hanldePaymentForReturnOrder, isReturnPaymentLoading } =
    usePlaceOrder()
  const [openDeliveryAddress, setOpenDeliveryAddress] = useState(false)
  const [isScheduling, setIsScheduling] = useState(false)
  const router = useRouter()
  const imageUpload = useImageUpload()

  useQuery({
    queryKey: ["returnOrder", order_id],
    queryFn: async () => {
      const data = await fetchProductsAndPackagingItems(order_id)
      if (data) {
        setProducts(data.products)
        setPackaging(data.packaging_items)
        return data
      }
      return null
    },
    enabled: !!order_id, // Only run if order_id is available
  })

  //additional order summmay
  const { data: additionalOrderSummary } = useQuery({
    queryKey: ["additoinal-order-summary", order_id],
    queryFn: () => fetchAdditionalOrderSummary(order_id),
    refetchOnWindowFocus: false,
    enabled: !!order_id,
  })

  const {
    data: orderDetails,
    isFetching: isOrderDetailsFetching,
    isLoading: isOrderDetailsLoading,
  } = useQuery({
    queryKey: ["order-details-fetch", order_id],
    queryFn: () => fetchOrderDetails(order_id ?? ""),
    refetchOnWindowFocus: true,
    enabled: !!order_id,
  })
  const {
    data: order,
    isFetching: isOrderFetching,
    isLoading: isOrderLoading,
  } = useQuery({
    queryKey: ["order-data-fetch", order_id],
    queryFn: async () => {
      const data = await fetchOrderWithoutCart(order_id ?? "")
      return data
    },
    enabled: !!order_id,
  })

  useEffect(() => {
    if (orderDetails) {
      if (!pickupData?.pickup_time)
        setPickupData({
          pickup_time: "",
          contact_person: "",
          phone_number: orderDetails.calling_number,
          country_code: 91,
        })

      if (orderDetails.deal_cf_pickup_date) {
        const daysDiff = differenceInCalendarDays(
          new Date(), // today
          new Date(orderDetails.deal_cf_pickup_date), // pickup date
        )

        const totalRent = Math.max(
          daysDiff * orderDetails.deal_cf_total_per_day_rent,
          0,
        )
        seTotalDelayDays(daysDiff)
        setDelayRentalCharges(totalRent)
      }

      calculateTotals()
    }
  }, [orderDetails, setDelayRentalCharges, setPickupData, calculateTotals])

  // Set order ID when component mounts
  useEffect(() => {
    if (order_id && order_id !== orderId) {
      setOrderId(order_id)
    }
  }, [order_id, orderId, setOrderId])

  useEffect(() => {
    if (additionalOrderSummary?.deal_name) {
      setPreviousPayment(additionalOrderSummary.return_charge_received)
    }
  }, [additionalOrderSummary])

  useEffect(() => {
    setActiveStep("checklist")
  }, [])

  // const handleConfirmReturn = async () => {
  //   setIsSubmitting(true)
  //   try {
  //     // Here you would submit the return order
  //     UpdateReturnDetails()
  //     // hanlde Normal shipment api even payment is success or failure

  //     // Handle success (redirect, show success message, etc.)
  //     console.log("Return order submitted successfully")
  //   } catch (error) {
  //     console.error("Error submitting return order:", error)
  //   } finally {
  //     setIsSubmitting(false)
  //   }
  // }

  // Dynamic step title based on active step
  const getStepTitle = () => {
    switch (activeStep) {
      case "checklist":
        return "Step 1/3: Order Return Checklist"
      case "packing":
        return "Step 2/3: Secure Package"
      case "pickup":
        return "Step 3/3: Schedule Return"
      default:
        return "Schedule Return"
    }
  }

  const { mutate: handleReturnItemUpdate } = useMutation({
    mutationKey: ["updateReturnOrderItems"],
    mutationFn: () =>
      fetchWithAuthPost(
        "https://api.sharepal.in/api:AIoqxnqr/return/update-items",
        {
          order_id: orderId,
          return_items: { ...packagingSelections, ...productSelections },
        },
      ),
    onSuccess: () => {},
    onError: (error) => {
      toast.error(JSON.parse(error.message)?.message ?? "Something Went Wrong")
    },
  })

  const { mutate: UpdateReturnDetails, isPending: isUpdatedetailsPending } =
    useMutation({
      mutationFn: () =>
        fetchWithAuthPost(
          "https://api.sharepal.in/api:AIoqxnqr/return/update-details",
          {
            order_id: orderId,
            delay_rental_charge: delayRentalCharges,
            timeslot_charges: timeSlotCharges,
            tsa_applied: tsaLockApplied,
            ziplock_applied: zipLockApplied,
            return_payment_option: paymentOption,
            total_lost_charges: buyLostTotal,
          },
        ),
      onSuccess: () => {
        if (totalCharges > 0 && paymentOption == "pay_later") {
          router.replace("/return-payment/" + orderId + "?status=paylater")
        } else if (totalCharges == 0) {
          router.replace(
            "/return-payment/" + orderId + "?status=without_payment",
          )
        }
        setTimeout(() => {
          setIsScheduling(false)
        }, 1000)
      },
      onError: () => {
        setIsScheduling(false)
      },
    })

  const { data: shipmentData } = useQuery({
    queryKey: ["fetch-order-shipment-details", order_id],
    queryFn: async () => {
      // https://api.sharepal.in/api:BV_IWA_a/shipment/details
      const response = await fetchWithAuthPost<{
        order_address: { address: Address; address_id: number }
        store_address: Address
        shipment_detail: ShipmentDetailsResponse
      }>("https://api.sharepal.in/api:AIoqxnqr/shipment/details", {
        order_id: order_id,
      })
      return response
    },
    enabled: !!order_id,
  })

  const {
    data: alreadyBooked,
    isFetching: alreadyBookedFetching,
    isPending: alreadyBookedPending,
  } = useQuery({
    queryKey: ["fetch-return-already-booked", order_id],
    queryFn: async () => {
      const data = await fetchWithAuthPost<{
        schedule_date: string
        schedule_time: string
      } | null>(
        "https://api.sharepal.in/api:AIoqxnqr/shipment/schedule-check",
        {
          order_id: order_id,
        },
      )
      return data
    },
    enabled: !!order_id,
  })

  const { mutate: bookShipment, isPending: isBookingPending } = useMutation({
    mutationFn: async () => {
      setIsScheduling(true)
      if (!shipmentData?.shipment_detail) {
        throw new Error("Shipment details not available")
      }

      if (!pickupData?.pickup_time || pickupData?.pickup_time === "") {
        toast.error("Please Select Time")
        throw new Error("Time not selected")
      }
      const shipmentDetailsPickupDate = shipmentData.shipment_detail.pickup_date
      const scheduleDate =
        (getDateDifference(new Date(shipmentDetailsPickupDate), new Date()) ||
          0) < 0
          ? shipmentDetailsPickupDate
          : new Date()

      const timestamp = getCombinedTimestamp(
        shipmentDetailsPickupDate,
        pickupData?.pickup_time,
      )

      const adjustedTimestamp = timestamp - 15 * 60 * 1000

      const payload = {
        order_id: order_id,
        schedule_timestamp: adjustedTimestamp,
        calling_number: user?.calling_number,
        whatsapp_number: user?.whatsapp_number,
        schedule_date: format(new Date(scheduleDate), "dd-MMM-yyyy"),
        schedule_time: pickupData?.pickup_time,
        shipment_number: pickupData?.phone_number ?? "",
      }

      return fetchWithAuthPost(
        `https://api.sharepal.in/api:AIoqxnqr/shipment/schedule`,
        payload,
      )
    },
    onSuccess: () => {
      toast.success("Shipment Booked Successfully")
      UpdateReturnDetails()
    },
    onError: (error) => {
      setIsScheduling(false)
      toast.error(
        JSON.parse((error as Error).message).message ??
          "Failed To Schedule Shipment",
      )
    },
  })

  const handlePickup = () => {
    if (!pickupData?.pickup_time) {
      toast.error("Please select a pickup time.")
      return
    }
    if (totalCharges > 0 && paymentOption == "pay_now") {
      hanldePaymentForReturnOrder({
        order_id: orderId ?? "",
        amount: totalCharges,
        onSuccess: () => {
          bookShipment()
        },
      })
    } else {
      bookShipment()
    }
  }

  if (
    isOrderDetailsFetching ||
    isOrderFetching ||
    isOrderDetailsLoading ||
    isOrderLoading ||
    alreadyBookedPending ||
    alreadyBookedFetching
  ) {
    // Update to skeleton
    return (
      <div className='container flex min-h-screen flex-col items-center justify-center px-4 py-16 md:py-24'>
        <ReturnOrderSkeleton />
      </div>
    )
  }

  if (
    !isOrderFetching &&
    !isOrderDetailsFetching &&
    alreadyBooked?.schedule_date &&
    !alreadyBookedPending &&
    !alreadyBookedFetching
  ) {
    return (
      <div className='flex min-h-screen flex-col items-center justify-center px-4 py-16 md:py-24'>
        <ReturnAlreadyBooked order_id={order_id} />
      </div>
    )
  }
  if (
    !isOrderFetching &&
    !isOrderDetailsFetching &&
    !shipmentData &&
    order &&
    !alreadyBookedPending &&
    !alreadyBookedFetching
  ) {
    return (
      <div className='flex min-h-screen flex-col items-center justify-center px-4 py-16 md:py-24'>
        <ReturnNotAvailable order_id={order_id} />
      </div>
    )
  }

  if (
    isUpdatedetailsPending ||
    isBookingPending ||
    isReturnPaymentLoading ||
    isScheduling
  )
    return <CheckoutRedirectLoading />

  if (!order)
    return (
      <div className='flex h-full w-full items-center justify-center px-4 py-12'>
        <div className='text-center'>
          <div className='mb-4 text-6xl'>🛒</div>
          <h2 className='text-2xl font-semibold text-gray-800'>
            No Order Found
          </h2>
          <p className='mt-2 text-gray-600'>
            We couldn&apos;t find any order data to display. Try refreshing the
            page or check back later.
          </p>
          <Link
            href={"/dashboard/orders"}
            className='shadow mt-6 inline-flex items-center rounded-xl bg-primary px-6 py-2 text-white transition hover:bg-primary/90'
          >
            Go Back
          </Link>
        </div>
      </div>
    )

  if (!orderDetails)
    return (
      <div className='flex h-full w-full items-center justify-center px-4 py-12'>
        <div className='text-center'>
          <div className='mb-4 text-6xl text-red-500'>⚠️</div>
          <h2 className='text-2xl font-semibold text-gray-800'>
            Something Went Wrong
          </h2>
          <p className='mt-2 text-gray-600'>
            An unexpected error occurred. Please try again or contact support if
            the issue persists.
          </p>
          <div className='mt-6 flex justify-center gap-4'>
            <button
              onClick={() => location.reload()}
              className='shadow inline-flex items-center rounded-xl bg-primary px-6 py-2 text-white transition hover:bg-primary/90'
            >
              Retry
            </button>
            <Link
              href='https://api.whatsapp.com/send?phone=+917619220543&text=Hi'
              className='inline-flex items-center rounded-xl border border-gray-300 px-6 py-2 text-gray-700 transition hover:bg-gray-100'
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    )

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "w-full py-16 pb-28 md:py-24",
        activeStep === "packing" && "pb-40 md:pb-24",
        activeStep === "pickup" && totalCharges > 0 && "pb-40 md:pb-24",
      )}
    >
      <DeliveryAddressSelect
        order_id={orderDetails.deal_name}
        total_amount={orderDetails.deal_cf_total_order_amount}
        openSideView={openDeliveryAddress}
        setOpenSideView={setOpenDeliveryAddress}
      />
      <div className='container grid grid-cols-1 gap-4 lg:gap-6 xl:grid-cols-[1fr,400px]'>
        <ReturnStepper />
        {/* Left: Steps and Forms */}
        <div className='relative rounded-2xl bg-gray-100 p-2 md:rounded-3xl md:p-4 lg:px-6 lg:pb-4 lg:pt-3'>
          <div className='mb-2 hidden items-center justify-start gap-1 md:gap-2 lg:flex'>
            {/* back button */}
            <button
              type='button'
              className='rounded-full !p-2 hover:bg-neutral-50 md:!p-2.5'
              onClick={goToPrevStep}
              disabled={activeStep === "checklist"}
              aria-label='Go back to previous step'
              title='Go back to previous step'
              aria-disabled={activeStep === "checklist"}
            >
              <ChevronLeftIcon className='h-4 w-4 md:size-6' />
            </button>
            <Typography as='h1' className='text-xl font-bold md:text-2xl'>
              {getStepTitle()}
            </Typography>
          </div>

          <Separator className='mb-4 hidden lg:flex' />

          <div className='w-full'>
            {/* Step 1: Product Checklist */}
            {activeStep === "checklist" && <ProductChecklistForm />}

            {/* Step 2: Packing Instructions */}
            {activeStep === "packing" && (
              <PackingInstructionsForm imageUpload={imageUpload} />
            )}

            {/* Step 3: Pickup Details */}
            {activeStep === "pickup" && (
              <PickupDetailsForm
                onConfirmAction={handlePickup}
                onPrevAction={goToPrevStep}
                order={{
                  id: orderDetails.deal_name,
                  main_order_id: orderDetails.main_order_id,
                  customerName:
                    orderDetails.first_name + " " + orderDetails.last_name,
                  phoneNumber: orderDetails.calling_number,
                  deliveryAddress: order.order.delivery_address,
                  deliveryDate: orderDetails.deal_cf_delivery_date,
                  pickupDate: orderDetails.deal_cf_pickup_date,
                  total_rent: orderDetails.deal_cf_total_rent,
                  address_id: order.order.address_id,
                  total_perday_rent: orderDetails.deal_cf_total_per_day_rent,
                }}
                setOpenDeliveryAddress={setOpenDeliveryAddress}
                isLoading={isSubmitting}
              />
            )}
          </div>
        </div>

        {/* Right: Summary Card */}
        <div className='hidden self-start md:block lg:sticky lg:top-5 lg:col-span-1'>
          <ReturnOrderSummary
            orderId={orderId || ""}
            handlePickup={handlePickup}
            isFileUploading={imageUpload.isUploading}
            filesCount={imageUpload?.files.length || 0}
            handleFileUpload={imageUpload.handleUpload}
            currentStep={activeStep}
            orderSummary={orderDetails}
          />
        </div>
      </div>

      {/* Review Modal */}
      {showReviewModal && (
        <ReviewModal
          onClose={() => toggleReviewModal(false)}
          onConfirm={() => {
            toggleReviewModal(false)
            handleReturnItemUpdate()
            goToNextStep()
          }}
          orderId={order_id}
        />
      )}
    </motion.div>
  )
}

export default ReturnOrder
