"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { Typography } from "@/components/ui/typography"

import { useReturnOrderStore } from "@/store/return-order-store"
import { OrderSummary } from "@/types/order"
import { ReturnStep } from "@/types/return-order"
import { formVariants } from "@/utils/animation-variants"
import { motion } from "framer-motion"
import { AlertCircleIcon, Download, LoaderCircleIcon } from "lucide-react"
import Link from "next/link"
import { useMemo } from "react"
import { InfoCircleFilledIcon } from "sharepal-icons"
import { toast } from "sonner"
import ReturnOrderCharges from "./return-order-charges"

interface ReturnOrderSummaryProps {
  orderId: string
  currentStep: ReturnStep
  handlePickup: () => void
  handleFileUpload: () => void
  filesCount: number
  isFileUploading: boolean
  orderSummary: OrderSummary
}

export function ReturnOrderSummary({
  orderId,
  handlePickup,
  filesCount,
  handleFileUpload,
  orderSummary,
  isFileUploading,
}: ReturnOrderSummaryProps) {
  const {
    totalCharges,
    activeStep,
    toggleReviewModal,
    tsaLockApplied,
    zipLockApplied,
    paymentOption,
    setPaymentOption,
    setTsaLockApplied,
    setZipLockApplied,
    products,
    packaging,
    productSelections,
    packagingSelections,
    pickupData,
    previousPayment,
    tsaPresent,
  } = useReturnOrderStore()

  const handleNextStep = (e: React.MouseEvent<HTMLButtonElement>): void => {
    e.preventDefault()
    // Logic to handle next step, e.g., navigate to payment or confirmation
    if (activeStep === "checklist") {
      //open review modal or proceed to next step
      toggleReviewModal(true)
    }
    if (activeStep == "packing") {
      if (!tsaLockApplied && tsaPresent) {
        toast.info("TSA Lock is Mandatory.")
        return
      }
      if (filesCount <= 0) {
        toast.error("Please upload at least one file.")
        return
      }

      handleFileUpload()
    }

    if (activeStep === "pickup") {
      if (!pickupData?.pickup_time) {
        toast.error("Please select a pickup time.")
        return
      }
      //open review modal or proceed to next step
      handlePickup()
    }
  }

  const { isNextButtonDisabled } = useMemo(() => {
    const totalProductsCount = products.length
    const checkedProductsCount = Object.keys(productSelections).length

    const totalPackagingCount = packaging.length
    const checkedPackagingCount = Object.keys(packagingSelections).length

    // Determine if the Next button should be disabled
    const hasPackaging = totalPackagingCount > 0
    const onlyHasPackgingItems =
      totalPackagingCount > 0 && totalProductsCount <= 0
    const allProductsSelected =
      checkedProductsCount === totalProductsCount && onlyHasPackgingItems
        ? totalProductsCount <= 0
        : totalProductsCount > 0
    const allPackagingSelected = hasPackaging
      ? checkedPackagingCount === totalPackagingCount
      : true

    let isNextButtonDisabled = false

    if (activeStep === "checklist") {
      isNextButtonDisabled = !(allProductsSelected && allPackagingSelected)
    } else if (activeStep === "packing") {
      isNextButtonDisabled = filesCount <= 0 || (!tsaLockApplied && tsaPresent)
    } else {
      isNextButtonDisabled = !pickupData?.pickup_time
    }

    return {
      totalProductsCount,
      checkedProductsCount,
      totalPackagingCount,
      checkedPackagingCount,
      isNextButtonDisabled,
    }
  }, [
    products,
    packaging,
    productSelections,
    packagingSelections,
    pickupData?.pickup_time,
    activeStep,
    filesCount,
    tsaLockApplied,
    tsaPresent,
  ])

  return (
    <motion.div
      variants={formVariants}
      initial='hidden'
      animate='visible'
      className='space-y-4'
    >
      <Card className='sticky top-5 rounded-2xl border border-neutral-200 bg-gray-100 md:rounded-3xl'>
        <CardContent className='p-4 md:p-6'>
          <div className='flex items-center justify-start gap-2'>
            {/* icon */}
            <div className='flex h-10 w-10 items-center justify-center bg-secondary-200 md:h-14 md:w-14 md:overflow-hidden md:rounded-full'>
              <svg
                xmlns='http://www.w3.org/2000/svg'
                width='100%'
                height='100%'
                fill='none'
                className='p-2.5'
                viewBox='0 0 32 32'
              >
                <path
                  fill='#1945E8'
                  d='M6.667 10.667v14.666h18.666V10.667h-4v10.666L16 18.667l-5.333 2.666V10.667zM4 28V7.733L7.067 4h17.866L28 7.733V28zM7.2 8h17.6l-1.133-1.333H8.333zm6.133 2.667V17L16 15.667 18.667 17v-6.333z'
                ></path>
              </svg>
            </div>

            <div>
              <Typography as={"h6"} className='text-sh4 font-bold md:text-h6'>
                #{orderId}
              </Typography>
              <Typography
                as={"p"}
                className='!text-o4 text-neutral-600 md:!text-b6'
              >
                Order Placed On:{"  "}
                <span className='text-neutral-800'>
                  {/* {formatDate(order.order_date)} */}
                </span>
              </Typography>
            </div>
          </div>

          <div className='mt-2'>
            <Button asChild variant='outline-primary' className='w-full'>
              <Link
                href={orderSummary.deal_cf_order_pdf}
                target='_blank'
                rel='noopener noreferrer'
              >
                <Download className='h-4 w-4' />
                Order Summary
              </Link>
            </Button>
          </div>

          <Separator className='my-4' />

          {activeStep === "checklist" && (
            <>
              <div className='mt-4 text-b2'>
                <Typography as='p' className='text-neutral-700'>
                  Returns can often get messy.
                </Typography>
                <Typography as='p' className='text-neutral-700'>
                  This checklist contains everything we sent in your order so
                  you can return hassle-free.
                </Typography>
                <Typography as='p' className='font-medium text-neutral-800'>
                  Select <span className='font-bold'>RETURN</span>,{" "}
                  <span className='font-bold'>BUY</span> or{" "}
                  <span className='font-bold'>LOST</span> for each item.
                </Typography>
              </div>

              {/* Info about charges */}
              {/* <div className='mt-4 flex items-start gap-2 rounded-2xl bg-neutral-150 p-3 text-b6'>
                <AlertCircle className='mt-0.5 h-5 w-5 flex-shrink-0 text-neutral-600' />
                <Typography as='p' className='text-sm text-neutral-700'>
                  Selecting BUY or LOST will incur charges equivalent to the
                  value of the respective product.
                </Typography>
              </div> */}

              <div className='mt-2 flex items-start justify-start gap-1 rounded-2xl bg-neutral-150 px-3 py-3'>
                <AlertCircleIcon className='size-4 min-w-4 text-neutral-700' />
                <Typography as='p' className='text-b6 text-neutral-700'>
                  Damage charges will be calculated once return is received at
                  warehouse and quality check is complete.
                </Typography>
              </div>
              {previousPayment > 0 && totalCharges == 0 && (
                <Typography
                  as='p'
                  className='my-2 flex items-center gap-1 rounded-md bg-neutral-150 p-1 text-xs text-neutral-700'
                >
                  <InfoCircleFilledIcon /> The excess amount paid will be
                  refunded by 3 to 7 working days.
                </Typography>
              )}
            </>
          )}
          {activeStep === "packing" && (
            <div className='mt-4 text-b2'>
              <Typography as='p' className='text-neutral-700'>
                TSA and zip locks help keep your package secure — no stress,
                even on the way back.
              </Typography>
            </div>
          )}

          {activeStep === "pickup" && (
            <div className='mt-4 text-b2'>
              <Typography as='p' className='text-neutral-700'>
                Select a time-slot when you are available to return your order
                at the pickup address.
              </Typography>
            </div>
          )}

          {/* Lock Type Selection - Show only on packing step */}
          {activeStep === "packing" && (
            <div className='mt-4 space-y-4 border-t pt-4'>
              <Typography as='h4' className='font-semibold text-neutral-900'>
                Lock Type Selection
              </Typography>

              <div className='space-y-3'>
                <div className='flex items-center space-x-3'>
                  <Checkbox
                    id='tsa-lock-summary'
                    checked={tsaLockApplied}
                    onCheckedChange={(checked) => setTsaLockApplied(!!checked)}
                  />
                  <label
                    htmlFor='tsa-lock-summary'
                    className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                  >
                    TSA Lock Applied
                    {tsaPresent && <span className='text-red-500'> * </span>}
                  </label>
                </div>

                <div className='flex items-center space-x-3'>
                  <Checkbox
                    id='zip-lock-summary'
                    checked={zipLockApplied}
                    onCheckedChange={(checked) => setZipLockApplied(!!checked)}
                  />
                  <label
                    htmlFor='zip-lock-summary'
                    className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                  >
                    Zip Lock Applied
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Only show summary if there are charges */}
          {typeof totalCharges === "number" &&
            (totalCharges > 0 || previousPayment > 0) && <ReturnOrderCharges />}

          {/* Payment Options - Show on pickup step when there are charges */}
          {activeStep === "pickup" && totalCharges > 0 && (
            <div className='mt-6 space-y-3 border-t pt-4'>
              <Typography as='h4' className='font-semibold text-neutral-900'>
                Payment Options
              </Typography>

              <RadioGroup
                value={paymentOption}
                onValueChange={setPaymentOption}
                className='space-y-1'
              >
                <div className='relative flex w-full items-center gap-2 rounded-2xl border-2 border-input bg-neutral-150 p-4 has-[[data-state=checked]]:border-secondary-600 has-[[data-state=checked]]:bg-transparent'>
                  <RadioGroupItem
                    value='pay_now'
                    id='pay_now'
                    className='after:absolute after:inset-0'
                  />
                  <label
                    htmlFor='pay_now'
                    className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                  >
                    Pay Now
                  </label>
                </div>

                <div className='relative flex w-full items-center gap-2 rounded-2xl border-2 border-input bg-neutral-150 p-4 has-[[data-state=checked]]:border-secondary-600 has-[[data-state=checked]]:bg-transparent'>
                  <RadioGroupItem
                    value='pay_later'
                    id='pay_later'
                    className='after:absolute after:inset-0'
                  />
                  <label
                    htmlFor='pay_later'
                    className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                  >
                    Pay Later
                  </label>
                </div>
              </RadioGroup>

              {paymentOption === "pay_later" && (
                <div className='rounded-lg border border-yellow-200 bg-yellow-50 px-3 py-2'>
                  <Typography as='p' className='text-xs text-yellow-800'>
                    Note: Late payment charges may apply if payment is not made
                    within the specified time.
                  </Typography>
                </div>
              )}
            </div>
          )}

          <div className='mt-6'>
            <Button
              onClick={handleNextStep}
              disabled={isNextButtonDisabled}
              variant={"primary"}
              className='w-full'
              size={"lg"}
            >
              {isFileUploading ? (
                <LoaderCircleIcon className='animate-spin' />
              ) : activeStep === "pickup" ? (
                totalCharges > 0 && paymentOption === "pay_now" ? (
                  "Pay and Schedule Return"
                ) : (
                  "Schedule Return"
                )
              ) : (
                "Next"
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
