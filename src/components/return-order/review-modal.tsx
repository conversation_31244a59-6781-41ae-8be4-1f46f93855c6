"use client"

import { AdaptiveWrapper } from "@/components/modals/adaptive-wrapper"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import { useReturnOrderStore } from "@/store/return-order-store"
import { AlertCircleIcon } from "lucide-react"
import { useMemo, useState } from "react"
import { ChargeItem } from "../checkout/order-summary-new/charge-item"

interface ReviewModalProps {
  onClose: () => void
  onConfirm: () => void
  orderId: string
}

export function ReviewModal({ onClose, onConfirm, orderId }: ReviewModalProps) {
  const [open, setOpen] = useState(true)
  const {
    buyLostItemsCount,
    totalCharges,
    buyLostTotal,
    delayRentalCharges,
    timeSlotCharges,
    productSelections,
    packagingSelections,
  } = useReturnOrderStore()

  const handleOpenChange = (open: boolean) => {
    setOpen(open)
    if (!open) onClose()
  }

  const showDamageMessage = useMemo(() => {
    // Check if any product is marked as damaged
    const hasDamagedProduct = Object.values(productSelections).some(
      (selection) => selection === "damaged",
    )

    // Check if any packaging is marked as damaged
    const hasDamagedPackaging = Object.values(packagingSelections).some(
      (selection) => selection === "damaged",
    )

    // Return true if any item is marked as damaged
    return hasDamagedProduct || hasDamagedPackaging
  }, [productSelections, packagingSelections])

  return (
    <AdaptiveWrapper
      open={open}
      onOpenChange={handleOpenChange}
      title='Review & Confirm Checklist'
      // title=''
      desktop={{ type: "dialog" }}
      tablet={{ type: "dialog" }}
      mobile={{ type: "drawer", side: "bottom" }}
      className='md:max-w-[600px]'
    >
      <div className='space-y-4 p-4 md:py-4 md:pt-0'>
        <Typography as={"p"} className='text-left text-sh6 md:text-center'>
          Order No: <span>{orderId}</span>
        </Typography>
        {totalCharges > 0 ? (
          <Typography
            as='p'
            className='text-left text-sm text-neutral-600 md:text-center'
          >
            Please confirm your selection to proceed to the next step. You can
            select mode of payment after scheduling return.
          </Typography>
        ) : (
          <Typography
            as='p'
            className='text-left text-sm text-neutral-600 md:text-center'
          >
            Please confirm your selection to proceed.
          </Typography>
        )}

        {showDamageMessage && (
          <div className='mt-2 flex items-start justify-start gap-1 rounded-2xl bg-neutral-150 px-2.5 py-2 md:mt-4 md:gap-2 md:bg-neutral-200 md:px-4 md:py-3'>
            <AlertCircleIcon className='size-3 min-w-3 text-neutral-700 md:size-4 md:min-w-4' />
            <Typography as='p' className='text-b6 text-neutral-700'>
              Damage charges will be calculated once return is received and
              quality check is complete.
            </Typography>
          </div>
        )}

        {totalCharges > 0 && (
          <div className='space-y-3 rounded-xl border-2 border-neutral-200 bg-neutral-50 p-4 md:rounded-2xl'>
            {buyLostTotal > 0 && (
              <ChargeItem
                item={{
                  label: "Lost Items",
                  items_count: buyLostItemsCount,
                  amount: buyLostTotal,
                  items_count_text: ` item${buyLostItemsCount > 1 ? "s" : ""} lost`,
                }}
              />
            )}

            {delayRentalCharges > 0 && (
              <ChargeItem
                item={{
                  label: "Delay Rental Charges",
                  amount: delayRentalCharges,
                  items_count: 1,
                  items_count_text: "day delayed",
                }}
              />
            )}
            {timeSlotCharges > 0 && (
              <ChargeItem
                item={{
                  label: "Time Slot Charges",
                  amount: timeSlotCharges,
                }}
              />
            )}

            <Separator className='my-2' />
            <div className='flex items-center justify-between'>
              <div>
                <Typography
                  as='p'
                  className='text-lg font-bold text-neutral-900'
                >
                  Total Charges
                </Typography>
                <div className='text-xs text-gray-500'>
                  Price incl. of all taxes
                </div>
              </div>

              <Typography as='p' className='text-lg font-bold'>
                {moneyFormatter(totalCharges)}
              </Typography>
            </div>
          </div>
        )}

        <div className='mx-auto flex flex-col gap-3 pt-4 md:max-w-80'>
          <Button
            onClick={onConfirm}
            size={"lg"}
            className='w-full bg-blue-600 text-white hover:bg-blue-700'
          >
            Confirm Selection & Proceed
          </Button>
          <Button
            variant='outline'
            onClick={onClose}
            size={"lg"}
            className='w-full border-blue-600 text-blue-600 hover:bg-blue-50'
          >
            Edit Checklist
          </Button>
        </div>
      </div>
    </AdaptiveWrapper>
  )
}
