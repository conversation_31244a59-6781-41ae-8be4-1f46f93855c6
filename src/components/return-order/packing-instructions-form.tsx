/* eslint-disable @next/next/no-img-element */
"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Typography } from "@/components/ui/typography"
import SpImage from "@/shared/SpImage/sp-image"
import { useReturnOrderStore } from "@/store/return-order-store"
import { toast } from "sonner"

import { ImageUploadType } from "@/hooks/use-image-upload"
import { AlertCircleIcon } from "lucide-react"
import { useEffect } from "react"
import ReturnOrderCharges from "./return-order-charges"
import UploadSection from "./upload-section"

interface PackingStep {
  title: string
  description: string
  image: string
}

interface PackingInstructions {
  steps: PackingStep[]
}

// Mock packing instructions
const packingInstructions: PackingInstructions = {
  steps: [
    {
      title: "Pack the Items",
      description: "Place all products in the packages.",
      image: "https://images.sharepal.in/misc/hard-coded/returnnew-step1.webp",
    },
    {
      title: "Secure the lock",
      description: "Apply the TSA lock and jumble the PIN.",
      image: "https://images.sharepal.in/misc/hard-coded/returnnew-step2.webp",
    },
    {
      title: "Confirm the lock",
      description: "Apply the ZIP lock and you’re done!",
      image: "https://images.sharepal.in/misc/hard-coded/returnnew-step3.webp",
    },
  ],
}

export function PackingInstructionsForm({
  imageUpload,
}: {
  imageUpload: ImageUploadType
}) {
  const {
    tsaLockApplied,
    zipLockApplied,
    setTsaLockApplied,
    setZipLockApplied,
    totalCharges,
    packagingSelections,
    packaging,
    setTsaPresent,
    tsaPresent,
  } = useReturnOrderStore()

  useEffect(() => {
    packaging.map((data) => {
      if (data.product_code == "TSA-LOCK") {
        if (packagingSelections[data.product_id] != "buy_lost") {
          setTsaPresent(true)
        }
      }
    })
  }, [packaging, packagingSelections, setTsaPresent])

  const handlePacking = () => {
    if (!tsaLockApplied && tsaPresent) {
      toast.info("TSA Lock is Mandatory.")
      return
    }
    if (imageUpload.files.length === 0) {
      toast.error("Please upload at least one image.")
      return
    }

    imageUpload.handleUpload()
  }
  return (
    <div className='w-full px-2 md:px-0'>
      {/* Header */}
      <Typography
        as='p'
        className='my-4 hidden text-left text-b2 font-medium text-neutral-700 md:pl-4 lg:block'
      >
        Simply upload pictures of the secured packages and tick the checkbox
        once you&apos;re done.
      </Typography>

      <div className='mb-4 flex flex-col items-center justify-center gap-1.5 p-2 md:gap-3 lg:hidden'>
        <Typography
          as='h2'
          className='text-center text-sh2 font-semibold md:text-left'
        >
          Secure Package
        </Typography>
        <Typography as='p' className='text-center text-b6 text-gray-500'>
          Simply upload pictures of the secured packages and tick the checkbox
          once you’re done.
        </Typography>
      </div>

      {/* Instructions Steps */}
      <div className='mt-2 space-y-3 rounded-xl border border-neutral-200 bg-white px-2 py-3 md:rounded-2xl'>
        <Typography as='h3' className='text-h7 text-neutral-800'>
          How to Pack Like a Pro
        </Typography>

        <div className='custom-scrollbar-black custom-scrollbar-black flex gap-3 overflow-x-auto py-2 md:gap-3 2xl:overscroll-none'>
          {packingInstructions.steps.map((step, index) => (
            <Card
              key={index}
              className='min-w-[220px] flex-1 overflow-hidden rounded-none border-none p-0'
            >
              <CardContent className='p-0'>
                <div className='relative'>
                  <div className='left-2 top-1 flex flex-col items-start justify-start gap-1 rounded-full px-0 pb-2 text-sh5 font-semibold text-neutral-800'>
                    Step {index + 1}
                    <Typography
                      as='p'
                      className='min-w-max text-b6 leading-relaxed text-neutral-600 md:text-b7'
                    >
                      {step.description}
                    </Typography>
                  </div>
                  <div className='from-primary-50 flex items-center justify-center rounded-2xl border border-neutral-150 bg-gradient-to-br to-primary-100'>
                    {/* <ImageIcon className='h-12 w-12 text-primary-300' /> */}
                    <SpImage
                      className='h-max min-h-[100px] w-full rounded-lg object-contain'
                      src={step.image}
                      width={500}
                      height={300}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className='flex items-start justify-start gap-1 py-1 text-neutral-400'>
          <AlertCircleIcon className='size-3 min-w-3' />
          <Typography as={"p"} className='text-b6 text-neutral-400'>
            Ensure all products are placed inside the packages; the ZIP lock
            once applied can only be cut off to open the package.
          </Typography>
        </div>
      </div>

      {/* Upload Section Component with all upload logic */}
      <UploadSection imageUpload={imageUpload} />

      {/* Only show summary if there are charges */}

      {typeof totalCharges === "number" && totalCharges > 0 && (
        <div className='block md:hidden'>
          <ReturnOrderCharges />
        </div>
      )}

      <div className='fixed bottom-0 left-0 right-0 flex w-full flex-col gap-3 bg-gray-100 p-3 pt-6 md:relative md:hidden'>
        <div className='flex items-center space-x-3'>
          <Checkbox
            id='tsa-lock-summary'
            checked={tsaLockApplied}
            onCheckedChange={(checked) => setTsaLockApplied(!!checked)}
          />
          <label
            htmlFor='tsa-lock-summary'
            className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            TSA Lock Applied
            {tsaPresent && <span className='text-red-500'>*</span>}
          </label>
        </div>

        <div className='flex items-center space-x-3'>
          <Checkbox
            id='zip-lock-summary'
            checked={zipLockApplied}
            onCheckedChange={(checked) => setZipLockApplied(!!checked)}
          />
          <label
            htmlFor='zip-lock-summary'
            className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
          >
            Zip Lock Applied
          </label>
        </div>

        <Button
          disabled={
            imageUpload?.files.length === 0 || (!tsaLockApplied && tsaPresent)
          }
          size={"lg"}
          variant={"primary"}
          onClick={handlePacking}
          className='w-full'
          type='submit'
        >
          Next
        </Button>
      </div>
    </div>
  )
}
