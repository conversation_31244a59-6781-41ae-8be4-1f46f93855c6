"use client"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useReturnOrderStore } from "@/store/return-order-store"
import { motion } from "framer-motion"
import { Check, ChevronLeft, ChevronRight } from "lucide-react"
import * as React from "react"

export function ReturnStepper() {
  const {
    isChecklistCompleted,
    isPackingCompleted,
    isPickupCompleted,
    activeStep,
    goToNextStep,
    goToPrevStep,
  } = useReturnOrderStore()

  // Define the order of steps
  const steps = ["checklist", "packing", "pickup"]

  // Get the current step index
  const currentStepIndex = steps.indexOf(activeStep)

  const isStepCompleted = (step: string) => {
    switch (step) {
      case "checklist":
        return isChecklistCompleted
      case "packing":
        return isPackingCompleted
      case "pickup":
        return isPickupCompleted
      default:
        return false
    }
  }

  return (
    <div className='flex w-full items-center justify-center gap-2 px-4 lg:hidden'>
      <Button
        variant='ghost'
        size='icon'
        onClick={goToPrevStep}
        disabled={currentStepIndex === 0}
        className='shrink-0'
      >
        <ChevronLeft className='h-4 w-4' />
      </Button>

      <div className='flex w-full items-center'>
        {steps.map((step, index) => {
          const isActive = step === activeStep
          const isCompleted = isStepCompleted(step)

          return (
            <React.Fragment key={step}>
              <div className='relative flex flex-1 items-center justify-center'>
                <motion.div
                  initial={false}
                  animate={{
                    scale: isActive ? 1.1 : 1,
                    backgroundColor: isActive
                      ? "rgb(37, 99, 235)"
                      : isCompleted
                        ? "rgb(34, 197, 94)"
                        : "rgb(229, 231, 235)",
                  }}
                  className={cn(
                    "relative z-10 flex h-6 w-6 items-center justify-center rounded-full text-sm font-medium md:h-8 md:w-8",
                    isActive || isCompleted ? "text-white" : "text-gray-600",
                  )}
                >
                  {isCompleted ? (
                    <Check className='size-3 md:size-4' />
                  ) : (
                    <span className='text-o4 md:text-o2'>{index + 1}</span>
                  )}
                </motion.div>

                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "absolute left-[50%] h-[2px] w-full transition-colors",
                      index < currentStepIndex || isCompleted
                        ? "bg-blue-600"
                        : "bg-gray-200",
                    )}
                  />
                )}
              </div>
            </React.Fragment>
          )
        })}
      </div>

      <Button
        variant='ghost'
        size='icon'
        onClick={goToNextStep}
        disabled={currentStepIndex === steps.length - 1}
        className='shrink-0'
      >
        <ChevronRight className='h-4 w-4' />
      </Button>
    </div>
  )
}
