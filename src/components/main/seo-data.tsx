"use client"
import useCommonDataStore, { SeoItem } from "@/store/common-data"
import React from "react"

type Props = {
  seoData: SeoItem
}

const SeoData = ({ seoData }: Props) => {
  const { setSeo } = useCommonDataStore()

  React.useEffect(() => {
    setSeo(seoData)
  }, [seoData, setSeo])
  return (
    <div className='hidden'>
      <div>{seoData.desc}</div>
    </div>
  )
}

export default SeoData
