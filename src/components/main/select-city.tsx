"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Tit<PERSON>,
} from "@/components/ui/dialog"
import type { City } from "@/types/address"
import { motion } from "framer-motion"
import Image from "next/image"
import { memo, useCallback, useEffect, useMemo, useState } from "react"
// import { useQuery } from '@tanstack/react-query'
import { Alert, AlertDescription } from "@/components/ui/alert"
// import { fetchCities } from '@/actions/city'
import { clearCart } from "@/actions/cart"
import { updateUserCity } from "@/actions/user"
import { Skeleton } from "@/components/ui/skeleton"
import { getCookie, setCookie } from "@/functions/cookies"
import { capitalizeFirstLetter } from "@/functions/small-functions"
import { trackCitySelected } from "@/lib/gtag-event"
import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, usePathname, useRouter } from "next/navigation"
import { SlidingAlert } from "../ui/sliding-alert"

interface CityDialogProps {
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  cities: City[] // Receiving cities from the server component
}

const POPULAR_CITIES = [
  "Bangalore",
  "Mumbai",
  "Delhi",
  "Hyderabad",
  "Kolkata",
  "Chennai",
]

function CityDialog({ isOpen, onOpenChange, cities }: CityDialogProps) {
  const { selectedCity, setSelectedCity, setCitySurge } = useRentalStore()
  const { items_count, resetCheckout } = useCheckoutStore()
  const router = useRouter()
  const { city, cat, subcat, product } = useParams()
  const pathname = usePathname()

  const [showAlert, setShowAlert] = useState(false)
  const [pendingCity, setPendingCity] = useState<City | null>(null)

  const handleDialogChange = (open: boolean) => {
    if (showAlert) {
      return // Prevent dialog from closing if alert is open
    }

    if (city === "india" && selectedCity.city_url) {
      // router.replace(`/${selectedCity.city_url}`)
      return
    } else if (city === "india") {
      return
    }

    if (selectedCity.city_url) {
      onOpenChange(open)
    }
  }

  // const {
  //   data: cities,
  //   isLoading,
  //   error,
  // } = useQuery({
  //   queryKey: ['cities'],
  //   queryFn: async () => await fetchCities(), // Explicitly calling the function
  //   refetchOnWindowFocus: false,
  // })
  const isLoading = false
  const error = false

  const { popularCities, otherCities, filteredCities } = useMemo(() => {
    if (!cities) return { popularCities: [], otherCities: [] }

    const filtered = cities.filter(
      (city) => city.city_type !== 1 && city.city_type !== 6,
    )

    return {
      popularCities: filtered.filter((city) =>
        POPULAR_CITIES.includes(city.city_name),
      ),
      otherCities: filtered.filter(
        (city) => !POPULAR_CITIES.includes(city.city_name),
      ),

      filteredCities: filtered,
    }
  }, [cities])

  const handleCitySelect = useCallback(
    (city: City) => {
      if (items_count > 0) {
        setPendingCity(city)
        setShowAlert(true)
        return
      }
      changeCityAndRedirect(city)
      updateUserCity(capitalizeFirstLetter(city?.city_url))
    },
    [items_count, cat, subcat, product, router, onOpenChange],
  )

  const changeCityAndRedirect = useCallback(
    (city: City) => {
      const updatedUrl = `/${city.city_url}${
        cat ? `/${cat}` : ""
      }${subcat ? `/${subcat}` : ""}${product ? `/${product}` : ""}`

      resetCheckout()
      router.push(updatedUrl)
      trackCitySelected(city.city_url)
      clearCart()
      onOpenChange(!city.city_url)
    },
    [cat, subcat, product, router, onOpenChange, resetCheckout],
  )

  useEffect(() => {
    if (selectedCity.city_name) setCitySurge(selectedCity.city_surge)
  }, [selectedCity.city_name, selectedCity.city_surge, setCitySurge])

  useEffect(() => {
    const allCities = [...popularCities, ...otherCities]
    const city_cookie = getCookie("selected_city")
    const city_data = allCities?.find(
      (data) => data.city_url === (city || city_cookie),
    )
    if (city_data?.city_url) {
      setSelectedCity(city_data)
      setCookie("selected_city", city_data?.city_url, { expires: 365 })
      updateUserCity(capitalizeFirstLetter(city_data?.city_url))
    }

    // if (pathname === '/' && city_data?.city_url) {
    //   router.replace(`/${city_data.city_url}`)
    // }
  }, [city, pathname, popularCities, otherCities, setSelectedCity, router])
  if (!isOpen) {
    return null
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleDialogChange}>
        <DialogContent
          className='max-h-screen w-[95%] max-w-3xl gap-2 overflow-y-auto !rounded-3xl bg-gray-100 p-6 md:gap-4'
          onInteractOutside={(e) => {
            if (showAlert) {
              e.preventDefault()
            }
          }}
          onEscapeKeyDown={(e) => {
            if (showAlert) {
              e.preventDefault()
            }
          }}
        >
          <DialogHeader>
            <DialogTitle className='text-center text-xl font-bold md:text-2xl'>
              Select Your City
            </DialogTitle>
          </DialogHeader>

          <div className='space-y-8'>
            {/* for functional city navigation */}
            <>
              <div className='space-y-4'>
                <div className='relative text-center'>
                  <div className='absolute inset-0 flex items-center'>
                    <span className='w-full border-[1.3px] border-t' />
                  </div>
                  <span className='relative bg-gray-100 px-4 text-sm text-muted-foreground'>
                    Popular Cities
                  </span>
                </div>
                {isLoading ? (
                  <div className='grid grid-cols-3 gap-1 md:grid-cols-6'>
                    {[...Array(6)].map((_, index) => (
                      <Skeleton
                        key={index}
                        className='h-24 w-full rounded-xl'
                      />
                    ))}
                  </div>
                ) : (
                  <div className='grid grid-cols-3 gap-1 md:grid-cols-6'>
                    {popularCities.map((city) => (
                      //changed from button to <a> tag just to top loading as on button the loader is not working
                      <motion.button
                        // href="#"
                        key={city.id}
                        onClick={() => handleCitySelect(city)}
                        className={cn(
                          "flex flex-col items-center space-y-2 rounded-xl p-3 transition-colors hover:bg-gray-50 md:min-w-[96px]",
                          city.city_url === selectedCity.city_url
                            ? "border border-primary-500 bg-primary-100"
                            : "",
                        )}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        whileHover={{ scale: 1 }}
                      >
                        <Image
                          src={`https://images.sharepal.in/cities/${city.city_url}.svg`}
                          // src={`/bangalore-city.png`}
                          alt={city.city_name}
                          width={48}
                          height={48}
                          className='h-12 w-12'
                        />

                        <span className='text-xs font-medium'>
                          {city.city_name}
                        </span>
                      </motion.button>
                    ))}
                  </div>
                )}
              </div>

              <div className='space-y-4'>
                <div className='relative text-center'>
                  <div className='absolute inset-0 flex items-center'>
                    <span className='w-full border-[1.3px] border-t' />
                  </div>
                  <span className='relative bg-gray-100 px-4 text-sm text-muted-foreground'>
                    Other Cities
                  </span>
                </div>
                {isLoading ? (
                  <div className='grid grid-cols-2 gap-3 sm:grid-cols-3 sm:gap-5 lg:grid-cols-4 lg:gap-7'>
                    {[...Array(8)].map((_, index) => (
                      <Skeleton key={index} className='h-8 w-full rounded-xl' />
                    ))}
                  </div>
                ) : (
                  <div className='grid grid-cols-2 gap-3 sm:grid-cols-3 sm:gap-5 lg:grid-cols-4 lg:gap-7'>
                    {otherCities.map((city) => (
                      <motion.button
                        key={city.id}
                        onClick={() => handleCitySelect(city)}
                        className={cn(
                          "rounded-xl border px-6 py-2 text-xs transition-colors hover:bg-gray-50",
                          city.city_url === selectedCity.city_url
                            ? "border border-primary-500 bg-primary-100"
                            : "",
                        )}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        // whileHover={{ scale: 1.05 }}
                      >
                        {city.city_name}
                      </motion.button>
                    ))}
                  </div>
                )}
              </div>
            </>

            {/* for seo city with Link */}
            {filteredCities?.map((city) => (
              <Link
                key={city.id}
                href={`/${city.city_url}${cat ? `/${cat}` : ""}${
                  subcat ? `/${subcat}` : ""
                }${product ? `/${product}` : ""}`}
                className={cn(
                  "hidden rounded-xl border px-6 py-2 text-xs transition-colors hover:bg-gray-50",
                  city.city_url === selectedCity.city_url
                    ? "border border-primary-500 bg-primary-100"
                    : "",
                )}
              >
                {city.city_name}
              </Link>
            ))}

            {/* <button
              type='button'
              onClick={getCurrentLocation}
              className='mx-auto flex w-full items-center justify-center gap-2 rounded-full border-2 border-dashed border-blue-600 py-3 text-blue-600'
            >
              <LocateFixedIcon className='h-5 w-5' />
              <span className='text-sm font-semibold'>
                Auto-detect my location
              </span>
            </button> */}
          </div>

          {error && (
            <Alert variant='destructive'>
              <AlertDescription>
                Failed to load cities. Please try again later.
              </AlertDescription>
            </Alert>
          )}
        </DialogContent>
      </Dialog>

      <SlidingAlert
        show={showAlert}
        title='Change City?'
        description='Changing your city will remove all items from your cart. Are you sure you want to continue?'
        confirmText='Change City'
        cancelText='Cancel'
        onCancel={() => {
          setShowAlert(false)
          setPendingCity(null)
        }}
        onConfirm={() => {
          if (pendingCity) {
            changeCityAndRedirect(pendingCity)
          }
          setShowAlert(false)
          setPendingCity(null)
        }}
      />
    </>
  )
}

export default memo(CityDialog)
