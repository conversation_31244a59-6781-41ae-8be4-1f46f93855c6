import Image from "next/image"
import SectionTitle from "../section-title"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { HARD_CODED_IMAGE_URL } from "@/constants"
import SpImage from "@/shared/SpImage/sp-image"
import { ChevronRight } from "lucide-react"
import Link from "next/link"
import { Button } from "../ui/button"
const WhyRentingisBetter = ({ city }: { city?: string }) => (
  <section className='flex flex-col gap-1 bg-bento-section py-5 md:gap-5 lg:gap-12 lg:py-12'>
    {/* Why Renting Better Title */}
    <SectionTitle
      cText='better?'
      cTColor='text-decorative-pink'
      nText='Why Renting is '
      className='px-4 text-center'
    />
    <div className='custom-scrollbar-black container mx-auto mt-5 flex w-full items-center gap-4 px-4 pb-5 max-lg:overflow-x-scroll md:gap-5'>
      <div className='flex w-full flex-col gap-4 md:gap-5'>
        <Card className='relative flex h-[184px] w-full min-w-[288px] overflow-visible rounded-2xl bg-bento bg-blend-multiply md:rounded-3xl lg:h-[356px] lg:max-w-[584px]'>
          <CardHeader className='max-lg:px-4 max-lg:py-3'>
            <CardTitle className='text-sm lg:text-2xl'>
              Good for Adventures
            </CardTitle>
            <CardDescription className='w-[110px] text-xs lg:w-[224px] lg:text-base'>
              Explore a wide range of products - Experience something new every
              time!
            </CardDescription>
          </CardHeader>

          <CardContent className='overflow-visible'>
            <CardContent className='p-0'>
              <SpImage
                // src={`https://images.sharepal.in/benefits-of-renting/qaulity-product-old.webp`}
                src='https://images.sharepal.in/benefits-of-renting/good-for-adventures.webp'
                alt='Good for Adventures'
                width={"515"}
                height={"515"}
                className='h-full w-full'
                containerClassName='absolute inset-0   '
              />
            </CardContent>
          </CardContent>
        </Card>

        <Card className='relative flex h-[144px] w-full min-w-[288px] rounded-2xl bg-bento bg-blend-multiply md:rounded-3xl lg:h-[256px] lg:max-w-[584px]'>
          <CardHeader className='max-lg:px-4 max-lg:py-3'>
            <CardTitle className='text-sm lg:text-2xl'>
              Good for the Economy
            </CardTitle>
            <CardDescription className='w-[120px] text-xs lg:w-[303px] lg:text-base'>
              Support the domestic marketand reduce imports by renting.A smarter
              choice that boosts local growth.
            </CardDescription>
          </CardHeader>
          <CardContent className='p-0'>
            <SpImage
              src='https://images.sharepal.in/benefits-of-renting/good-for-economy.webp'
              alt='Good for Economy'
              width={"315"}
              height={"315"}
              className='h-full w-full'
              containerClassName='absolute inset-0 -right-0'
            />
          </CardContent>
        </Card>
      </div>
      <div className='flex w-full flex-col gap-4 md:gap-5'>
        <Card className='relative flex h-[144px] w-full min-w-[288px] overflow-x-clip rounded-2xl bg-bento bg-blend-multiply md:rounded-3xl lg:h-[256px] lg:max-w-[584px]'>
          <CardHeader className='max-lg:px-4 max-lg:py-3'>
            <CardTitle className='text-sm lg:text-2xl'>
              Good for our Planet
            </CardTitle>
            <CardDescription className='w-[120px] text-xs lg:w-[224px] lg:text-base'>
              Reduce waste, cut down on landfills, and lower your carbon
              footprint by renting.A greener choice for a better future.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Image
              src={`${HARD_CODED_IMAGE_URL}/earth-cropped.webp`}
              alt='Good for Planet'
              width={"190"}
              height={"132"}
              quality={100}
              className='absolute -right-2 bottom-0 max-lg:w-[160px] lg:-right-8 lg:w-[373px]'
            />
          </CardContent>
        </Card>
        <Card className='relative flex h-[184px] w-full min-w-[288px] rounded-2xl bg-bento bg-blend-multiply md:rounded-3xl lg:h-[356px] lg:max-w-[584px]'>
          <CardHeader className='max-lg:px-4 max-lg:py-3'>
            <CardTitle className='text-sm lg:text-2xl'>
              Good for Your Pocket
            </CardTitle>
            <CardDescription className='w-[120px] text-xs lg:w-[272px] lg:text-base'>
              Save big by renting instead of buying. Enjoy premium products
              without breaking the bank.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Image
              src={`${HARD_CODED_IMAGE_URL}/Coin+3D.webp`}
              alt='Good for Your Pocket'
              width={"49"}
              height={"62"}
              className='right-18 absolute -top-2 lg:w-[89px]'
            />
            <Image
              src={`${HARD_CODED_IMAGE_URL}/piggy-bank1.webp`}
              alt='Good for Your Pocket'
              width={"153"}
              className='absolute bottom-0 right-1 lg:w-[351px]'
              height={156}
            />
          </CardContent>
        </Card>
      </div>
    </div>

    {city && city !== "" && (
      <Button
        asChild
        className='mh-9 mx-auto mt-5 w-max text-xs md:mt-0 md:h-11 md:text-sm'
      >
        {/* <Link href={`${city}/photography-on-rent`}> */}
        <Link
          href={`/${city}#categories`}
          className='flex cursor-pointer items-center'
        >
          Explore All Product
          <ChevronRight className='h-4 w-4 md:h-6 md:w-6' />
        </Link>
      </Button>
    )}
  </section>
)

export default WhyRentingisBetter
