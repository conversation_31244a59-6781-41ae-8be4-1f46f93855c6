import Image from "next/image"
import ZeroPolicyCard from "../cards/zero-policy-card"
import SectionTitle from "../section-title"

const ZeroPolicy = () => (
  <section className='container mt-0 space-y-5 bg-neutral-150 p-4 px-0 py-5 md:mt-16'>
    {/* Zero Policy Header */}
    <div className='mx-auto flex items-center justify-start gap-0 px-4 pt-5 max-lg:flex-col max-lg:items-start md:py-5 lg:flex lg:gap-5'>
      <Image
        src={`https://images.sharepal.in/zero-policy/sharepal-zero-policy.svg`}
        width={350}
        height={100}
        alt='Zero Policy Title Card'
        className='h-auto w-[122px] md:h-auto md:w-[244px]'
      />

      {/* Zero Policy Title */}
      <SectionTitle
        cText='Zero Surprises'
        cTColor='text-category-purple'
        nText='Transparent prices'
        className='max-w-80 md:w-full'
      />
    </div>
    {/* Zero Policy Cards */}
    <div className='custom-scrollbar-black flex gap-3 overflow-x-auto px-4 py-3 lg:gap-8 2xl:overscroll-none'>
      <ZeroPolicyCard
        key={"locker"}
        description='We’ll leave the deposit drama to your landlord-because your adventures should be exciting, not expensive.'
        //   image_url="https://images.sharepal.in/Static+Images/1731658304534-SafeWalt.png"
        // image_url={`${HARD_CODED_IMAGE_URL}/locker-cropped.webp`}
        image_url='https://images.sharepal.in/zero-policy/zero-deposit.svg'
        title='Zero Security Deposit'
      />
      <ZeroPolicyCard
        key={"parcel-box"}
        description="We'll leave that for your food deliveries. Get your SharePal rental orders delivered for free, every time!"
        // image_url={`${HARD_CODED_IMAGE_URL}/parcel-box-cropped.webp`}
        image_url='https://images.sharepal.in/zero-policy/zero-delivery.svg'
        title='Zero Delivery Charges'
      />{" "}
      <ZeroPolicyCard
        key={"magnifier"}
        description='Pay what you see. No surge fee, No Cancellation fee, no iPhone fee, no why are you still reading fee, no......'
        // image_url={`${HARD_CODED_IMAGE_URL}/magnifier-cropped.webp`}
        image_url='https://images.sharepal.in/zero-policy/zero-hidden-charges.svg'
        title='Zero Hidden Charges'
      />
    </div>
  </section>
)

// const ZeroPolicyPurpleTag = () => {
//   return (
//     <div className="relative h-[40px] w-[150px] skew-x-[-15deg] rounded-[5.8px] bg-zero-policy-title-card lg:h-[54px] lg:w-[294px] lg:rounded-[12px]">
//       <div className="flex w-full skew-x-[15deg]">
//         <Image
//           src={`${HARD_CODED_IMAGE_URL}/purple-star.webp`}
//           width={100}
//           height={10}
//           alt="Star"
//           className="-translate-x-2 -translate-y-3 max-lg:h-16 max-lg:w-16 lg:-translate-x-2 lg:-translate-y-4"
//         />
//         <div className="flex w-full -translate-x-4 flex-col items-start p-0 lg:-translate-x-6">
//           <div className="flex -translate-x-8 scale-50 items-start justify-start lg:-translate-x-3 lg:scale-75">
//             <IconLogoLeft />
//             <IconLogoRight color="#fff" />
//           </div>
//           <span className="flex h-max w-full -translate-y-4 items-center gap-1 text-pretty bg-zero-policy-title-card-text bg-clip-text p-0 text-[15px] font-medium italic leading-[34px] text-[#00000000] lg:-translate-y-2 lg:text-2xl">
//             <span className="font-[800]">ZERO</span>Policy
//           </span>
//         </div>
//       </div>
//     </div>
//   )
// }

export default ZeroPolicy
