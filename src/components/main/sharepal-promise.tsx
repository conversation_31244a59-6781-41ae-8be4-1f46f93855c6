import React from "react"
import Image from "next/image"
import {
  IconDelivery,
  IconLogoLeft,
  IconLogoRight,
  IconSupport,
  IconVerified,
} from "../Icons"
import { HARD_CODED_IMAGE_URL } from "@/constants"
import { Typography } from "../ui/typography"

const promises = [
  {
    icon: <IconVerified className='h-[72px] max-h-max w-[72px]' />,
    title: "12 Point Quality Check",
    description:
      "Before delivery, we conduct a 12-point quality check to ensure your order meets our quality standard.",
  },
  {
    icon: <IconDelivery className='h-[72px] max-h-max w-[72px]' />,
    title: "Lowest Price Guarantee",
    description:
      "We provide lowest price guarantee so you can rent with confidence. No more searching here and there.",
  },
  {
    icon: <IconSupport className='h-[72px] max-h-max w-[72px]' />,
    title: "Prompt Help & Support",
    description:
      "Fast and efficient support, right at your fingertips. We are available from 10 am to 10 pm everyday.",
  },
]

function SharePalPromise() {
  return (
    <section className='container px-4 py-5 lg:py-[4.5rem]'>
      <div className='container mx-auto flex w-full rounded-2xl bg-primary-850 p-4 max-lg:flex-col md:rounded-radius lg:gap-6 lg:p-8'>
        <div className='flex flex-col gap-4 md:mr-3'>
          <div className='relative h-[40px] w-[150px] skew-x-[-15deg] rounded-[5.8px] bg-sharepal-promise-card lg:h-[54px] lg:w-[212px] lg:rounded-[12px]'>
            <div className='flex w-full skew-x-[15deg] gap-2'>
              <Image
                src={`${HARD_CODED_IMAGE_URL}/shield1.webp`}
                width={80}
                height={10}
                alt='Sharepal Promise'
                className='max-lg:h-10 max-lg:w-10 lg:-translate-y-3'
              />
              <div className='flex w-full flex-col items-start'>
                <div className='flex -translate-x-8 scale-50 lg:-translate-x-6 lg:scale-[0.6]'>
                  <IconLogoLeft />
                  <IconLogoRight color='#fff' />
                </div>
                <span className='-translate-y-4 bg-zero-policy-title-card-text bg-clip-text text-[15px] font-extrabold italic leading-[34px] text-transparent lg:-translate-y-2 lg:text-2xl'>
                  Promise
                </span>
              </div>
            </div>
          </div>
          <Typography
            as='h3'
            className='text-h4 text-gray-100 md:py-4 lg:text-h2'
          >
            {/* Rent your favorite products with peace of mind */}
            Rent with peace
          </Typography>
        </div>
        <div className='grid gap-6 pt-5 max-lg:flex-col md:grid-cols-3 md:pt-0'>
          {promises.map(({ icon, title, description }, index) => (
            <div
              key={index}
              className='flex items-start justify-start gap-3 md:gap-6 lg:flex-col'
            >
              {icon}
              <div className='flex flex-col gap-1 lg:gap-3'>
                <Typography
                  as={"h5"}
                  className='text-sh4 text-gray-100 lg:text-h4'
                >
                  {title}
                </Typography>
                <p className='text-b7 text-primary-250 lg:text-b5'>
                  {description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default SharePalPromise
