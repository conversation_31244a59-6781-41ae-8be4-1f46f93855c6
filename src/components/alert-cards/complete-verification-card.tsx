"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"

import { HARD_CODED_IMAGE_URL } from "@/constants"
import { cn } from "@/lib/utils"
import Image from "next/image"
import Link from "next/link"
import { ChevronRightIcon, TickCircleOutlinedIcon } from "sharepal-icons"
import { Alert, AlertDescription } from "../ui/alert"
interface CompleteVerificationProps {
  verified: boolean
}

const CompleteVerificationCard = ({ verified }: CompleteVerificationProps) => (
  <Alert className='border-none p-0'>
    <AlertDescription className='bg-gray-100 text-green-900'>
      <Link
        // aria-label="button"
        href={"/complete-verification"}
        className={cn(
          "flex h-20 items-center gap-2 overflow-hidden rounded-3xl bg-gradient-to-r p-2 md:h-24 md:gap-4 md:p-3",
          verified
            ? "from-[#EDF4FF] to-[#F7FFEA]"
            : "from-[#EDF4FF] to-[#F7FFEA]",
          // : 'from-destructive-100 to-destructive-200',
        )}
      >
        <div className='flex gap-2'>
          <Image
            src={`${HARD_CODED_IMAGE_URL}/shield1.webp`}
            width={80}
            height={10}
            alt='Sharepal Promise Image'
            className='w-16 object-contain'
          />
        </div>
        <div className='flex flex-col items-start justify-start'>
          <span className='text-start text-sm font-bold text-primary-900 md:text-base'>
            {verified
              ? "Your profile is verified!"
              : "Complete Profile Verification"}
          </span>

          <div className='text-start text-[10px] font-semibold text-zinc-700 md:text-sm'>
            {verified
              ? "Enjoy seamless rental experience with SharePal"
              : "Complete your verification for a seamless rental experience"}
          </div>
        </div>

        <Button
          type='button'
          className={cn(
            "h-8 min-h-8 w-8 min-w-8 rounded-full md:min-h-10 md:min-w-10",
            verified
              ? "bg-success-600 hover:bg-success-300"
              : "bg-destructive-50 hover:bg-destructive-30",
            // : 'bg-destructive-500 hover:bg-destructive-300',
          )}
          size={"icon"}
        >
          {verified ? (
            <TickCircleOutlinedIcon className='h-4 w-4 md:h-6 md:w-6' />
          ) : (
            <ChevronRightIcon className='h-4 w-4 text-primary-900 md:h-8 md:w-8' />
          )}
        </Button>
      </Link>
    </AlertDescription>
  </Alert>
)

export default CompleteVerificationCard
