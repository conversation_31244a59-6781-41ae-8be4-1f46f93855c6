"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useUserStore } from "@/store/user-store"
import { fetchWithAuthGet, fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { zodResolver } from "@hookform/resolvers/zod"
import { useMutation, useQuery } from "@tanstack/react-query"
import { AlertCircle, Loader, Mail, MessageCircle, Plus, X } from "lucide-react"
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"
import { IconSuccess } from "../Icons/checkout"
import { Typography } from "../ui/typography"
import SideView from "./modal-wrapper"

export interface SideViewProps {
  openSideView: boolean
  setOpenSideView: Dispatch<SetStateAction<boolean>> | (() => void)
}

export const customerTicketSchema = z.object({
  type: z.string().min(1, "Please select the type of problem"),
  description: z
    .string()
    .max(200, "Description should be less than 200 characters")
    .optional()
    .or(z.literal("")),
  attachment: z.string().optional().or(z.literal("")),
  user_new_id: z.number(),
  calling_number: z.string().min(1, "Calling number is required"),
})

type CustomerTicket = z.infer<typeof customerTicketSchema>

const ContactSupportHelp = ({
  openSideView,
  setOpenSideView,
}: SideViewProps) => {
  const { user } = useUserStore()
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement | null>(null)

  const {
    control,
    handleSubmit,
    watch,
    reset,
    setValue,
    formState: { errors },
  } = useForm<CustomerTicket>({
    resolver: zodResolver(customerTicketSchema),
    defaultValues: {
      type: "",
      description: "",
      attachment: "",
      user_new_id: user?.id || 0,
      calling_number: user?.calling_number || "",
    },
  })

  const descriptionLength = watch("description")?.length || 0

  const { mutate, isPending } = useMutation({
    mutationKey: ["contactSupport"],
    mutationFn: (data: CustomerTicket) =>
      fetchWithAuthPost<CustomerTicket>(
        "https://api.sharepal.in/api:AIoqxnqr/support/raise-ticket",
        data,
      ),
    onSuccess: () => {
      setIsSubmitted(true)
      toast.success("Callback request submitted successfully")
    },
    onError: () => {
      toast.error("Failed to submit callback request. Please try again.")
    },
  })

  const onSubmit = (data: CustomerTicket) => {
    mutate(data)
  }

  useEffect(() => {
    if (openSideView) {
      reset()
      setImagePreview(null)
      setIsSubmitted(false)
    }
  }, [openSideView, reset])

  const { data: tickets } = useQuery({
    queryKey: ["ticket_types"],
    queryFn: () =>
      fetchWithAuthGet<{ ticket: string; type: string }[]>(
        "https://api.sharepal.in/api:EhQQ9F-L/support/tickets",
      ),
    enabled: openSideView,
    refetchOnWindowFocus: false,
  })

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"]
    if (!allowedTypes.includes(file.type)) {
      toast.error("Only JPEG, JPG, and PNG files are supported.")
      return
    }

    const reader = new FileReader()
    reader.onloadend = () => {
      const base64 = reader.result as string
      setImagePreview(base64)
      setValue("attachment", base64)
    }
    reader.readAsDataURL(file)
  }

  const removeImage = () => {
    setImagePreview(null)
    setValue("attachment", "")
    if (fileInputRef.current) fileInputRef.current.value = ""
  }

  return (
    <SideView
      openSideView={openSideView}
      setOpenSideView={setOpenSideView}
      title='Contact Support for Help'
      className='h-full'
    >
      {isSubmitted ? (
        <CallBackRaised />
      ) : (
        <form
          onSubmit={handleSubmit(onSubmit)}
          className='relative h-full w-full'
        >
          <div className='h-full w-full max-w-2xl flex-col items-start justify-start gap-6 px-4 py-3 md:p-6'>
            <div className='flex items-center gap-1.5 self-stretch rounded-lg bg-violet-100 px-3 py-2 md:rounded-full'>
              <AlertCircle className='h-4 w-4 text-blue-900' />
              <Typography
                as={"p"}
                className='text-b6 font-medium leading-none text-blue-900'
              >
                Fill out the form for a callback. Our team will contact you on
                WhatsApp within an hour during our operational hours (9 AM–9 PM)
                Mon - Fri.
              </Typography>
            </div>

            <div className='flex flex-col items-start justify-start gap-5 self-stretch pt-2 md:pt-6'>
              {/* Problem Type */}
              <div className='flex flex-col gap-1.5 self-stretch'>
                <div className='flex items-center gap-1 px-1'>
                  <label
                    htmlFor='problem-type'
                    className='text-sm font-medium leading-none text-zinc-950'
                  >
                    <Typography as={"p"} className='text-b4 font-medium'>
                      What type of problem are you facing?
                    </Typography>
                  </label>
                  <span className='text-xs font-medium text-red-500'>*</span>
                </div>
                <Controller
                  name='type'
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger
                        id='problem-type'
                        className='rounded-2xl border-2 border-gray-200 bg-gray-100 px-3 py-5'
                      >
                        <SelectValue placeholder='Select the type of problem' />
                      </SelectTrigger>
                      <SelectContent>
                        {tickets?.map((ticket) => (
                          <SelectItem key={ticket.ticket} value={ticket.type}>
                            {ticket.ticket}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.type && (
                  <p className='mt-1 text-xs text-red-500'>
                    {errors.type.message}
                  </p>
                )}
              </div>

              {/* Description */}
              <div className='flex flex-col gap-1.5 self-stretch'>
                <div className='flex items-center gap-1 px-1'>
                  <label htmlFor='problem-description'>
                    <Typography as={"p"} className='text-b4 font-medium'>
                      Please tell us more about your problem
                    </Typography>
                  </label>
                  <Typography as={"span"} className='text-b6 text-neutral-400'>
                    (Optional)
                  </Typography>
                </div>
                <Controller
                  name='description'
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      id='problem-description'
                      placeholder='Type your details here...'
                      className='h-36 resize-none rounded-2xl bg-gray-100 p-3 text-sh4 md:text-b2'
                      {...field}
                    />
                  )}
                />
                <div className='text-o4 text-zinc-400'>
                  {descriptionLength}/200
                </div>
                {errors.description && (
                  <Typography as={"p"} className='mt-1 text-o4 text-red-500'>
                    {errors.description.message}
                  </Typography>
                )}
              </div>

              {/* Attach Files */}
              <div className='flex flex-col gap-2 self-stretch'>
                <input
                  ref={fileInputRef}
                  type='file'
                  accept='image/png, image/jpeg, image/jpg'
                  onChange={handleFileUpload}
                  hidden
                />
                <Button
                  type='button'
                  variant='outline-primary'
                  className='w-fit'
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Plus className='h-5 w-5 text-blue-700' />
                  <span className='px-2 text-bt3 font-semibold text-blue-700'>
                    Attach Image
                  </span>
                </Button>

                {imagePreview && (
                  <div className='relative mt-2 w-fit'>
                    <img
                      src={imagePreview}
                      alt='Preview'
                      className='h-32 rounded-xl border object-cover'
                    />
                    <button
                      type='button'
                      onClick={removeImage}
                      className='shadow absolute right-1 top-1 rounded-full bg-white p-1'
                    >
                      <X className='h-4 w-4 text-red-500' />
                    </button>
                  </div>
                )}

                <div className='flex items-center gap-1 px-2 py-1.5'>
                  <AlertCircle className='h-4 w-4 text-zinc-700' />
                  <Typography as={"p"} className='text-b6 text-zinc-700'>
                    Supported file formats: JPEG, JPG, PNG only.
                  </Typography>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className='sticky bottom-0 mt-auto w-full border-t-2 border-neutral-200 max-md:bg-gray-100 max-md:p-4 max-md:shadow-sm md:p-6'>
            <Button
              type='submit'
              variant='primary'
              disabled={isPending}
              size='lg'
              className='max-md:textbt-2 textbt-4 w-full rounded-full px-4 py-3'
            >
              {isPending && <Loader className='mr-2 animate-spin' />}
              Raise Callback Request
            </Button>
          </div>
        </form>
      )}
    </SideView>
  )
}

const CallBackRaised = () => (
  <div className='flex h-full max-w-2xl flex-col items-center justify-start gap-6 overflow-hidden px-4 py-3 md:p-6'>
    <div className='flex w-full flex-grow flex-col items-center justify-start md:px-14 md:pt-6'>
      <IconSuccess className={""} />
      <div className='flex w-full flex-col items-center justify-start gap-12'>
        <div className='flex w-full flex-col items-start justify-start gap-3'>
          <Typography
            as={"h4"}
            className='w-full text-center text-h4 font-bold leading-loose text-stone-950'
          >
            We&apos;ve Received Your Callback Request
          </Typography>
          <Typography
            as={"p"}
            className='w-full text-center text-b4 text-neutral-500'
          >
            Our team will contact you on WhatsApp within 1 hour. Please be
            patient—we&apos;re working quickly to resolve your issue.
          </Typography>
        </div>
        <div className='flex w-full flex-col items-center justify-start gap-6'>
          <Typography
            as={"p"}
            className='w-full text-center text-b4 text-neutral-500'
          >
            Or else you can reach-out directly to us on:
          </Typography>
          <div className='flex w-full items-center justify-start gap-4 md:gap-6 md:px-12'>
            <div className='flex w-full flex-col items-center justify-start gap-3'>
              <div className='flex items-center justify-start overflow-hidden rounded-xl bg-violet-100 p-2'>
                <MessageCircle className='h-10 w-10 text-slate-900 md:h-16 md:w-16' />
              </div>
              <Typography
                as={"p"}
                className='flex w-full flex-col items-center justify-center text-center text-b4'
              >
                <span className='text-neutral-500'>Chat with us: </span>
                <span className='w-full text-neutral-700'>
                  SharePal WhatsApp
                </span>
              </Typography>
            </div>
            <div className='flex w-full flex-col items-center justify-start gap-3'>
              <div className='flex items-center justify-start overflow-hidden rounded-xl bg-violet-100 p-2'>
                <Mail className='h-10 w-10 text-slate-900 md:h-16 md:w-16' />
              </div>
              <Typography
                as={"p"}
                className='flex w-full flex-col text-center text-b4'
              >
                <span className='text-neutral-500'>Email us: </span>
                <span className='text-neutral-700'><EMAIL></span>
              </Typography>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
)

export default ContactSupportHelp
