import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { FaqType, SideViewProps } from "@/types"
import { AdaptiveWrapper } from "./adaptive-wrapper"

interface Props extends SideViewProps {
  faqs: FaqType[]
}

const Faqs = ({ openSideView, setOpenSideView, faqs }: Props) => (
  <AdaptiveWrapper
    open={openSideView}
    onOpenChange={setOpenSideView}
    title='FAQs'
    className='h-full max-w-2xl md:min-w-[40%]'
  >
    <Accordion
      type='single'
      collapsible
      className='max-w-2xl space-y-4 bg-neutral-150 p-4 md:p-6'
    >
      {faqs.map((item, index) => (
        <AccordionItem
          key={index}
          className='max-w-2xl border-none'
          value={`item-${index}`}
        >
          <AccordionTrigger className='rounded-t-xl bg-gray-100 px-4 py-3 text-base font-semibold text-foreground transition data-[state=closed]:rounded-xl hover:underline focus:outline-none'>
            {item.question}
          </AccordionTrigger>
          <AccordionContent className='rounded-b-xl bg-gray-100 px-4 py-3 text-sm font-medium text-neutral-500'>
            {item.answer}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  </AdaptiveWrapper>
)

export default Faqs
