"use client"
import { useDebounce } from "@/hooks/use-debounce"
import { useRentalStore } from "@/store/rental-store"
import { useSearchStore } from "@/store/search-store"
import { SearchResponse } from "@/types/search"
import { customFetchPost } from "@/utils/customFetch"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { usePathname } from "next/navigation"
import { memo, useCallback, useEffect, useRef, useState } from "react"
import PopularItems from "../search/popular-items"
import PromotionalBanner from "../search/promotional-banner"
import { SearchContainer } from "../search/search-container"
import { SearchResults } from "../search/search-results"
import { SectionHeader } from "../search/section-header"
import { AdaptiveWrapper } from "./adaptive-wrapper"

const SEARCH_QUERY_KEY = "search-results"

const searchProducts = async (
  searchText: string,
  cityName: string,
  admin_only: boolean,
) => {
  if (!searchText) return { items: [] }

  return customFetchPost(
    "https://api.sharepal.in/api:T_tcT7oq/search_rental_items_new",
    {
      search_text: searchText,
      city: cityName,
      paging: {
        page: 1,
        per_page: 10,
      },
      admin_only,
    },
  )
}

const Search = () => {
  const { setIsSearchOpen, isSearchOpen } = useSearchStore()
  const { selectedCity } = useRentalStore()
  const queryClient = useQueryClient()
  const pathname = usePathname()
  const previousPathname = useRef(pathname)

  const [searchText, setSearchText] = useState("")
  const debouncedSearchText = useDebounce(searchText, 300)

  const [adminOnly, setAdminOnly] = useState(false)
  useEffect(() => {
    setAdminOnly(
      ((window &&
        window.sessionStorage &&
        window.sessionStorage.getItem("backend_order")) ??
        "") == "true",
    )
  }, [])

  // Close search when pathname changes
  useEffect(() => {
    if (pathname !== previousPathname.current && isSearchOpen) {
      setIsSearchOpen(false)
    }

    previousPathname.current = pathname
  }, [pathname, setIsSearchOpen, isSearchOpen])

  const {
    data: searchResults,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: [SEARCH_QUERY_KEY, debouncedSearchText, selectedCity.city_name],
    queryFn: () =>
      searchProducts(debouncedSearchText, selectedCity.city_name, adminOnly),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
    enabled: Boolean(debouncedSearchText),
    refetchOnWindowFocus: false,
    retry: 1,
  })

  const handleClearSearch = useCallback(() => {
    setSearchText("")
    queryClient.setQueryData([SEARCH_QUERY_KEY, "", selectedCity.city_name], {
      items: [],
    })
  }, [queryClient, selectedCity.city_name])

  if (!isSearchOpen) {
    return null
  }

  return (
    <AdaptiveWrapper
      open={isSearchOpen}
      onOpenChange={setIsSearchOpen}
      title='Search Products'
      mobile={{
        type: "sheet",
        side: "bottom",
      }}
      className='min-h-[80vh] max-md:max-h-[85vh] max-md:rounded-t-3xl'
    >
      <div className='flex h-full flex-col'>
        <div className='sticky top-0 z-10 bg-gray-100 p-4'>
          <SearchContainer
            searchText={searchText}
            setSearchText={setSearchText}
            searchResults={searchResults as SearchResponse}
            isLoading={isLoading}
            isFetching={isFetching}
            onClearSearch={handleClearSearch}
          />
        </div>

        <div className='flex-1 space-y-6 p-4 pb-20 md:pb-8'>
          <SearchResults results={searchResults as SearchResponse} />

          <PromotionalBanner />
          <div className='space-y-4 pb-10'>
            <SectionHeader title='Popular Items' />
            <PopularItems />
          </div>
        </div>
      </div>
    </AdaptiveWrapper>
  )
}

export default memo(Search)
