import { moneyFormatter } from "@/functions/small-functions"
import { RentOrder } from "@/types"

import { CalendarEndFilledIcon } from "sharepal-icons"

import { cn } from "@/lib/utils"
import { formatDateWithOrdinal } from "@/utils/date-logics"
import { format } from "date-fns"
import { ChargeItem } from "../checkout/order-summary-new/charge-item"
import { ProductSummary } from "../checkout/product-summary"
import { Button } from "../ui/button"
import { Typography } from "../ui/typography"

function ExtendRentalDialog({
  order_id,
  updateOrder,
  totalExtensionAmount,
  extensionDays,
  pickupDate,
}: {
  order_id: string
  updateOrder: (amount: number, days: number, new_pickup_date: Date) => void
  order: RentOrder
  totalExtensionAmount: number
  openExtendRentalDialog: boolean
  extensionDays: number
  pickupDate: Date
}) {
  return (
    <div className='flex h-[95%] flex-col justify-between'>
      <div className='space-y-4 p-4'>
        {/* Title */}
        <div className='space-y-4 text-center'>
          <div className='flex items-center justify-center space-x-1 text-o4 text-neutral-500 md:text-b4'>
            <Typography as='p'>Order No:</Typography>
            <Typography as='p'>#{order_id}</Typography>
          </div>
        </div>

        <div
          className={cn(
            "flex flex-col gap-4 rounded-lg border-2 border-neutral-200 bg-gray-100 p-4",
          )}
        >
          <div className='flex items-start justify-between gap-2'>
            <ProductSummary
              showControls={false}
              showViewItem={false}
              totalCharges={0}
              items_count_text='Items Ordered'
            />
          </div>
          <div className='border-b'></div>
          <div className='flex items-baseline justify-between'>
            <Typography as='h2' className='text-sh2'>
              Rental Period Extended by:
            </Typography>
            <div className='flex items-baseline gap-1'>
              <Typography as='h1' className='text-h1'>
                {extensionDays}
              </Typography>
              <Typography as='h6' className='text-h6 text-gray-700'>
                Days
              </Typography>
            </div>
          </div>
          <div className='border-b'></div>
          <div className='w-full rounded-lg bg-primary-100 py-3'>
            <div className='flex items-center justify-center gap-2'>
              <CalendarEndFilledIcon className='h-4 min-h-4 w-4 min-w-4 text-primary-700' />
              <Typography as={"p"} className='text-sh5 text-primary-700'>
                Updated Pickup Date:
              </Typography>

              <Typography as={"p"} className='text-sh4 text-neutral-900'>
                {formatDateWithOrdinal(pickupDate)}
              </Typography>
              <p className='text-primary-300'>•</p>
              <Typography as={"p"} className='text-sh4 text-neutral-900'>
                {format(pickupDate, "EEE")}
              </Typography>
            </div>
          </div>
          <div className='border-b'></div>
          <ChargeItem
            item={{
              label: "Total Extension Charges",
              amount: totalExtensionAmount,
              items_count: extensionDays || 0,
              items_count_text: `day${extensionDays && extensionDays > 1 ? "s" : ""} added`,
            }}
          />
          <div className='flex w-full items-center justify-between gap-4'>
            <div>
              <div className='flex cursor-pointer items-center gap-2 text-neutral-900'>
                <Typography as={"h4"} className='text-sh6 md:text-h4'>
                  Total Charges
                </Typography>
              </div>
              <p className='text-sm text-gray-800'>price incl. of all taxes</p>
            </div>

            <h2 className='text-lg font-bold text-primary-900'>
              {moneyFormatter(totalExtensionAmount)}
            </h2>
          </div>
        </div>

        {/* <AnimatePresence>
          {showTotalCharges && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{
                height: showTotalCharges ? "auto" : 0,
                opacity: showTotalCharges ? 1 : 0,
                marginTop: showTotalCharges ? 1 : 0,
              }}
              transition={{ duration: 0.3 }}
              exit={{ height: 0, opacity: 0 }}
              className='overflow-hidden'
            ></motion.div>
          )}
        </AnimatePresence> */}
      </div>

      <div className='sticky bottom-0 bg-gray-100 p-4 shadow-sm'>
        <Button
          onClick={() => {
            updateOrder(totalExtensionAmount, extensionDays, pickupDate)
          }}
          variant={"primary"}
          className='w-full text-bt2 text-gray-100'
          size='lg'
        >
          Confirm Order Extension
        </Button>
      </div>
    </div>
  )
}

export default ExtendRentalDialog
