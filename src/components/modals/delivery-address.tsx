import { SideViewProps } from "@/types"
import React, { useEffect } from "react"

import { Plus } from "lucide-react"

import { getUserAddresses } from "@/actions/user"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Address } from "@/types/address"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import DeliveryAddressCard from "../custom/delivery-address-card"
// import { useUserStore } from '@/store/use-user-store'
import {
  generateFullAddress,
  generateUserLabel,
} from "@/functions/address-utils"
import { loadScript } from "@/functions/loadScript"
import { useUserStore } from "@/store/user-store"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { toast } from "sonner"
import { AddDeliveryAddress } from "../checkout/delivery-address/add-delivery-address"
import { AdaptiveWrapper } from "./adaptive-wrapper"

interface DeliveryAddressProps extends SideViewProps {
  total_amount: number
  order_id: string
  // refetchDetails: () => void
}

const DeliveryAddressSelect = ({
  openSideView,
  setOpenSideView,
  total_amount,
  order_id,
  // refetchDetails,
}: DeliveryAddressProps) => {
  const [showNewAddress, setShowNewAddress] = React.useState(false)
  const [selectedAddress, setSelectedAddress] = React.useState<number | null>(
    null,
  )
  const [isScriptLoaded, setIsScriptLoaded] = React.useState(false)
  const { user } = useUserStore()
  const queryClient = useQueryClient()

  const { data: addresses = [], isLoading: addressesLoading } = useQuery<
    Address[]
  >({
    queryKey: ["user_addresses"],
    queryFn: getUserAddresses,
    refetchOnWindowFocus: false,
    enabled: !!user && openSideView,
  })

  React.useEffect(() => {
    if (addresses && addresses.length > 0) {
      setSelectedAddress(addresses[0].id)
    }
  }, [addresses])

  const { mutate: handleUseAddress, isPending: availabilityCheckLoading } =
    useMutation({
      mutationFn: async () => {
        const address = addresses?.find((a) => a.id === selectedAddress)
        if (address) {
          const isAvailable = await fetchWithAuthPost<{ servicable: boolean }>(
            "https://api.sharepal.in/api:AIoqxnqr/address/availability-check",
            {
              latitude: address?.latitude,
              longitude: address?.longitude,
              address_id: address?.id,
              total_rent: total_amount,
            },
          )
          if (!isAvailable.servicable) {
            toast.error("Address is not serviceable")
            return isAvailable.servicable
          }
          const data = await fetchWithAuthPost(
            "https://api.sharepal.in/api:AIoqxnqr/order/update-address",
            {
              order_id: order_id,
              address_id: selectedAddress,
            },
          )
          return data
        }
      },
      onSuccess: (data) => {
        if (data) {
          toast.success("Address Modified Successfully")
          setOpenSideView(false)
          queryClient.invalidateQueries({
            queryKey: ["order-data-fetch"],
          })
        }
      },
      onError: () => {
        toast.error("Failed to update address")
      },
    })

  useEffect(() => {
    if (!isScriptLoaded) {
      loadScript(
        `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`,
      )
      setIsScriptLoaded(true)
    }
  }, [isScriptLoaded, showNewAddress])
  return (
    <>
      <AddDeliveryAddress
        open={showNewAddress}
        onOpenChange={setShowNewAddress}
        isScriptLoaded={isScriptLoaded}
      />
      <AdaptiveWrapper
        open={openSideView}
        onOpenChange={setOpenSideView}
        title='Select Delivery Address'
        className='max-md:max-h-[95%]'
      >
        <div className='flex h-full w-full max-w-2xl flex-col justify-between'>
          {addressesLoading ? (
            <div className='space-y-4'>
              {[...Array(4)].map((_, index) => (
                <Skeleton key={index} className='h-24 w-full' />
              ))}
            </div>
          ) : addresses.length > 0 ? (
            <>
              <DeliveryAddressCard
                className='p-4'
                options={addresses.map((address) => ({
                  value: address.id.toString(),
                  label: generateFullAddress(address),
                  subLabel: `${address.city}, ${address.state}, ${address.pincode}`,
                  userLabel: generateUserLabel(
                    user?.first_name,
                    address.shipment_number,
                    user?.calling_number,
                  ),
                  isSelected: address.id === selectedAddress,
                }))}
                showAll
                onChange={(value) => setSelectedAddress(Number(value))}
                selectedValue={selectedAddress?.toString() ?? ""}
              />
              {/* <div className='mt-4 px-4'>
                  <p className='text-sm text-gray-500'>
                    {organizedAddresses.length} saved address
                    {organizedAddresses.length !== 1 ? "es" : ""}
                  </p>
                </div> */}
            </>
          ) : (
            <div className='flex h-full w-full items-center justify-center'>
              <p className='text-center text-gray-500'>
                No addresses found. Please add a new address.
              </p>
            </div>
          )}
          <div className='sticky bottom-0 z-[10] mt-4 w-full space-y-4 bg-white p-5 shadow-sm'>
            <Button
              type='button'
              variant='outline'
              size='lg'
              className='w-full rounded-full border-primary-500 bg-gray-100 py-5 font-bold text-primary-500'
              onClick={() => setShowNewAddress(true)}
            >
              <Plus className='mr-2 h-4 w-4' />
              Add New Address
            </Button>

            <Button
              onClick={() => handleUseAddress()}
              disabled={
                !selectedAddress || availabilityCheckLoading || addressesLoading
              }
              className='w-full rounded-full'
              variant='primary'
              size='lg'
            >
              {availabilityCheckLoading ? "Processing..." : "Use this Address"}
            </Button>
          </div>
        </div>
      </AdaptiveWrapper>
    </>
  )
}

export default DeliveryAddressSelect
