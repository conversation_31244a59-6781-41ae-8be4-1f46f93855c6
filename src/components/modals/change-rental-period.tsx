import { useEffect, useState } from "react"

import { RentOrder, SideViewProps } from "@/types"

import { getCookie } from "@/functions/cookies"
import useMediaQuery from "@/hooks/use-media-query"
import { useThrottle } from "@/hooks/use-throttle"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"
import { RentalPeriodSelector } from "../custom/rental-dates-select"
import { RentalPeriodSelectorDesktop } from "../custom/rental-dates-select-desktop"
import { RentalPeriodSelectorMobile } from "../custom/rental-dates-select-mobile"

import useCoupon from "@/hooks/use-coupon"
import { addDays } from "date-fns"
import { ProductSummary } from "../checkout/product-summary"
import { IconCalenderEdit } from "../Icons"
import { Button } from "../ui/button"
import { Typography } from "../ui/typography"
import { AdaptiveWrapper } from "./adaptive-wrapper"
import ChangeRentalDialog from "./change-rental-dialog"
import { RentalChargeAndDateChange } from "./rental-charge-and-date-change"
interface ChangeRentalPeriod extends SideViewProps {
  main_order_id: number
  order_id: string
  order: RentOrder
}

const ChangeRentalPeriod = ({
  openSideView,
  setOpenSideView,
  order_id,
  main_order_id,
  order,
}: ChangeRentalPeriod) => {
  const { finalAmount, applied_coupon_code, handling_charges } =
    useCheckoutStore()

  const queryClient = useQueryClient()

  const [openCalendar, setOpenCalendar] = useState(false)
  const [openChangeRentalDialog, setOpenChangeRentalDialog] = useState(false)
  const { user } = useUserStore()
  const { delivery_date, pickup_date, total_days, same_day_surge } =
    useRentalStore()

  const { mutate: updateOrder, isPending } = useMutation({
    mutationFn: () =>
      fetchWithAuthPost("https://api.sharepal.in/api:AIoqxnqr/order/update", {
        order_id: main_order_id,
        user_uid: user ? user.user_uid : getCookie("uid"),
        coupon_code: applied_coupon_code,
        total_amount: finalAmount(),
        update_user_id: user ? user.id : 0,
        delivery_date: addDays(delivery_date ?? new Date(), 1),
        return_date: addDays(pickup_date ?? new Date(), 1),
        total_days,
        cod_handling_charges: handling_charges,
        same_day_surge,
      }),
    onSuccess: (data) => {
      if (data) {
        queryClient.invalidateQueries({
          queryKey: ["order-details-fetch", order_id],
        })
        toast.success("Your OrderDate Updated Succesfully")
        setOpenSideView(false)
        setOpenChangeRentalDialog(false)
        // refetchDetails()
      }
    },
    onError: () => {
      toast.error("Sorry, Failed To Update Your Order Date")
    },
  })

  const throttleUpdate = useThrottle(updateOrder)
  const isMobile = useMediaQuery("(max-width: 767px)")

  const { handleApplyCoupon } = useCoupon()
  const { total_rent, setAppliedCouponCode } = useCheckoutStore()

  useEffect(() => {
    if (openChangeRentalDialog) {
      if (order.coupon_code) handleApplyCoupon(order.coupon_code, total_rent)
    } else {
      setAppliedCouponCode("", 0)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    applied_coupon_code,
    openChangeRentalDialog,
    order,
    setAppliedCouponCode,
    total_rent,
  ])

  return (
    <AdaptiveWrapper
      open={openSideView}
      onOpenChange={setOpenSideView}
      desktop={{
        type: "sheet",
        side: "right",
      }}
      mobile={{
        type: "drawer",
      }}
      title='Change Rental Period'
      titleClassName='bg-neutral-150'
      className={"h-full overflow-hidden bg-neutral-150 md:max-w-2xl"}
    >
      <div className='flex w-full flex-col items-start justify-start gap-4 p-4 md:px-6'>
        <div className='flex w-full items-start justify-between gap-4 rounded-xl bg-gray-100 p-4'>
          <div className='flex items-start justify-between gap-2'>
            <ProductSummary showViewItem={false} totalCharges={0} />
          </div>
        </div>

        <RentalChargeAndDateChange />

        <Button
          onClick={() => setOpenCalendar(true)}
          variant={"outline-primary"}
          className='w-full'
        >
          <span className='scale-[1.5]'>
            <IconCalenderEdit />
          </span>
          <span>Change Dates in Calendar View</span>
        </Button>
      </div>
      <div className='flex w-full flex-col items-start justify-start gap-3 p-4 md:px-6'>
        <Typography as={"h6"} className='self-stretch text-sh3 md:text-h7'>
          Enjoy the Benefits of Dynamic Rental Rates:
        </Typography>
        <div className='self-stretch text-neutral-500'>
          <Typography
            as={"span"}
            className='mr-1 text-sh6 font-bold md:text-sh4'
          >
            1.{"  "}
          </Typography>
          <Typography
            as={"span"}
            className='text-b6 text-neutral-300 md:text-b4'
          >
            Delivery time is 3-8pm. Return time is 10am-2pm. Delivery and Return
            dates are not chargeable. Select the dates accordingly
          </Typography>
          <br />
          <Typography
            as={"span"}
            className='mr-1 text-sh6 font-bold md:text-sh4'
          >
            2. {"  "}
          </Typography>
          <Typography
            as={"span"}
            className='text-b6 text-neutral-300 md:text-b4'
          >
            When you rent longer, the per day rental reduces. Select your dates
            accordingly.
          </Typography>
        </div>
      </div>
      <div className='sticky bottom-0 w-full max-w-2xl bg-gray-100 p-5 max-md:shadow-md'>
        <Button
          onClick={() => {
            setOpenChangeRentalDialog(true)
          }}
          className='h-12 w-full items-center justify-center gap-5 rounded-full bg-primary-500 text-center font-inter text-base font-semibold leading-tight text-gray-100 hover:bg-primary-600'
        >
          <span className='scale-[1.5]'>
            <IconCalenderEdit />
          </span>
          <span>Change Rental Period</span>
        </Button>
      </div>
      <RentalPeriodSelector />
      {!isMobile ? (
        <RentalPeriodSelectorDesktop
          isCalenderOpen={openCalendar}
          closeCalendarFn={() => {
            setOpenCalendar(false)
            setOpenChangeRentalDialog(false)
          }}
          onContinue={async () => {}}
        />
      ) : (
        <RentalPeriodSelectorMobile
          isCalenderOpen={openCalendar}
          onContinue={async () => {
            setOpenCalendar(false)
          }}
          closeCalendarFn={() => {
            setOpenCalendar(false)
            setOpenChangeRentalDialog(false)
          }}
        />
      )}
      <AdaptiveWrapper
        open={openChangeRentalDialog}
        desktop={{
          type: "dialog",
        }}
        tablet={{
          type: "dialog",
        }}
        title='Review & Confirm Order Modification'
        className='overflow-x-hidden md:max-h-[95%] md:p-0'
        onOpenChange={(value) => setOpenChangeRentalDialog(value)}
      >
        <ChangeRentalDialog
          updateOrder={throttleUpdate}
          isPending={isPending}
          order_id={order_id}
          order={order}
          openChangeRentalDialog={openChangeRentalDialog}
        />
      </AdaptiveWrapper>
    </AdaptiveWrapper>
  )
}

export default ChangeRentalPeriod
