"use client"

import { fetchAdditionalOrderSummary } from "@/actions/return-order"
import usePlaceOrder from "@/hooks/use-place-order"
import { useReturnOrderStore } from "@/store/return-order-store"
import type { OrderSummary } from "@/types/order"
import type { AlreadyBookedResponse, ScheduleFormData } from "@/types/schedule"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { getCombinedTimestamp } from "@/utils/is-time-valid"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { Loader2 } from "lucide-react"
import { useRouter } from "next/navigation"
import type React from "react"
import { useEffect } from "react"
import { toast } from "sonner"
import { ScheduleAgain } from "../dashboard/schedule-again"
import { AdaptiveWrapper } from "./adaptive-wrapper"

interface ScheduleModalProps {
  openSideView: boolean
  setOpenSideView: React.Dispatch<React.SetStateAction<boolean>> | (() => void)
  order: OrderSummary
}

export default function ScheduleAgainModal({
  openSideView,
  setOpenSideView,
  order,
}: ScheduleModalProps) {
  const { deal_name: order_id } = order

  //additional order summmay
  const { data: additionalOrderSummary } = useQuery({
    queryKey: ["additoinal-order-summary", order_id],
    queryFn: () => fetchAdditionalOrderSummary(order_id),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: openSideView, // Only fetch when the modal is open
  })

  const {
    data: alreadyBooked,
    isPending: isAlreadyBookedPending,
    refetch,
  } = useQuery({
    queryKey: ["alreadyBooked", order_id],
    queryFn: async () => {
      const data = await fetchWithAuthPost<AlreadyBookedResponse | null>(
        "https://api.sharepal.in/api:AIoqxnqr/shipment/schedule-check",
        {
          order_id: order_id,
        },
      )
      return data
    },
    enabled: openSideView,
  })

  useEffect(() => {
    if (openSideView) {
      refetch()
    }
  }, [openSideView, refetch])

  const queryClient = useQueryClient()
  const router = useRouter()

  // const isReturnSchedule = deal_deal_stage_name === "Pickup Scheduled"
  const isReturnSchedule = true

  const { hanldePaymentForReturnOrder, isReturnPaymentLoading } =
    usePlaceOrder()

  const { timeSlotCharges } = useReturnOrderStore()

  const { mutate: UpdateReturnDetails } = useMutation({
    mutationFn: () =>
      fetchWithAuthPost(
        "https://api.sharepal.in/api:AIoqxnqr/return/update-details",
        {
          order_id: additionalOrderSummary?.deal_name,
          delay_rental_charge: additionalOrderSummary?.delay_rental_charge,
          timeslot_charges: timeSlotCharges,
          tsa_applied: additionalOrderSummary?.tsa_applied,
          ziplock_applied: additionalOrderSummary?.ziplock_applied,
          return_payment_option: additionalOrderSummary?.return_payment_option,
          total_lost_charges: additionalOrderSummary?.total_lost_charges,
        },
      ),
    // onSuccess: () => {},
    // onError: () => {},
  })

  const { mutate: bookShipment, isPending: isShipmentBooking } = useMutation({
    mutationFn: async (data: ScheduleFormData) => {
      const reduceTime = isReturnSchedule

      if (!data.timeSlot || data.timeSlot === "") {
        toast.error("Please Select Time")
        throw new Error("Time not selected")
      }

      const timestamp = getCombinedTimestamp(
        Number(alreadyBooked?.schedule_timestamp),
        data.timeSlot,
      )

      const adjustedTimestamp = reduceTime
        ? timestamp - 15 * 60 * 1000
        : timestamp

      const payload = {
        order_id: order_id,
        schedule_timestamp: adjustedTimestamp,
        schedule_time: data.timeSlot,
        calling_number: alreadyBooked?.calling_number,
        whatsapp_number: alreadyBooked?.whatsapp_number,
        schedule_date: alreadyBooked?.schedule_date,
      }

      return fetchWithAuthPost(
        `https://api.sharepal.in/api:AIoqxnqr/shipment/schedule-again`,
        payload,
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["orderStatus"],
      })
      setOpenSideView(false)
      UpdateReturnDetails()
      router.replace(
        "/return-payment/" + order_id + "?status=reschedule_payment_success",
      )
      toast.success("Shipment Booked Successfully")
    },
    onError: (error) => {
      toast.error(
        JSON.parse((error as Error).message).message ??
          "Failed To Schedule Shipment",
      )
    },
  })

  const handleScheduleAgain = async (args: ScheduleFormData) => {
    if (
      (additionalOrderSummary?.return_payment_option === "pay_now" ||
        additionalOrderSummary?.return_payment_option === "without_payment") &&
      timeSlotCharges > 0 &&
      additionalOrderSummary?.timeslot_charges <= 0
    ) {
      await hanldePaymentForReturnOrder({
        order_id: order_id ?? "",
        amount: timeSlotCharges,
        onSuccess: () => {
          bookShipment(args)
        },
      })
      setOpenSideView(false)
    } else {
      bookShipment(args)
    }
  }

  const getTitle = () => {
    if (isReturnSchedule) {
      return "Change Order Pickup Time"
    } else {
      return "Change Order Delivery Time"
    }
  }

  return (
    <>
      <AdaptiveWrapper
        open={openSideView}
        onOpenChange={setOpenSideView}
        title={getTitle()}
        className='h-full w-full max-w-xl'
      >
        {isAlreadyBookedPending ? (
          <div className='flex h-full items-center justify-center'>
            <Loader2 className='h-10 w-10 animate-spin text-primary' />
          </div>
        ) : (
          <>
            <ScheduleAgain
              order_id={order_id}
              order={order}
              isShipmentBooking={isShipmentBooking || isReturnPaymentLoading}
              type={isReturnSchedule ? "pickup" : "delivery"}
              address={{
                name: order.first_name,
                fullAddress: order?.deal_cf_delivery_address ?? "",
              }}
              defaultDate={new Date(
                alreadyBooked?.schedule_date || new Date(),
              )?.getTime()}
              handleScheduleAgain={handleScheduleAgain}
              onClose={() => setOpenSideView(false)}
              alreadyBooked={alreadyBooked ? alreadyBooked : null}
              additionalOrderSummary={additionalOrderSummary}
            />
          </>
        )}
      </AdaptiveWrapper>
    </>
  )
}
