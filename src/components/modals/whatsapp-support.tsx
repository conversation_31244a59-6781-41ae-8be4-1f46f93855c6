"use client"
import { zodResolver } from "@hookform/resolvers/zod"
import React from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"

import { fetchCategories } from "@/actions/category"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { useRentalStore } from "@/store/rental-store"

import { Category } from "@/types"
import { useQuery } from "@tanstack/react-query"
import { IconCategory, IconPerson, IconWhatsapp } from "../Icons"
import IconWhatsappSupportPerson from "../Icons/icon-whatsapp-support"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import SideView from "./modal-wrapper"

// Define Zod schema for validation
const formSchema = z.object({
  customer_name: z.string().nonempty("Name is required"),
  whatsapp_number: z
    .string()
    .nonempty("Whatsapp number is required")
    .regex(/^\d+$/, "Whatsapp number must be numeric"),
  category: z.string().nonempty("Category is required"),
})

const WhatsappSupportModal = ({
  openView,
  setOpenView,
}: {
  openView: boolean
  setOpenView: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      customer_name: "",
      whatsapp_number: "",
      category: "",
    },
  })

  const { data: categories } = useQuery<Category[]>({
    queryKey: ["fetch-categories"],
    queryFn: fetchCategories,
    enabled: openView,
  })

  const { selectedCity } = useRentalStore()

  const onSubmit = (formData: z.infer<typeof formSchema>) => {
    window.open(
      `https://api.whatsapp.com/send?phone=+917619220543&text=Hi ,My name is ${formData.customer_name} from ${selectedCity.city_url}. I am looking to rent ${formData.category}.`,
      "_blank",
    )
  }

  return (
    <SideView
      openSideView={openView}
      setOpenSideView={setOpenView}
      className='h-full'
      title='WhatsApp Chat Support'
    >
      <div className='flex h-full w-full max-w-xl flex-col items-start justify-start gap-6 px-4 py-3 md:p-6'>
        <div className='flex h-[27.125rem] w-full flex-col items-start justify-start gap-6'>
          <div className='flex h-[10.5rem] w-full flex-col items-center justify-center gap-2 pb-3 pt-3'>
            <IconWhatsappSupportPerson />
            <div className='w-full text-center text-sm font-normal leading-[1.25rem] text-[#383A44] md:max-w-[80%]'>
              Share your contact details, and we will reach out and help you
              with your queries
            </div>
          </div>

          <div className='w-full'>
            <form
              onSubmit={handleSubmit(onSubmit)}
              className='flex flex-col gap-4'
            >
              {/* Name Field */}
              <div className='flex w-full flex-col gap-2'>
                <label htmlFor='customer_name' className='text-sm font-medium'>
                  Your Name <span className='text-destructive-500'>*</span>
                </label>
                <div className='clas flex w-full items-center gap-2 rounded-[12] bg-gray-100 p-3 focus-within:ring-ring focus:ring-1'>
                  <IconPerson />
                  <Input
                    {...register("customer_name")}
                    type='text'
                    className='h-max w-full border-0 bg-transparent p-0 text-sm shadow-[0px] outline-none ring-0 focus:border-0 focus:ring-0 focus-visible:border-0'
                    placeholder='Enter your name'
                  />
                </div>
                {errors.customer_name && (
                  <p className='text-sm text-destructive-500'>
                    {errors.customer_name.message}
                  </p>
                )}
              </div>

              {/* Whatsapp Number Field */}
              <div className='flex w-full flex-col gap-2'>
                <label
                  htmlFor='whatsapp_number'
                  className='text-sm font-medium'
                >
                  Whatsapp Number{" "}
                  <span className='text-destructive-500'>*</span>
                </label>
                <div className='flex w-full items-center gap-2 rounded-[12] bg-gray-100 p-3 focus-within:ring-ring'>
                  <IconWhatsapp className='h-4 w-4 [&>path]:fill-neutral-600' />
                  <Input
                    {...register("whatsapp_number")}
                    type='text'
                    pattern='\d+'
                    className='h-max w-full border-0 bg-transparent p-0 text-sm shadow-[0px] outline-none ring-0 focus:border-0 focus:ring-0 focus-visible:border-0'
                    placeholder='Enter whatsapp number'
                  />
                </div>
                {errors.whatsapp_number && (
                  <p className='text-sm text-destructive-500'>
                    {errors.whatsapp_number.message}
                  </p>
                )}
              </div>

              {/* Category Field */}
              <div className='flex w-full flex-col gap-2'>
                <label htmlFor='category' className='text-sm font-medium'>
                  Select Category for Enquiry{" "}
                  <span className='text-destructive-500'>*</span>
                </label>
                <Select onValueChange={(value) => setValue("category", value)}>
                  <SelectTrigger className='flex justify-between rounded-xl border border-none border-black bg-gray-100 !py-6'>
                    <div className='flex items-center gap-4'>
                      <IconCategory />
                      <SelectValue
                        placeholder='Select Category'
                        className='text-sm text-neutral-600'
                      />
                    </div>
                  </SelectTrigger>
                  <SelectContent className='verflow-y-scroll z-[1000] max-w-fit bg-gray-100 py-2'>
                    <SelectGroup>
                      {categories?.map((category) => (
                        <SelectItem
                          key={category.category_short_name}
                          value={category.category_short_name}
                        >
                          {category.category_short_name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
                {errors.category && (
                  <p className='text-sm text-destructive-500'>
                    {errors.category.message}
                  </p>
                )}
              </div>

              <Button
                type='submit'
                variant={"primary"}
                size={"lg"}
                className='w-full'
              >
                Continue
              </Button>
            </form>
          </div>
        </div>
      </div>
    </SideView>
  )
}

export default WhatsappSupportModal
