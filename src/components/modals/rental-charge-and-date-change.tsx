import { moneyFormatter } from "@/functions/small-functions"

import useCalculateRent from "@/hooks/use-calculate-rent"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import {
  formatDateWithOrdinal,
  getDay,
  updateRentalDates,
} from "@/utils/date-logics"
import { CalendarEndFilledIcon, CalendarStartFilledIcon } from "sharepal-icons"

import { IconSparkle } from "../Icons"
import { Typography } from "../ui/typography"
import { DateSelector, RentalInfo } from "./rental-charge-calculator"

export const RentalChargeAndDateChange = () => {
  const {
    total_days,
    delivery_date,
    pickup_date,
    setDeliveryDate,
    setPickupDate,
  } = useRentalStore()

  const { total_rent } = useCheckoutStore()

  const handleDateChange = (
    changeType: "delivery" | "pickup",
    days: number,
  ) => {
    if (delivery_date && pickup_date) {
      const updatedDates = updateRentalDates(
        delivery_date,
        pickup_date,
        changeType,
        days < 0 ? "minus" : "add",
      )
      setDeliveryDate(updatedDates.newDeliveryDate)
      setPickupDate(updatedDates.newPickupDate)
    }
  }

  const { getTotalCartExtraDayRent } = useCalculateRent({ type: "cart" })

  return (
    <>
      <div className='flex flex-col items-start justify-center gap-8 self-stretch rounded-2xl bg-gray-100 p-4'>
        <div className='inline-flex items-center justify-start gap-4 self-stretch'>
          <RentalInfo
            title='Total Rental Charges'
            value={moneyFormatter(total_rent)}
            subValue=''
            description={`Rental Rate:`}
            description2={`@ ${moneyFormatter(total_rent / total_days)}/day`}
          />
          <div className='h-32 w-[2px] bg-neutral-200'></div>
          <RentalInfo
            title='Chargeable Rental Period'
            value={total_days.toString().padStart(2, "0")}
            subValue='Days'
            description={"*Excluding delivery & pickup days"}
          />
        </div>

        <div className='flex w-full items-center justify-start gap-4 rounded-lg bg-primary-100 p-4 max-md:flex-col md:gap-6'>
          <DateSelector
            icon={
              <CalendarStartFilledIcon className='h-4 w-4 text-primary-500' />
            }
            label='Delivery Date:'
            date={formatDateWithOrdinal(delivery_date || new Date()) || ""}
            day={getDay(delivery_date || new Date())}
            addDate={() => handleDateChange("delivery", 1)}
            minusDate={() => handleDateChange("delivery", -1)}
          />

          <div className='h-[2px] w-full bg-primary-150 md:h-24 md:w-[2px]'></div>
          <DateSelector
            icon={
              <CalendarEndFilledIcon className='h-4 w-4 text-primary-500' />
            }
            label='Pickup Date:'
            date={formatDateWithOrdinal(pickup_date || new Date()) || ""}
            day={getDay(pickup_date || new Date())}
            addDate={() => handleDateChange("pickup", 1)}
            minusDate={() => handleDateChange("pickup", -1)}
          />
        </div>
      </div>
      <div className='inline-flex items-center justify-start gap-2 self-stretch rounded-full bg-secondary-200 px-3 py-1.5 text-success-500 md:h-8'>
        <IconSparkle />
        <div className='shrink grow basis-0'>
          <Typography
            as={"span"}
            className='text-b6 text-success-700 md:text-b4'
          >
            Add a extra day by paying{" "}
            <Typography as='strong' className='text-sh6 md:text-sh4'>
              {" "}
              {moneyFormatter(getTotalCartExtraDayRent())} /day
            </Typography>{" "}
            only
          </Typography>
        </div>
      </div>
    </>
  )
}
