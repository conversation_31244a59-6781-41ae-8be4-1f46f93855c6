"use client"

import { generateFullAddress } from "@/functions/address-utils"
import { useUserStore } from "@/store/user-store"
import type { Address } from "@/types/address"
import type { OrderSummary } from "@/types/order"
import type { AlreadyBookedResponse, ScheduleFormData } from "@/types/schedule"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { getCombinedTimestamp, isPickup } from "@/utils/is-time-valid"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { format } from "date-fns"
import { Loader2 } from "lucide-react"
import type React from "react"
import { useEffect, useState } from "react"
import { toast } from "sonner"
import { Schedule } from "../dashboard/schedule"
import { AdaptiveWrapper } from "./adaptive-wrapper"

interface ScheduleModalProps {
  openSideView: boolean
  setOpenSideView: React.Dispatch<React.SetStateAction<boolean>> | (() => void)
  order: OrderSummary
}

export type ShipmentDetailsResponse = {
  order_stage: string
  pickup_date: number
  delivery_date: number
  pickup_method: string
  shipping_city: string
  calling_number: number
  pickup_partner: string
  whatsapp_number: number
}

export default function ScheduleModal({
  openSideView,
  setOpenSideView,
  order,
}: ScheduleModalProps) {
  const { deal_name: order_id, deal_deal_stage_name } = order

  const [extensionDays] = useState(1)
  const { user } = useUserStore()
  const [shipmentDetailError, setShipmentDetailError] = useState("")

  const {
    data: shipmentData,
    refetch: shipmentDataFetch,
    isPending: shipmentDataPending,
  } = useQuery({
    queryKey: ["shipmentDetails", order_id],
    queryFn: async () => {
      try {
        const response = await fetchWithAuthPost<{
          order_address: { address: Address; address_id: number }
          store_address: Address
          shipment_detail: ShipmentDetailsResponse
        }>("https://api.sharepal.in/api:AIoqxnqr/shipment/details", {
          order_id: order_id,
        })
        return response
      } catch (error) {
        setShipmentDetailError(JSON.parse((error as Error).message)?.message)
      }
    },
    enabled: openSideView,
  })

  const {
    data: alreadyBooked,
    isPending: isAlreadyBookedPending,
    refetch,
  } = useQuery({
    queryKey: ["alreadyBooked", order_id],
    queryFn: async () => {
      const data = await fetchWithAuthPost<AlreadyBookedResponse | null>(
        "https://api.sharepal.in/api:AIoqxnqr/shipment/schedule-check",
        {
          order_id: order_id,
        },
      )
      return data
    },
    enabled: openSideView,
  })

  useEffect(() => {
    if (openSideView) {
      refetch()
      shipmentDataFetch()
    }
  }, [openSideView, refetch, shipmentDataFetch])

  const queryClient = useQueryClient()

  const { mutate: bookShipment, isPending: isShipmentBooking } = useMutation({
    mutationFn: async (data: ScheduleFormData) => {
      if (!shipmentData?.shipment_detail) {
        throw new Error("Shipment details not available")
      }

      const reduceTime = !isPickup(shipmentData.shipment_detail)

      if (!data.timeSlot || data.timeSlot === "") {
        toast.error("Please Select Time")
        throw new Error("Time not selected")
      }

      const shipmentDetails = shipmentData.shipment_detail

      const timestamp = getCombinedTimestamp(
        isPickup(shipmentDetails)
          ? shipmentDetails.pickup_date
          : shipmentDetails.delivery_date,
        data.timeSlot,
      )

      const adjustedTimestamp = reduceTime
        ? timestamp - 15 * 60 * 1000
        : timestamp

      const scheduleDate = isPickup(shipmentDetails)
        ? shipmentDetails.pickup_date
        : shipmentDetails.delivery_date

      const payload = {
        order_id: order_id,
        schedule_timestamp: adjustedTimestamp,
        calling_number: user?.calling_number,
        whatsapp_number: user?.whatsapp_number,
        schedule_date: format(new Date(scheduleDate), "dd-MMM-yyyy"),
        schedule_time: data.timeSlot,
      }

      return fetchWithAuthPost(
        `https://api.sharepal.in/api:AIoqxnqr/shipment/schedule`,
        payload,
      )
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["orderStatus"],
      })
      setOpenSideView(false)
      toast.success("Shipment Booked Successfully")
    },
    onError: (error) => {
      toast.error(
        JSON.parse((error as Error).message).message ??
          "Failed To Schedule Shipment",
      )
    },
  })

  const { mutate: extendRentalPeriod } = useMutation({
    mutationFn: async () =>
      fetchWithAuthPost(
        `https://api.sharepal.in/api:AIoqxnqr/shipment/extension-request`,
        {
          order_id: order_id,
          number_of_days: extensionDays,
        },
      ),
    onSuccess: () => {
      toast.success(
        "Your Extension request has been received. Please connect with our support team on WhatsApp!",
      )
      window.open(
        `https://api.whatsapp.com/send?phone=+917619220543&text=Hi, I want to extend my order ${order_id} for ${extensionDays} days.`,
        "_blank",
      )
    },
    onError: (error) => {
      toast.error(
        JSON.parse((error as Error).message).message ??
          "Failed To Extend Order",
      )
    },
  })

  const isPickupOrder = deal_deal_stage_name == "Pickup Due"

  return (
    <AdaptiveWrapper
      open={openSideView}
      onOpenChange={setOpenSideView}
      title={`Schedule ${isPickupOrder ? "Pickup" : "Delivery"}`}
      className='h-full w-full max-w-xl'
    >
      {shipmentDetailError ? (
        <div className='flex h-full items-center justify-center text-red-500'>
          {shipmentDetailError}
        </div>
      ) : isAlreadyBookedPending || shipmentDataPending ? (
        <div className='flex h-full items-center justify-center'>
          <Loader2 className='h-10 w-10 animate-spin text-primary' />
        </div>
      ) : (
        <>
          {/* <div className='mb-4 p-2'>
            <Badge variant='secondary' className='flex items-center gap-2'>
              {isPickupOrder ? (
                <>
                  <Truck className='h-4 w-4' />
                  Pickup
                </>
              ) : (
                <>
                  <CalendarIcon className='h-4 w-4' />
                  Delivery
                </>
              )}
              Address
            </Badge>
          </div> */}
          <Schedule
            order_id={order_id}
            order={order}
            isShipmentBooking={isShipmentBooking}
            type={isPickupOrder ? "pickup" : "delivery"}
            address={{
              name: order.first_name,
              fullAddress: shipmentData?.order_address?.address
                ? generateFullAddress(shipmentData.order_address.address)
                : "",
            }}
            defaultDate={
              isPickupOrder
                ? shipmentData?.shipment_detail.pickup_date
                : shipmentData?.shipment_detail.delivery_date
            }
            onSchedule={bookShipment}
            onClose={() => setOpenSideView(false)}
            isRental={true}
            alreadyBooked={alreadyBooked ? alreadyBooked : null}
            extendRentalPeriod={extendRentalPeriod}
          />
        </>
      )}
    </AdaptiveWrapper>
  )
}
