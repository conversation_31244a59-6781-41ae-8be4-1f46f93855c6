"use client"

import { Drawer, DrawerContent } from "@/components/ui/drawer"
import MobileCategory from "../custom/mobile-category"

// Mobile Category Wrapper Component
type MobileCategoryWrapperProps = {
  isOpen: boolean
  onClose: () => void
}

const MobileCategoryWrapper = ({
  isOpen,
  onClose,
}: MobileCategoryWrapperProps) => (
  <Drawer open={isOpen} onOpenChange={(open) => !open && onClose()}>
    <DrawerContent className='h-[calc(100vh-5rem)] border-b border-red-50 bg-gray-100 p-0'>
      <MobileCategory onClose={onClose} />
    </DrawerContent>
  </Drawer>
)

export default MobileCategoryWrapper
