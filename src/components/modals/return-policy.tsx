import React from "react"
import { Typography } from "../ui/typography"

interface ReturnPolicyProps {
  title: string
  description: string
  policies: { id: string; title: string; content: string }[]
}

export default function ReturnPolicy({
  title,
  description,
  policies,
}: ReturnPolicyProps) {
  return (
    <main className='flex max-w-2xl basis-0 flex-col items-start justify-start gap-6 overflow-hidden p-6'>
      <section className='flex flex-col items-start justify-start gap-6'>
        <header className='space-y-3 border-b border-gray-200'>
          <Typography as={"h1"} className='text-h6 text-stone-950 md:text-h1'>
            {title}
          </Typography>
          <Typography as={"h1"} className='text-b6 text-zinc-950 md:text-sh1'>
            {description}
          </Typography>
        </header>

        <article className='flex flex-col items-start justify-start gap-5'>
          {policies.map((policy, index) => (
            <section
              key={index}
              className='flex flex-col items-start justify-start gap-2'
            >
              <Typography
                as={"h2"}
                className='text-sh5 text-stone-950 md:text-sh3'
              >
                {policy.title}:
              </Typography>
              <Typography as={"p"} className='text-b6 text-zinc-700'>
                {policy.content?.split("\n").map((desc, index) => (
                  <React.Fragment key={index}>
                    {desc}
                    <br />
                    <br />
                  </React.Fragment>
                ))}
              </Typography>
            </section>
          ))}
        </article>
      </section>
    </main>
  )
}
