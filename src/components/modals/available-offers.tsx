import { DiscountCoupons, SideViewProps } from "@/types"
import { CouponCard } from "../cards/coupon-card"
import SideView from "./modal-wrapper"

// interface OfferCardProps {
//   title: string
//   description: string
// }

// const OfferCard: React.FC<OfferCardProps> = ({ title, description }) => {
//   return (
//     <div className="flex flex-col items-start justify-center gap-4 self-stretch overflow-hidden rounded-xl bg-gray-100 p-4">
//       <div className="shrink grow basis-0 font-inter text-sm font-semibold leading-none text-foreground">
//         {title}
//       </div>
//       <div className="flex flex-col items-start justify-start gap-4">
//         <div className="shrink grow basis-0 font-inter text-sm font-medium leading-none text-neutral-500">
//           {description}
//         </div>
//         {/* <Button className="bg-transparent p-0 text-center font-inter text-sm font-semibold leading-none text-primary-900 hover:bg-transparent">
//           View details
//         </Button> */}
//       </div>
//     </div>
//   )
// }

interface AvailableOffersProps extends SideViewProps {
  coupons: DiscountCoupons[]
}

const AvailableOffers = ({
  openSideView,
  setOpenSideView,
  coupons,
}: AvailableOffersProps) => (
  <SideView
    openSideView={openSideView}
    setOpenSideView={setOpenSideView}
    title='Available Offers'
    className='h-full md:max-w-md'
  >
    <div className='inline-flex flex-col items-start justify-start gap-6 overflow-hidden p-4 md:max-w-xl md:p-6'>
      <div className='flex flex-col items-start justify-start gap-4 self-stretch'>
        {coupons.map((coupon, index) => (
          <CouponCard key={index} coupon={coupon} />
        ))}
      </div>
    </div>
  </SideView>
)

export default AvailableOffers
