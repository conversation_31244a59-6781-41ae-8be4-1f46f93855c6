// Optimized and Refactored CartItems Component
import { cn } from "@/lib/utils"
import { AnimatePresence, motion } from "framer-motion"
import Link from "next/link"
import React, { useEffect, useState } from "react"
import SelectDate from "../custom/select-date"
import { Button } from "../ui/button"

import {
  deleteCartItem,
  fetchAllCartItems,
  updateCartItemQuantity,
} from "@/actions/cart"
import { getCookie } from "@/functions/cookies"
import { moneyFormatter } from "@/functions/small-functions"
import useCalculateRent from "@/hooks/use-calculate-rent"
import useCoupon from "@/hooks/use-coupon"
import { useThrottle } from "@/hooks/use-throttle"
import { trackCartViewAndUpdated, trackRemoveFromCart } from "@/lib/gtag-event"
import { useCheckoutStore } from "@/store/checkout-store"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useCalendarStore, useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { CartItem } from "@/types"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { ChevronDownIcon, Loader2 } from "lucide-react"
import { ChevronRightIcon, ShoppingCartFilledIcon } from "sharepal-icons"
import { toast } from "sonner"
import { CartAddedProduct } from "../cards/cart-added-product"
import ApplyCoupon from "../checkout/apply-coupon"
import { Charges } from "../checkout/order-summary-new/charge-item"
import { CouponInput } from "../checkout/order-summary-new/coupon-input"
import { WalletCheckbox } from "../checkout/order-summary-new/wallet-checkbox"
import EmptyIconCard from "../Icons/EmptyIconCard"
import { Skeleton } from "../ui/skeleton"
import { Typography } from "../ui/typography"
import { AdaptiveWrapper } from "./adaptive-wrapper"

const MAX_QUANTITY = 50

// Types
interface UpdateCartItemQuantityArgs {
  type: "add" | "remove"
  cartItem: CartItem
}

// Main CartItems Component
const CartItems: React.FC<{
  showCart: boolean
  setShowCart: React.Dispatch<React.SetStateAction<boolean>>
  refetchCart?: boolean
}> = ({ showCart, setShowCart }) => {
  const [showTotalCharges, setShowTotalCharges] = useState(false)
  const { openCalendar } = useCalendarStore()
  // Store Data
  const {
    total_days,
    same_day_surge,
    selectedCity,
    delivery_date,
    pickup_date,
  } = useRentalStore()
  const {
    cart_items,
    removeFromCart,
    finalAmount,
    setCartItems,
    updateCartItemQuantity: updateCartItemQuantityStore,
  } = useCheckoutStore()

  const { user } = useUserStore()
  const { openModal } = useOnboardingStore()
  const queryClient = useQueryClient()

  const { getRent, getTotalCartExtraDayRent } = useCalculateRent({
    type: "cart",
  })

  const couponProps = useCoupon()

  useEffect(() => {
    if (showCart)
      if (!delivery_date || !pickup_date) {
        setShowCart(false)
        openCalendar()
      }
  }, [delivery_date, openCalendar, pickup_date, setShowCart, showCart])

  const { mutate: handleUpdateQuantity, isPending: cartItemsUpdateLoading } =
    useMutation({
      mutationFn: async ({ cartItem, type }: UpdateCartItemQuantityArgs) => {
        // If trying to reduce quantity when it's 1, delete the item instead
        if (type === "remove" && cartItem.quantity === 1) {
          return handleDeleteCartItem(cartItem)
        }

        // Set the item being changed for loading state
        setChangingCartItem(cartItem)

        // Calculate new quantity
        const newQuantity =
          type === "add"
            ? Math.min(cartItem.quantity + 1, MAX_QUANTITY) // Max quantity of 50
            : Math.max(cartItem.quantity - 1, 1) // Min quantity of 1

        // If quantity hasn't changed (due to limits), return early
        if (newQuantity === cartItem.quantity) {
          toast.info("Quantity limit reached", { id: "quantityLimit" })
          return null
        }

        return await updateCartItemQuantity({
          user_uid: getCookie("uid") || "",
          cart_item_id: cartItem.id || 0,
          quantity: newQuantity,
        })
      },
      onSuccess: (data) => {
        // Clear the changing item state
        setChangingCartItem(null)

        // If data is null, it means we hit a quantity limit
        if (!data) return

        // Update the store with new data
        updateCartItemQuantityStore(data, same_day_surge)

        toast.success("Cart item updated successfully", {
          id: "updateCartItem",
        })
      },
      onError: (error) => {
        // Clear the changing item state
        setChangingCartItem(null)

        toast.error("Failed to update cart item", { id: "updateCartItem" })
        console.error("Error updating cart item:", error)
      },
      onSettled: () => {
        // Refetch cart items to ensure we have the latest data
        queryClient.invalidateQueries({ queryKey: ["cart_items"] })
      },
    })

  const { mutate: handleDeleteCartItem, isPending: cartItemsDeleteLoading } =
    useMutation({
      mutationFn: async (cartItem: CartItem) => {
        setChangingCartItem(cartItem)
        return await deleteCartItem({
          user_uid: getCookie("uid") || "",
          cart_item_id: cartItem.id || 0,
        })
      },

      onSuccess: (_, cartItem) => {
        toast.success("Item removed from cart", { id: "deleteCartItem" })

        trackRemoveFromCart(
          cartItem.item_name,
          getRent({ type: "cart", cart: cartItem }).rent,
          // getRent({ type: 'cart', cart: cartItem }),
          cartItem.product_code,
          cartItem.cart_image,
          cartItem.cat_sname,
          cartItem.size,
          cartItem.quantity,
          "rent",
        )
        removeFromCart(cartItem, same_day_surge)
      },
      onError: () => {
        toast.error("Failed to remove item from cart", { id: "deleteCartItem" })
      },
    })

  const [changingCartItem, setChangingCartItem] = useState<CartItem | null>(
    null,
  )

  const throttleDeleteCartItem = useThrottle(handleDeleteCartItem, 0)
  const throttleUpdateQuantity = useThrottle(handleUpdateQuantity, 0)

  const { isLoading: cartItemsLoading } = useQuery({
    queryKey: ["cart_items", delivery_date, pickup_date, showCart],
    queryFn: async () => {
      if (!showCart) return []

      const cartResult = await fetchAllCartItems({
        num_days: total_days,
        user_uid: getCookie("uid") || "",
      })
      if (!cartResult) return []
      setCartItems(cartResult, same_day_surge)
      return cartResult
    },
    enabled: true,
  })

  // Tracking Cart View and Updated
  useEffect(() => {
    if (cart_items.length === 0) return
    trackCartViewAndUpdated(cart_items, "Cart Updated", selectedCity.city_url)
  }, [cart_items, selectedCity.city_url])

  useEffect(() => {
    if (showCart) {
      trackCartViewAndUpdated(cart_items, "Cart Viewed", selectedCity.city_url)
    }
  }, [showCart])

  return (
    <AdaptiveWrapper
      // openSideView={showCart}
      // setOpenSideView={setShowCart}
      open={showCart}
      onOpenChange={setShowCart}
      title='Cart Items'
      className='h-full'
      badgeLabel={
        cart_items.length > 0 ? `${cart_items.length} items added` : ""
      }
    >
      {cartItemsLoading ? (
        <div className='flex h-full w-full flex-col items-center justify-center bg-neutral-150 md:max-w-2xl'>
          <Loader2 className='h-20 w-20 animate-spin text-primary-500' />
        </div>
      ) : (
        <div className='flex h-full w-full flex-col justify-between bg-neutral-150 md:max-w-2xl'>
          {/* Empty Cart Message */}
          {cart_items.length === 0 ? (
            <div className='flex h-[80vh] flex-col items-center justify-center gap-8'>
              <EmptyIconCard />
              <div className='flex max-w-[302px] flex-col items-center justify-center gap-3'>
                <h2 className='text-center text-sh2 text-neutral-900 md:text-h2'>
                  Oops! Your Cart is Feeling Lonely...
                </h2>
                <p className='text-center text-b6 text-neutral-400 md:text-b2'>
                  Looks like you left your cart empty. Give it some love and
                  fill it up!
                </p>
              </div>
              <Button
                onClick={() => setShowCart(false)}
                variant={"outline-primary"}
                className='px-6 py-3 text-primary-500'
                asChild
              >
                <Link
                  href={`/${selectedCity.city_url}/#categories`}
                  className='flex items-center gap-2'
                >
                  <Typography as={"span"} className='!text-bt2'>
                    {" "}
                    Explore All Products
                  </Typography>
                  <ChevronRightIcon className='size-6' />
                </Link>
              </Button>
            </div>
          ) : (
            <>
              {/* Cart Items */}
              <div className='flex max-h-max flex-col gap-3 p-4 md:p-6'>
                {cartItemsLoading && <Skeleton></Skeleton>}
                {!cartItemsLoading &&
                  cart_items.map((cartItem) => (
                    <CartAddedProduct
                      key={cartItem.id}
                      cartItem={cartItem}
                      sameDaySurge={same_day_surge}
                      changingCartItem={changingCartItem}
                      onUpdateQuantity={throttleUpdateQuantity}
                      onDeleteItem={throttleDeleteCartItem}
                      cartItemsDeleteLoading={cartItemsDeleteLoading}
                      cartItemsUpdateLoading={cartItemsUpdateLoading}
                    />
                  ))}
              </div>

              {/* Footer Section */}
              <div className='sticky bottom-0 flex w-full flex-col gap-4 bg-gray-100 p-4 shadow-xs md:p-6'>
                {/* Date component */}
                <SelectDate
                  inCart={true}
                  extraDayPrice={getTotalCartExtraDayRent()}
                />

                <div className='space-y-1 overflow-hidden'>
                  <div
                    className={cn(
                      "flex w-full gap-5 max-md:flex-col max-md:gap-4 md:justify-between",
                    )}
                  >
                    {
                      <div className='h-max min-w-[200px] md:block'>
                        <WalletCheckbox cart />
                      </div>
                    }
                    <CouponInput {...couponProps} inCart />
                  </div>
                  <ApplyCoupon
                    open={couponProps.showViewCoupons}
                    onOpenChange={couponProps.setShowViewCoupons}
                    onApplyCoupon={couponProps.handleApplyCoupon}
                    inCart
                  />
                </div>
                <AnimatePresence>
                  {showTotalCharges && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{
                        height: showTotalCharges ? "auto" : 0,
                        opacity: showTotalCharges ? 1 : 0,
                      }}
                      transition={{ duration: 0.3 }}
                      exit={{ height: 0, opacity: 0 }}
                      className='overflow-hidden'
                    >
                      <Charges />
                    </motion.div>
                  )}
                </AnimatePresence>
                <div
                  className={cn(
                    "z-[100] flex items-center justify-between border-t border-neutral-200 pt-4 transition-all",
                    // showTotalCharges ? 'mt-4 md:mt-6' : 'mt-0',
                  )}
                  // onClick={(e) => e.stopPropagation()}
                >
                  <div>
                    <div
                      onClick={() => setShowTotalCharges((prev) => !prev)}
                      className='flex cursor-pointer items-center gap-2 text-neutral-900'
                    >
                      <Typography as={"h4"} className='text-sh6 md:text-h4'>
                        Total Charges
                      </Typography>

                      <ChevronDownIcon
                        className={cn(
                          "h-4 w-4 transition-all",
                          showTotalCharges ? "rotate-0" : "rotate-180",
                        )}
                      />
                    </div>
                    <Typography
                      as='p'
                      className='text-b6 text-gray-400 max-md:hidden'
                    >
                      Price incl. of all taxes
                    </Typography>
                    <Typography
                      as={"h4"}
                      className='hidden text-h4 text-neutral-900 max-md:block'
                    >
                      {moneyFormatter(finalAmount())}
                    </Typography>
                  </div>

                  <div className='flex items-center gap-6'>
                    <Typography
                      as={"h1"}
                      className='text-h1 text-neutral-900 max-md:hidden'
                    >
                      {moneyFormatter(finalAmount())}
                    </Typography>
                    {user ? (
                      <Button size={"extra-lg"} variant={"primary"} asChild>
                        <Link href='/checkout'>
                          <ShoppingCartFilledIcon className='h-6 w-6 fill-gray-100 text-gray-100' />
                          <Typography as='span' className='!text-bt2'>
                            Go to Checkout
                          </Typography>
                        </Link>
                      </Button>
                    ) : (
                      <Button
                        onClick={() => {
                          openModal()
                        }}
                        size={"extra-lg"}
                        variant={"primary"}
                        className='!text-bt2'
                      >
                        <Typography as='span' className='!text-bt2'>
                          Login to CheckOut
                        </Typography>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </AdaptiveWrapper>
  )
}

export default CartItems
