import React, { useEffect, useState } from "react"
import { Icon<PERSON>rrowR<PERSON>, IconPlus, IconTick } from "../Icons"

import { addCartItem } from "@/actions/cart"
import {
  formatUrlName,
  getImage,
  moneyFormatter,
} from "@/functions/small-functions"
import useCalculateRent from "@/hooks/use-calculate-rent"
import SpImage from "@/shared/SpImage/sp-image"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { RentalItem, RentalItemProduct } from "@/types"
import SelectDate from "../custom/select-date"
import { Button } from "../ui/button"
import SideView from "./modal-wrapper"

import { fetchRentalVarients } from "@/actions/category"
import { fetchAddOns } from "@/actions/product"
import { getCookie } from "@/functions/cookies"
import { useThrottle } from "@/hooks/use-throttle"
import { trackAddTo<PERSON>art } from "@/lib/gtag-event"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { useInView } from "react-intersection-observer"
import { toast } from "sonner"
import SelectColorAndSize from "./select-color-size"

const CartAddedProduct = ({
  product,
  totalRent,
}: {
  product: RentalItem
  totalRent: number
}) => (
  <div className='flex w-full items-center gap-5 overflow-hidden rounded-2xl bg-neutral-50 p-2 md:min-w-[558px]'>
    <SpImage
      alt={product.ri_image_alt_text}
      src={getImage(product.ri_image)}
      width={70}
      height={70}
      className=''
    />
    <div className='flex w-full gap-2 max-md:flex-col'>
      <div className='flex w-full items-center justify-between gap-5'>
        <div>
          <h2 className='font-bold max-md:text-sm'>{product.ri_short_name}</h2>
          <p className='text-xs text-neutral-600 max-md:text-[10px]'>
            {product.ri_name}
          </p>
        </div>
        <span className='md:hidden'>
          <IconTick />
        </span>
      </div>
      <div className='flex items-center justify-between max-md:w-full md:flex-col'>
        <p className='font-bold'>{moneyFormatter(totalRent)}</p>
        <p className='text-xs text-neutral-600 md:text-nowrap'>Added to Cart</p>
      </div>
      <span className='max-md:hidden'>
        <IconTick />
      </span>
    </div>
  </div>
)

const RecommedProduct = ({ product }: { product: RentalItem }) => {
  const [selectedSize, setSelectedSize] = useState<string>("")
  const [showSizeColorSelect, setShowSizeColorSelect] = useState(false)
  const {} = product
  const { city_surge, same_day_surge, surge_factor, total_days } =
    useRentalStore()

  const { rent, mainSurge } = useCalculateRent({ product, type: "product" })

  // console.log({ city_surge, same_day_surge, surge_factor })
  const queryClient = useQueryClient()
  const { user } = useUserStore()
  const { addToCart } = useCheckoutStore()
  const [isCartAddtionLoading, setisCartAddtionLoading] = useState(false)

  const handleAddToCart = async () => {
    setisCartAddtionLoading(true)
    if (product.size_specific) {
      if (selectedSize == "") {
        if (!showSizeColorSelect) setShowSizeColorSelect(true)
        setisCartAddtionLoading(false)
        return
      }
    }
    setShowSizeColorSelect(false)
    try {
      const response = await addCartItem({
        user_id: user ? user.id : 0,
        id: product.id,
        user_uid: getCookie("uid") || "",
        num_days: total_days,
        size: selectedSize,
        cart_type: "rent",
        surge_factor,
        city_surge,
        same_day_surge,
        final_surge: mainSurge,
      })
      if (response) {
        trackAddToCart(
          product.ri_short_name,
          0, // You'll need to pass the correct rent value here
          product.ri_code,
          getImage(product.ri_image),
          product.category_short_name,
          selectedSize,
          "Rent",
          formatUrlName(product.super_category_url),
        )
        addToCart(response, same_day_surge)
        toast.success("Added To Cart")
        queryClient.invalidateQueries({
          queryKey: ["cart_items"],
        })
      }
    } catch (error) {
      console.error("Error adding to cart:", error)
    } finally {
      setisCartAddtionLoading(false)
      setSelectedSize("")
    }
  }
  const {
    data: sizes = [],

    // error: sizesError,
  } = useQuery({
    queryKey: ["rentalVariants", product.ri_short_name],
    queryFn: () => fetchRentalVarients(product.ri_short_name),
    enabled: !!product.ri_short_name && product.size_specific, // Prevents running if product name is empty
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  })

  const throtleAddToCart = useThrottle(handleAddToCart, 2000)

  return (
    <>
      <SelectColorAndSize
        sizes={sizes}
        product={product}
        setSelectedSize={setSelectedSize}
        selectedSize={selectedSize}
        handleAddToCart={throtleAddToCart}
        isCartAddtionLoading={isCartAddtionLoading}
        openSideView={showSizeColorSelect}
        setOpenSideView={setShowSizeColorSelect}
      />
      <div className='flex h-72 w-36 flex-col items-center gap-2 overflow-hidden rounded-2xl p-2'>
        <div className='flex h-32 w-32 items-center justify-center overflow-hidden rounded-lg bg-neutral-50'>
          <SpImage
            width={100}
            height={100}
            className='object-contain p-1'
            src={getImage(product.ri_image)}
            alt={product.ri_image_alt_text}
          />
        </div>
        <div className='flex w-full flex-col gap-2'>
          <div className='flex flex-col gap-2'>
            <div className='line-clamp-1 text-sm font-bold leading-none text-neutral-900'>
              {product.ri_short_name}
            </div>
            <div className='line-clamp-1 text-xs font-semibold leading-none text-neutral-600'>
              {product.ri_name}
            </div>
          </div>
          <div className='flex flex-col gap-1'>
            <div className='text-xs font-bold text-neutral-600'>
              Rent for{" "}
              <span className='text-neutral-900'>{total_days} days</span>
            </div>
            <div className='text-sm font-bold text-neutral-900'>
              {moneyFormatter(rent)}
            </div>
          </div>

          <Button
            onClick={throtleAddToCart}
            variant={"normal"}
            className='flex items-center gap-2 text-nowrap rounded-full border-2 border-neutral-900 bg-neutral-50 px-3 py-2 text-sm font-semibold text-neutral-900'
          >
            <IconPlus />
            Add to Cart
          </Button>
        </div>
      </div>
    </>
  )
}

interface Props {
  openView: boolean
  setOpenView: React.Dispatch<React.SetStateAction<boolean>>
  setShowCart: React.Dispatch<React.SetStateAction<boolean>>
  product: RentalItemProduct
  totalRent: number
}

const RecommedProducts: React.FC<Props> = ({
  openView,
  setOpenView,
  setShowCart,
  product,
  totalRent,
}) => {
  const [page, setPage] = useState(1)
  const [products, setProducts] = useState<RentalItem[]>([])

  const { ref, inView } = useInView({
    threshold: 0.5, // Trigger when 50% of the element is visible
    triggerOnce: false, // Allow continuous triggering for pagination
  })

  const { data, isFetching, isLoading, isPlaceholderData, isSuccess } =
    useQuery({
      queryKey: ["recommended", page, product.ri_short_name],
      queryFn: () =>
        fetchAddOns({
          city: product.ri_city,
          shortName: product.ri_short_name,
          pageNumber: page,
        }),
      gcTime: 5 * 60 * 1000, // 5 minutes
      staleTime: 2 * 60 * 1000, // 2 minutes
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      select: (response) => response.items,
      enabled: openView,
    })

  useEffect(() => {
    if (isSuccess && data) {
      if (page === 1) {
        setProducts(data)
      } else {
        setProducts((prevProducts) => [...prevProducts, ...data])
      }
    }
  }, [isSuccess, data, page])

  // Infinite scrolling logic
  useEffect(() => {
    if (inView && !isLoading && !isFetching) {
      setPage((prevPage) => prevPage + 1)
    }
  }, [inView, isLoading, isFetching])
  const { getTotalCartExtraDayRent } = useCalculateRent({
    type: "cart",
  })
  return (
    <SideView
      openSideView={openView}
      setOpenSideView={setOpenView}
      className='h-full'
      title='Recommended Products'
    >
      <div className='p-4'>
        <CartAddedProduct product={product} totalRent={totalRent} />

        <div className='flex flex-col space-y-4 py-5'>
          <h1 className='text-lg font-bold'>You may also like</h1>
          <hr />

          <div className='grid min-h-96 w-full grid-cols-2 items-center justify-stretch justify-items-center md:grid-cols-3'>
            {products?.map((recommendedProduct: RentalItem, index: number) => (
              <RecommedProduct
                product={recommendedProduct}
                key={recommendedProduct.id || index}
              />
            ))}
            {data && data.length > 0 && !isFetching && !isPlaceholderData && (
              <div ref={ref} className='h-5' />
            )}
          </div>
        </div>
      </div>

      {/* Sticky Bottom Bar */}
      <div className='sticky bottom-0 inline-flex w-full flex-col items-center justify-start gap-4 overflow-hidden bg-neutral-50 p-4 shadow-md md:p-6'>
        <div className='flex flex-col items-start justify-start gap-6 self-stretch'>
          <SelectDate extraDayPrice={getTotalCartExtraDayRent()} />
          <div className='inline-flex items-start justify-start gap-6 self-stretch'>
            <Button
              onClick={() => {
                setShowCart(true)
                setOpenView(false)
              }}
              className='flex h-12 shrink grow basis-0 items-center justify-center overflow-hidden rounded-full bg-primary-500 px-4 py-3 text-neutral-50 hover:bg-primary-500'
            >
              <div className='flex items-center justify-center gap-1 px-2 py-0.5'>
                <div className='text-center text-base font-semibold'>
                  Continue to Cart
                </div>
              </div>
              <IconArrowRight />
            </Button>
          </div>
        </div>
      </div>
    </SideView>
  )
}

export default RecommedProducts
