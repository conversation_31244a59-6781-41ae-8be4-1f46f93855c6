import { getCookie } from "@/functions/cookies"
import { moneyFormatter } from "@/functions/small-functions"
import useCoupon from "@/hooks/use-coupon"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { SideViewProps } from "@/types"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { Loader2 } from "lucide-react"
import { useEffect, useState } from "react"
import { toast } from "sonner"
import PartyPopperIcon from "../Icons/party-popper-icon"
import { Charges } from "../checkout/order-summary-new/charge-item"
import { CouponInput } from "../checkout/order-summary-new/coupon-input"
import { Button } from "../ui/button"
import { Typography } from "../ui/typography"
import SideView from "./modal-wrapper"

interface ApplyCouponProps extends SideViewProps {
  order_id: number
  coupon_code: string
  total_rent_amount: number
  discount_amount: number
}

const ApplyCouponSideView = ({
  openSideView,
  setOpenSideView,
  order_id,
  coupon_code,
  discount_amount,
}: ApplyCouponProps) => {
  const {
    couponCode,
    handleApplyCoupon,
    isLoading,
    setCouponCode,
    setShowViewCoupons,
  } = useCoupon()

  const props = {
    couponCode,
    isLoading,
    setCouponCode,
    setShowViewCoupons,
  }

  const queryClient = useQueryClient()

  const { user } = useUserStore()
  const { delivery_date, pickup_date, total_days } = useRentalStore()
  const { finalAmount, handling_charges, setAppliedCouponCode } =
    useCheckoutStore()

  const [isSameApplied, setIsSameApplied] = useState(false)

  // need to review it again
  useEffect(() => {
    if (openSideView && coupon_code) {
      setAppliedCouponCode(coupon_code, discount_amount)
    }
    if (couponCode && coupon_code) setIsSameApplied(couponCode == coupon_code)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openSideView])

  const { mutate, isPending } = useMutation({
    mutationFn: (code: string) =>
      fetchWithAuthPost("https://api.sharepal.in/api:AIoqxnqr/order/update", {
        order_id: order_id,
        user_uid: user ? user.user_uid : getCookie("uid"),
        coupon_code: code,
        total_amount: finalAmount(),
        update_user_id: user ? user.id : 0,
        delivery_date,
        return_date: pickup_date,
        total_days,
        cod_handling_charges: handling_charges,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["order-details-fetch"],
      })
      queryClient.invalidateQueries({
        queryKey: ["order-data-fetch"],
      })
      toast.success("Coupon Applied Successfully")
      setOpenSideView(false)
    },
    onError: (error) => {
      toast.error(
        JSON.parse(error.message).message ?? "Failed To Update Coupon",
      )
    },
  })

  //   const handleApplyCouponUpdate = async (code: string): Promise<void> => {
  //     if (await handleApplyCoupon(code)) {
  //       console.log('Final Amount', finalAmount())
  //       mutate(code)
  //     }
  //   }

  const handleApplyCouponUpdate = async (): Promise<void> => {
    mutate(couponCode)
  }

  return (
    <SideView
      openSideView={openSideView}
      setOpenSideView={setOpenSideView}
      title='Apply Discount Coupon'
      className='h-full bg-gray-100'
    >
      <div className='h-full max-w-2xl'>
        <div className='relative flex h-full max-w-2xl flex-col justify-between'>
          <div className='px-4 pt-2 md:px-6 md:pt-6'>
            <div className='flex items-center justify-start gap-4 rounded-3xl bg-gradient-to-r from-indigo-50 to-lime-50 p-3'>
              <PartyPopperIcon />
              <div className='inline-flex flex-col items-start justify-start'>
                <Typography
                  as={"span"}
                  className='!text-sh6 text-pink-500 md:!text-sh2'
                >
                  Forgot to apply a discount coupon?{" "}
                </Typography>
                <Typography
                  as={"span"}
                  className='!text-sh6 text-gray-900 md:!text-sh2'
                >
                  No worries — on SharePal, you can add coupons after placing
                  your order, as long as it hasn&apos;t been packed for
                  delivery.
                </Typography>
              </div>
            </div>
            <div className='mt-6 w-full'>
              <CouponInput
                handleApplyCoupon={(code) => {
                  handleApplyCoupon(code)
                }}
                //   handleApplyCoupon={handleApplyCouponUpdate}
                showToggleViewCoupons={true}
                showViewCoupons={true}
                {...props}
              />
            </div>

            <div className='space-y-5 py-5'>
              <Charges />
              <div className='flex items-center justify-between'>
                <div>
                  <Typography as={"span"} className='text-sh4 md:text-sh2'>
                    Order Total
                  </Typography>
                  <Typography
                    as={"span"}
                    className='block text-o4 !font-bold text-gray-500 md:text-b6'
                  >
                    Price incl. of all taxes
                  </Typography>
                </div>

                <Typography as={"span"} className='text-h4'>
                  {moneyFormatter(finalAmount())}
                </Typography>
              </div>
            </div>
          </div>

          {couponCode && (
            <div className='sticky bottom-0 right-0 z-50 bg-gray-100 p-4 shadow-sm'>
              <Button
                disabled={isPending || isSameApplied}
                onClick={handleApplyCouponUpdate}
                type='button'
                className='mt-3 w-full'
                variant={"primary"}
                size={"lg"}
              >
                Apply Discount
                {isPending && <Loader2 className='h-5 w-4 animate-spin' />}
              </Button>
            </div>
          )}
        </div>
        {/* <div className="space-y-6 p-6">
          <section className="flex flex-col items-start justify-start gap-3 self-stretch">
            <h2 className="self-stretch font-['Inter'] text-lg font-semibold leading-normal text-zinc-950">
              How to Apply Discount Vouchers?
            </h2>
            <ul className="space-y-2 self-stretch font-['Inter'] text-sm font-medium leading-none text-gray-600">
              <li>
                <strong className="font-bold">Browse Coupons:</strong> Explore
                available coupons on SharePal.
              </li>
              <li>
                <strong className="font-bold">Select a Coupon:</strong> Choose
                one that fits your order.
              </li>
              <li>
                <strong className="font-bold">Apply at Checkout:</strong> Enter
                the coupon code to see your discount instantly.
              </li>
              <li>
                <strong className="font-bold">Post-Order Application:</strong>{' '}
                You can apply a coupon even after placing your order—as long as
                it hasn&apos;t been packed and is still pending delivery.
              </li>
              <li>
                <strong className="font-bold">Review Terms:</strong> Check the
                coupon&apos;s terms and conditions for more details.
              </li>
            </ul>
          </section>
          <section className="flex flex-col items-start justify-start gap-3 self-stretch">
            <h2 className="self-stretch font-['Inter'] text-lg font-semibold leading-normal text-zinc-950">
              Terms & Conditions:
            </h2>
            <ul className="space-y-2 self-stretch font-['Inter'] text-sm font-medium leading-none text-gray-600">
              <li>Each coupon is valid for a single use per customer.</li>
              <li>Maximum discount is capped at ₹500 per order.</li>
              <li>Coupons cannot be combined with other offers.</li>
              <li>
                Must be applied before the order is packed and ready for
                delivery.
              </li>
              <li>Additional conditions may apply.</li>
            </ul>
          </section>
        </div> */}
      </div>
    </SideView>
  )
}

export default ApplyCouponSideView
