import React from "react"

import { cn } from "@/lib/utils"
import { IconClose } from "../Icons"
import {
  Sheet,
  SheetClose,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from "../ui/sheet"

type Props = {
  title: string
  className?: string
  children: React.ReactNode
  trigger: React.ReactNode
}

const SideView = ({ children, title, className, trigger }: Props) => (
  <Sheet>
    <SheetTrigger className='h-0 w-full'>{trigger}</SheetTrigger>
    <SheetContent
      side={"custom"}
      className={cn(
        "h-max overflow-hidden overflow-y-scroll rounded-tl-xl p-0 max-md:rounded-tr-xl md:min-w-max md:rounded-bl-3xl md:rounded-tl-3xl",
        className,
      )}
    >
      <SheetHeader className='sticky top-0 flex flex-row items-center justify-start gap-4 space-y-0 bg-gray-100 p-4 md:min-w-max md:p-6'>
        <SheetClose className='cursor-pointer md:scale-[1.5]'>
          <IconClose />
        </SheetClose>
        <SheetTitle className='w-max font-bold md:text-xl'>{title}</SheetTitle>
      </SheetHeader>
      <div className='h-max'>{children}</div>
    </SheetContent>
  </Sheet>
)

export default SideView
