import { Skeleton } from "@/components/ui/skeleton"
import { getImage, moneyFormatter } from "@/functions/small-functions"
import { useCalendarStore, useRentalStore } from "@/store/rental-store"
import { RentalItem, SideViewProps } from "@/types"
import {
  formatDateWithOrdinal,
  getDay,
  updateRentalDates,
} from "@/utils/date-logics"
import Image from "next/image"
import React, { useEffect, useState } from "react"
import { IconCalenderEdit, IconSparkle } from "../Icons"
import { Button } from "../ui/button"

import useCalculateRent from "@/hooks/use-calculate-rent"
import { cn } from "@/lib/utils"
import {
  CalendarEndFilledIcon,
  CalendarStartFilledIcon,
  MinusOutlinedIcon,
  PlusAddOutlinedIcon,
} from "sharepal-icons"
import { Typography } from "../ui/typography"
import { AdaptiveWrapper } from "./adaptive-wrapper"

interface RentalInfoProps {
  title: string
  value: string
  subValue: string
  description: string
  description2?: string
}

export const RentalInfo: React.FC<RentalInfoProps> = ({
  title,
  value,
  subValue,
  description,
  description2,
}) => (
  <div className='inline-flex shrink grow basis-0 flex-col items-center justify-center md:gap-5'>
    <Typography
      as='p'
      className='self-stretch p-2 text-center text-sh7 leading-normal text-gray-600 md:text-sh3'
    >
      {title}
    </Typography>
    <div className='flex h-14 flex-col items-center justify-start gap-3 self-stretch'>
      <div className='flex items-end justify-center gap-1'>
        {value ? (
          <Typography as='p' className='text-h2 text-gray-900 md:text-h1'>
            {value}
          </Typography>
        ) : (
          <Skeleton className='h-8 min-w-28 bg-gray-200 md:h-9' />
        )}
        <Typography as='p' className='text-sh2 text-gray-700 md:text-h6'>
          {subValue}
        </Typography>
      </div>
      <div className='flex w-full items-center justify-center max-md:flex-col'>
        <Typography
          as='span'
          className='text-center text-o4 text-gray-600 md:text-b4'
        >
          {description}
        </Typography>
        {description2 && (
          <Typography
            as='span'
            className='text-center text-o3 text-gray-900 md:text-b4'
          >
            {description2}
          </Typography>
        )}
      </div>
    </div>
  </div>
)

interface DateSelectorProps {
  icon: React.ReactNode
  label: string
  date: string
  day: string
  addDate: () => void
  minusDate: () => void
  className?: string
}

export const DateSelector: React.FC<DateSelectorProps> = ({
  icon,
  label,
  date,
  day,
  addDate,
  minusDate,
  className,
}) => (
  <div
    className={cn(
      "inline-flex shrink grow basis-0 flex-col items-center justify-center gap-4",
      className,
    )}
  >
    <div className='inline-flex items-center justify-start gap-1.5'>
      {icon}
      <Typography as='p' className='text-sh5 text-primary-700 md:text-sh7'>
        {label}
      </Typography>
    </div>
    <div className='flex flex-col items-center justify-start gap-2 md:h-14'>
      <div className='flex items-center justify-between gap-3'>
        <button
          onClick={minusDate}
          name='delivery'
          className='flex items-center justify-center rounded-full border-2 border-primary-500 bg-gray-100 p-2'
        >
          <MinusOutlinedIcon className='text-primary-500' />
        </button>
        <Typography
          as='p'
          className='min-w-max max-w-[200px] shrink grow basis-0 px-3 text-center text-h6 md:text-h4'
        >
          {date}
        </Typography>
        <button
          onClick={addDate}
          name='delivery'
          className='flex items-center justify-center rounded-full border-2 border-primary-500 bg-gray-100 p-2'
        >
          <PlusAddOutlinedIcon className='text-primary-500' />
        </button>
      </div>
      <Typography as='p' className='text-b6 text-neutral-400 md:text-b4'>
        {day}
      </Typography>
    </div>
  </div>
)

interface RentalChargeCalculatorProps extends SideViewProps {
  product: RentalItem
}

const RentalChargeCalculator = ({
  product,
  openSideView,
  setOpenSideView,
}: RentalChargeCalculatorProps) => {
  const { ri_short_name, ri_name } = product

  const { openCalendar } = useCalendarStore()

  return (
    <AdaptiveWrapper
      // openSideView={openSideView}
      // setOpenSideView={setOpenSideView}
      open={openSideView}
      onOpenChange={setOpenSideView}
      title='Rental Charges Calculator'
      className={"h-full bg-neutral-150 md:max-w-xl"}
      titleClassName='bg-neutral-150'
    >
      <div className='inline-flex h-max w-full flex-col items-start justify-start gap-8 p-6'>
        <div className='flex flex-col items-start justify-start gap-6 self-stretch'>
          <div className='inline-flex items-center justify-start self-stretch rounded-xl bg-gray-100 p-2'>
            <Image
              width={92}
              height={92}
              className='w-16 rounded-md object-contain md:w-24'
              src={getImage(product.ri_image)}
              alt={"Product Image"}
            />
            <div className='inline-flex flex-col items-start justify-center gap-2 px-3'>
              <Typography
                as={"p"}
                className='shrink grow basis-0 !text-h6 md:text-h4'
              >
                {ri_short_name}
              </Typography>
              <Typography
                as={"p"}
                className='!text-b4 text-neutral-300 md:text-b2'
              >
                {ri_name}
              </Typography>
            </div>
          </div>

          <RentalChargenAndDateChange product={product} />
          <Button
            onClick={openCalendar}
            className='inline-flex h-12 w-full items-center justify-center gap-5 rounded-full bg-primary-500 text-center text-base font-semibold leading-tight text-gray-100 hover:bg-primary-600 max-md:hidden'
          >
            <span className='scale-[1.5]'>
              {" "}
              <IconCalenderEdit />
            </span>
            <span>Change Rental Period</span>
          </Button>
        </div>

        <div className='flex w-full flex-col items-start justify-start gap-3 self-stretch'>
          <Typography as={"h6"} className='self-stretch text-sh3 md:text-h7'>
            Enjoy the Benefits of Dynamic Rental Rates:
          </Typography>
          <div className='self-stretch text-neutral-500'>
            <Typography
              as={"span"}
              className='mr-1 text-sh6 font-bold md:text-sh4'
            >
              1.{"  "} Seasonal Pricing Adjustments:
            </Typography>
            <Typography
              as={"span"}
              className='text-b6 text-neutral-300 md:text-b4'
            >
              Rates adapt based on the time of year to ensure competitive
              pricing.
            </Typography>
            <br />
            <Typography
              as={"span"}
              className='mr-1 text-sh6 font-bold md:text-sh4'
            >
              2. {"  "}Longer Rentals, Lower Rates:
            </Typography>
            <Typography
              as={"span"}
              className='text-b6 text-neutral-300 md:text-b4'
            >
              The more days you rent, the less you pay per day.
            </Typography>
            <br />
            <Typography
              as={"span"}
              className='mr-1 text-sh6 font-bold md:text-sh4'
            >
              3.{"  "} Maximize Value:
            </Typography>
            <Typography
              as={"span"}
              className='text-b6 text-neutral-300 md:text-b4'
            >
              Choose extended rental periods for the best deals on your favorite
              products.
            </Typography>
          </div>
        </div>
      </div>
      <div className='sticky bottom-0 flex w-full items-center justify-center bg-gray-100 p-4 md:hidden'>
        <Button
          onClick={openCalendar}
          className='inline-flex h-12 w-full items-center justify-center gap-5 rounded-full bg-primary-500 text-center text-base font-semibold leading-tight text-gray-100 md:hidden'
        >
          <span className='scale-[1.5]'>
            {" "}
            <IconCalenderEdit />
          </span>
          <span>Change Rental Period</span>
        </Button>
      </div>
    </AdaptiveWrapper>
  )
}

export default RentalChargeCalculator

export const RentalChargenAndDateChange = ({
  product,
}: {
  product: RentalItem
}) => {
  const { total_days, delivery_date, pickup_date } = useRentalStore()

  const [deliveryDate, setDeliveryDate] = useState(delivery_date || new Date())
  const [pickupDate, setPickupDate] = useState(pickup_date || new Date())
  const [totalDays, setTotalDays] = useState(total_days ?? 1)

  const { rent, extraDayRent } = useCalculateRent({
    type: "product",
    product: product,
    custom_total_days: totalDays,
  })

  const handleDateChange = (
    changeType: "delivery" | "pickup",
    days: number,
  ) => {
    const updatedDates = updateRentalDates(
      deliveryDate,
      pickupDate,
      changeType,
      days < 0 ? "minus" : "add",
    )

    setDeliveryDate(updatedDates.newDeliveryDate)
    setPickupDate(updatedDates.newPickupDate)
    setTotalDays(updatedDates.newTotalDays)
  }

  useEffect(() => {
    if (delivery_date) setDeliveryDate(delivery_date)
    if (pickup_date) setPickupDate(pickup_date)
    if (total_days) setTotalDays(total_days)
  }, [delivery_date, pickup_date, total_days])

  return (
    <>
      <div className='flex flex-col items-start justify-center gap-8 self-stretch rounded-2xl bg-gray-100 p-4'>
        <div className='inline-flex items-center justify-start gap-4 self-stretch'>
          <RentalInfo
            title='Total Rental Charges'
            value={moneyFormatter(rent)}
            subValue=''
            description={`Rental Rate:`}
            description2={`@ ${moneyFormatter(rent / totalDays)}/day`}
          />
          <div className='h-32 w-[2px] bg-neutral-200'></div>
          <RentalInfo
            title='Chargeable Rental Period'
            value={totalDays.toString().padStart(2, "0")}
            subValue='Days'
            description={"*Excluding delivery & pickup days"}
          />
        </div>

        <div className='flex w-full items-center justify-start gap-4 rounded-lg bg-primary-100 p-4 max-md:flex-col md:gap-6'>
          <DateSelector
            icon={
              <CalendarStartFilledIcon className='h-4 w-4 text-primary-500' />
            }
            label='Delivery Date:'
            date={formatDateWithOrdinal(deliveryDate || new Date()) || ""}
            day={getDay(deliveryDate || new Date())}
            addDate={() => handleDateChange("delivery", 1)}
            minusDate={() => handleDateChange("delivery", -1)}
          />

          <div className='h-[2px] w-full bg-primary-150 md:h-24 md:w-[2px]'></div>
          <DateSelector
            icon={
              <CalendarEndFilledIcon className='h-4 w-4 text-primary-500' />
            }
            label='Pickup Date:'
            date={formatDateWithOrdinal(pickupDate || new Date()) || ""}
            day={getDay(pickupDate || new Date())}
            addDate={() => handleDateChange("pickup", 1)}
            minusDate={() => handleDateChange("pickup", -1)}
          />
        </div>
      </div>

      <div className='inline-flex items-center justify-start gap-2 self-stretch rounded-full bg-secondary-200 px-3 py-1.5 text-success-500 md:h-8'>
        <IconSparkle />
        <div className='shrink grow basis-0'>
          <Typography
            as={"span"}
            className='text-b6 text-success-700 md:text-b4'
          >
            Increase your Rental Period by paying{" "}
            <Typography as='strong' className='text-sh6 md:text-sh4'>
              {" "}
              {moneyFormatter(extraDayRent)} /day
            </Typography>{" "}
            extra only
          </Typography>
        </div>
      </div>
    </>
  )
}
