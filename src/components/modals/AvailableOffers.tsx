import React from "react"

import { IconClose, IconDatePicker } from "../Icons"

import { DialogClose, DialogContent, DialogTitle } from "../ui/dialog"

import { Button } from "../ui/button"

const AvailableOffersModal = () => (
  <DialogContent className='max-w-screen fixed z-50 flex h-full w-full flex-col justify-end border-0 bg-transparent p-0'>
    <div className='h-[90%] min-h-max w-full rounded-t-[12px] bg-gray-150 pb-2'>
      {/* title */}
      <DialogTitle className='flex items-center justify-between border-b border-neutral-200 p-4'>
        <div className='flex gap-4'>
          <IconDatePicker />
          <h1 className='font-bold'>Available Offers for you </h1>
        </div>
        <DialogClose>
          {" "}
          <IconClose />
        </DialogClose>
      </DialogTitle>
      {/* Dates */}
      <div className=''></div>
      {/* Button */}
      <div className='px-4'>
        <Button className='w-full bg-primary-500'>Continue</Button>
      </div>
    </div>
  </DialogContent>
)

export default AvailableOffersModal
