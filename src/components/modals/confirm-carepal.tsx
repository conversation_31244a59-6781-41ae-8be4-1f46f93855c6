import { useState } from "react"

import { moneyFormatter } from "@/functions/small-functions"
import { RentOrder } from "@/types"

import { Loader } from "lucide-react"
import { ChevronDownIcon } from "sharepal-icons"

import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"
import { AnimatePresence, motion } from "framer-motion"
import { Charges } from "../checkout/order-summary-new/charge-item"
import { ProductSummary } from "../checkout/product-summary"
import { Button } from "../ui/button"
import { Typography } from "../ui/typography"

function ConfirmCarePal({
  order_id,
  updateOrder,
  isPending,
}: {
  order_id: string
  isPending: boolean
  updateOrder: () => void
  order: RentOrder
  openChangeRentalDialog: boolean
}) {
  const [showTotalCharges, setShowTotalCharges] = useState(true)
  const { finalAmount } = useCheckoutStore()
  return (
    <div className='flex h-[95%] flex-col justify-between'>
      <div className='space-y-4 p-4'>
        {/* Title */}
        <div className='space-y-4 text-center'>
          <div className='flex items-center justify-center space-x-1 text-o4 text-neutral-500 md:text-b4'>
            <Typography as='p'>Order No:</Typography>
            <Typography as='p'>#{order_id}</Typography>
          </div>
          <Typography as='p' className='text-b6 text-neutral-500 md:text-b2'>
            Please confirm your modified order details so we can update and
            process your new order.
          </Typography>
        </div>

        <div
          className={cn(
            "flex flex-col gap-4 rounded-lg border-2 border-neutral-200 bg-gray-100 p-4",
          )}
        >
          <div className='flex items-start justify-between gap-2'>
            <ProductSummary
              showControls={false}
              showViewItem={true}
              totalCharges={0}
            />
          </div>
          <AnimatePresence>
            {showTotalCharges && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{
                  height: showTotalCharges ? "auto" : 0,
                  opacity: showTotalCharges ? 1 : 0,
                  marginTop: showTotalCharges ? 1 : 0,
                }}
                transition={{ duration: 0.3 }}
                exit={{ height: 0, opacity: 0 }}
                className='overflow-hidden'
              >
                <Charges />
              </motion.div>
            )}
          </AnimatePresence>
          <div className='flex w-full items-center justify-between gap-4'>
            <div>
              <div
                onClick={() => setShowTotalCharges((prev) => !prev)}
                className='flex cursor-pointer items-center gap-2 text-neutral-900'
              >
                <Typography as={"h4"} className='text-sh6 md:text-h4'>
                  Total Charges
                </Typography>

                <ChevronDownIcon
                  className={cn(
                    "h-4 w-4 transition-all",
                    showTotalCharges ? "rotate-0" : "rotate-180",
                  )}
                />
              </div>
              <p className='text-sm text-gray-800'>price incl. of all taxes</p>
            </div>

            <h2 className='text-lg font-bold text-primary-900'>
              {moneyFormatter(finalAmount())}
            </h2>
          </div>
        </div>

        <AnimatePresence>
          {showTotalCharges && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{
                height: showTotalCharges ? "auto" : 0,
                opacity: showTotalCharges ? 1 : 0,
                marginTop: showTotalCharges ? 1 : 0,
              }}
              transition={{ duration: 0.3 }}
              exit={{ height: 0, opacity: 0 }}
              className='overflow-hidden'
            ></motion.div>
          )}
        </AnimatePresence>
      </div>

      <div className='sticky bottom-0 bg-gray-100 p-4 shadow-sm'>
        <Button
          disabled={isPending}
          onClick={() => updateOrder()}
          variant={"primary"}
          className='w-full text-bt2 text-gray-100'
          size='lg'
        >
          {isPending && <Loader className='animate-spin' />}
          {isPending ? "Please Wait" : "Confirm Order Modification"}
        </Button>
      </div>
    </div>
  )
}

export default ConfirmCarePal
