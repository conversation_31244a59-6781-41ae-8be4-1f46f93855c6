import useCalculateRent from "@/hooks/use-calculate-rent"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { RentalItem, RentalItemVarient, SideViewProps } from "@/types"
import { Loader2 } from "lucide-react"
import { Dispatch, SetStateAction } from "react"
import { IconArrowRight } from "../Icons"
import { Button } from "../ui/button"
import { Label } from "../ui/label"
import { RadioGroup, RadioGroupItem } from "../ui/radio-group"
import { Typography } from "../ui/typography"
import SideView from "./modal-wrapper"

const CartAddedProduct = ({ product }: { product: RentalItem }) => {
  const { rent } = useCalculateRent({ type: "product", product })
  return (
    <div className='flex w-full items-center gap-5 overflow-hidden rounded-lg bg-neutral-50 p-2 md:min-w-[558px]'>
      <SpImage
        alt={product.ri_image_alt_text}
        src={product.ri_image.split(";")[0].trim()}
        width={70}
        height={70}
        className=''
      />
      <div className='flex w-full flex-col'>
        <h2 className='font-bold max-md:text-sm'>{product.ri_short_name}</h2>
        <p className='text-xs text-neutral-600 max-md:text-[10px]'>
          {product.ri_name}
        </p>
        <p className='font-bold'>₹{rent}</p>
      </div>
    </div>
  )
}

interface SelectSizeProps extends SideViewProps {
  sizes: RentalItemVarient[]
  setSelectedSize: Dispatch<SetStateAction<string>>
  selectedSize: string
  handleAddToCart: () => void
  product: RentalItem
  isCartAddtionLoading: boolean
}

const SelectColorAndSize = ({
  selectedSize,
  setSelectedSize,
  sizes,
  openSideView,
  setOpenSideView,
  handleAddToCart,
  product,
  isCartAddtionLoading,
}: SelectSizeProps) => (
  <SideView
    title='Select Size'
    className='h-full max-w-2xl'
    setOpenSideView={setOpenSideView}
    openSideView={openSideView}
  >
    <div className='h-full space-y-4 overflow-hidden p-4'>
      <CartAddedProduct product={product} />
      <div className='flex flex-col items-start justify-start gap-3 overflow-hidden rounded-lg bg-neutral-100 px-3 py-2'>
        <div className='flex w-full items-center justify-between'>
          <div className='flex gap-1'>
            <Typography
              as={"p"}
              className='text-sm font-semibold leading-none text-neutral-500'
            >
              Choose preferred
            </Typography>
            <div className='text-sm font-bold leading-none text-stone-950'>
              Size
            </div>
          </div>
          <div className='flex items-center overflow-hidden rounded-full opacity-0'>
            <div className='flex gap-1 px-1 py-px'>
              <div className='text-sm font-semibold leading-none text-blue-700'>
                Size Chart
              </div>
            </div>
            <div className='flex h-5 w-5 overflow-hidden'>
              <div className='h-5 w-5 py-1 pl-2 pr-1.5' />
            </div>
          </div>
        </div>
        <div className='flex max-w-2xl gap-3 overflow-x-scroll pb-2 scrollbar-none'>
          {sizes &&
            sizes.map((size, index) => (
              <button
                onClick={() => setSelectedSize(size.size)}
                key={index}
                className={cn(
                  "flex h-12 w-12 min-w-max cursor-pointer items-center justify-center rounded-xl border-2 border-transparent bg-neutral-150 px-3 text-center text-stone-950 transition-all",
                  selectedSize == size.size && "border-secondary-600",
                )}
              >
                <div className='text-sm font-bold leading-tight'>
                  {size.size}
                </div>
              </button>
            ))}
        </div>
      </div>
      <div className='flex flex-col items-start justify-center rounded-2xl bg-gray-100 p-2'>
        <div className='flex w-full flex-col overflow-hidden rounded-lg border border-neutral-250'>
          <div className='flex w-full bg-[#f3f5f7]'>
            {["Website Size", "Size in cm"].map((header, index) => (
              <div
                key={index}
                className='w-full border-r border-neutral-250 py-3 text-center font-inter font-bold leading-none text-[#101010] max-md:text-xs'
              >
                {header}
              </div>
            ))}
          </div>
          <RadioGroup
            className='gap-0'
            value={selectedSize}
            onValueChange={(value) => setSelectedSize(value)}
          >
            {sizes &&
              sizes.map((size, index) => (
                <div key={index} className='flex w-full'>
                  <div
                    className={cn(
                      "flex flex-1 items-center justify-between border-r border-t border-neutral-250",
                      selectedSize == size.size && "bg-primary-100",
                    )}
                  >
                    <div className='flex h-12 w-full cursor-pointer items-center border-r border-neutral-250 text-center font-inter font-medium leading-none text-[#101010] max-md:text-sm'>
                      <span className='cursor-pointer px-2'>
                        <RadioGroupItem
                          className='h-5 w-5'
                          value={size.size}
                          id={size.size}
                        />
                      </span>
                      <Label
                        className='flex h-full w-full cursor-pointer items-center justify-center text-center md:text-base'
                        htmlFor={size.size}
                      >
                        {size.size}
                      </Label>
                    </div>
                    <Label
                      htmlFor={size.size}
                      className='flex h-12 w-full cursor-pointer items-center justify-center text-center font-inter font-medium leading-none text-[#101010] max-md:text-sm md:text-base'
                    >
                      {size.size_in_cms}
                    </Label>
                  </div>
                </div>
              ))}
          </RadioGroup>
        </div>
      </div>
    </div>

    <div className='sticky bottom-0 inline-flex w-full flex-col items-center justify-start gap-4 self-end justify-self-end overflow-hidden bg-neutral-100 p-4 text-gray-50 md:p-6'>
      <Button
        onClick={handleAddToCart}
        disabled={isCartAddtionLoading}
        className='h-10 w-full bg-primary-500 hover:bg-primary-600 md:h-12'
      >
        {isCartAddtionLoading ? (
          <Loader2 className='h-5 w-5 animate-spin' />
        ) : (
          <>
            Add To Cart
            <span className='scale-[1.5]'>
              <IconArrowRight />
            </span>
          </>
        )}
      </Button>
    </div>
  </SideView>
)

export default SelectColorAndSize
