import { cn } from "@/lib/utils"
import { RentalItemVarient, SideViewProps } from "@/types"
import { Dispatch, SetStateAction } from "react"
import { Button } from "../ui/button"
import { Label } from "../ui/label"
import { RadioGroup, RadioGroupItem } from "../ui/radio-group"
import { Typography } from "../ui/typography"
import SideView from "./modal-wrapper"

interface SelectSizeProps extends SideViewProps {
  sizes: RentalItemVarient[]
  setSelectedSize: Dispatch<SetStateAction<string>>
  selectedSize: string
}

const SelectSize = ({
  openSideView,
  setOpenSideView,
  setSelectedSize,
  selectedSize,
  sizes,
}: SelectSizeProps) => (
  <SideView
    openSideView={openSideView}
    setOpenSideView={setOpenSideView}
    title='Select Size'
    className='h-full bg-neutral-150'
  >
    <div className='max-w-[606px] md:min-w-[606px]'>
      <div className='space-y-8 p-6'>
        <div className='flex w-full flex-col gap-5 text-center text-lg font-medium text-stone-950'>
          <iframe
            className='h-[200px] w-full rounded-xl md:h-[250px]'
            src='https://www.youtube.com/embed/w_m9lN5M8l4?si=YzlVzRaoD0vZmP7i'
            title='YouTube video player'
            allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share'
            referrerPolicy='strict-origin-when-cross-origin'
            allowFullScreen
          ></iframe>

          <div className='flex flex-col items-start justify-center rounded-2xl bg-gray-100 p-2'>
            <div className='flex w-full flex-col overflow-hidden rounded-lg border border-neutral-250'>
              <div className='flex w-full bg-[#f3f5f7]'>
                {["Website Size", "Size in cm"].map((header, index) => (
                  <Typography
                    as={"h1"}
                    key={index}
                    className='w-full border-r border-neutral-250 py-3 text-center font-inter font-bold leading-none text-[#101010] max-md:text-xs'
                  >
                    {header}
                  </Typography>
                ))}
              </div>
              <RadioGroup
                className='gap-0'
                value={selectedSize}
                onValueChange={(value) => setSelectedSize(value)}
              >
                {sizes &&
                  sizes.map((size, index) => (
                    <div key={index} className='flex w-full'>
                      <div
                        className={cn(
                          "flex flex-1 items-center justify-between border-r border-t border-neutral-250",
                          selectedSize == size.size && "bg-primary-100",
                        )}
                      >
                        <div
                          className={cn(
                            "flex h-12 w-full cursor-pointer items-center border-r border-neutral-250 text-center font-inter font-medium leading-none text-[#101010] max-md:text-sm",
                          )}
                        >
                          <Typography
                            as={"span"}
                            className='cursor-pointer px-2'
                          >
                            <RadioGroupItem
                              className='h-5 w-5'
                              value={size.size}
                              id={size.size}
                            />
                          </Typography>
                          <Label
                            className='flex h-full w-full cursor-pointer items-center justify-center text-center md:text-base'
                            htmlFor={size.size}
                          >
                            {size.size}
                          </Label>
                        </div>
                        <Label
                          htmlFor={size.size}
                          className='flex h-12 w-full cursor-pointer items-center justify-center text-center font-inter font-medium leading-none text-[#101010] max-md:text-sm md:text-base'
                        >
                          {size.size_in_cms}
                        </Label>
                      </div>
                    </div>
                  ))}
              </RadioGroup>
            </div>
          </div>
        </div>
        {/* removed for now */}
        {/* <div className="flex w-full flex-col hidd pl-6">
            <div className="text-lg font-semibold leading-none text-zinc-950">
              Size & Fit
            </div>
            <div className="mt-3 text-sm font-bold leading-5 text-gray-600">
              <ol className="list-decimal">
                <li> Fit: Slim Fit:</li>
                <ol>
                  <li>
                    <span className="font-medium">
                      {' '}
                      a:The model (height 6&apos;) is wearing a size 40
                    </span>
                  </li>
                </ol>
                <li> Material & Care:</li>
                <ol>
                  <li>
                    <span className="font-medium">a: 100% Cotton</span>
                  </li>
                  <li>
                    <span className="font-medium">b: Machine Wash</span>
                  </li>
                </ol>
              </ol>
            </div>
          </div> */}
      </div>
      <div className='sticky bottom-0 bg-gray-50 p-4 md:relative md:p-6'>
        <Button
          type='button'
          onClick={() => {
            setOpenSideView(false)
          }}
          className='w-full bg-primary-500 hover:bg-primary-600'
        >
          Select Size
        </Button>
      </div>
    </div>
  </SideView>
)

export default SelectSize
