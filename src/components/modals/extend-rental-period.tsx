import { useEffect, useState } from "react"

import { Rent<PERSON>rder, SideViewProps } from "@/types"

import { useThrottle } from "@/hooks/use-throttle"

import { getDateDifference } from "@/functions/date"
import { moneyFormatter } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import { OrderSummary } from "@/types/order"
import { formatDateWithOrdinal } from "@/utils/date-logics"
import { addDays, format, startOfDay } from "date-fns"
import {
  CalendarEndFilledIcon,
  CautionCircleOutlinedIcon,
  ChevronDownIcon,
  MinusOutlinedIcon,
  PlusAddOutlinedIcon,
} from "sharepal-icons"
import { ChargeItem } from "../checkout/order-summary-new/charge-item"
import { ProductSummary } from "../checkout/product-summary"
import { Button } from "../ui/button"
import { Typography } from "../ui/typography"
import { AdaptiveWrapper } from "./adaptive-wrapper"
import ExtendRentalDialog from "./extend-rental-dialog"
interface ExtendRentalPeriod extends SideViewProps {
  main_order_id: number
  order_id: string
  order: RentOrder
  orderSummary: OrderSummary
  handleExtensionPayment: (
    amount: number,
    days: number,
    new_pickup_date: Date,
  ) => void
}

import { isBefore, startOfToday } from "date-fns"
import { Input } from "../ui/input"

function getValidPickupDate(pickupDate: Date): Date {
  const today = startOfToday()
  return isBefore(startOfDay(pickupDate), today)
    ? today
    : startOfDay(pickupDate)
}

const ExtendRentalPeriod = ({
  openSideView,
  setOpenSideView,
  order_id,
  order,
  orderSummary,
  handleExtensionPayment,
}: ExtendRentalPeriod) => {
  // const queryClient = useQueryClient()

  const [openExtendRentalDialog, setOpenExtendRentalDialog] = useState(false)

  const [showTotalCharges, setShowTotalCharges] = useState(true)

  const validPickupDate = addDays(
    getValidPickupDate(orderSummary.deal_cf_pickup_date),
    1,
  )

  const [pickupDate, setPickupDate] = useState(validPickupDate)
  const minimumExtensionDays =
    getDateDifference(
      orderSummary.deal_cf_pickup_date,
      getValidPickupDate(validPickupDate),
    ) || 1
  const [extensionDays, setExtensionDays] = useState(
    getDateDifference(
      orderSummary.deal_cf_pickup_date,
      getValidPickupDate(validPickupDate),
    ) || 1,
  )
  const [totalExtensionAmount, setTotalExtensionAmount] = useState(
    orderSummary.deal_cf_total_per_day_rent * extensionDays,
  )

  const [disableButton, setDisableButton] = useState({
    add: extensionDays >= 30,
    minus: extensionDays == 1,
  })

  useEffect(() => {
    setTotalExtensionAmount(
      orderSummary.deal_cf_total_per_day_rent * extensionDays,
    )
    setPickupDate(
      addDays(startOfDay(orderSummary.deal_cf_pickup_date), extensionDays),
    )
    setDisableButton({
      add: extensionDays >= 30,
      minus: extensionDays == 1,
    })
    if (
      extensionDays <
      (getDateDifference(
        orderSummary.deal_cf_pickup_date,
        addDays(getValidPickupDate(orderSummary.deal_cf_pickup_date), 1),
      ) || 1)
    ) {
      setValidationError(true)
    } else {
      setValidationError(false)
    }
  }, [extensionDays])

  const throttleUpdate = useThrottle(handleExtensionPayment)
  const isTodayAfterPickupDate = isBefore(
    pickupDate,
    addDays(startOfToday(), 1),
  )

  const handleChangeDays = (type: "minus" | "add") => {
    if (isTodayAfterPickupDate && type == "minus") return
    const days = type == "add" ? extensionDays + 1 : extensionDays - 1
    setExtensionDays(days)
  }

  const [vaidationError, setValidationError] = useState(false)

  const handleChangeInputDays = (e: React.ChangeEvent<HTMLInputElement>) => {
    const numberOfDays = Number(e.target.value.replace(/\D/g, ""))
    if (numberOfDays > 30) return
    setExtensionDays(numberOfDays)
  }

  return (
    <AdaptiveWrapper
      open={openSideView}
      onOpenChange={setOpenSideView}
      desktop={{
        type: "sheet",
        side: "right",
      }}
      mobile={{
        type: "drawer",
      }}
      title='Extend Rental Period'
      titleClassName='bg-neutral-150'
      className={"h-full overflow-hidden bg-neutral-150 md:max-w-2xl"}
    >
      <div className='flex flex-col'>
        <div className='flex w-full flex-col items-start justify-start gap-4 p-4 md:px-6'>
          <div className='flex w-full items-start justify-between gap-4 rounded-xl bg-gray-100 p-4'>
            <div className='flex items-start justify-between gap-2'>
              <ProductSummary
                showViewItem={false}
                totalCharges={0}
                items_count_text='Items Ordered'
              />
            </div>
          </div>
          <div className='flex w-full flex-col gap-8 rounded-2xl bg-gray-100 p-4'>
            <div className='flex w-full flex-col gap-4'>
              <Typography as='p' className='text-center text-sh3 text-gray-600'>
                Extend Rental Period by:
              </Typography>
              <div className='flex flex-col items-center justify-start gap-2 md:h-14'>
                <div className='flex items-center justify-between gap-3'>
                  <button
                    onClick={() => handleChangeDays("minus")}
                    name='minus'
                    disabled={
                      disableButton.minus ||
                      isBefore(pickupDate, addDays(new Date(), 1))
                    }
                    className='flex items-center justify-center rounded-full border-2 border-primary-500 bg-gray-100 p-2 text-primary-500 disabled:border-neutral-500 disabled:text-neutral-500'
                  >
                    <MinusOutlinedIcon className='' />
                  </button>
                  <div
                    className={`flex h-[50px] !w-[150px] items-center justify-center rounded-md border-2 border-gray-400 p-2 ${vaidationError && "border-destructive-400"}`}
                  >
                    <Input
                      className={`{vaidationError && "border-destructive-500"} h-[50px] w-full max-w-[50px] rounded-none border-none p-0 text-center !text-h1`}
                      value={extensionDays}
                      pattern='\d*'
                      onChange={handleChangeInputDays}
                    />
                    <Typography
                      as='p'
                      className='mt-3 text-center text-sh3 text-gray-600'
                    >
                      Days
                    </Typography>
                  </div>
                  <button
                    onClick={() => handleChangeDays("add")}
                    name='add'
                    disabled={disableButton.add}
                    className='flex items-center justify-center rounded-full border-2 border-primary-500 bg-gray-100 p-2'
                  >
                    <PlusAddOutlinedIcon className='text-primary-500' />
                  </button>
                </div>
                {vaidationError && (
                  <Typography
                    as={"p"}
                    className='flex items-center gap-2 text-b6 text-destructive-600'
                  >
                    <CautionCircleOutlinedIcon className='' />
                    Minimum Extension Allowed: {minimumExtensionDays} Days
                  </Typography>
                )}
              </div>
            </div>
            <div className='rounded-lg bg-primary-100 py-3'>
              <div className='flex items-center justify-center gap-2'>
                <CalendarEndFilledIcon className='h-4 min-h-4 w-4 min-w-4 text-primary-700' />
                <Typography as={"p"} className='text-sh5 text-primary-700'>
                  Updated Pickup Date:
                </Typography>

                <Typography as={"p"} className='text-sh4 text-neutral-900'>
                  {formatDateWithOrdinal(pickupDate || new Date())}
                </Typography>
                <p className='text-primary-300'>•</p>
                <Typography as={"p"} className='text-sh4 text-neutral-900'>
                  {format(pickupDate || new Date(), "EEE")}
                </Typography>
              </div>
            </div>
          </div>
        </div>
        <div className='flex w-full flex-col items-start justify-start gap-3 p-4 md:px-6'>
          <Typography as={"h6"} className='self-stretch text-sh3 md:text-h7'>
            Enjoy the Benefits of Dynamic Rental Rates:
          </Typography>
          <div className='self-stretch text-neutral-500'>
            <Typography
              as={"span"}
              className='mr-1 text-sh6 font-bold md:text-sh4'
            >
              1.{"  "}
            </Typography>
            <Typography
              as={"span"}
              className='text-b6 text-neutral-300 md:text-b4'
            >
              Choose the number of days, check the new pickup date, and pay to
              extend.
            </Typography>
            <br />
            <Typography
              as={"span"}
              className='mr-1 text-sh6 font-bold md:text-sh4'
            >
              2. {"  "}
            </Typography>
            <Typography
              as={"span"}
              className='text-b6 text-neutral-300 md:text-b4'
            >
              Extension rates match your original booking rate shown from May 1,
              2025. For orders before this date, extension rates have been
              updated against your order.
            </Typography>
            <br />
            <Typography
              as={"span"}
              className='mr-1 text-sh6 font-bold md:text-sh4'
            >
              3. {"  "}
            </Typography>
            <Typography
              as={"span"}
              className='text-b6 text-neutral-300 md:text-b4'
            >
              If the pickup date has passed, charges apply from that date. For
              example, extending on May 22 for a May 20 pickup means you&apos;re
              charged from May 20.
            </Typography>
          </div>
        </div>
      </div>

      <div className='sticky bottom-0 w-full space-y-4 bg-gray-100 p-5 max-md:shadow-md'>
        <ChargeItem
          item={{
            label: "Total Extension Charges",
            amount: totalExtensionAmount,
            items_count: extensionDays || 0,
            items_count_text: `day${extensionDays && extensionDays > 1 ? "s" : ""} added`,
          }}
        />
        <div className='border-b py-2'></div>

        <div className='flex gap-5 max-md:flex-col'>
          <div className='flex w-full items-center justify-between gap-4'>
            <div>
              <div
                onClick={() => setShowTotalCharges((prev) => !prev)}
                className='flex cursor-pointer items-center gap-2 text-neutral-900'
              >
                <Typography as={"h4"} className='text-sh6 md:text-h4'>
                  Total Charges
                </Typography>

                <ChevronDownIcon
                  className={cn(
                    "h-4 w-4 transition-all",
                    showTotalCharges ? "rotate-0" : "rotate-180",
                  )}
                />
              </div>
              <p className='text-sm text-gray-800'>price incl. of all taxes</p>
            </div>

            <Typography
              as={"h1"}
              className='text-h1 font-bold text-primary-900'
            >
              {moneyFormatter(totalExtensionAmount)}
            </Typography>
          </div>
          <Button
            onClick={() => {
              setOpenExtendRentalDialog(true)
            }}
            disabled={
              extensionDays <= 0 ||
              extensionDays <
                (getDateDifference(
                  orderSummary.deal_cf_pickup_date,
                  addDays(
                    getValidPickupDate(orderSummary.deal_cf_pickup_date),
                    1,
                  ),
                ) || 1) ||
              vaidationError
            }
            className='h-12 items-center justify-center gap-5 rounded-full bg-primary-500 text-center font-inter text-base font-semibold leading-tight text-gray-100 hover:bg-primary-600'
          >
            <span>Extend Rental Period</span>
          </Button>
        </div>
      </div>
      {/* <RentalPeriodSelector /> */}

      <AdaptiveWrapper
        open={openExtendRentalDialog}
        desktop={{
          type: "dialog",
        }}
        tablet={{
          type: "dialog",
        }}
        title='Review & Confirm Order Extension'
        className='overflow-x-hidden md:max-h-[95%] md:p-0'
        onOpenChange={(value) => setOpenExtendRentalDialog(value)}
      >
        <ExtendRentalDialog
          updateOrder={throttleUpdate}
          extensionDays={extensionDays || 0}
          order_id={order_id}
          pickupDate={pickupDate}
          totalExtensionAmount={totalExtensionAmount}
          order={order}
          openExtendRentalDialog={openExtendRentalDialog}
        />
      </AdaptiveWrapper>
    </AdaptiveWrapper>
  )
}

export default ExtendRentalPeriod
