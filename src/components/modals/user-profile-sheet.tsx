"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>et<PERSON>it<PERSON> } from "@/components/ui/sheet-new"
import { AnimatePresence, motion } from "framer-motion"
import * as React from "react"

import { getOfferCoupon } from "@/actions/coupon"
import { moneyFormatter } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useUserStore } from "@/store/user-store"
import { useQuery } from "@tanstack/react-query"
import dynamic from "next/dynamic"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  BoxOutlinedIcon,
  ChevronRightIcon,
  HeartOutlinedIcon,
  HelpOutlinedIcon,
  SharPalLogoMarkOutlinedIcon,
  WalletOutlinedIcon,
} from "sharepal-icons"
import PartyPopperIcon from "../Icons/party-popper-icon"
import { Al<PERSON>, AlertDescription } from "../ui/alert"
import { AdaptiveWrapper } from "./adaptive-wrapper"

const UserProfileSkeleton = dynamic(
  () => import("@/components/skeletons/user-profile-skeleton"),
  {
    ssr: false,
  },
)

interface MenuItem {
  icon: React.ElementType
  label: string
  link?: string
  badge?: {
    text: string
    type: "destructive" | "success" | "neutral"
  }
}

interface UserProfileSheetProps {
  isOpen: boolean
  setIsProfileOpen: React.Dispatch<React.SetStateAction<boolean>>
}

export function UserProfileSheet({
  isOpen,
  setIsProfileOpen,
}: UserProfileSheetProps) {
  const { user, isLoggedIn, userLoading, wallet, logout } = useUserStore()
  const { openModal } = useOnboardingStore()
  const pathname = usePathname()

  React.useEffect(() => {
    setIsProfileOpen(false)
  }, [pathname, setIsProfileOpen])

  // const verified = user?.identity_kyc_received && user?.occupation_kyc_received

  const menuItems: Record<string, MenuItem[]> = {
    verified: [
      {
        icon: SharPalLogoMarkOutlinedIcon,
        label: "My Account",
        link: "/account",
      },
      {
        icon: BoxOutlinedIcon,
        label: "My Orders",
        link: "/orders",
        // badge: { text: "Action required", type: "destructive" },
      },
      { icon: HeartOutlinedIcon, label: "My Wishlist", link: "/wishlist" },
      {
        icon: WalletOutlinedIcon,
        label: "Pal Wallet",
        link: "/wallet",
        badge: {
          text: `${moneyFormatter(wallet?.amount || 0)}`,
          type: "neutral",
        },
      },
      // {
      // icon: PalCoinOutlinedIcon,
      //   label: 'Referral Program',
      //   link: '/referral',
      //   badge: { text: 'Get ₹250', type: 'success' },
      // },
      // { icon: HelpOutlinedIcon, label: "Help & Support" },
    ],
    // unverified: [{ icon: HelpOutlinedIcon, label: "Help & Support" }],
    // "logged-out": [
    //   {
    //     icon: HelpOutlinedIcon,
    //     label: "Help & Support",
    //     link: "/help-and-support",
    //   },
    // ],
  }

  const { data: offerCoupon } = useQuery({
    queryKey: ["offer_coupon"],
    queryFn: getOfferCoupon,
    staleTime: 1000 * 60 * 10, // 10 minutes
    enabled: isOpen,
  })

  // const businessItems = [
  //   {
  //     icon: Star,
  //     label: 'SharePal for Creators',
  //     description: 'Save more upto 80%',
  //   },
  //   {
  //     icon: Briefcase,
  //     label: 'SharePal for Business',
  //     description: 'Bulk orders + upto 28% GST Saving',
  //   },
  // ]

  if (userLoading) {
    return (
      <AdaptiveWrapper
        open={isOpen}
        onOpenChange={(open) => setIsProfileOpen(open)}
      >
        <UserProfileSkeleton />
      </AdaptiveWrapper>
    )
  }

  return (
    // <Sheet
    //   open={isOpen}
    //   onOpenChange={(open) => {
    //     setIsProfileOpen(open)
    //   }}
    // >
    //   <SheetContent className="w-full max-w-md bg-neutral-150 p-0 font-inter sm:max-w-lg md:rounded-bl-3xl md:rounded-tl-3xl">

    <AdaptiveWrapper
      open={isOpen}
      onOpenChange={(open) => {
        setIsProfileOpen(open)
      }}
    >
      <div className='flex h-full min-h-[600px] flex-col'>
        {/* Header */}

        <div className='bg-gray-100 p-4 pt-4 md:rounded-tl-3xl md:pl-7 md:pt-16 lg:pt-20'>
          <SheetHeader className='relative space-y-4 md:space-y-6'>
            <div className='flex items-center justify-between'>
              <SheetTitle className='font-ubuntu text-2xl font-bold text-neutral-900 md:text-4xl'>
                Hi, {isLoggedIn && user ? user?.first_name || "Pal" : "Pal"}!
              </SheetTitle>

              {isLoggedIn ? (
                <Button
                  onClick={logout}
                  variant='outline'
                  className='h-10 px-3 md:h-11 md:px-6'
                >
                  Log out
                  <ChevronRightIcon />
                </Button>
              ) : (
                <Button
                  onClick={() => openModal()}
                  variant='default'
                  className='h-10 px-3 md:h-11 md:px-6'
                >
                  Log In
                  <ChevronRightIcon />
                </Button>
              )}
            </div>

            <Alert className='border-none p-0'>
              <AlertDescription className='bg-gray-100 text-green-900'>
                <div className='flex h-24 items-center gap-4 overflow-hidden rounded-3xl bg-gradient-to-r from-[#EDF4FF] to-[#F7FFEA] p-3'>
                  <PartyPopperIcon />
                  <div className='flex flex-col gap-2 text-start'>
                    <div>
                      <span className='text-base font-bold text-pink-500'>
                        {offerCoupon?.coupon_description?.split("off")[0]}
                      </span>
                      <span className='text-base font-bold text-zinc-950'>
                        {offerCoupon?.coupon_description?.split("off")[1]}
                      </span>
                    </div>
                    <div className='text-start text-sm font-semibold text-zinc-700'>
                      Use Coupon - {offerCoupon?.coupon_code}
                    </div>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          </SheetHeader>
        </div>

        {/* Menu Items */}
        <div className='flex-1 space-y-4 overflow-y-auto bg-neutral-150 p-4'>
          <AnimatePresence mode='wait'>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className='space-y-2'
            >
              {/* {menuItems[isLoggedIn && user ? "verified" : "logged-out"].map( */}
              {menuItems[isLoggedIn && user ? "verified" : ""]?.map(
                (item, index) => (
                  <motion.button
                    key={item.label}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className='w-full'
                  >
                    <Link
                      className='flex w-full items-center justify-between rounded-xl border-none border-gray-150 bg-neutral-150 p-3 px-4 text-left transition-all hover:bg-neutral-50 active:bg-neutral-100'
                      key={item.label}
                      href={item.link ? `/dashboard${item.link}` : "#"}
                      prefetch={false}
                    >
                      <div className='flex items-center gap-3'>
                        <item.icon className='h-5 w-5 text-neutral-500' />
                        {/* <SharPalLogoMarkOutlinedIcon /> */}
                        <span className='text-sm font-semibold text-gray-900 md:text-base'>
                          {item.label}
                        </span>

                        <div className='flex items-center gap-2'>
                          {item.badge && (
                            <Badge
                              variant='outline'
                              className={cn(
                                "border-none",
                                item.badge.type === "destructive"
                                  ? "border-destructive-200 bg-destructive-100 text-destructive-500"
                                  : item.badge.type === "success"
                                    ? "border-success-300 bg-success-100 text-success-600"
                                    : "border-neutral-200 bg-neutral-100 text-neutral-600",
                              )}
                            >
                              {item.badge.text}
                            </Badge>
                          )}
                          {/* <ChevronRight className="h-4 w-4 text-neutral-400" /> */}
                        </div>
                      </div>

                      <ChevronRightIcon className='h-4 w-4 text-neutral-400' />
                    </Link>
                  </motion.button>
                ),
              )}
              {/* help and support */}
              <motion.button
                key={"help and support"}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
                className='w-full'
              >
                <Link
                  className='flex w-full items-center justify-between rounded-xl border-none border-gray-150 bg-neutral-150 p-3 px-4 text-left transition-all hover:bg-neutral-50 active:bg-neutral-100'
                  href='https://wa.me/917619220543'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  <div className='flex items-center gap-3'>
                    <HelpOutlinedIcon className='h-5 w-5 text-neutral-500' />

                    <span className='text-sm font-semibold text-gray-900 md:text-base'>
                      Help & Support
                    </span>
                  </div>

                  <ChevronRightIcon className='h-4 w-4 text-neutral-400' />
                </Link>
              </motion.button>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </AdaptiveWrapper>

    //   </SheetContent>
    // </Sheet>
  )
}
