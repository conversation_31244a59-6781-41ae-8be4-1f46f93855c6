"use client"

import type React from "react"
import { type Dispatch, type SetStateAction } from "react"
import { AdaptiveWrapper } from "./adaptive-wrapper"

type Props = {
  openSideView: boolean
  setOpenSideView: Dispatch<SetStateAction<boolean>> | (() => void)
  title: string
  className?: string
  children: React.ReactNode
  autoClose?: boolean
  titleClassName?: string
}

const SideView = ({
  openSideView,
  setOpenSideView,
  children,
  title,
  className,
  titleClassName,
  autoClose = false,
}: Props) => (
  <AdaptiveWrapper
    className={className}
    title={title}
    open={openSideView}
    onOpenChange={setOpenSideView}
    autoClose={autoClose}
    titleClassName={titleClassName}
  >
    {children}
  </AdaptiveWrapper>
  // <Sheet open={openSideView} onOpenChange={(open) => setOpenSideView(open)}>
  //   <SheetContent
  //     onInteractOutside={(event) => {
  //       if (autoClose) {
  //         event.preventDefault()
  //       }
  //     }}
  //     side={"custom"}
  //     className={cn(
  //       "h-full overflow-hidden rounded-tl-xl p-0 max-md:rounded-tr-xl md:min-w-max md:rounded-bl-3xl md:rounded-tl-3xl",
  //       className,
  //     )}
  //   >
  //     <SheetHeader className='sticky top-0 z-10 flex flex-row items-center justify-start gap-4 space-y-0 bg-gray-100 p-4 md:min-w-max md:p-6'>
  //       <div
  //         onClick={() => setOpenSideView(false)}
  //         className='cursor-pointer md:scale-[1.5]'
  //         aria-label='Close side view'
  //       >
  //         <IconClose />
  //       </div>
  //       <SheetTitle className='w-max font-bold md:text-xl'>{title}</SheetTitle>
  //     </SheetHeader>
  //     <div className='h-[calc(100%-3.8rem)] overflow-hidden overflow-y-auto md:h-[calc(100%-5rem)]'>

  //     </div>
  //   </SheetContent>
  // </Sheet>
)

export default SideView
