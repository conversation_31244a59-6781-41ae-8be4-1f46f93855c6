"use client"

import { motion } from "framer-motion"

import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"
import type { CheckoutSection } from "@/types/checkout"
import { CrossLOutlinedIcon } from "sharepal-icons"
import { Typography } from "../ui/typography"

interface SectionHeaderProps {
  number: number
  title: string
  section: CheckoutSection
  isCompleted: boolean
  canOpen: boolean
  children: React.ReactNode
  updateLabel?: string
}

export function SectionHeader({
  number,
  title,
  section,
  canOpen,
  children,

  updateLabel = "Update",
}: SectionHeaderProps) {
  const { active_section, setActiveSection } = useCheckoutStore()
  const isActive = active_section === section

  return (
    <motion.div
      whileTap={{ scale: canOpen ? 0.995 : 1 }}
      onClick={() => canOpen && setActiveSection(section)}
      className={cn(
        "flex w-full items-start justify-between gap-5 p-4 transition-all duration-200 md:px-5 md:py-8",
        !canOpen && "cursor-not-allowed opacity-50",
      )}
    >
      <div className='flex items-start justify-start gap-8'>
        <div className='flex min-w-[256px] items-center gap-4'>
          {/* <motion.div
            initial={false}
            animate={isCompleted ? 'completed' : 'default'}
            variants={{
              default: { scale: 1 },
              completed: { scale: [1, 1.2, 1], transition: { duration: 0.3 } },
            }}
            className={cn(
              'flex h-9 w-9 items-center justify-center rounded-full text-sm font-bold transition-colors duration-200',
              // isActive && 'bg-neutral-100 text-neutral-800',
            )}
          >

          </motion.div> */}
          <div className='text-left'>
            <Typography as={"span"} className='!text-neutral-900 md:!text-h2'>
              {" "}
              {number}. {title}
            </Typography>
            {/* {isCompleted && (
              <motion.p
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="text-sm text-primary-600"
              >
                Completed
              </motion.p>
            )} */}
          </div>
        </div>

        {/* childre in middle */}
        {!isActive && children}
      </div>

      {/* { (
        <p>
          {contact_details.first_name} {contact_details.last_name}
        </p>
      )} */}

      {canOpen && (
        <motion.div
          // animate={{ rotate: isActive ? 180 : 0 }}
          transition={{ duration: 0.2 }}
          className={cn("min-w-max cursor-pointer text-bt3 text-primary-500")}
        >
          {isActive ? (
            <Typography
              as={"p"}
              className='hidden items-center justify-center gap-1 !text-bt3'
            >
              Close
              <CrossLOutlinedIcon className='!h-5 !w-5' />
            </Typography>
          ) : (
            <Typography className='!text-bt3' as={"p"}>
              {updateLabel}
            </Typography>
          )}
          <span></span>
        </motion.div>
      )}
    </motion.div>
  )
}
