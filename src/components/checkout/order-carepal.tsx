// "use client"

// import { Button } from "@/components/ui/button"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
// import { Separator } from "@/components/ui/separator"
// import { RentOrder } from "@/types"
// import { useQuery } from "@tanstack/react-query"
// import { motion } from "framer-motion"

// import { fetchOrderWithoutCart } from "@/actions/orders"
// import Link from "next/link"
// import { useParams, useSearchParams } from "next/navigation"
// import React from "react"
// import { IconCancelled, IconSuccess } from "../Icons/checkout"
// import { Skeleton } from "../ui/skeleton"
// import { Typography } from "../ui/typography"

// const OrderProcessedInfo = ({
//   order,
//   transaction = true,
// }: {
//   order: RentOrder
//   transaction?: boolean
// }) => (
//   <Card className='rounded-lg px-3 py-2 sm:rounded-xl sm:px-4 sm:py-3 md:rounded-2xl md:px-6 md:py-5'>
//     <CardHeader className='flex flex-row items-center justify-between space-y-0 p-0 pb-4'>
//       <CardTitle className='text-base font-bold text-gray-900 sm:text-lg md:text-xl'>
//         Carepal Applied!
//       </CardTitle>
//       <span className='text-sm text-gray-500 sm:text-base md:text-lg'>
//         #{order.order_id}
//       </span>
//     </CardHeader>
//     <Separator />
//     <CardContent className='flex w-full flex-col items-center justify-start gap-4 p-0 pt-4 sm:gap-5'>
//       <div className='grid w-full grid-cols-1 items-start justify-start gap-4 sm:grid-cols-2'>
//         <div>
//           <h3 className='mb-2 text-sm font-semibold text-neutral-900 sm:text-base'>
//             Contact Details
//           </h3>
//           <div className='text-xs text-neutral-500 sm:text-sm'>
//             <p className=''>{order.first_name}</p>
//             <p className=''>{order.calling_number} (WhatsApp)</p>
//             <p className=''>{order.email}</p>
//           </div>
//         </div>
//         <div>
//           <h3 className='mb-2 text-sm font-semibold text-neutral-900 sm:text-base'>
//             Delivery Address
//           </h3>
//           <p className='text-wrap break-words text-xs text-neutral-500 sm:text-sm'>
//             {order.delivery_address}
//           </p>
//         </div>
//       </div>
//       {transaction && (
//         <div className='grid w-full grid-cols-1 items-start justify-start gap-4 sm:grid-cols-2'>
//           <div>
//             <h3 className='mb-2 text-sm font-semibold text-neutral-900 sm:text-base'>
//               Transaction Details
//             </h3>
//             <p className='flex items-center gap-1 text-xs text-gray-700 sm:text-sm'>
//               Transaction ID:- {order.rzp_order_id}
//             </p>
//           </div>
//         </div>
//       )}
//     </CardContent>
//   </Card>
// )

// const CustomWrapperCard = ({
//   icon,
//   title_1,
//   title_2,
//   title_3,
//   title_4,
//   children,
//   showNeedHelp = true,
// }: {
//   title_1: string
//   title_2: string
//   title_3: string
//   title_4: string
//   icon: React.ReactNode
//   showNeedHelp?: boolean
//   children: React.ReactNode
// }) => (
//   <div className='mx-auto flex max-w-2xl flex-col items-center justify-center gap-4 rounded-xl bg-gray-100 px-4 py-6 sm:gap-5 sm:rounded-2xl sm:px-12 sm:py-8 md:rounded-3xl md:px-16 md:py-10'>
//     <motion.div
//       initial={{ scale: 0 }}
//       animate={{ scale: 1 }}
//       transition={{ type: "spring", duration: 0.5 }}
//     >
//       {icon}
//     </motion.div>

//     <motion.div
//       initial={{ opacity: 0, y: 20 }}
//       animate={{ opacity: 1, y: 0 }}
//       transition={{ delay: 0.2 }}
//       className='w-full space-y-3 text-center md:space-y-4'
//     >
//       <Typography as='h3' className='!text-sh5 md:!text-sh3'>
//         {title_1}
//       </Typography>

//       <Typography as='h1' className='!text-h4 text-gray-900 md:!text-h1'>
//         {title_2}
//       </Typography>

//       <Typography as='p' className='w-full !text-b6 text-gray-600 md:!text-b4'>
//         {title_3}
//       </Typography>

//       <Typography as='p' className='!text-b4 text-gray-700 md:!text-b2'>
//         {title_4}
//       </Typography>
//     </motion.div>

//     {children}

//     {showNeedHelp && (
//       <motion.a
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//         transition={{ delay: 0.6 }}
//         href='#'
//         className='mt-8 text-bt2 text-blue-600 hover:underline'
//       >
//         Need help? Contact us
//       </motion.a>
//     )}
//   </div>
// )

// const CarepalPaymentSuccess = ({ order }: CarepalStatusType) => (
//   <CustomWrapperCard
//     title_1={`Thank you, ${order.first_name}!`}
//     title_2={"Carepal Applied!"}
//     title_3={`Order No: #${order.order_id}`}
//     title_4={`Carepal has been succesfully applied`}
//     icon={<IconSuccess className='h-16 w-16 md:h-24 md:w-24' />}
//   >
//     <Button
//       variant={"primary"}
//       size={"lg"}
//       // className='w-full'
//       onClick={() => console.log("View order details")}
//     >
//       <Link href={`/dashboard/orders/${order?.order_id}`}>
//         Go to Order Details
//       </Link>
//     </Button>

//     <OrderProcessedInfo order={order} />
//   </CustomWrapperCard>
// )

// const CarepalPaymentFailed = ({ order }: CarepalStatusType) => (
//   <CustomWrapperCard
//     title_1='Payment didn’t go through, but don’t worry!'
//     title_2={"Apply Carepal!"}
//     title_3={` Order No: #${order.order_id}`}
//     title_4={`There was a transaction failure`}
//     icon={<IconCancelled className='h-16 w-16 md:h-24 md:w-24' />}
//     showNeedHelp={false}
//   >
//     <Typography
//       as='p'
//       className='text-center !text-b4 text-gray-700 md:!text-b2'
//     >
//       Any deducted amount will be automatically refunded to your payment source
//       within 3 to 7 days.
//     </Typography>
//     <Typography
//       as='p'
//       className='text-center !text-b4 text-gray-700 md:!text-b2'
//     >
//       Try again with a different payment method or opt to pay later.
//     </Typography>
//     <motion.div
//       initial={{ opacity: 0, y: 20 }}
//       animate={{ opacity: 1, y: 0 }}
//       transition={{ delay: 0.3 }}
//       className='mx-auto flex w-full max-w-sm flex-col items-center justify-center gap-3 sm:gap-4'
//     >
//       <Button variant={"primary"} size={"lg"} className='w-full'>
//         <Link href={``}>Retry Payment</Link>
//       </Button>
//       {/* <Button
//         variant={"normal"}
//         className='flex h-max w-full flex-col rounded-full bg-violet-200 text-center hover:bg-violet-300'
//       >
//         <Link href={``}>Pay Later</Link>
//       </Button> */}
//     </motion.div>
//   </CustomWrapperCard>
// )

// export type CarepalStatusType = {
//   order: RentOrder
// }

// export type CarepalStatus = "payment_failed" | "payment_success"

// const componentMap = {
//   payment_failed: CarepalPaymentFailed,
//   payment_confirmed: CarepalPaymentSuccess,
// } as const

// export default function OrderCarepal() {
//   const { order_id } = useParams<{ order_id: string }>()
//   const searchParams = useSearchParams()
//   const status = searchParams.get("status") as keyof typeof componentMap | null

//   const {
//     data: orderData,
//     isPending,
//     isFetching,
//     isLoading,
//   } = useQuery({
//     queryKey: ["orderData", order_id],
//     queryFn: () => fetchOrderWithoutCart(order_id),
//     refetchOnWindowFocus: false,
//   })

//   if (isPending || isFetching || isLoading) {
//     return <Skeleton className='h-96 w-full rounded-3xl' />
//   }

//   if (!orderData || !status || !componentMap[status]) return null

//   const Component = componentMap[status]

//   return <Component order={orderData.order} />
// }
