"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { motion } from "framer-motion"
import { useForm } from "react-hook-form"

import { SectionCard } from "@/components/checkout/section-card"
import { CustomFormField } from "@/components/custom/form-field"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  contactDetailsSchema,
  type ContactDetailsFormData,
} from "@/lib/validations/checkout"

import { updateUser } from "@/actions/user"
import { trackContactDetailSubmitted } from "@/lib/gtag-event"
import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"
import { useUserStore } from "@/store/user-store"
import { useMutation } from "@tanstack/react-query"
import { InfoIcon, Loader2 } from "lucide-react"
import { useEffect } from "react"
import { toast } from "sonner"
import { PhoneInput } from "../custom/phone-input"

const formVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
}

const inputVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.2,
    },
  },
}

export function ContactDetailsForm() {
  const {
    setActiveSection,
    setContactDetails,
    setStepCompleted,
    cart_items,
    finalAmount,
    total_deposit,
    total_discount,
    applied_coupon_code,
    delivery_charges,
    contact_details,
  } = useCheckoutStore()

  const { user, setUser } = useUserStore()

  const form = useForm<ContactDetailsFormData>({
    resolver: zodResolver(contactDetailsSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      country_code_calling: 91,
      calling_number: "",
      country_code_whatsapp: 91,
      whatsapp_number: "",
      isWhatsappSame:
        contact_details?.calling_number === contact_details?.whatsapp_number
          ? true
          : false,
      ...contact_details,
    },
  })

  useEffect(() => {
    form.setValue("first_name", contact_details?.first_name ?? "")
    form.setValue("last_name", contact_details?.last_name ?? "")
    form.setValue("email", contact_details?.email ?? "")
    form.setValue(
      "country_code_calling",
      contact_details?.country_code_calling ?? 91,
    )
    form.setValue("calling_number", contact_details?.calling_number ?? "")
    form.setValue(
      "country_code_whatsapp",
      contact_details?.country_code_whatsapp ?? 91,
    )
    form.setValue("whatsapp_number", contact_details?.whatsapp_number ?? "")
  }, [contact_details, form])

  const { mutate: onSubmit, isPending: isSubmitting } = useMutation({
    mutationFn: async (data: ContactDetailsFormData) => {
      setContactDetails({
        ...data,
      })

      //check if calling number is same as user calling number
      if (data.calling_number !== user?.calling_number) {
        toast.error("You can not update your calling number")
        return
      }

      //check erros of form

      if (user) {
        trackContactDetailSubmitted(
          user.id,
          cart_items,
          data?.whatsapp_number,
          finalAmount(),
          total_deposit,
          total_discount,
          applied_coupon_code,
          delivery_charges.final,
        )
        const userUpdateResponse = await updateUser({
          ...user,
          ...data,
        })

        setStepCompleted("contact", true)
        setActiveSection("delivery")
        return userUpdateResponse
      }
    },
    onSuccess: (data) => {
      if (data) setUser(data)
    },
  })

  const isWhatsappSame = form.watch("isWhatsappSame")

  return (
    <SectionCard>
      <motion.div
        variants={formVariants}
        initial='hidden'
        animate='visible'
        className='w-full'
      >
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit((data) => onSubmit(data))}
            className='flex flex-col gap-4'
          >
            <div className='grid gap-4 md:grid-cols-2'>
              <div
                className={cn(
                  `grid items-end gap-2 md:grid-cols-2`,
                  // form.formState.errors.first_name && 'items-center',
                  // form.formState.errors.last_name && 'items-start',
                )}
              >
                <CustomFormField
                  form={form}
                  name='first_name'
                  label='Your Name'
                  placeholder='First name'
                  required
                />
                <CustomFormField
                  form={form}
                  name='last_name'
                  label=''
                  placeholder='Last name'
                />
              </div>
              <CustomFormField
                form={form}
                name='email'
                required
                label='Email Address'
                placeholder='<EMAIL>'
                type='email'
              />
              <PhoneInput
                form={form}
                phoneFieldName='calling_number'
                countryCodeFieldName='country_code_calling'
                placeholder='Enter Phone Number'
                label='Phone Number'
                required
                readOnly={true}
              />

              <FormField
                control={form.control}
                name='isWhatsappSame'
                render={({ field }) => (
                  <FormItem className='space-y-3'>
                    <FormLabel className='!text-b4'>
                      Is this your WhatsApp Number?
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) => {
                          // console.log(value)
                          field.onChange(value === "yes")
                          if (value === "yes") {
                            form.setValue(
                              "whatsapp_number",
                              form.getValues("calling_number"),
                            )
                          } else {
                            form.setValue("whatsapp_number", "")
                          }
                        }}
                        defaultValue={field.value ? "yes" : "no"}
                        className='grid-cols-2'
                      >
                        {[
                          { id: "yes-radio", value: "yes", label: "Yes" },
                          { id: "no-radio", value: "no", label: "No" },
                        ].map((item) => (
                          <div
                            key={item.id}
                            // className="relative flex flex-col gap-4 rounded-2xl border-2 border-input p-4 has-[[data-state=checked]]:border-ring"
                            className='relative flex flex-col gap-4 rounded-2xl border-2 border-input border-neutral-150 bg-neutral-150 p-4 has-[[data-state=checked]]:border-secondary-500 has-[[data-state=checked]]:bg-gray-100'
                          >
                            <div className='flex justify-between gap-2'>
                              <RadioGroupItem
                                id={item.id}
                                value={item.value}
                                className='order-1 after:absolute after:inset-0'
                                // className="after:absolute after:inset-0"
                              />
                              <Label htmlFor={item.id}>{item.label}</Label>
                            </div>
                          </div>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {!isWhatsappSame && (
                <motion.div
                  variants={inputVariants}
                  initial='hidden'
                  animate='visible'
                  className='col-span-full grid w-full items-end gap-4 md:grid-cols-2'
                >
                  <PhoneInput
                    form={form}
                    placeholder='Enter Whatsapp Number'
                    phoneFieldName='whatsapp_number'
                    countryCodeFieldName='country_code_whatsapp'
                    label='WhatsApp Number'
                    required
                  />
                  <WhatsappRequiredNote />
                </motion.div>
              )}
            </div>
            {isWhatsappSame && <WhatsappRequiredNote />}

            <div className='fixed bottom-0 left-0 right-0 z-50 bg-gray-100 p-4 md:static md:flex md:justify-start md:gap-4 md:space-y-4 md:bg-transparent md:p-0 md:pb-4 md:pt-0'>
              <Button
                type='submit'
                variant={"primary"}
                size='lg'
                disabled={isSubmitting}
                className='w-full md:h-11 md:w-auto'
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Saving...
                  </>
                ) : (
                  "Save Details"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </motion.div>
    </SectionCard>
  )
}

const WhatsappRequiredNote = () => (
  <div className='flex min-h-14 items-center justify-start gap-1 rounded-2xl bg-secondary-100 px-2 py-1.5'>
    <InfoIcon
      size={18}
      className='inline-block align-middle font-bold text-primary-500'
    />
    <div className=''>
      <FormLabel className='text-xs font-bold text-primary-500'>
        WhatsApp number required for order details
      </FormLabel>
      <p className='text-xs text-neutral-500'>
        All order notifications are sent to you over WhatsApp.
      </p>
    </div>
  </div>
)
