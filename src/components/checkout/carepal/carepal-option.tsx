import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { RadioGroupItem } from "@/components/ui/radio-group"
import { Typography } from "@/components/ui/typography"
import React from "react"

interface CarepalOptionProps {
  id: string
  value: string
  label: string
  price: string
  sublabel?: string
  description?: string
  icon?: React.ReactNode
  badge?: string
}

export function CarepalOption({
  id,
  value,
  label,
  price,
  sublabel,
  description,
  icon,
  badge,
}: CarepalOptionProps) {
  return (
    <div className='relative flex min-h-[50px] w-full items-center gap-2 rounded-2xl border-2 border-neutral-150 bg-neutral-150 px-4 py-2 has-[[data-state=checked]]:border-primary-500 has-[[data-state=checked]]:bg-transparent'>
      <RadioGroupItem
        id={id}
        value={value}
        aria-describedby={`${id}-description`}
        className='after:absolute after:inset-0'
      />
      <div className='flex grow flex-wrap items-center gap-1 md:gap-3'>
        {/* {icon && <div className='shrink-0'>{icon}</div>} */}
        <div className='grid grow gap-2'>
          <Label
            htmlFor={id}
            className='flex w-max items-center justify-center gap-1'
          >
            <div className='flex flex-row items-center justify-center gap-1'>
              <Typography
                as={"span"}
                className='inline-flex !text-sh4 text-gray-900'
              >
                {label}
              </Typography>{" "}
              {badge && (
                <Badge variant={"primary"} className='hidden !text-b6 md:block'>
                  {" "}
                  {badge}
                </Badge>
              )}
              {icon && <div className='shrink-0'>{icon}</div>}
            </div>

            {sublabel && (
              <Typography
                as={"span"}
                className='inline-flex !text-b4 !text-gray-600'
              >
                ({sublabel})
              </Typography>
            )}
          </Label>
          {description && (
            <Typography
              as={"p"}
              id={`${id}-description`}
              className='text-sh4 text-gray-900'
            >
              {description}
            </Typography>
          )}
        </div>
        <div className='text-right'>
          <Typography as='p' className='text-bt3 font-bold text-neutral-900'>
            {price}
          </Typography>
        </div>
      </div>
    </div>
  )
}
