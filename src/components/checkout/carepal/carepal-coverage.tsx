"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import { CheckIcon, InfoIcon, XIcon } from "lucide-react"
import Image from "next/image"

// Coverage Item Component
interface CoverageItemProps {
  icon: React.ReactNode
  text: string
  type: "covered" | "not-covered"
}

const CoverageItem = ({ icon, text, type }: CoverageItemProps) => (
  <div className='flex items-start gap-2'>
    <div
      className={cn(
        "flex h-4 w-4 items-center justify-center rounded-full p-0.5",
        type === "covered"
          ? "border border-carepal-dark bg-carepal-light/10"
          : "border border-carepal-dark bg-carepal-light/10",
      )}
    >
      {icon}
    </div>
    <Typography as='span' className='text-b6 text-neutral-700'>
      {text}
    </Typography>
  </div>
)

// Coverage Section Component
interface CoverageSectionProps {
  onOpen: () => void
}

const CoverageSection = ({ onOpen }: CoverageSectionProps) => (
  <div className='flex flex-col gap-3 rounded-xl border border-carepal-lighter bg-gradient-to-b from-white to-[rgba(255,217,230,0.36)] p-4 md:flex-row md:gap-6 md:p-5'>
    <div className='flex w-full items-start justify-between gap-4 md:w-max'>
      {/* CarePal SECURE Badge */}
      {/* <CarepalSecureBadge /> */}
      <Image
        src='https://images.sharepal.in/carepal/carepal-secure.svg'
        width={350}
        height={100}
        alt='CarePal Assure Logo'
        className='h-auto w-[122px] md:h-auto md:w-[200px]'
      />
      {/* info icon */}
      <Button
        size={"icon"}
        variant={"ghost"}
        className='block md:hidden'
        onClick={onOpen}
      >
        <InfoIcon className='size-3' />
      </Button>
    </div>
    <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
      {/* What is Covered */}
      <div>
        <Typography
          as='h4'
          className='mb-3 text-b4 font-semibold text-neutral-900 md:mb-4 md:text-sh5'
        >
          What is Covered?
        </Typography>
        <div className='space-y-2 md:space-y-3'>
          <CoverageItem
            icon={<CheckIcon className='size-3 text-carepal-dark' />}
            text='Damage costs up to ₹25,000.'
            type='covered'
          />
          <CoverageItem
            icon={<CheckIcon className='size-3 text-carepal-dark' />}
            text='Unforeseen physical damages to goods rented.'
            type='covered'
          />
        </div>
      </div>

      {/* What is NOT Covered */}
      <div>
        <Typography
          as='h4'
          className='mb-3 text-b4 font-semibold text-neutral-900 md:mb-4 md:text-sh5'
        >
          What is NOT Covered?
        </Typography>
        <div className='space-y-2 md:space-y-3'>
          <CoverageItem
            icon={<XIcon className='size-3 text-carepal-dark' />}
            text='Missing items, burglary, theft.'
            type='not-covered'
          />
          <CoverageItem
            icon={<XIcon className='size-3 text-carepal-dark' />}
            text='Damages due to Improper Usage'
            type='not-covered'
          />
        </div>
      </div>
    </div>
  </div>
)
export default CoverageSection
