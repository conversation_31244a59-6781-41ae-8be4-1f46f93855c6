import { FAQS } from "@/components/custom/faq"
import { AdaptiveWrapper } from "@/components/modals/adaptive-wrapper"
import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"
import { FaqType } from "@/types"
import { CheckIcon, XIcon } from "lucide-react"
import Image from "next/image"
import React from "react"
import { InfoCircleOutlinedIcon } from "sharepal-icons"

interface CarepalInformationProps {
  isOpen: boolean
  onOpenChange: (isOpen: boolean) => void
  faqs: FaqType[] | []
  checkoutButtons?: boolean
  children?: React.ReactNode
  showActionButtons?: boolean
}

const CarepalInformation = ({
  isOpen,
  onOpenChange,
  faqs,
  showActionButtons = false,

  children,
}: CarepalInformationProps) => {
  const { total_goods_value, carepal_fee, carepal_coverage } =
    useCheckoutStore()

  return (
    <AdaptiveWrapper
      title={"CarePal Information"}
      open={isOpen}
      onOpenChange={onOpenChange}
      className='max-h-screen'
    >
      <div className='p-4 md:p-6'>
        <div className='h-max w-full'>
          <Typography as='h3' className='text-sh4 text-neutral-900 md:text-h6'>
            What is CarePal?
          </Typography>
          {/* Image  */}
          <div className='relative h-max w-full overflow-hidden rounded-xl bg-gray-100 py-4 md:mt-3 md:p-4'>
            <Image
              src='https://images.sharepal.in/carepal/why-choose-mobile.webp'
              width={900}
              height={1900}
              alt='CarePal Assure Logo'
              className='block h-auto w-full md:hidden'
            />
            <Image
              src='https://images.sharepal.in/carepal/why-choose-desktop.webp'
              width={1900}
              height={900}
              alt='CarePal Assure Logo'
              className='hidden h-auto w-full md:block'
            />
          </div>
        </div>

        {/* terms and conditions */}
        <div className='h-max w-full md:mt-5'>
          <Typography as='h3' className='text-sh4 text-neutral-900 md:text-h6'>
            Terms & Conditions
          </Typography>
          {/* box with whats conver and whats not converted */}
          <div className='mt-4 flex flex-col gap-4 rounded-xl border border-carepal-lighter bg-gradient-to-b from-white to-[rgba(255,217,230,0.36)] p-5 md:flex-row md:gap-6'>
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
              {/* What is Covered */}
              <div>
                <Typography
                  as='h4'
                  className='mb-3 text-sh7 font-semibold text-neutral-900 md:mb-4 md:text-sh5'
                >
                  What is Covered?
                </Typography>
                <div className='space-y-2 md:space-y-3'>
                  <CoverageItem
                    icon={<CheckIcon className='size-3 text-carepal-dark' />}
                    text='Accidental damages.'
                    type='covered'
                  />
                  <CoverageItem
                    icon={<CheckIcon className='size-3 text-carepal-dark' />}
                    text='Damage costs up to ₹25,000.'
                    type='covered'
                  />
                </div>
              </div>

              {/* What is NOT Covered */}
              <div>
                <Typography
                  as='h4'
                  className='mb-4 text-sh7 font-semibold text-neutral-900 md:mb-6 md:text-sh5'
                >
                  What is NOT Covered?
                </Typography>
                <div className='space-y-2 md:space-y-3'>
                  <CoverageItem
                    icon={<XIcon className='size-3 text-carepal-dark' />}
                    text='Damages due to improper usage.'
                    type='not-covered'
                  />
                  <CoverageItem
                    icon={<XIcon className='size-3 text-carepal-dark' />}
                    text='Theft, burglary or lost items'
                    type='not-covered'
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Info Section */}
          <div className='mt-3 flex w-full flex-wrap items-center justify-between gap-0 md:gap-2'>
            <div className='flex items-center justify-center gap-1 text-neutral-400 md:gap-2'>
              <InfoCircleOutlinedIcon className='size-3 md:size-4' />
              <Typography as='p' className='text-b6'>
                The waiver premium is non-refundable.
              </Typography>
            </div>
            {/* <Button size={"sm"} asChild variant='link'>
              <Link href='/carepal' target='_blank'>
                Read Policy
                <span className='hidden md:inline'>Wording</span>
              </Link>
            </Button> */}
          </div>
        </div>

        {/* faqs */}
        <div className='mt-4 h-max min-h-[300px] w-full md:mt-6'>
          <Typography as='h3' className='text-sh4 text-neutral-900 md:text-h6'>
            Frequently Asked Questions (FAQs)
          </Typography>
          <div className='w-ful mt-4 rounded-3xl bg-gray-100'>
            <FAQS faqs={faqs} />
          </div>
        </div>
      </div>
      {/* bottom buttons */}
      {showActionButtons && (
        <div className='sticky bottom-0 z-50 flex w-full flex-wrap justify-end gap-4 bg-gray-100 p-4 md:flex md:justify-start md:gap-4'>
          {/* info */}
          <div className='w-full rounded-3xl bg-secondary-150 px-4 py-3'>
            <Typography as='p' className='text-b6 text-success-700 md:text-b4'>
              With CarePal, since you are renting goods worth{" "}
              <span>
                <Typography as='span' className='font-bold text-success-700'>
                  {moneyFormatter(total_goods_value)}
                </Typography>
              </span>{" "}
              you are eligible for a damage coverage of{" "}
              <span>
                <Typography as='span' className='font-bold text-success-700'>
                  {moneyFormatter(carepal_coverage)}
                </Typography>
              </span>{" "}
              at just{" "}
              <span>
                <Typography as='span' className='font-bold text-success-700'>
                  {moneyFormatter(carepal_fee)}
                </Typography>
              </span>
            </Typography>
          </div>

          {children}

          {/* {checkoutButtons ? (
            <div className='flex w-full flex-col items-center justify-center gap-2 md:flex-row'>
              <Button
                size={"lg"}
                className='w-full flex-1 rounded-full'
                variant={"primary"}
                onClick={() => handleCarepalChange("yes")}
              >
                Get CarePal at {moneyFormatter(carepal_fee)}
              </Button>
              <Button
                size={"lg"}
                className='w-full flex-1 rounded-full'
                variant={"outline-primary"}
                onClick={() => handleCarepalChange("no")}
              >
                Take the Risk
              </Button>
            </div>
          ) : (
            <div className='flex w-full flex-col items-center justify-center gap-2 md:flex-row md:gap-6'>
              <div className='mr-4 w-max'>
                <div className='flex cursor-pointer items-center gap-2 text-neutral-900'>
                  <Typography as={"p"} className='text-sh7 md:text-sh4'>
                    Total Charges
                  </Typography>
                </div>
                <p className='text-h4 font-bold text-gray-900'>
                  {moneyFormatter(carepal_fee)}
                </p>
              </div>
              <Button
                size={"lg"}
                className='w-full flex-1 rounded-full'
                variant={"primary"}
                onClick={() => handleCarepalChange("yes")}
              >
                Get CarePal Assure
              </Button>
            </div>
          )} */}
        </div>
      )}
    </AdaptiveWrapper>
  )
}

interface CoverageItemProps {
  icon: React.ReactNode
  text: string
  type: "covered" | "not-covered"
}

const CoverageItem = ({ icon, text, type }: CoverageItemProps) => (
  <div className='flex items-start gap-2'>
    <div
      className={cn(
        "flex h-4 w-4 items-center justify-center rounded-full p-0.5",
        type === "covered"
          ? "border border-carepal-dark bg-carepal-light/10"
          : "border border-carepal-dark bg-carepal-light/10",
      )}
    >
      {icon}
    </div>
    <Typography as='span' className='text-b6 text-neutral-700'>
      {text}
    </Typography>
  </div>
)

export default CarepalInformation
