"use client"

import { fetchDiscountCoupons } from "@/actions/checkout"
import { Skeleton } from "@/components/ui/skeleton"
import { check_coupon_validity } from "@/services/coupon"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { Coupon } from "@/types/coupon"
import { useQuery } from "@tanstack/react-query"
import React, { useMemo } from "react"
// import { toast } from 'sonner'
import { useThrottle } from "@/hooks/use-throttle"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { CouponCard } from "../cards/coupon-card"
// import { addDays } from 'date-fns'

type Props = {
  open: boolean
  onOpenChange: React.Dispatch<React.SetStateAction<boolean>>
  onApplyCoupon: (
    code: string,
    total_rental_amount?: number,
    removeCoupon?: true,
  ) => unknown
  inCart: boolean
}

const ApplyCoupon: React.FC<Props> = ({
  open,
  onOpenChange,
  onApplyCoupon,
  inCart,
}) => {
  const { total_rent } = useCheckoutStore()
  const { delivery_date } = useRentalStore()

  const { data: coupons = [], isLoading } = useQuery<Coupon[], Error>({
    queryKey: ["coupons"],
    queryFn: fetchDiscountCoupons,
    enabled: open,
    staleTime: 5 * 60 * 1000,
  })

  const validCoupons = useMemo(() => {
    if (!delivery_date || !total_rent)
      return coupons.map((coupon) => ({ ...coupon, isValid: false }))
    return coupons.map((coupon) => ({
      ...coupon,
      isValid: check_coupon_validity({
        delivery_date,
        total_rent,
        ...coupon,
      }),
    }))
  }, [coupons, delivery_date, total_rent])

  // already implemented in use-coupon.ts
  // const handleApplyCoupon = useCallback(
  //   async (couponCode: string) => {
  //     try {
  //       if (!delivery_date) {
  //         toast.error('Please select a delivery date')
  //         return
  //       }
  //       const verified = await verifyCoupon(
  //         couponCode,
  //         addDays(delivery_date, 1),
  //       )
  //       if (!verified) {
  //         toast.error('This coupon is not valid')
  //         return
  //       }

  //       const couponIsValid = check_coupon_validity({
  //         delivery_date,
  //         total_rent,
  //         ...verified,
  //       })

  //       if (!couponIsValid) {
  //         toast.error('This coupon is not applicable to your current order')
  //         return
  //       }

  //       await onApplyCoupon(couponCode)
  //       onOpenChange(false)
  //       // toast.success('Coupon applied successfully')
  //     } catch (error) {
  //       console.error('Error applying coupon:', error)
  //       toast.error(
  //         error instanceof Error ? error.message : 'Failed to apply coupon',
  //       )
  //     }
  //   },
  //   [onApplyCoupon, onOpenChange, delivery_date, total_rent],
  // )

  const handleApplyCoupon = async (couponCode: string) => {
    await onApplyCoupon(couponCode, undefined, true)
    onOpenChange(false)
  }

  const throttleHanldeApplyCoupon = useThrottle(handleApplyCoupon, 1000)

  const { applied_coupon_code } = useCheckoutStore()

  return (
    <motion.div
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: open ? "auto" : 0, opacity: open ? 1 : 0 }}
      transition={{ duration: 0.3 }}
      className=''
    >
      <div
        className={cn(
          "hide-scrollbar z-0 flex w-full items-center justify-start gap-4 overflow-x-auto",
          !open && "pointer-events-none",
          inCart && "!p-0",
        )}
      >
        {isLoading ? (
          <div>
            <Skeleton />
          </div>
        ) : (
          validCoupons.map((coupon, index) => (
            <CouponCard
              className={cn("min-w-72 max-w-72")}
              key={index}
              coupon={coupon}
              onApply={() => throttleHanldeApplyCoupon(coupon.coupon_code)}
              isValid={coupon.isValid}
              showCheckbox
              isSelected={applied_coupon_code === coupon.coupon_code}
            />
          ))
        )}
      </div>
    </motion.div>
  )
}

export default ApplyCoupon
