import { Button } from "../ui/button"

import { useCalendarStore, useRentalStore } from "@/store/rental-store"
import { formatShortDate } from "@/utils/date-logics"
import { useEffect } from "react"
import { CalendarEditFilledIcon, CalendarStartFilledIcon } from "sharepal-icons"
import { Typography } from "../ui/typography"

export const CheckoutDateSelectDesktop = ({
  handleRemoveCoupon,
}: {
  handleRemoveCoupon: () => void
}) => {
  const { delivery_date, pickup_date } = useRentalStore()
  const { openCalendar } = useCalendarStore()

  useEffect(() => {
    handleRemoveCoupon()
  }, [delivery_date, pickup_date])
  return (
    <div
      onClick={() => {
        openCalendar()
      }}
      aria-label='Edit Dates'
      role='button'
      className='flex w-full items-center justify-between gap-2 rounded-full bg-gray-100 ring-2 ring-gray-200'
    >
      <div className='flex items-center justify-center gap-2 pl-4'>
        <CalendarStartFilledIcon className='!h-5 !w-5' />
        {delivery_date && pickup_date ? (
          <div>
            <Typography as={"span"} className='!text-b4 !text-neutral-400'>
              Rent for:{" "}
            </Typography>
            <Typography as={"span"} className='!text-sh5 text-neutral-700'>
              {formatShortDate(delivery_date)} • {formatShortDate(pickup_date)}
            </Typography>
          </div>
        ) : (
          <Typography as={"span"} className='text-sm'>
            Select Dates Again
          </Typography>
        )}
      </div>

      {/* <Edit2Icon className="h-4 w-4" /> */}
      <Button className='hover:bg-primary-900'>
        <CalendarEditFilledIcon className='!h-4 !w-4' />
        Edit
      </Button>
    </div>
  )
}
