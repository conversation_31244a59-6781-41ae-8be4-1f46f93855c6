"use client"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { CheckoutSection } from "@/types/checkout"
import { motion } from "framer-motion"
import { Check, ChevronLeft, ChevronRight } from "lucide-react"
import * as React from "react"

interface StepperProps {
  currentSection: CheckoutSection
  is_contact_completed: boolean
  is_delivery_completed: boolean
  is_carepal_completed: boolean
  is_review_completed: boolean
  onNext?: () => void
  onPrev?: () => void
}

const sectionToStep = {
  contact: 1,
  delivery: 2,
  carepal: 3,
  review: 4,
}

export function Stepper({
  currentSection,
  is_contact_completed,
  is_delivery_completed,
  is_carepal_completed,
  is_review_completed,
  onNext,
  onPrev,
}: StepperProps) {
  const currentStep = sectionToStep[currentSection]

  const isStepCompleted = (step: number) => {
    switch (step) {
      case 1:
        return is_contact_completed
      case 2:
        return is_delivery_completed
      case 3:
        return is_carepal_completed
      case 4:
        return is_review_completed
      default:
        return false
    }
  }

  return (
    <div className='flex w-full items-center justify-center gap-2 px-4 md:hidden'>
      <Button
        variant='ghost'
        size='icon'
        onClick={onPrev}
        disabled={currentStep === 1}
        className='shrink-0'
      >
        <ChevronLeft className='h-4 w-4' />
      </Button>

      <div className='flex w-full items-center'>
        {[1, 2, 3, 4].map((step) => {
          const isActive = step === currentStep
          const isCompleted = isStepCompleted(step)

          return (
            <React.Fragment key={step}>
              <div className='relative flex flex-1 items-center justify-center'>
                <motion.div
                  initial={false}
                  animate={{
                    scale: isActive ? 1.1 : 1,
                    backgroundColor: isActive
                      ? "rgb(37, 99, 235)"
                      : isCompleted
                        ? "rgb(34, 197, 94)"
                        : "rgb(229, 231, 235)",
                  }}
                  className={cn(
                    "relative z-10 flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium",
                    isActive || isCompleted ? "text-white" : "text-gray-600",
                  )}
                >
                  {isCompleted ? (
                    <Check className='h-4 w-4' />
                  ) : (
                    <span>{step}</span>
                  )}
                </motion.div>

                {step < 4 && (
                  <div
                    className={cn(
                      "absolute left-[50%] h-[2px] w-full transition-colors",
                      step < currentStep || isCompleted
                        ? "bg-blue-600"
                        : "bg-gray-200",
                    )}
                  />
                )}
              </div>
            </React.Fragment>
          )
        })}
      </div>

      <Button
        variant='ghost'
        size='icon'
        onClick={onNext}
        disabled={currentStep === 4}
        className='shrink-0'
      >
        <ChevronRight className='h-4 w-4' />
      </Button>
    </div>
  )
}
