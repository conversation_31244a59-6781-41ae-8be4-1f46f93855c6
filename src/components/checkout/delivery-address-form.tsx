"use client"

import { AnimatePresence, motion } from "framer-motion"
import * as React from "react"

import { getUserAddresses } from "@/actions/user"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import {
  generateFullAddress,
  generateUser<PERSON>abel,
  organizeAddresses,
} from "@/functions/address-utils"
import { loadScript } from "@/functions/loadScript"
import { trackAddressAdded, trackAddressSubmitted } from "@/lib/gtag-event"
import { useCheckoutStore } from "@/store/checkout-store"
import { useUserStore } from "@/store/user-store"
import { Address } from "@/types/address"
import { formVariants } from "@/utils/animation-variants"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useMutation, useQuery } from "@tanstack/react-query"
import { PlusAddOutlinedIcon } from "sharepal-icons"
import { toast } from "sonner"
import DeliveryAddressCard from "../custom/delivery-address-card"
import { Typography } from "../ui/typography"
import { AddDeliveryAddress } from "./delivery-address/add-delivery-address"
import { SelecteDeliveryAddress } from "./delivery-address/select-delivery-address"
import { SectionCard } from "./section-card"

export function DeliveryAddressForm() {
  const [showNewAddress, setShowNewAddress] = React.useState(false)
  const [showSelectAddress, setShowSelectAddress] = React.useState(false)
  const [selectedAddress, setSelectedAddress] = React.useState<number | null>(
    null,
  )
  const [isScriptLoaded, setIsScriptLoaded] = React.useState(false)
  const { user } = useUserStore()
  const {
    setActiveSection,
    finalAmount,
    cart_items,
    delivery_charges,
    applied_coupon_code,
    total_deposit,
    total_discount,
    setStepCompleted,
    setDeliveryAddress,
  } = useCheckoutStore()

  const { data: addresses = [], isLoading: addressesLoading } = useQuery<
    Address[]
  >({
    queryKey: ["user_addresses"],
    queryFn: getUserAddresses,
    refetchOnWindowFocus: false,
    enabled: !!user,
  })

  const organizedAddresses = React.useMemo(
    () => organizeAddresses({ addresses, selectedAddress }),
    [addresses, selectedAddress],
  )

  React.useEffect(() => {
    if (addresses && addresses.length > 0) {
      setSelectedAddress(addresses[0].id)
    }
  }, [addresses])

  const { mutate: handleUseAddress, isPending: availabilityCheckLoading } =
    useMutation({
      mutationFn: async () => {
        const address = addresses?.find((a) => a.id === selectedAddress)
        if (address) {
          setDeliveryAddress(address)
          const isAvailable = await fetchWithAuthPost<{ servicable: boolean }>(
            "https://api.sharepal.in/api:AIoqxnqr/address/availability-check",
            {
              latitude: address?.latitude,
              longitude: address?.longitude,
              address_id: address?.id,
              total_rent: finalAmount(),
            },
          )
          if (!isAvailable.servicable) {
            toast.success("Address is not serviceable")
            return isAvailable.servicable
          }
          if (user) {
            trackAddressAdded(
              cart_items,
              user?.whatsapp_number,
              finalAmount(),
              total_deposit,
              total_discount,
              applied_coupon_code,
              delivery_charges.final,
              address.city,
              address.state,
              address.pincode,
            )
          }
          if (user) {
            trackAddressSubmitted(
              cart_items,
              user?.whatsapp_number,
              finalAmount(),
              total_deposit,
              total_discount,
              applied_coupon_code,
              delivery_charges.final,
              address.city,
              address.state,
              address.pincode,
            )
          }
          setStepCompleted("delivery", true)
          setActiveSection("carepal")
          return isAvailable
        }
      },
      onError: () => {
        toast.error("Failed to Validate Address")
      },
    })

  // const handleScriptLoad = () => {
  //   // console.log('Google Maps API loaded')
  //   setIsScriptLoaded(true)
  // }

  React.useEffect(() => {
    if (showSelectAddress) {
      loadScript(
        `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`,
      )
      setIsScriptLoaded(true)
    }
  }, [showSelectAddress])

  return (
    <>
      {/* <Script
        src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`}
        onLoad={handleScriptLoad}
      /> */}

      <AddDeliveryAddress
        open={showNewAddress}
        onOpenChange={setShowNewAddress}
        isScriptLoaded={isScriptLoaded}
      />
      <SelecteDeliveryAddress
        open={showSelectAddress}
        addresses={addresses || []}
        onOpenChange={setShowSelectAddress}
        isScriptLoaded={isScriptLoaded}
        setSelectedAddress={setSelectedAddress}
        selectedAddress={selectedAddress}
        setShowNewAddress={setShowNewAddress}
        handleUseAddress={handleUseAddress}
      />

      <AnimatePresence mode='wait'>
        <SectionCard>
          <motion.div
            key='address-list'
            variants={formVariants}
            initial='hidden'
            animate='visible'
            exit='exit'
          >
            <Card className='border-0 p-0'>
              <CardContent className='flex min-h-72 flex-col items-center justify-between p-0'>
                {addressesLoading ? (
                  <div className='grid w-full gap-4 md:grid-cols-2'>
                    {[...Array(4)].map((_, index) => (
                      <Skeleton key={index} className='h-24 w-full' />
                    ))}
                  </div>
                ) : organizedAddresses.length > 0 ? (
                  <DeliveryAddressCard
                    className='hide-scrollbar grid max-h-72 gap-4 overflow-y-auto md:grid-cols-2'
                    options={organizedAddresses.map((address) => ({
                      value: address.id.toString(),
                      label: generateFullAddress(address),
                      subLabel: `${address.city}, ${address.state}, ${address.pincode}`,
                      userLabel: generateUserLabel(
                        user?.first_name,
                        address.shipment_number,
                        user?.calling_number,
                      ),
                      isSelected: address.id === selectedAddress,
                    }))}
                    onChange={(value) => setSelectedAddress(Number(value))}
                    selectedValue={selectedAddress?.toString() ?? ""}
                  />
                ) : (
                  <div className='flex h-full w-full items-center justify-center'>
                    <p className='text-center text-gray-500'>
                      No addresses found. Please add a new address.
                    </p>
                  </div>
                )}

                <CardFooter className='flex w-full flex-col gap-3 p-0'>
                  <Button
                    type='button'
                    variant='secondary'
                    size={"lg"}
                    className='my-3 flex w-full items-center justify-center rounded-full !bg-neutral-150 py-5'
                    onClick={() => setShowSelectAddress(true)}
                  >
                    <PlusAddOutlinedIcon className='!h-6 !w-6 text-neutral-900' />
                    <Typography
                      as={"span"}
                      className='!text-bt2 !text-neutral-900'
                    >
                      Select/Add New Delivery Address
                    </Typography>
                  </Button>

                  <div className='fixed bottom-0 left-0 right-0 z-50 flex w-full flex-wrap justify-end gap-4 bg-gray-100 p-4 md:static md:flex md:justify-start md:gap-4 md:space-y-4 md:bg-transparent md:px-0 md:pb-4 md:pt-0'>
                    <Button
                      onClick={() => handleUseAddress()}
                      disabled={
                        !selectedAddress ||
                        availabilityCheckLoading ||
                        addressesLoading
                      }
                      className='w-full rounded-full md:h-11 md:w-auto'
                      variant={"primary"}
                    >
                      <Typography as={"span"} className='!text-bt2'>
                        {availabilityCheckLoading
                          ? "Processing..."
                          : "Use this Address"}
                      </Typography>
                    </Button>
                  </div>
                </CardFooter>
              </CardContent>
            </Card>
          </motion.div>
        </SectionCard>
      </AnimatePresence>
    </>
  )
}
