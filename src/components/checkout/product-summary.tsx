"use client"

import { AnimatePresence, motion } from "framer-motion"
import { ChevronDown } from "lucide-react"
import { useEffect, useState } from "react"
// import { Separator } from '@/components/ui/separator'
import { moneyFormatter } from "@/functions/small-functions"
import { useCheckoutStore } from "@/store/checkout-store"

import { deleteCartItem, updateCartItemQuantity } from "@/actions/cart"
import { getCookie } from "@/functions/cookies"
import useCalculateRent from "@/hooks/use-calculate-rent"
import { trackCartViewAndUpdated, trackRemoveFromCart } from "@/lib/gtag-event"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { useRentalStore } from "@/store/rental-store"
import { CartItem } from "@/types"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"
import { CartAddedProduct } from "../cards/cart-added-product"
import { Typography } from "../ui/typography"

interface ProductSummaryProps {
  totalCharges: number
  showViewItem?: boolean
  showControls?: boolean
  items_count_text?: string
}
interface UpdateCartItemQuantityArgs {
  type: "add" | "remove"
  cartItem: CartItem
}
const MAX_QUANTITY = 50

export function ProductSummary({
  totalCharges,
  showViewItem = true,
  showControls = true,
  items_count_text,
}: ProductSummaryProps) {
  const {
    cart_items,
    removeFromCart,
    setAppliedCouponCode,
    updateCartItemQuantity: updateCartItemQuantityStore,
  } = useCheckoutStore()
  const [isProductsExpanded, setIsProductsExpanded] = useState(false)
  const { same_day_surge, selectedCity } = useRentalStore()

  const { getRent } = useCalculateRent({ type: "cart" })
  const [changingCartItem, setChangingCartItem] = useState<CartItem | null>(
    null,
  )
  const queryClient = useQueryClient()
  const { mutate: handleUpdateQuantity, isPending: cartItemsUpdateLoading } =
    useMutation({
      mutationFn: async ({ cartItem, type }: UpdateCartItemQuantityArgs) => {
        // If trying to reduce quantity when it's 1, delete the item instead
        if (type === "remove" && cartItem.quantity === 1) {
          return handleDeleteCartItem(cartItem)
        }

        // Set the item being changed for loading state
        setChangingCartItem(cartItem)

        // Calculate new quantity
        const newQuantity =
          type === "add"
            ? Math.min(cartItem.quantity + 1, MAX_QUANTITY) // Max quantity of 50
            : Math.max(cartItem.quantity - 1, 1) // Min quantity of 1

        // If quantity hasn't changed (due to limits), return early
        if (newQuantity === cartItem.quantity) {
          toast.info("Quantity limit reached", { id: "quantityLimit" })
          return null
        }

        return await updateCartItemQuantity({
          user_uid: getCookie("uid") || "",
          cart_item_id: cartItem.id || 0,
          quantity: newQuantity,
        })
      },
      onSuccess: (data) => {
        // Clear the changing item state
        setChangingCartItem(null)

        // If data is null, it means we hit a quantity limit
        if (!data) return

        // Update the store with new data
        updateCartItemQuantityStore(data, same_day_surge)

        toast.success("Cart item updated successfully", {
          id: "updateCartItem",
        })
      },
      onError: (error) => {
        // Clear the changing item state
        setChangingCartItem(null)

        toast.error("Failed to update cart item", { id: "updateCartItem" })
        console.error("Error updating cart item:", error)
      },
      onSettled: () => {
        // Refetch cart items to ensure we have the latest data
        queryClient.invalidateQueries({ queryKey: ["cart_items"] })
      },
    })

  const { mutate: handleDeleteCartItem, isPending: cartItemsDeleteLoading } =
    useMutation({
      mutationFn: async (cartItem: CartItem) => {
        setChangingCartItem(cartItem)
        return await deleteCartItem({
          user_uid: getCookie("uid") || "",
          cart_item_id: cartItem.id || 0,
        })
      },

      onSuccess: (_, cartItem) => {
        toast.success("Item removed from cart", { id: "deleteCartItem" })
        setAppliedCouponCode("", 0)
        trackRemoveFromCart(
          cartItem.item_name,
          getRent({ type: "cart", cart: cartItem }).rent,
          // getRent({ type: 'cart', cart: cartItem }),
          cartItem.product_code,
          cartItem.cart_image,
          cartItem.cat_sname,
          cartItem.size,
          cartItem.quantity,
          "rent",
        )
        removeFromCart(cartItem, same_day_surge)
      },
      onError: () => {
        toast.error("Failed to remove item from cart", { id: "deleteCartItem" })
      },
    })

  // Tracking Cart  Updated
  useEffect(() => {
    if (cart_items.length === 0) return
    trackCartViewAndUpdated(cart_items, "Cart Updated", selectedCity.city_url)
  }, [cart_items, selectedCity.city_url])

  return (
    <div className='w-full rounded-xl'>
      <div
        className={cn(
          "border-neutral-200 pb-4",
          isProductsExpanded && "border-b",
          !showViewItem && "!border-none !pb-0",
        )}
      >
        <div className='flex items-start justify-between gap-2'>
          <CartProducts items_count_text={items_count_text} />
          {totalCharges > 0 && (
            <div className='text-right'>
              <span className='text-lg font-semibold'>
                {moneyFormatter(totalCharges)}
              </span>
              <p className='text-xs text-gray-600'>Total Charges</p>
            </div>
          )}
        </div>
      </div>

      {/* <Separator /> */}
      {showViewItem && (
        <AnimatePresence>
          {isProductsExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className='hide-scrollbar max-h-[400px] overflow-hidden overflow-y-auto md:max-h-[420px]'
            >
              <div className='space-y-4 py-4'>
                {cart_items.map((cart_item) => (
                  <CartAddedProduct
                    cartItemsDeleteLoading={cartItemsDeleteLoading}
                    cartItemsUpdateLoading={cartItemsUpdateLoading}
                    key={cart_item.id}
                    showControls={showControls}
                    cartItem={cart_item}
                    changingCartItem={changingCartItem}
                    onUpdateQuantity={handleUpdateQuantity}
                    onDeleteItem={handleDeleteCartItem}
                    sameDaySurge={same_day_surge}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      )}
      {/* <Separator /> */}

      {showViewItem && (
        <div className='relative mb-3 flex w-full items-center justify-center'>
          <div className='absolute inset-0 flex items-center'>
            <div className='w-full border-t border-neutral-200' />
          </div>
          <div className='relative'>
            <button
              onClick={() => setIsProductsExpanded(!isProductsExpanded)}
              className='flex w-full items-center justify-center gap-2 rounded-full bg-neutral-150 px-4 py-1.5 text-xs font-medium text-neutral-500 transition-colors hover:text-gray-900'
            >
              <motion.div
                animate={{ rotate: isProductsExpanded ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronDown className='h-4 w-4 !text-primary-900' />
              </motion.div>
              <Typography as={"span"} className='!text-bt4 !text-primary-900'>
                {isProductsExpanded ? "Hide" : "View"} Items
              </Typography>
              <motion.div
                animate={{ rotate: isProductsExpanded ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronDown className='h-4 w-4 !text-primary-900' />
              </motion.div>
            </button>
          </div>
        </div>
      )}
      {/* <button
        onClick={() => setIsProductsExpanded(!isProductsExpanded)}
        className="flex w-full items-center justify-center gap-2 py-4 text-sm text-gray-600 transition-colors hover:text-gray-900"
      >
        {isProductsExpanded ? 'Hide' : 'View'} Items
        <motion.div
          animate={{ rotate: isProductsExpanded ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          {isProductsExpanded ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </motion.div>
      </button> */}
    </div>
  )
}

export const CartProducts = ({
  items_count_text,
}: {
  items_count_text?: string
}) => {
  const { cart_items, items_count } = useCheckoutStore()
  return (
    <div className='flex items-start gap-4'>
      {/* change space x for stacing effect */}
      <div className='flex -space-x-5 md:-space-x-10'>
        {cart_items.slice(0, 4).map((cart, index) => (
          <div
            key={cart.id}
            className='relative h-10 w-10 overflow-hidden rounded-md border bg-gray-100 p-1 md:h-16 md:w-16 md:rounded-lg'
            style={{ zIndex: items_count - index }}
          >
            <SpImage
              src={cart.cart_image}
              alt={cart.cat_sname}
              width={64}
              height={64}
              className='h-full w-full object-cover'
            />
          </div>
        ))}

        {cart_items.length > 4 && (
          <div
            className='relative flex h-10 w-10 items-center justify-end overflow-hidden rounded-md border bg-gray-100 p-0.5 text-xs font-semibold text-neutral-400 md:h-16 md:w-16 md:rounded-xl md:text-sm'
            style={{ zIndex: cart_items.length - 4 }}
          >
            +{cart_items.length - 4}
          </div>
        )}
      </div>
      <div className='space-y-1'>
        <h3 className='text-sm font-semibold text-gray-900 md:text-base'>
          {items_count} {items_count_text || "Items added"}
        </h3>
        <p className='line-clamp-2 text-xs text-gray-600 md:text-sm'>
          {cart_items.map((p) => p.item_name).join(" + ")}
        </p>
      </div>
    </div>
  )
}
