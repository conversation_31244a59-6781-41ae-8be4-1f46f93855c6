"use client"

import { fetchPaymentOptions } from "@/actions/checkout"
import { HARD_CODED_IMAGE_URL } from "@/constants"
import { moneyFormatter } from "@/functions/small-functions"
import useCoupon from "@/hooks/use-coupon"
import SpImage from "@/shared/SpImage/sp-image"
import { useCheckoutStore } from "@/store/checkout-store"
import { PaymentOption } from "@/types/checkout"
import { formVariants } from "@/utils/animation-variants"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { Separator } from "../ui/separator"
import { Typography } from "../ui/typography"
import { Charges } from "./order-summary-new/charge-item"
import { ClaimGSTInput } from "./order-summary-new/claim-gst-input"
import { CouponInput } from "./order-summary-new/coupon-input"
import PlaceOrderButton from "./order-summary-new/place-order"
import { WalletCheckbox } from "./order-summary-new/wallet-checkbox"
import { PaymentOptionsSelection } from "./payment-options"
import { ProductSummary } from "./product-summary"
import { SectionCard } from "./section-card"

export function ReviewAndPay() {
  const {
    total_rent,
    setPaymentOption,
    payment_option,
    setPaymentType,
    finalAmount,
    setHandlingCharges,
  } = useCheckoutStore()

  // Fetch payment options
  const { data: paymentOptions } = useQuery({
    queryKey: ["payment_options"],
    queryFn: async () => {
      const data = await fetchPaymentOptions(
        sessionStorage.getItem("backend_order") ?? "",
      )
      return data
    },
  })

  const onPaymentOptionChange = (type: string) => {
    const selectedPayment = paymentOptions?.find(
      (payment: PaymentOption) => payment.payment_type?.toString() === type,
    )

    if (selectedPayment) {
      // Set payment option to the store
      setPaymentOption(selectedPayment)
      // Optionally set the payment type
      setPaymentType(selectedPayment.payment_type)
      if (selectedPayment.payment_type === 5) {
        setHandlingCharges(99)
      } else {
        setHandlingCharges(0)
      }
    }
  }

  const CouponProps = useCoupon()

  return (
    <motion.div
      variants={formVariants}
      initial='hidden'
      animate='visible'
      exit='exit'
      className='space-y-6'
    >
      <SectionCard>
        <ProductSummary totalCharges={total_rent} />

        <div className='flex w-full flex-col items-center justify-center gap-3 md:hidden'>
          <Separator />

          <div className='w-full space-y-4'>
            <WalletCheckbox />
            <ClaimGSTInput />

            <CouponInput {...CouponProps} />
          </div>

          <Separator />

          <div className='mb-3 w-full'>
            <Charges />
          </div>
        </div>

        <div className='mt-4 space-y-4 md:mt-10'>
          <h3 className='text-bt2'>How would you like to pay?</h3>
          <div className='space-y-6'>
            <PaymentOptionsSelection
              paymentOptions={paymentOptions}
              // selectedPaymentOption={payment_type?.toString()}
              // or
              selectedPaymentOption={
                payment_option?.payment_type?.toString() || "1"
              }
              onPaymentOptionChange={onPaymentOptionChange}
            />

            <div className='flex items-center justify-between'>
              <div>
                <Typography as={"h4"} className='!text-h4'>
                  Order Total
                </Typography>
                <Typography as={"span"} className='block text-b6 text-gray-500'>
                  Price incl. of all taxes
                </Typography>
              </div>
              <Typography as={"h1"} className='!text-h1'>
                {moneyFormatter(finalAmount())}
              </Typography>
            </div>

            <div className='flex items-center justify-start gap-4'>
              <div className='fixed bottom-0 left-0 right-0 z-50 flex flex-wrap justify-end gap-4 bg-gray-100 max-md:p-4 md:static md:flex md:justify-start md:gap-4 md:space-y-4 md:bg-transparent md:px-0 md:pt-0'>
                <PlaceOrderButton />
              </div>

              <div className='flex w-full items-center justify-start gap-[5px] rounded-lg bg-neutral-150 px-2 py-[6px] md:w-auto'>
                <SpImage
                  src={`${HARD_CODED_IMAGE_URL}/razorpay.webp`}
                  alt='Razorpay'
                  width={20}
                  height={20}
                  className='rounded'
                />
                <div className='flex flex-col items-start justify-start'>
                  <Typography
                    as={"span"}
                    className='!text-sh5 !text-neutral-900'
                  >
                    Pay via Razorpay
                  </Typography>
                  <Typography as={"span"} className='text-b7 text-neutral-500'>
                    Cards, Netbanking, Wallet & UPI
                  </Typography>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SectionCard>
    </motion.div>
  )
}
