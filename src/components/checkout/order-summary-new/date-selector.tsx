"use client"

import { Calendar } from "@/components/ui/calendar"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import * as React from "react"
import { CalendarStartFilledIcon, ClockOutlinedIcon } from "sharepal-icons"
import { toast } from "sonner"

const formatDate = (date: Date | null) => {
  if (!date) return ""
  return format(date, "LLL dd, y")
}

interface DateSelectorProps {
  label: string
  date: Date | null
  onSelect: (date: Date | undefined) => void
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  disabledDate: (date: Date) => boolean
  timeRange: string
  disabled?: boolean
}

export function DateSelector({
  label,
  date,
  onSelect,
  isOpen,
  setIsOpen,
  disabledDate,
  timeRange,
  disabled,
}: DateSelectorProps) {
  // Add ref for the calendar container
  const calendarRef = React.useRef<HTMLDivElement>(null)

  // Determine the default month to display
  // const getDefaultMonth = React.useCallback(() => {
  //   // If there's a selected date, use it as default month
  //   if (date) {
  //     return date
  //   }

  //   // Otherwise use current date
  //   const today = new Date()

  //   // If today is disabled, find the next valid month
  //   if (disabledDate(today)) {
  //     // Try next few months until we find a valid one
  //     for (let i = 1; i <= 3; i++) {
  //       const nextMonth = new Date(today)
  //       nextMonth.setMonth(today.getMonth() + i)
  //       nextMonth.setDate(1) // First day of month
  //       if (!disabledDate(nextMonth)) {
  //         return nextMonth
  //       }
  //     }
  //   }

  //   return today
  // }, [date, disabledDate])

  // Determine the default month to display based on the next valid date
  const getDefaultMonth = React.useCallback(() => {
    // Use selected date if available
    if (date) {
      return date
    }

    const current = new Date() // Start from today

    // Keep checking the next day until a valid one is found
    for (let i = 0; i < 90; i++) {
      // Cap the search to 90 days ahead
      if (!disabledDate(current)) {
        return current
      }
      current.setDate(current.getDate() + i)
    }

    // Fallback if no valid date is found
    return new Date()
  }, [date, disabledDate])

  // Handle outside clicks
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, setIsOpen])

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Close any other open calendar before opening this one
    try {
      setIsOpen(!isOpen)
    } catch (error) {
      console.error("Error toggling date selector:", error)
      toast.error("Failed to open date selector. Please try again.")
    }
  }

  const handleDateSelect = (selectedDate: Date | undefined): void => {
    try {
      onSelect(selectedDate)
      setIsOpen(false) // Close calendar after selection
    } catch (error) {
      console.error("Error selecting date:", error)
      toast.error("Failed to select date. Please try again.")
    }
  }

  return (
    <div className='space-y-1.5 md:space-y-2'>
      <Label className='font-medium'>
        {label} <span className='text-destructive'>*</span>
      </Label>
      <div ref={calendarRef}>
        <button
          disabled={disabled}
          onClick={handleClick}
          className='w-full disabled:opacity-50'
          type='button'
        >
          <div className='relative'>
            <div className='flex h-11 w-full items-center justify-start rounded-2xl border-2 border-neutral-200 bg-gray-100 pl-8'>
              {date ? (
                <span className='text-sh5 text-neutral-900'>
                  {formatDate(date)}
                </span>
              ) : (
                <span className='text-b4 text-neutral-300'>
                  Select {label.toLowerCase()}
                </span>
              )}
            </div>
            <CalendarStartFilledIcon className='absolute left-3 top-1/2 size-4 -translate-y-1/2' />
          </div>
        </button>

        {isOpen && (
          <div className='absolute z-10 mt-2 rounded-lg bg-white shadow-lg'>
            <Calendar
              mode='single'
              selected={date || undefined}
              onSelect={handleDateSelect}
              disabled={disabledDate}
              defaultMonth={getDefaultMonth()}
              className='rounded-md border'
              modifiersClassNames={{
                selected: cn(
                  "bg-secondary-500 text-neutral-900 font-bold hover:bg-secondary-600 focus:bg-secondary-500",
                ),
                range: "!bg-secondary-200",
                range_middle: "!bg-secondary-200",
                range_start: "!font-bold",
                range_end: "!font-bold",
              }}
            />
          </div>
        )}
      </div>

      <div className='flex items-center gap-2 text-xs text-muted-foreground'>
        <ClockOutlinedIcon className='h-4 w-4' />
        <span>{timeRange}</span>
      </div>
    </div>
  )
}
