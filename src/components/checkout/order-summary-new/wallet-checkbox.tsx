// WalletCheckbox Component

import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"

import { WalletOutlinedIcon } from "sharepal-icons"

interface WalletProps {
  cart?: boolean
}

export function WalletCheckbox({ cart }: WalletProps) {
  const { wallet_used, wallet_balance, setUseWallet } = useCheckoutStore()
  if (wallet_balance <= 0 && !wallet_used) return <></>
  return (
    <div
      className={cn(
        "flex",
        cart ? "flex-col gap-2" : "items-center justify-between",
      )}
    >
      <div
        className={cn(
          "flex items-center gap-2",
          cart ? "h-10 rounded-full bg-neutral-150 px-3" : "",
        )}
      >
        <Checkbox
          id='wallet'
          checked={wallet_used}
          onCheckedChange={(checked) => setUseWallet(checked as boolean)}
          // onCheckedChange={setUseWallet}
          className={cn(
            "!h-5 !w-5 !rounded-[4px] !border-2 !border-neutral-500",
            wallet_used && "!border-primary-500",
          )}
        />
        <label htmlFor='wallet' className='cursor-pointer'>
          <Typography as='span' className='!text-sh5 !text-neutral-900'>
            Pay via Wallet?
          </Typography>
        </label>
      </div>
      <div className='flex justify-between'>
        {cart ? (
          <Typography
            as='span'
            className='flex gap-2 !text-b6 !text-neutral-400'
          >
            <WalletOutlinedIcon className='h-4 w-4' /> Wallet Balance :{" "}
            <Typography as={"span"} className='!text-sh6 !text-gray-900'>
              {moneyFormatter(wallet_balance)}
            </Typography>
          </Typography>
        ) : (
          <Badge
            variant={"secondary"}
            className='flex w-max items-center gap-1 rounded-full bg-neutral-150'
          >
            <WalletOutlinedIcon className='!h-4 !w-4 text-neutral-400' />
            <Typography as='span' className='!text-sh4 !text-gray-900'>
              {moneyFormatter(wallet_balance)}
            </Typography>
          </Badge>
        )}
      </div>
    </div>
  )
}
