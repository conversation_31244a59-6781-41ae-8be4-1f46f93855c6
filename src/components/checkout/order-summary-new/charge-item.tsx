import { CarepalHeartIcon } from "@/components/Icons/carepal-heart"
import { Badge } from "@/components/ui/badge"
import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import { useCheckoutStore } from "@/store/checkout-store"
import { AnimatePresence, motion } from "framer-motion"
import { OfferOutlinedIcon, ShieldPalPromiseOutlinedIcon } from "sharepal-icons"

export interface ChargeItem {
  label: string
  amount: number
  strikethrough?: number
  badge?: {
    text: string
    variant?: "primary" | "success" | "carepal"
    icon?: React.ReactNode
  }
  items_count?: number
  items_count_text?: string
}

interface ChargeItemProps {
  item: ChargeItem
  type?: "discount" | "charge"
}

export function ChargeItem({ item, type = "charge" }: ChargeItemProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className='flex items-center justify-between'
    >
      <div className='flex flex-wrap items-center justify-start gap-0 text-right md:gap-2'>
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className='!text-b6 text-gray-900 underline decoration-dotted underline-offset-4 md:!text-b4'
        >
          {item.label}
        </motion.span>
        {item.items_count !== undefined && item.items_count > 0 && (
          <motion.span
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            className='block rounded-full bg-neutral-150 px-2 py-1 !text-o4 text-gray-500 md:!text-b6'
          >
            {item.items_count} {item.items_count_text || "items added"}
          </motion.span>
        )}
        {item.badge?.text && (
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
          >
            <Badge variant={item.badge.variant} className='mx-1 px-1 py-[3px]'>
              {item.badge.icon ? (
                item.badge.icon
              ) : (
                <ShieldPalPromiseOutlinedIcon className='h-4 w-4' />
              )}
              <Typography as={"p"} className='!text-o4 md:!text-b6'>
                {item.badge.text}
              </Typography>
            </Badge>
          </motion.div>
        )}
      </div>
      <div className='flex items-center justify-end gap-1'>
        {item.strikethrough !== undefined && item.strikethrough > 0 && (
          <motion.span
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            className='!text-b5 text-gray-400 line-through md:!text-b3'
          >
            {moneyFormatter(item.strikethrough)}
          </motion.span>
        )}
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className={`!text-sh5 md:!text-sh3 ${
            type === "discount" && "text-success-600"
          }`}
        >
          {item.amount < 0 || type === "discount" ? "-" : ""}

          {moneyFormatter(Math.abs(item.amount))}
        </motion.span>
      </div>
    </motion.div>
  )
}

interface ChargesProps {
  total_rent: number
  total_deposit: number
  delivery_charges?: number
  pickup_charges?: number
  items_count: number
  total_discount?: number
  wallet_balance_used?: number
  applied_coupon_code?: string
  coupon_discount?: number
  handling_charges?: number
  wallet_used?: boolean
  carepal_selected?: boolean
  carepal_fee?: number
}

export function Charges({ chargesData }: { chargesData?: ChargesProps }) {
  const {
    total_rent,
    total_deposit,
    delivery_charges,
    pickup_charges,
    items_count,
    total_discount,
    wallet_balance_used,
    applied_coupon_code,
    coupon_discount,
    handling_charges,
    wallet_used,
    carepal_selected,
    carepal_fee,
  } = useCheckoutStore()

  const charges: ChargeItem[] = [
    {
      label: "Total Rental Charges",
      amount: chargesData ? chargesData.total_rent : total_rent,
      items_count: items_count,
    },
    {
      label: "Security Deposit",
      amount: chargesData ? chargesData.total_deposit : total_deposit,
      badge: {
        text: "Zero Policy",
        variant: "primary",
        icon: (
          <ShieldPalPromiseOutlinedIcon className='mr-1 !h-4 !w-4 text-primary-600' />
        ),
      },
    },
  ]

  if (!chargesData) {
    if (delivery_charges.final !== undefined) {
      charges.push({
        label: "Delivery Charges",
        amount: delivery_charges.final,
        strikethrough:
          delivery_charges.final === 0 ? delivery_charges.original : undefined,
        badge:
          delivery_charges.final === 0
            ? {
                text: "Zero Policy",
                variant: "primary",
                icon: (
                  <ShieldPalPromiseOutlinedIcon className='mr-1 !h-4 !w-4 text-primary-600' />
                ),
              }
            : undefined,
      })
    }
    if (pickup_charges && pickup_charges.final !== undefined) {
      charges.push({
        label: "Pickup Charges",
        amount: pickup_charges.final,
        strikethrough:
          pickup_charges.final === 0 ? delivery_charges.original : undefined,
        badge:
          pickup_charges.final === 0
            ? {
                text: "Zero Policy",
                variant: "primary",
                icon: (
                  <ShieldPalPromiseOutlinedIcon className='mr-1 !h-4 !w-4 text-primary-600' />
                ),
              }
            : undefined,
      })
    }
    if (carepal_selected && carepal_fee > 0) {
      charges.push({
        label: "Damage Waiver Fee",
        amount: carepal_fee,
        badge: {
          text: "Carepal Assure",
          variant: "carepal",
          icon: (
            <CarepalHeartIcon className='mr-1 !h-4 !w-4 fill-carepal-darker' />
          ),
        },
      })
    }
    if (coupon_discount !== undefined && applied_coupon_code) {
      charges.push({
        label: "Coupon Discount",
        amount: -coupon_discount,
        badge: {
          icon: (
            <OfferOutlinedIcon className='mr-1 !h-4 !w-4 text-success-600' />
          ),

          text: applied_coupon_code,
          variant: "success",
        },
      })
    }
    if (wallet_balance_used !== undefined && wallet_used) {
      charges.push({
        label: "Wallet Used",
        amount: -wallet_balance_used,
      })
    }
    if (total_discount !== undefined && total_discount > 0) {
      charges.push({
        label: "Discount Coupon",
        amount: -total_discount,
        badge: {
          text: applied_coupon_code || "Coupon Applied",
          variant: "success",
        },
        strikethrough: total_discount + total_rent,
      })
    }
    if (handling_charges !== undefined && handling_charges > 0) {
      charges.push({
        label: "Pay On Delivery Charges",
        amount: handling_charges,
      })
    }
  } else {
    if (chargesData.delivery_charges !== undefined) {
      const deliveryAmount = chargesData.delivery_charges ?? 0
      charges.push({
        label: "Delivery Charges",
        amount: deliveryAmount,
        strikethrough: deliveryAmount === 0 ? 299 : undefined,
        badge:
          deliveryAmount === 0
            ? {
                text: "Zero Policy",
                variant: "primary",
                icon: (
                  <ShieldPalPromiseOutlinedIcon className='mr-1 !h-4 !w-4 text-primary-600' />
                ),
              }
            : undefined,
      })
    }
    if (chargesData.pickup_charges !== undefined) {
      const pickupAmount = chargesData.pickup_charges ?? 0
      charges.push({
        label: "Pickup Charges",
        amount: pickupAmount,
        strikethrough: pickupAmount === 0 ? 299 : undefined,
        badge:
          pickupAmount === 0
            ? {
                text: "Zero Policy",
                variant: "primary",
                icon: (
                  <ShieldPalPromiseOutlinedIcon className='mr-1 !h-4 !w-4 text-primary-600' />
                ),
              }
            : undefined,
      })
    }
    if (chargesData !== undefined && chargesData.carepal_selected) {
      charges.push({
        label: "Damage Waiver",
        amount: chargesData.carepal_fee ?? 0,
        badge: {
          text: "Carepal Assure",
          variant: "carepal",
          icon: (
            <CarepalHeartIcon className='mr-1 !h-4 !w-4 fill-carepal-darker' />
          ),
        },
      })
    }
    if (chargesData !== undefined && chargesData.applied_coupon_code) {
      charges.push({
        label: "Coupon Discount",
        amount: -(chargesData?.coupon_discount || 0),
        badge: {
          icon: (
            <OfferOutlinedIcon className='mr-1 !h-4 !w-4 text-success-600' />
          ),

          text: chargesData?.applied_coupon_code || "",
          variant: "success",
        },
      })
    }
    if (chargesData !== undefined && chargesData.wallet_used) {
      charges.push({
        label: "Wallet Used",
        amount: -(chargesData.wallet_balance_used || 0),
      })
    }
    if (chargesData !== undefined && (chargesData.total_discount || 0) > 0) {
      charges.push({
        label: "Discount Coupon",
        amount: -(chargesData.total_discount || 0),
        badge: {
          text: chargesData.applied_coupon_code || "Coupon Applied",
          variant: "success",
        },
        strikethrough:
          (chargesData.total_discount ?? 0) + chargesData.total_rent,
      })
    }
    if (chargesData !== undefined && (chargesData.handling_charges || 0) > 0) {
      charges.push({
        label: "Pay On Delivery Charges",
        amount: chargesData.handling_charges ?? 0,
      })
    }
  }

  return (
    <div className='w-full space-y-3'>
      <AnimatePresence>
        {charges.map((item) => (
          <motion.div
            key={item.label}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ChargeItem item={item} />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}
