import { moneyFormatter } from "@/functions/small-functions"

interface ChargeItem {
  label: string
  amount: number
  strikethrough?: number
  badge?: string
  items_count?: number
}

interface ChargesBreakdownProps {
  items: ChargeItem[]
}

export function ChargesBreakdown({ items }: ChargesBreakdownProps) {
  return (
    <div className='space-y-3'>
      {items.map((item, index) => (
        <div key={index} className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium text-gray-900 underline'>
              {item.label}
            </span>
            {item.items_count && (
              <span className='rounded-full bg-neutral-150 px-2 py-1 text-xs font-normal text-gray-500'>
                {item.items_count} items added
              </span>
            )}
            {item.badge && (
              <span className='inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700'>
                {item.badge}
              </span>
            )}
          </div>
          <div className='flex items-center gap-2'>
            {item.strikethrough && (
              <span className='text-gray-400 line-through'>
                {moneyFormatter(item.strikethrough)}
              </span>
            )}
            <span className='font-bold'>
              {item.amount < 0 ? "-" : ""}
              {moneyFormatter(item.amount)}
            </span>
          </div>
        </div>
      ))}
    </div>
  )
}
