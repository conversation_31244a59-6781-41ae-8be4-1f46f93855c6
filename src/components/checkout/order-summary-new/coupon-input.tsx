"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"
import { Dispatch, SetStateAction, useState } from "react"
import {
  ChevronDownIcon,
  OfferOutlinedIcon,
  TickCircleOutlinedIcon,
} from "sharepal-icons"
import ApplyCoupon from "../apply-coupon"

interface CouponInputProps {
  inCart?: boolean
  showViewCoupons: boolean
  couponCode: string
  isLoading: boolean
  setCouponCode: Dispatch<SetStateAction<string>>
  setShowViewCoupons: Dispatch<SetStateAction<boolean>>
  handleApplyCoupon: (code: string) => unknown
  showToggleViewCoupons?: boolean
}

export function CouponInput(props: CouponInputProps) {
  const {
    couponCode,
    handleApplyCoupon,
    isLoading,
    setCouponCode,
    setShowViewCoupons,
    showViewCoupons,
    inCart,
    showToggleViewCoupons,
  } = props

  const {
    applied_coupon_code,
    setAppliedCouponCode,
    setDeliveryCharges,
    setAdjustedAmount,
    total_amount,
  } = useCheckoutStore()

  const [localAdjustedAmount, setLocalAdjustedAmount] = useState("")
  const backend_order = Boolean(sessionStorage.getItem("backend_order"))

  return (
    <div className='w-full space-y-2'>
      {/* adjust amount */}
      {backend_order && (
        <div className='flex w-full items-center gap-2 rounded-full border-2 border-gray-200 bg-gray-100 focus-within:border-primary-500'>
          <div className='flex flex-1 items-center gap-2 pl-3'>
            <Input
              type='number'
              value={localAdjustedAmount}
              onChange={(e) => {
                const value = e.target.value

                // Allow empty string so the user can delete and type freely
                if (value === "") {
                  setLocalAdjustedAmount(value)
                  return
                }

                let numericValue = parseFloat(value)
                if (isNaN(numericValue)) return // Don't update state if it's NaN

                const totalRent = total_amount
                numericValue = Math.max(
                  -totalRent,
                  Math.min(totalRent, numericValue),
                ) // Ensure within range

                setLocalAdjustedAmount(numericValue.toLocaleString())
              }}
              className='h-9 appearance-none border-0 bg-transparent p-0 text-sh5 ring-0 placeholder:text-gray-500 focus-visible:border-0 [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none'
              placeholder='Adjust Amount Here'
            />
          </div>
          <Button
            onClick={() => {
              setAdjustedAmount(Number(localAdjustedAmount))
            }}
            className='hover:bg-primary-900'
          >
            Adjust Amount Here
          </Button>
        </div>
      )}

      {/* coupon */}
      <div className='flex w-full items-center gap-2 rounded-full border-2 border-gray-200 bg-gray-100 focus-within:border-primary-500'>
        <div className='flex flex-1 items-center gap-2 pl-3'>
          <span className='scale-[1.5]'>
            <OfferOutlinedIcon className='!h-4 !w-4' />
          </span>
          <Input
            value={couponCode}
            onChange={(e) => {
              const value = e.target.value.trimStart().toUpperCase()
              setCouponCode(value)
            }}
            className={cn(
              "h-9 border-0 bg-transparent p-0 !text-sh5 ring-0 placeholder:text-gray-500 focus-visible:border-0",
              inCart && "!text-b4",
            )}
            placeholder='Enter coupon code'
            disabled={!!applied_coupon_code}
          />
        </div>
        {applied_coupon_code ? (
          <Button
            onClick={() => {
              setAppliedCouponCode("", 0)
              if (applied_coupon_code === "PAIDREPLACEMENT")
                setDeliveryCharges(0)
            }}
            disabled={isLoading}
            className='hover:bg-primary-900'
          >
            Remove
          </Button>
        ) : (
          <Button
            onClick={() => handleApplyCoupon(couponCode)}
            disabled={isLoading || !couponCode}
            className='hover:bg-primary-900'
          >
            {isLoading ? "Applying..." : "Apply"}
          </Button>
        )}
      </div>
      <div className='mt-2 flex items-center justify-between gap-4'>
        <div className='flex items-center gap-1.5 text-green-600'>
          {applied_coupon_code && (
            <>
              <TickCircleOutlinedIcon className='!h-4 !w-4' />
              <span className='text-xs'>Discount coupon applied</span>
            </>
          )}
        </div>

        {!showToggleViewCoupons && (
          <button
            onClick={() => setShowViewCoupons((prev) => !prev)}
            className='flex items-center gap-1 self-end text-blue-600 hover:text-blue-700'
          >
            <Typography as={"span"} className='!text-bt4'>
              View Coupons
            </Typography>
            <ChevronDownIcon
              className={cn(
                "!h-4 !w-4 !transition-all",
                showViewCoupons ? "rotate-180" : "rotate-0",
              )}
            />
          </button>
        )}
      </div>
      {!inCart && (
        <ApplyCoupon
          inCart
          open={showViewCoupons}
          onOpenChange={setShowViewCoupons}
          onApplyCoupon={handleApplyCoupon}
        />
      )}
    </div>
  )
}
