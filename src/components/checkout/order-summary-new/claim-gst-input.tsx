import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useMutation } from "@tanstack/react-query"
import { AnimatePresence, motion } from "framer-motion"
import { useState } from "react"
import { DocOutlineIcon } from "sharepal-icons"
import { toast } from "sonner"

export function ClaimGSTInput() {
  // const [gst_number, setGstNumber] = useState("")
  const [showGSTInput, setShowGSTInput] = useState(false)
  // const [isClaimed, setIsClaimed] = useState(false)
  const { gst_number, gst_claimed, setGSTNumber, setUseGST } =
    useCheckoutStore()

  const {
    mutate: handleClaimGST,
    isPending,
    data,
  } = useMutation({
    mutationFn: () =>
      fetchWithAuthPost<string>(
        "https://api.sharepal.in/api:AIoqxnqr/checkout/gst-details",
        {
          gst_number,
        },
      ),
    onSuccess: () => {
      setUseGST(true)
    },
    onError: (error) => {
      toast.error(
        JSON.parse(error.message).message ?? "Failed To Claim GST Number",
      )
    },
  })

  return (
    <div className='w-full'>
      <div className='flex items-center gap-2'>
        <Checkbox
          id='gst'
          checked={showGSTInput}
          onCheckedChange={(checked) => {
            setShowGSTInput(checked as boolean)
            if (!checked) {
              setUseGST(false)
              setGSTNumber("")
            }
          }}
          className={cn(
            "!h-5 !w-5 !rounded-[4px] !border-2 !border-neutral-500",
            showGSTInput && "!border-primary-500",
          )}
        />
        <label htmlFor='gst' className='cursor-pointer'>
          <Typography as='span' className='!text-sh5 !text-neutral-900'>
            Claim GST credit up to 18% on this order
          </Typography>
        </label>
      </div>

      <AnimatePresence>
        {showGSTInput && (
          <motion.div
            className='mt-2 w-full'
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
            <div className='flex w-full items-center gap-2 rounded-full border-2 border-gray-200 bg-gray-100 focus-within:border-primary-500'>
              <div className='flex flex-1 items-center gap-2 pl-3'>
                <DocOutlineIcon className='!h-4 !w-4 !text-gray-900' />
                <Input
                  value={gst_number}
                  onChange={(e) => setGSTNumber(e.target.value)}
                  className='h-9 border-0 bg-transparent p-0 !text-sh5 ring-0 placeholder:text-gray-500 focus-visible:border-0'
                  placeholder='Enter GST Number'
                />
              </div>
              <Button
                onClick={() => {
                  if (gst_claimed) {
                    setUseGST(false)
                    setGSTNumber("")
                    return
                  }
                  handleClaimGST()
                }}
                disabled={isPending || !gst_number}
                className={cn(
                  "hover:bg-primary-900",
                  // isClaimed && "bg-success-600",
                )}
              >
                {isPending ? "Claiming..." : gst_claimed ? "Change" : "Claim"}
              </Button>
            </div>
            {typeof data == "string" && gst_claimed && (
              <Typography
                as='p'
                className='mt-1 text-wrap text-o4 text-success-500'
              >
                {data}
              </Typography>
            )}
          </motion.div>

          // show firm name
        )}
      </AnimatePresence>
    </div>
  )
}
