"use client"

import { But<PERSON> } from "@/components/ui/button"
import usePlaceOrder from "@/hooks/use-place-order"
import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"

export default function PlaceOrderButton({
  className,
}: {
  className?: string
}) {
  const { handlePayment, isLoading } = usePlaceOrder()
  const { getCartCount, payment_option } = useCheckoutStore()
  return (
    <Button
      className={cn(
        "h-full w-max rounded-full max-md:w-full md:h-11 lg:min-w-72",
        className,
      )}
      type='button'
      variant='primary'
      size='lg'
      onClick={handlePayment}
      disabled={isLoading || getCartCount() === 0}
    >
      {isLoading
        ? "Processing..."
        : payment_option?.payment_type === 5
          ? "Place Your Order"
          : "Place Your Order & Pay"}
    </Button>
  )
}
