"use client"

import { Typography } from "@/components/ui/typography"
import { HARD_CODED_IMAGE_URL } from "@/constants"
import { useCheckoutStore } from "@/store/checkout-store"
import Image from "next/image"

export function PaymentSection() {
  const { payment_option } = useCheckoutStore()
  return (
    <div className='space-y-4'>
      <div className='flex items-center justify-between gap-2'>
        <Typography
          as={"span"}
          className='min-w-max text-center text-b4 text-gray-900 underline decoration-dotted underline-offset-4'
        >
          Payment Mode:
        </Typography>
        <Typography
          as={"span"}
          className='min-w-max text-center text-sh7 text-neutral-900 md:text-sh4'
        >
          {payment_option?.visible_text}
        </Typography>
      </div>

      <div className='space-y-3 rounded-2xl bg-neutral-150 p-2 px-3'>
        <div className='flex items-center gap-2'>
          <Image
            // src="/placeholder.svg?height=32&width=32"
            src={`${HARD_CODED_IMAGE_URL}/razorpay.webp`}
            alt='Razorpay'
            width={32}
            height={32}
            className='rounded'
          />
          <div>
            <Typography as={"h4"} className='text-sm font-bold tracking-wide'>
              Pay via Razorpay
            </Typography>
            <Typography as={"p"} className='text-sm text-gray-500'>
              Cards, Netbanking, Wallet & UPI
            </Typography>
          </div>
        </div>
        {/* <div className="flex items-center gap-2">
          <CreditCard className="h-5 w-5 text-gray-400" />
          <span className="text-sm text-gray-600">
            Secure payments by Razorpay
          </span>
        </div> */}
      </div>
    </div>
  )
}
