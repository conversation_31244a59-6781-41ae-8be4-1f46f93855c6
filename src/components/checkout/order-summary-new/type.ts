export interface DateRange {
  delivery_date: Date
  pickup_date: Date
}

export interface ChargeItem {
  label: string
  amount: number
  strikethrough?: number
  badge?: {
    text: string
    variant?: "primary" | "success"
  }
  items_count?: number
}

export interface OrderSummaryProps {
  // total_rent: number
  // total_deposit: number
  // delivery_charges: {
  //   original: number
  //   final: number
  // }
  // pickup_charges: {
  //   original: number
  //   final: number
  // }
  // items_count: number
  // total_discount: number
  // coupon_code: string
  // wallet_balance: number
  // total_amount: number
  onDateChange: (range: DateRange) => void
  onApplyCoupon: (code: string) => Promise<void>
  onRemoveCoupon: () => void
  onPlaceOrder: () => Promise<void>
}
