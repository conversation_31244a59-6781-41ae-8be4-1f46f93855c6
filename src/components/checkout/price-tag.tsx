import { moneyFormatter } from "@/functions/small-functions"
import { cn } from "@/lib/utils"

interface PriceTagProps {
  amount: number
  period?: string
  size?: "sm" | "md" | "lg"
  strikethrough?: boolean
}

export function PriceTag({
  amount,
  period,
  size = "md",
  strikethrough,
}: PriceTagProps) {
  return (
    <span
      className={cn(
        "font-medium",
        size === "sm" && "text-sm",
        size === "md" && "text-base",
        size === "lg" && "text-lg",
        strikethrough && "text-neutral-400 line-through",
      )}
    >
      {moneyFormatter(amount)}
      {period && <span className='text-sm text-neutral-500'>/{period}</span>}
    </span>
  )
}
