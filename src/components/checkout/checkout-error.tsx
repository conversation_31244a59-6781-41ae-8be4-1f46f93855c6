"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { AnimatePresence, motion } from "framer-motion"
import { <PERSON>ertCircle, RefreshCw, WifiOff } from "lucide-react"
import { useEffect, useState } from "react"

type ErrorType = "network" | "checkout" | null

export function CheckoutError({ cartError }: { cartError: boolean }) {
  const [errorType, setErrorType] = useState<ErrorType>(null)
  const [isRetrying, setIsRetrying] = useState(false)

  const checkError = async () => {
    setIsRetrying(true)
    // Handle network connectivity error
    if (!navigator.onLine) {
      setErrorType("network")
      setIsRetrying(false)
      return
    }

    // Handle checkout error based on the `cartError` prop
    if (cartError) {
      setErrorType("checkout")
      setIsRetrying(false)
      return
    }

    try {
      // Simulate an API call to check for additional checkout errors
      const response = await fetch("/api/checkout-status")
      if (!response.ok) {
        throw new Error("Checkout error")
      }
      // If no error exists, clear the error type
      setErrorType(null)
    } catch (error) {
      console.error(error)
      setErrorType("checkout")
    } finally {
      setIsRetrying(false)
    }
  }

  useEffect(() => {
    const checkError = async () => {
      setIsRetrying(true)
      // Handle network connectivity error
      if (!navigator.onLine) {
        setErrorType("network")
        setIsRetrying(false)
        return
      }

      // Handle checkout error based on the `cartError` prop
      if (cartError) {
        setErrorType("checkout")
        setIsRetrying(false)
        return
      }

      try {
        // Simulate an API call to check for additional checkout errors
        const response = await fetch("/api/checkout-status")
        if (!response.ok) {
          throw new Error("Checkout error")
        }
        // If no error exists, clear the error type
        setErrorType(null)
      } catch (error) {
        console.error(error)
        setErrorType("checkout")
      } finally {
        setIsRetrying(false)
      }
    }
    checkError()
  }, [cartError])

  const handleRetry = () => {
    // router.refresh()
    setIsRetrying(true)
    checkError()
    location.reload()
  }

  if (errorType === null) {
    return null // No error, render nothing
  }

  return (
    <AnimatePresence mode='wait'>
      <motion.div
        key={errorType}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        // className="fixed inset-0 flex items-center justify-center bg-opacity-50 p-4"
        className='relative inset-0 flex min-h-screen items-center justify-center bg-opacity-50 p-4'
      >
        <Card className='w-full max-w-md'>
          <CardHeader>
            <CardTitle className='flex items-center justify-center text-2xl font-bold'>
              {errorType === "network" ? (
                <WifiOff className='mr-2 h-8 w-8 text-yellow-500' />
              ) : (
                <AlertCircle className='mr-2 h-8 w-8 text-red-500' />
              )}
              {errorType === "network" ? "Network Error" : "Checkout Error"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-center text-gray-600'>
              {errorType === "network"
                ? "Oops! It looks like you're offline. Please check your internet connection and try again."
                : "We're sorry, but we encountered an error while processing your checkout. Please try again later."}
            </p>
          </CardContent>
          <CardFooter className='flex justify-center'>
            <Button
              onClick={handleRetry}
              disabled={isRetrying}
              className='w-full max-w-xs'
            >
              {isRetrying ? (
                <RefreshCw className='mr-2 h-5 w-5 animate-spin' />
              ) : (
                <RefreshCw className='mr-2 h-5 w-5' />
              )}
              {isRetrying ? "Retrying..." : "Retry"}
            </Button>
          </CardFooter>
        </Card>
      </motion.div>
    </AnimatePresence>
  )
}
