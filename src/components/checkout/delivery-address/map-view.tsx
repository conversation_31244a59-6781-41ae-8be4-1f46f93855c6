"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useGoogleMaps } from "@/hooks/use-google-maps"
import { cn } from "@/lib/utils"
import type { MapViewProps } from "@/types/google-maps"
import { Crosshair, Loader2, MapPin, Search } from "lucide-react"
import { useCallback, useEffect } from "react"
import { MapErrorBoundary } from "./map-error-boundary"

export function MapView({
  onLocationSelect,
  isScriptLoaded,
  compact = false,
  fullAddress,
}: MapViewProps) {
  const {
    mapRef,
    addressDetails,
    isLoaded,
    getCurrentLocation,
    setIsLoaded,
    searchInputRef,
  } = useGoogleMaps()

  // Callback for selecting a location
  const handleLocationUpdate = useCallback(() => {
    if (!addressDetails) return
    const {
      lat,
      lng,
      formatted_address,
      city,
      state,
      pincode,
      area_details,
      landmark,
    } = addressDetails

    onLocationSelect({
      lat,
      lng,
      address: formatted_address,
      city,
      state,
      pincode,
      area_details,
      landmark,
    })
  }, [addressDetails, onLocationSelect])

  // Update location details when the map is fully loaded
  useEffect(() => {
    if (isLoaded) {
      handleLocationUpdate()
    }
  }, [isLoaded, handleLocationUpdate])

  // Set map as loaded when the script is ready
  useEffect(() => {
    if (isScriptLoaded) {
      setIsLoaded(true)
    }
  }, [isScriptLoaded, setIsLoaded])

  // this is needed to allow pointer events to go through, used to fix the google autocomplete input
  useEffect(() => {
    if (typeof window === "undefined") return
    setTimeout(() => (document.body.style.pointerEvents = ""), 0)
  }, [isLoaded])

  if (!isLoaded) {
    return (
      <div
        className='flex h-[300px] items-center justify-center bg-muted'
        role='progressbar'
        aria-label='Loading map'
      >
        <Loader2 className='h-6 w-6 animate-spin text-primary' />
      </div>
    )
  }

  return (
    <MapErrorBoundary>
      <div
        className={cn(
          "relative w-full overflow-hidden rounded-lg border-neutral-300 p-3 shadow-transparent",
          compact ? "h-[300px]" : "h-[calc(100vh-5rem)]",
        )}
      >
        {/* Search Input */}
        <div className='absolute left-4 right-4 top-4 z-10 rounded-2xl bg-gray-100 px-2'>
          <div className='relative flex items-center gap-3'>
            <Search className='text-neutral-700-foreground pointer-events-none absolute left-0 top-1/2 h-4 w-4 -translate-y-1/2 transform' />

            <Input
              ref={searchInputRef}
              id='pac-input'
              type='text'
              placeholder='Search your location...'
              className='w-full border-0 bg-transparent px-5 text-base ring-0 placeholder:text-gray-500 focus-visible:border-0'
              aria-label='Search for a location'
            />

            <Button
              onClick={getCurrentLocation}
              className='absolute right-0 top-1/2 h-4 min-h-8 w-4 min-w-8 -translate-y-1/2 transform'
              size='icon'
              aria-label='Get current location'
            >
              <Crosshair className='h-4 w-4' />
            </Button>
          </div>
        </div>

        {/* Map Container */}
        <div
          ref={mapRef}
          className='h-full w-full rounded-md border-neutral-500 md:rounded-lg'
          role='application'
          aria-label='Google Map'
        />

        {/* Location Information */}
        <div className='absolute bottom-4 left-4 right-4 z-10'>
          <div className='text-neutral-700-foreground flex items-center justify-center rounded-lg bg-background/90 p-2 text-sm backdrop-blur-sm'>
            <MapPin className='mr-2 h-4 w-4' />
            <span>
              {addressDetails?.formatted_address ||
                fullAddress ||
                "Drag the pin to adjust your location"}
            </span>
          </div>
        </div>
      </div>
    </MapErrorBoundary>
  )
}
