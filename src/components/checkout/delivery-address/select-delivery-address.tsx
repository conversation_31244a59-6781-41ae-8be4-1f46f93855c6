"use client"

import DeliveryAdd<PERSON><PERSON>ard from "@/components/custom/delivery-address-card"
import { AdaptiveWrapper } from "@/components/modals/adaptive-wrapper"
import { Button } from "@/components/ui/button"
import { generateFullAddress } from "@/functions/address-utils"
import { useUserStore } from "@/store/user-store"
import { Address } from "@/types/address"
import { PlusAddOutlinedIcon } from "sharepal-icons"

import { IconArrowRight } from "@/components/Icons"

interface SelecteDeliveryAddressProps {
  open: boolean
  isScriptLoaded: boolean
  addresses: Address[]
  onOpenChange: React.Dispatch<React.SetStateAction<boolean>>
  setSelectedAddress: React.Dispatch<React.SetStateAction<number | null>>
  selectedAddress: number | null
  setShowNewAddress: React.Dispatch<React.SetStateAction<boolean>>
  handleUseAddress: () => void
}

export function SelecteDeliveryAddress({
  open,
  onOpenChange,
  addresses,
  selectedAddress,
  setSelectedAddress,
  setShow<PERSON>ew<PERSON>ddress,
  handleUseAddress,
}: SelecteDeliveryAddressProps) {
  const { user } = useUserStore()

  return (
    <AdaptiveWrapper
      open={open}
      onOpenChange={onOpenChange}
      title='Select Delivery Address'
      className='max-md:max-h-[95%]'
    >
      <div className='relative flex flex-col justify-between'>
        <div className='h-full min-h-[80vh]'>
          <DeliveryAddressCard
            className='hide-scrollbar flex flex-col gap-4 p-4'
            options={addresses.map((address) => ({
              value: address.id.toString(),
              label: generateFullAddress(address),
              subLabel: `${address.city}, ${address.state}, ${address.pincode}`,
              userLabel:
                user?.first_name +
                " | " +
                (address.shipment_number || user?.calling_number),
            }))}
            showAll
            onChange={(value) => setSelectedAddress(Number(value))}
            selectedValue={selectedAddress?.toString() ?? ""}
          />
        </div>

        <div className='sticky bottom-0 z-10 flex w-full gap-2 bg-gray-100 px-2 py-4 shadow-xs max-md:flex-col md:gap-4 md:p-6'>
          <Button
            size='lg'
            variant={"outline-primary"}
            className='flex w-full items-center justify-center gap-2 text-bt2 text-primary-500'
            onClick={() => {
              setShowNewAddress(true)
            }}
          >
            <PlusAddOutlinedIcon className='h-5 w-5' />
            Add New Address
          </Button>
          {addresses.length > 0 && (
            <Button
              size='lg'
              variant={"primary"}
              className='flex w-full items-center justify-center gap-2 text-bt2 text-gray-100'
              onClick={() => {
                handleUseAddress()
                onOpenChange(false)
              }}
            >
              Use this Address
              <IconArrowRight />
            </Button>
          )}
        </div>
      </div>
      {/* </div> */}
    </AdaptiveWrapper>
  )
}
