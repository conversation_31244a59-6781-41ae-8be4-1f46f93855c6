// import * as z from 'zod'

// export const deliveryAddressSchema = z.object({
//   buildingName: z.string().min(1, 'Building name is required'),
//   streetAddress: z.string().min(1, 'Street address is required'),
//   city: z.string().min(1, 'City is required'),
//   state: z.string().min(1, 'State is required'),
//   pincode: z.string().min(6, 'Invalid pincode').max(6, 'Invalid pincode'),
//   deliveryInstructions: z.string().optional(),
//   isDefault: z.boolean().default(false),
//   latitude: z.number(),
//   longitude: z.number(),
//   fullAddress: z.string(),
// })

// export type DeliveryAddressFormData = z.infer<typeof deliveryAddressSchema>
