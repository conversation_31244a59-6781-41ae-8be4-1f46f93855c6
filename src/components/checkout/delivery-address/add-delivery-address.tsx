"use client"

import { IconPlus } from "@/components/Icons"
import { AdaptiveWrapper } from "@/components/modals/adaptive-wrapper"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  deliveryAddressSchema,
  type DeliveryAddressFormData,
} from "@/lib/validations/checkout"
import { useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { GoogleMapLocation } from "@/types/google-maps"
import { fetchWithAuth, fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { zodResolver } from "@hookform/resolvers/zod"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { AnimatePresence } from "framer-motion"
import { Loader2 } from "lucide-react"
import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { MapView } from "./map-view"
interface AddDeliveryAddressProps {
  open: boolean
  isScriptLoaded: boolean
  onOpenChange: React.Dispatch<React.SetStateAction<boolean>>
}

export function AddDeliveryAddress({
  open,
  onOpenChange,
  isScriptLoaded,
}: AddDeliveryAddressProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const formRef = useRef<HTMLFormElement>(null)
  const { user } = useUserStore()
  const { selectedCity } = useRentalStore()
  const queryClient = useQueryClient()

  // Memoize default form values
  const defaultValues = useMemo(
    () => ({
      user_email: user?.email || "",
      full_address: "",
      house_details: "",
      road: "",
      landmark: "",
      area_details: "",
      city: "",
      state: "",
      pincode: "",
      latitude: "",
      longitude: "",
      nickname: "",
      username: "",
      shipping_number: "",
    }),
    [user?.email],
  )

  const form = useForm<DeliveryAddressFormData>({
    resolver: zodResolver(deliveryAddressSchema),
    defaultValues,
    mode: "onChange",
  })

  const [showFields, setShowFields] = useState({
    pincode: false,
    city: false,
    state: false,
  })

  // Memoize availability check mutation
  const { mutate: checkAvailability } = useMutation({
    mutationFn: async (location: { latitude: string; longitude: string }) =>
      await fetchWithAuthPost<{ servicable: boolean }>(
        "https://api.sharepal.in/api:AIoqxnqr/address/availability-check-add",
        location,
      ),
    onSuccess: (data) => {
      if (!data.servicable) {
        toast.error("Address is not serviceable")
        return
      }
      setCurrentStep(2)
    },
    onError: () => {
      toast.error("Failed to check address serviceability")
    },
  })

  // Memoize location selection handler
  const handleLocationSelect = useCallback(
    async (location: GoogleMapLocation) => {
      const formValues = {
        latitude: location.lat.toString(),
        longitude: location.lng.toString(),
        full_address: location.address || "",
        city: location.city || "",
        state: location.state || "",
        pincode: location.pincode ? parseInt(location.pincode, 10) : "",
        area_details: location.area_details,
        landmark: location.landmark || "",
      }

      setShowFields({
        pincode: !formValues.pincode,
        city: !formValues.city,
        state: !formValues.state,
      })

      // Batch form updates
      Object.entries(formValues).forEach(([key, value]) => {
        form.setValue(
          key as keyof DeliveryAddressFormData,
          typeof value === "number" ? value.toString() : value,
        )
      })

      checkAvailability({
        latitude: location.lat.toString(),
        longitude: location.lng.toString(),
      })
    },
    [form, checkAvailability],
  )

  // Memoize address submission mutation
  const { mutate: handleAddressSubmit, isPending: isSubmitting } = useMutation({
    mutationFn: async (data: DeliveryAddressFormData) => {
      const addressData = {
        email: data.user_email,
        whatsapp: user?.whatsapp_number,
        user_id: user?.id,
        city: selectedCity.city_url,
        area_details: data.area_details,
        house_details: data.house_details,
        latitude: data.latitude,
        longitude: data.longitude,
        location: data.area_details,
        road: data.road,
        landmark: data.landmark,
        pincode: data.pincode,
        state: data.state,
        username: data.username,
        active: true,
        shipping_number: data.shipping_number,
        transit_address: false,
      }

      return await fetchWithAuth(
        "https://api.sharepal.in/api:AIoqxnqr/user/add-address",
        {
          method: "POST",
          body: JSON.stringify(addressData),
        },
      )
    },
    onSuccess: () => {
      onOpenChange(false)
      toast.success("Address Added Successfully")
      queryClient.invalidateQueries({ queryKey: ["user_addresses"] })
      form.reset(defaultValues)
    },
    onError: (error) => {
      toast.error(JSON.parse(error.message)?.message ?? "Something Went Wrong")
    },
  })

  // Memoize submit handler
  const onSubmit = useCallback(
    (data: DeliveryAddressFormData) => {
      handleAddressSubmit(data)
    },
    [handleAddressSubmit],
  )

  // Reset form and step when modal opens
  useEffect(() => {
    if (open) {
      setCurrentStep(1)
      form.reset(defaultValues)
    }
  }, [open, form, defaultValues])

  // Memoize form fields to prevent unnecessary re-renders
  // const formFields = useMemo(
  //   () => (
  //     <div className='space-y-4 p-6'>
  //       <FormField
  //         control={form.control}
  //         name='house_details'
  //         render={({ field }) => (
  //           <FormItem>
  //             <FormLabel>
  //               House / Flat / Block No. <span className='text-red-500'>*</span>
  //             </FormLabel>
  //             <FormControl>
  //               <Input {...field} placeholder='e.g., A-101' />
  //             </FormControl>
  //             <FormMessage />
  //           </FormItem>
  //         )}
  //       />

  //       <FormField
  //         control={form.control}
  //         name='road'
  //         render={({ field }) => (
  //           <FormItem>
  //             <FormLabel>Apartment / Road / Area</FormLabel>
  //             <FormControl>
  //               <Input {...field} placeholder='Apartment / Road / Area' />
  //             </FormControl>
  //             <FormMessage />
  //           </FormItem>
  //         )}
  //       />

  //       <FormField
  //         control={form.control}
  //         name='shipping_number'
  //         render={({ field }) => (
  //           <FormItem>
  //             <FormLabel className='flex gap-2'>
  //               Shipping Contact Number
  //               <span className='text-red-500'>*</span>
  //             </FormLabel>
  //             <FormControl>
  //               <Input {...field} placeholder='Shipping Contact Number' />
  //             </FormControl>
  //             <FormMessage />
  //           </FormItem>
  //         )}
  //       />
  //     </div>
  //   ),
  //   [form.control],
  // )

  return (
    <AdaptiveWrapper
      open={open}
      onOpenChange={onOpenChange}
      title='Add New Delivery Address'
      className='h-screen'
      autoClose
    >
      <div className='flex h-full w-full flex-col md:min-w-[576px]'>
        <AnimatePresence mode='wait'>
          {/* <motion.div
            key={`step-${currentStep}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
            className='flex-1 space-y-5'
          > */}
          <MapView
            onLocationSelect={handleLocationSelect}
            isScriptLoaded={isScriptLoaded}
            compact={currentStep === 2}
            fullAddress={form.getValues("full_address")}
          />
          {currentStep === 2 && (
            <Form {...form}>
              <form
                ref={formRef}
                onSubmit={form.handleSubmit(onSubmit)}
                className='space-y-6'
              >
                <div className='space-y-4 p-6'>
                  <FormField
                    control={form.control}
                    name='house_details'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          House / Flat / Block No.{" "}
                          <span className='text-red-500'>*</span>
                        </FormLabel>
                        <FormControl>
                          <Input {...field} placeholder='e.g., A-101' />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='road'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Apartment / Road / Area</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Apartment / Road / Area'
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='shipping_number'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='flex gap-2'>
                          Shipping Contact Number
                          {/* Remove required asterisk since it's optional */}
                        </FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder='Shipping Contact Number (Optional)'
                            onChange={(e) => {
                              const value = e.target.value
                              // Only set value if it's empty or contains only digits up to 10
                              if (!value || /^\d{0,10}$/.test(value)) {
                                field.onChange(value)
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {showFields.pincode && (
                    <FormField
                      control={form.control}
                      name='pincode'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className='flex gap-2'>
                            Pincode
                            {/* Remove required asterisk since it's optional */}
                            <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder='Pincode'
                              onChange={(e) => {
                                const value = e.target.value
                                // Only set value if it's empty or contains only digits up to 10
                                if (!value || /^\d{0,6}$/.test(value)) {
                                  field.onChange(value)
                                }
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {showFields.city && (
                    <FormField
                      control={form.control}
                      name='city'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            City <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input {...field} placeholder='City' />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                  {showFields.state && (
                    <FormField
                      control={form.control}
                      name='state'
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            State <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl>
                            <Input {...field} placeholder='State' />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
                <div className='sticky bottom-0 w-full bg-neutral-50 p-5'>
                  <Button
                    type='submit'
                    variant='primary'
                    className='w-full py-5'
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                        Saving...
                      </>
                    ) : (
                      <>
                        <IconPlus />
                        Save Address
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          )}
          {/* </motion.div> */}
        </AnimatePresence>
      </div>
    </AdaptiveWrapper>
  )
}
