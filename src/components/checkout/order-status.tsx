// 'use client'

// import { motion } from 'framer-motion'
// import { AlertCircle, Check, X } from 'lucide-react'
// import { Button } from '@/components/ui/button'
// import { OrderDetails, OrderStatus } from './order-confirmation'
// import Link from 'next/link'

// const statusConfig = {
//   confirmed: {
//     icon: Check,
//     iconColor: 'bg-green-500',
//     title: 'Thank you',
//     heading: 'Your Order is Confirmed!',
//   },
//   awaiting: {
//     icon: AlertCircle,
//     iconColor: 'bg-orange-500',
//     title: 'Uh-oh',
//     heading: 'Your Order is Awaiting Confirmation!',
//   },
//   cancelled: {
//     icon: X,
//     iconColor: 'bg-red-500',
//     title: 'Oh-no',
//     heading: 'Your Order got Cancelled!',
//   },
// } as const

// export default function OrderStatusPage({
//   status,
//   orderData,
// }: {
//   status: OrderStatus
//   orderData: OrderDetails
// }) {
//   const config = statusConfig[status]

//   return (
//     <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 px-4 py-12">
//       <motion.div
//         initial={{ scale: 0 }}
//         animate={{ scale: 1 }}
//         transition={{ type: 'spring', duration: 0.5 }}
//         className={`h-16 w-16 ${config.iconColor} mb-6 flex items-center justify-center rounded-full`}
//       >
//         <config.icon className="h-8 w-8 text-white" />
//       </motion.div>

//       <motion.div
//         initial={{ opacity: 0, y: 20 }}
//         animate={{ opacity: 1, y: 0 }}
//         transition={{ delay: 0.2 }}
//         className="max-w-xl text-center"
//       >
//         <h1 className="mb-2 text-xl text-gray-800">
//           {config.title}, {orderData.customerName}!
//         </h1>
//         <h2 className="mb-4 text-4xl font-bold text-gray-900">
//           {config.heading}
//         </h2>
//         <p className="mb-4 text-gray-600">Order No: {orderData.orderNumber}</p>

//         {status === 'awaiting' && (
//           <>
//             <p className="mb-4 text-gray-600">
//               We&apos;re still waiting for payment confirmation from your bank.
//               Sometimes, it takes a little while for banks to process
//               transactions.
//             </p>
//             <p className="mb-8 text-gray-600">
//               If we receive confirmation within the next hour, your order will
//               be automatically confirmed.
//             </p>
//             <motion.div
//               initial={{ opacity: 0, y: 20 }}
//               animate={{ opacity: 1, y: 0 }}
//               transition={{ delay: 0.3 }}
//             >
//               <Button
//                 className="mb-4 w-full rounded-full bg-blue-600 py-6 text-lg text-white hover:bg-blue-700"
//                 onClick={() => console.log('View order details')}
//               >
//                 View Order Details
//               </Button>
//             </motion.div>
//           </>
//         )}

//         {status === 'cancelled' && (
//           <>
//             <p className="mb-4 text-gray-600">
//               Your order was cancelled due to a transaction failure. Any
//               deducted amount will be automatically refunded to your payment
//               source within 3 to 7 days.
//             </p>
//             <p className="mb-8 text-gray-600">
//               Please try again with a different payment method.
//             </p>
//             <motion.div
//               initial={{ opacity: 0, y: 20 }}
//               animate={{ opacity: 1, y: 0 }}
//               transition={{ delay: 0.3 }}
//               className="space-y-4"
//             >
//               <Link href={'/checkout'}>
//                 <Button
//                   className="w-full rounded-full bg-blue-600  py-6 text-lg text-white hover:bg-blue-700"
//                   onClick={() => console.log('Retry payment')}
//                 >
//                   Retry Payment
//                 </Button>
//               </Link>

//               <Button
//                 variant="secondary"
//                 className="w-full rounded-full py-6 text-lg"
//                 onClick={() => console.log('Go to cart')}
//               >
//                 Go to My Cart
//               </Button>
//             </motion.div>
//           </>
//         )
//         }
//       </motion.div >

//       <motion.a
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//         transition={{ delay: 0.6 }}
//         href="#"
//         className="mt-8 text-blue-600 hover:underline"
//       >
//         Need help? Contact us
//       </motion.a>
//     </div >
//   )
// }
