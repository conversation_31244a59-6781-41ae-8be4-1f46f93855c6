"use client"

import { cn } from "@/lib/utils"
import { motion } from "framer-motion"

interface SectionCardProps {
  children: React.ReactNode
  className?: string
}

export function SectionCard({ children, className }: SectionCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={cn(
        "rounded-xl border-neutral-150 bg-gray-100 md:rounded-2xl",
        "py-4 transition-shadow duration-200 md:p-6 md:pt-2",
        // 'md:rounded-radius rounded-xl md:border border-neutral-150 bg-gray-100  ',
        // 'transition-shadow duration-200 ',
        className,
      )}
    >
      {children}
    </motion.div>
  )
}
