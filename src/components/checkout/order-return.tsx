"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { DealAdditionalDetails, RentOrder } from "@/types"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"

import { fetchOrderDetails, fetchOrderWithoutCart } from "@/actions/orders"
import usePlaceOrder from "@/hooks/use-place-order"
import { OrderSummary } from "@/types/order"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import Link from "next/link"
import { useParams, useSearchParams } from "next/navigation"
import React from "react"
import { IconCancelled, IconSuccess, IconWarning } from "../Icons/checkout"
import CheckoutRedirectLoading from "../loadings/checkout-redirect-loading"
import { Skeleton } from "../ui/skeleton"
import { Typo<PERSON> } from "../ui/typography"

const OrderProcessedInfo = ({
  order,
  orderDetails,
  transaction = true,
}: {
  order: RentOrder
  orderDetails: OrderSummary
  transaction?: boolean
}) => (
  <Card className='rounded-lg px-3 py-2 sm:rounded-xl sm:px-4 sm:py-3 md:rounded-2xl md:px-6 md:py-5'>
    <CardHeader className='flex flex-row items-center justify-between space-y-0 p-0 pb-4'>
      <CardTitle className='text-base font-bold text-gray-900 sm:text-lg md:text-xl'>
        Return Scheduled!
      </CardTitle>
      {/* <span className='text-sm text-gray-500 sm:text-base md:text-lg'>
        #{order.order_id}
      </span> */}
    </CardHeader>
    <Separator />
    <CardContent className='flex w-full flex-col items-center justify-start gap-4 p-0 pt-4 sm:gap-5'>
      <div className='grid w-full grid-cols-1 items-start justify-start gap-4 sm:grid-cols-2'>
        <div>
          <h3 className='mb-2 text-sm font-semibold text-neutral-900 sm:text-base'>
            Pickup Details
          </h3>
          <div className='text-xs text-neutral-500 sm:text-sm'>
            <p className=''>{order.first_name}</p>
            <p className=''>{orderDetails.shipment_calling_number} </p>
            <p className=''>{order.email}</p>
          </div>
        </div>
        <div>
          <h3 className='mb-2 text-sm font-semibold text-neutral-900 sm:text-base'>
            Pickup Address
          </h3>
          <p className='text-wrap break-words text-xs text-neutral-500 sm:text-sm'>
            {order.delivery_address}
          </p>
        </div>
      </div>
      {transaction && (
        <div className='grid w-full grid-cols-1 items-start justify-start gap-4 sm:grid-cols-2'>
          <div>
            <h3 className='mb-2 text-sm font-semibold text-neutral-900 sm:text-base'>
              Transaction Details
            </h3>
            <p className='flex items-center gap-1 text-xs text-gray-700 sm:text-sm'>
              Transaction ID:- {order.rzp_order_id}
            </p>
          </div>
        </div>
      )}
    </CardContent>
  </Card>
)

const CustomWrapperCard = ({
  icon,
  title_1,
  title_2,
  title_3,
  title_4,
  children,
  order_id,
  showNeedHelp = true,
}: {
  title_1: string
  title_2: string
  title_3: string
  title_4: string
  order_id: string
  icon: React.ReactNode
  showNeedHelp?: boolean
  children: React.ReactNode
}) => (
  <div className='mx-auto flex max-w-2xl flex-col items-center justify-center gap-4 rounded-xl bg-gray-100 px-4 py-6 sm:gap-5 sm:rounded-2xl sm:px-12 sm:py-8 md:rounded-3xl md:px-16 md:py-10'>
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", duration: 0.5 }}
    >
      {icon}
    </motion.div>

    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className='w-full space-y-3 text-center md:space-y-4'
    >
      <Typography as='h3' className='!text-sh5 md:!text-sh3'>
        {title_1}
      </Typography>

      <Typography as='h1' className='!text-h4 text-gray-900 md:!text-h1'>
        {title_2}
      </Typography>

      <Typography as='p' className='w-full !text-b6 text-gray-600 md:!text-b4'>
        {title_3}
      </Typography>

      <Typography as='p' className='!text-b4 text-gray-700 md:!text-b2'>
        {title_4}
      </Typography>
    </motion.div>

    {children}

    {showNeedHelp && (
      <motion.a
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        href={
          "https://api.whatsapp.com/send?phone=+917619220543&text=Hi Need support for" +
          order_id
        }
        className='mt-8 text-bt2 text-blue-600 hover:underline'
      >
        Need help? Contact us
      </motion.a>
    )}
  </div>
)

const ReturnPaymentSuccess = ({
  order,
  shimentData,
  orderDetails,
}: ReturnStatusType) => (
  <CustomWrapperCard
    title_1={`Thank you, ${order.first_name}!`}
    title_2={"Return Scheduled!"}
    order_id={order.order_id}
    title_3={`Order No: #${order.order_id}`}
    title_4={`Your return has been scheduled for ${shimentData?.schedule_date ?? ""} at ${shimentData?.schedule_time ?? ""} Please be available to return your parcel at the scheduled time and location to avoid the hassles of rescheduling.`}
    icon={<IconSuccess className='h-16 w-16 md:h-24 md:w-24' />}
  >
    <Button
      variant={"primary"}
      size={"lg"}
      // className='w-full'
      // onClick={() => console.log("View order details")}
    >
      <Link href={`/dashboard/orders/${order?.order_id}?open=RETURN_STATUS`}>
        View Return Status
      </Link>
    </Button>

    <OrderProcessedInfo orderDetails={orderDetails} order={order} />
  </CustomWrapperCard>
)

const ReturnPayLater = ({
  order,
  shimentData,
  orderDetails,
  hanldePaymentForReturnOrder,
}: ReturnStatusType) => (
  <CustomWrapperCard
    title_1={`Thank you, ${order.first_name}!`}
    title_2={"Return Scheduled!"}
    title_3={`Order No: #${order.order_id}`}
    title_4={`Your return has been scheduled for ${shimentData?.schedule_date ?? ""} at ${shimentData?.schedule_time ?? ""} IST.Please be available to return your parcel at the scheduled time and location to avoid the hassles of rescheduling.`}
    order_id={order.order_id}
    icon={<IconSuccess className='h-16 w-16 md:h-24 md:w-24' />}
  >
    <Button
      variant={"primary"}
      size={"lg"}
      // className='w-full'
      onClick={hanldePaymentForReturnOrder}
    >
      Make Pending Payment{" "}
    </Button>
    <Button
      variant={"normal"}
      className='flex h-max w-full flex-col rounded-full bg-violet-200 text-center hover:bg-violet-300'
    >
      <Link href={`/dashboard/orders/` + order.order_id}>Pay Later</Link>
    </Button>
    <OrderProcessedInfo
      orderDetails={orderDetails}
      order={order}
      transaction={false}
    />
  </CustomWrapperCard>
)

const ReturnWithoutPayment = ({
  order,
  shimentData,
  orderDetails,
}: ReturnStatusType) => (
  <CustomWrapperCard
    title_1={`Thank you, ${order.first_name}!`}
    title_2={"Return Scheduled!"}
    title_3={`Order No: #${order.order_id}`}
    title_4={`Your return has been scheduled for ${shimentData?.schedule_date ?? ""} at ${shimentData?.schedule_time ?? ""} IST. Please be available to return your parcel at the scheduled time and location to avoid the hassles of rescheduling.`}
    icon={<IconSuccess className='h-16 w-16 md:h-24 md:w-24' />}
    order_id={order.order_id}
  >
    <Button
      variant={"primary"}
      size={"lg"}
      // onClick={() => console.log("View order details")}
    >
      <Link href={`/dashboard/orders/${order?.order_id}?open=RETURN_STATUS`}>
        View Return Status{" "}
      </Link>
    </Button>
    <OrderProcessedInfo
      orderDetails={orderDetails}
      order={order}
      transaction={false}
    />
  </CustomWrapperCard>
)

const ReturnPaymentFailed = ({
  order,
  hanldePaymentForReturnOrder,
}: ReturnStatusType) => (
  <CustomWrapperCard
    title_1='Payment didn’t go through, but don’t worry!'
    title_2={"Return Scheduled Failed!"}
    title_3={` Order No: #${order.order_id}`}
    order_id={order.order_id}
    title_4={`There was a transaction failure but your return was scheduled.
  `}
    icon={<IconCancelled className='h-16 w-16 md:h-24 md:w-24' />}
    showNeedHelp={false}
  >
    <Typography as='p' className='!text-b4 text-gray-700 md:!text-b2'>
      Any deducted amount will be automatically refunded to your payment source
      within 3 to 7 days.
    </Typography>
    <Typography as='p' className='!text-b4 text-gray-700 md:!text-b2'>
      Try again with a different payment method or opt to pay later.
    </Typography>
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className='mx-auto flex w-full max-w-sm flex-col items-center justify-center gap-3 sm:gap-4'
    >
      <Button
        onClick={hanldePaymentForReturnOrder}
        variant={"primary"}
        size={"lg"}
        className='w-full'
      >
        Retry Payment
      </Button>
      <Button
        variant={"normal"}
        className='flex h-max w-full flex-col rounded-full bg-violet-200 text-center hover:bg-violet-300'
      >
        <Link href={`/dashboard/orders/` + order.order_id}>Pay Later</Link>
      </Button>
    </motion.div>
  </CustomWrapperCard>
)

export const ReturnNotAvailable = ({ order_id }: { order_id: string }) => (
  <CustomWrapperCard
    title_1='Uh-oh, something’s changed!'
    title_2={"Order not Due for Return!"}
    title_3={` Order No: #${order_id}`}
    title_4={`Your order is not due for return. Please contact our support team.
  `}
    order_id={order_id}
    icon={<IconWarning className='h-16 w-16 md:h-24 md:w-24' />}
  >
    <Typography
      as='p'
      className='!text-b4 text-gray-700 md:!text-b2'
    ></Typography>
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className='mx-auto flex w-full max-w-sm flex-col items-center justify-center gap-3 sm:gap-4'
    >
      <Button
        variant={"primary"}
        size={"lg"}
        // onClick={() => console.log("View order details")}
      >
        <Link href={`/dashboard/orders/${order_id}`}>View Order Details </Link>
      </Button>
    </motion.div>
  </CustomWrapperCard>
)

export const ReturnAlreadyBooked = ({ order_id }: { order_id: string }) => (
  <CustomWrapperCard
    title_1='The Shipment is already booked!'
    title_2={"Order Shipment Already Booked!"}
    title_3={`Order No: #${order_id}`}
    title_4={``}
    order_id={order_id}
    icon={<IconWarning className='h-16 w-16 md:h-24 md:w-24' />}
  >
    <Typography
      as='p'
      className='!text-b4 text-gray-700 md:!text-b2'
    ></Typography>
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className='mx-auto flex w-full max-w-sm flex-col items-center justify-center gap-3 sm:gap-4'
    >
      <Button
        variant={"primary"}
        size={"lg"}
        // onClick={() => console.log("View order details")}
      >
        <Link href={`/dashboard/orders/${order_id}`}>View Order Details </Link>
      </Button>
    </motion.div>
  </CustomWrapperCard>
)

export const ReschedulePaymentSuccess = ({
  order,
  orderDetails,
  shimentData,
}: ReturnStatusType) => (
  <CustomWrapperCard
    title_1={`Thank you, ${order.first_name}!`}
    title_2={"Return Pickup Time Updated!!"}
    order_id={order.order_id}
    title_3={`Order No: #${order.order_id}`}
    title_4={`Your return has been scheduled for ${shimentData?.schedule_date ?? ""} at ${shimentData?.schedule_time ?? ""} Please be available to return your parcel at the scheduled time and location to avoid the hassles of rescheduling.`}
    icon={<IconSuccess className='h-16 w-16 md:h-24 md:w-24' />}
  >
    <Button
      variant={"primary"}
      size={"lg"}
      // className='w-full'
      // onClick={() => console.log("View order details")}
    >
      <Link href={`/dashboard/orders/${order?.order_id}?open=RETURN_STATUS`}>
        View Return Status
      </Link>
    </Button>

    <OrderProcessedInfo orderDetails={orderDetails} order={order} />
  </CustomWrapperCard>
)

export const ReschedulePaymentFailed = ({
  order,
  hanldePaymentForReturnOrder,
}: ReturnStatusType) => (
  <CustomWrapperCard
    title_1='Payment didn’t go through, but don’t worry!'
    title_2={"Return Scheduled!"}
    title_3={` Order No: #${order.order_id}`}
    order_id={order.order_id}
    title_4={`There was a transaction failure but your return was scheduled.
  `}
    icon={<IconCancelled className='h-16 w-16 md:h-24 md:w-24' />}
    showNeedHelp={false}
  >
    <Typography as='p' className='!text-b4 text-gray-700 md:!text-b2'>
      Any deducted amount will be automatically refunded to your payment source
      within 3 to 7 days.
    </Typography>
    <Typography as='p' className='!text-b4 text-gray-700 md:!text-b2'>
      Try again with a different payment method or opt to pay later.
    </Typography>
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className='mx-auto flex w-full max-w-sm flex-col items-center justify-center gap-3 sm:gap-4'
    >
      <Button
        onClick={hanldePaymentForReturnOrder}
        variant={"primary"}
        size={"lg"}
        className='w-full'
      >
        Retry Payment
      </Button>
      <Button
        variant={"normal"}
        className='flex h-max w-full flex-col rounded-full bg-violet-200 text-center hover:bg-violet-300'
      >
        <Link href={`/dashboard/orders/` + order.order_id}>Pay Later</Link>
      </Button>
    </motion.div>
  </CustomWrapperCard>
)

export type ReturnStatusType = {
  order: RentOrder
  orderDetails: OrderSummary
  hanldePaymentForReturnOrder?: () => void
  shimentData?:
    | {
        schedule_date: string
        schedule_time: string
      }
    | null
    | undefined
}

export type ReturnStatus =
  | "payment_failed"
  | "payment_success"
  | "without_payment"
  | "paylater"
  | "reschedule_payment_success"
  | "reschedule_payment_failed"

const componentMap = {
  payment_failed: ReturnPaymentFailed,
  payment_confirmed: ReturnPaymentSuccess,
  paylater: ReturnPayLater,
  without_payment: ReturnWithoutPayment,
  reschedule_payment_success: ReschedulePaymentSuccess,
  reschedule_payment_failed: ReturnPaymentFailed,
} as const

export default function OrderReturn() {
  const { order_id } = useParams<{ order_id: string }>()
  const { hanldePaymentForReturnOrder, isReturnPaymentLoading } =
    usePlaceOrder()
  const searchParams = useSearchParams()
  const status = searchParams.get("status") as keyof typeof componentMap | null

  const { data: order } = useQuery({
    queryKey: ["order-details-fetch", order_id],
    queryFn: () => fetchOrderDetails(order_id),
    refetchOnWindowFocus: true,
  })

  const {
    data: orderData,
    isPending,
    isFetching,
    isLoading,
  } = useQuery({
    queryKey: ["fetch-order-return-data", order_id],
    queryFn: () => fetchOrderWithoutCart(order_id),
  })

  const {
    data: orderDealAdditonalDetails,
    isLoading: isDealAddtionalDetailsLoading,
  } = useQuery({
    queryKey: ["fetch-order-return-additional-details", order_id],
    queryFn: () =>
      fetchWithAuthPost<DealAdditionalDetails>(
        "https://api.sharepal.in/api:AIoqxnqr/orders/additional-details",
        { order_id },
      ),
  })

  const { data: shimentData, isLoading: shimentDataLoading } = useQuery({
    queryKey: ["alreadybooked-check", order_id],
    queryFn: async () =>
      fetchWithAuthPost<{
        schedule_date: string
        schedule_time: string
      } | null>(
        "https://api.sharepal.in/api:AIoqxnqr/shipment/schedule-check",
        { order_id },
      ),
  })

  if (
    isPending ||
    isFetching ||
    isLoading ||
    isDealAddtionalDetailsLoading ||
    shimentDataLoading
  ) {
    return <Skeleton className='h-96 w-full rounded-3xl' />
  }

  if (!orderData || !status || !componentMap[status] || !order) return null

  const Component = componentMap[status]
  if (isReturnPaymentLoading) return <CheckoutRedirectLoading />
  return (
    <Component
      orderDetails={order}
      hanldePaymentForReturnOrder={() => {
        if (orderDealAdditonalDetails?.total_return_charges)
          hanldePaymentForReturnOrder({
            order_id,
            amount: orderDealAdditonalDetails?.total_return_charges,
          })
      }}
      order={orderData.order}
      shimentData={shimentData}
    />
  )
}
