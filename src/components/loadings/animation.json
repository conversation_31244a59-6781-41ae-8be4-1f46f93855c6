{"v": "5.12.2", "fr": 60, "ip": 0, "op": 241, "w": 700, "h": 700, "nm": "Target", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Null 48", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 77, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 81, "s": [4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [-4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 88, "s": [-3]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 91, "s": [3]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 93, "s": [-2]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 95, "s": [2]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [-1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 98, "s": [1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 99, "s": [-1]}, {"t": 100, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.5, "y": 1}, "o": {"x": 0.764, "y": 0}, "t": 60, "s": [-49.99, 635.057, 0], "to": [73.785, -45.427, 0], "ti": [-73.785, 45.427, 0]}, {"t": 80, "s": [392.721, 362.495, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [100, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [80, 80, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 19", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-194.497, 179.73, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.297, 0.775], [7.883, 8.606], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-10.195, 13.829], [0, 0], [2.153, 8.259]], "o": [[-11.137, -3.759], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [4.134, -16.676], [0, 0], [0, 0], [-2.608, -10.004]], "v": [[48.184, -50.241], [17.693, -71.629], [-62.68, -25.101], [-13.339, 1.111], [-13.272, 1.227], [-53.97, 71.629], [32.547, 21.548], [33.294, 18.535], [54.998, -27.67], [54.998, -27.67], [62.363, -40.282]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219607858097, 0.803921628466, 0.427451010311, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 18", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-53.634, 89.379, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.009, 3.288], [4.682, 2.703], [1.557, -1.347], [0, 0], [0, 0], [0.007, -2.622], [-4.552, -2.628], [-1.516, 1.237], [0, 0]], "o": [[0.015, -5.367], [-2.689, -1.552], [0, 0], [0, 0], [-1.508, 0.848], [-0.015, 5.23], [2.562, 1.479], [0, 0], [2.174, -0.512]], "v": [[155.843, -79.422], [147.401, -94.044], [140.762, -94.185], [-153.413, 74.497], [-153.39, 74.538], [-155.843, 79.845], [-147.635, 94.073], [-141.267, 94.29], [152.232, -73.538]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 1", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-217.466, 177.955, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [252.936, 766.074, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[51.167, -16.382], [55.827, -33.7], [-55.827, 30.19], [-35.345, 33.7]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [257.291, 805.758], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[50.422, -68.099], [29.951, -71.629], [-50.422, -25.102], [-1.081, 1.111], [-1.013, 1.07], [-41.712, 71.629], [44.805, 21.548]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [243.176, 764.319], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Layer 17", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [388.512, 366.538, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [0, 0, 100]}, {"t": 60, "s": [80, 80, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, time_max, n, t, t, v;\namp = 0.1;\nfreq = 2;\ndecay = 5;\nn = 0;\n$bm_rt = time_max = 4;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < time_max) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.083, -29.352], [-25.544, -14.748], [-0.083, 29.352], [25.544, 14.748]], "o": [[-25.544, -14.748], [-0.083, 29.352], [25.544, 14.748], [0.083, -29.352], [0, 0]], "v": [[0.151, -53.149], [-46.254, -26.705], [-0.151, 53.149], [46.254, 26.705], [0.151, -53.149]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Layer 16", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [388.512, 366.538, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 20, "s": [0, 0, 100]}, {"t": 50, "s": [80, 80, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, time_max, n, t, t, v;\namp = 0.1;\nfreq = 2;\ndecay = 5;\nn = 0;\n$bm_rt = time_max = 4;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < time_max) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.173, -61.022], [-53.105, -30.66], [-0.173, 61.022], [53.105, 30.66]], "o": [[-53.105, -30.66], [-0.173, 61.022], [53.105, 30.66], [0.173, -61.022], [0, 0]], "v": [[0.313, -110.488], [-96.154, -55.515], [-0.313, 110.488], [96.154, 55.515], [0.313, -110.488]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.219607843137, 0.803921568627, 0.427450980392, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Layer 15", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [388.512, 366.538, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 10, "s": [0, 0, 100]}, {"t": 40, "s": [80, 80, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, time_max, n, t, t, v;\namp = 0.1;\nfreq = 2;\ndecay = 5;\nn = 0;\n$bm_rt = time_max = 4;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < time_max) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-81.334, -46.958], [0.265, -93.468], [81.342, 46.963], [-0.265, 93.459]], "o": [[81.342, 46.963], [-0.265, 93.459], [-81.334, -46.958], [0.265, -93.468]], "v": [[0.476, -169.234], [147.277, 85.035], [-0.484, 169.231], [-147.277, -85.025]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Layer 14", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [388.512, 366.538, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [0, 0, 100]}, {"t": 30, "s": [80, 80, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, time_max, n, t, t, v;\namp = 0.1;\nfreq = 2;\ndecay = 5;\nn = 0;\n$bm_rt = time_max = 4;\nif (numKeys > 0) {\n    $bm_rt = n = nearestKey(time).index;\n    if (key(n).time > time) {\n        n--;\n    }\n}\nif (n == 0) {\n    $bm_rt = t = 0;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n}\nif (n > 0 && t < time_max) {\n    v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-110.276, -63.668], [0.359, -126.716], [110.276, 63.668], [-0.359, 126.716]], "o": [[110.276, 63.668], [-0.359, 126.716], [-110.276, -63.668], [0.359, -126.716]], "v": [[0.647, -229.439], [199.676, 115.285], [-0.654, 229.438], [-199.676, -115.281]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.98431372549, 0.760784313725, 0.149019607843, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Layer 13", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [50.139, -19.001, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.024, 1.786], [0.012, 0.535], [0.024, 0.668], [0.024, 0.608], [0.024, 0.632], [0.085, 1.385], [0.024, 0.462], [0.012, 0.122], [0.146, 1.726], [0.036, 0.425], [0.097, 1.106], [0.036, 0.352], [0.061, 0.632], [0.146, 1.288], [0.073, 0.632], [0.049, 0.377], [0.146, 1.021], [0.073, 0.498], [0.279, 1.823], [0.012, 0.073], [0.061, 0.377], [0.267, 1.519], [0.122, 0.644], [0.146, 0.741], [0.097, 0.523], [0.134, 0.729], [0.267, 1.252], [0.146, 0.681], [0.061, 0.28], [0.279, 1.179], [0.109, 0.474], [0.45, 1.847], [0.024, 0.097], [0.122, 0.498], [0.389, 1.446], [0.182, 0.693], [0.231, 0.79], [0.134, 0.462], [0.219, 0.741], [0.365, 1.227], [0.219, 0.729], [0.109, 0.352], [0.316, 1.009], [0.182, 0.571], [0.62, 1.884], [0.012, 0.036], [1.082, 3.014], [0.121, 0.352], [0.097, 0.292], [1.106, 2.844], [0.049, 0.122], [0.121, 0.328], [1.057, 2.54], [0.146, 0.352], [0.085, 0.194], [1.215, 2.759], [0.097, 0.207], [0.17, 0.365], [1.203, 2.576], [0.122, 0.267], [0.049, 0.097], [1.531, 3.099], [0.158, 0.316], [0.194, 0.389], [0.51, 0.997], [0.522, 0.997], [0.535, 0.984], [0.328, 0.608], [0.146, 0.279], [1.434, 2.564], [0.194, 0.352], [1.337, 2.273], [0.207, 0.352], [0.219, 0.377], [0.62, 1.033], [0.571, 0.936], [0.632, 1.021], [0.571, 0.911], [0.644, 1.009], [0.595, 0.899], [0.668, 1.009], [0.438, 0.632], [0.121, 0.17], [1.823, 2.613], [0.377, 0.535], [1.945, 2.637], [0.036, 0.049], [1.373, 1.823], [0.474, 0.62], [0.96, 1.227], [0.122, 0.158], [0.182, 0.219], [2.394, 2.892], [0.024, 0.024], [0.049, 0.061], [2.37, 2.71], [0.182, 0.207], [0.085, 0.097], [2.297, 2.504], [0.231, 0.243], [0.328, 0.34], [0.608, 0.644], [0.826, 0.863], [0.62, 0.632], [0.34, 0.352], [0.498, 0.498], [0.644, 0.632], [0.863, 0.839], [0.632, 0.608], [0.207, 0.194], [0.681, 0.644], [0.632, 0.583], [0.936, 0.851], [0.595, 0.535], [0.097, 0.085], [1.033, 0.911], [0.413, 0.352], [1.531, 1.288], [0.024, 0.012], [0.316, 0.267], [1.227, 0.997], [0.559, 0.45], [0.826, 0.656], [0.182, 0.146], [0.595, 0.45], [0.972, 0.741], [0.632, 0.474], [0.547, 0.401], [0.413, 0.304], [0.632, 0.45], [0.972, 0.681], [0.62, 0.425], [0.279, 0.194], [0.802, 0.547], [0.474, 0.316], [1.556, 0.984], [0.024, 0.012], [0.182, 0.109], [1.385, 0.851], [0.523, 0.316], [0.693, 0.401], [0.656, 0.377], [1.944, 1.057], [0.17, 0.097], [1.215, 0.632], [0.122, 0.073], [1.373, 0.681], [0.182, 0.085], [0.462, 0.219], [1.106, 0.51], [0.535, 0.243], [0.413, 0.194], [0.668, 0.292], [0.486, 0.194], [1.519, 0.608], [1.507, 0.559], [0.486, 0.17], [0.681, 0.243], [0.365, 0.122], [0.547, 0.182], [0.997, 0.316], [0.51, 0.158], [0.243, 0.073], [0.814, 0.243], [0.462, 0.122], [1.47, 0.377], [0.024, 0.012], [0.194, 0.049], [1.288, 0.279], [0.51, 0.097], [0.717, 0.134], [0.243, 0.049], [0.681, 0.122], [0.826, 0.134], [0.668, 0.097], [0.486, 0.073], [0.316, 0.036], [0.729, 0.073], [0.766, 0.073], [0.729, 0.049], [0.413, 0.024], [0.316, 0.012], [0.839, 0.036], [0.681, 0.012], [0.839, 0], [0.45, 0], [0.207, 0], [1.203, -0.049], [0.389, -0.012], [1.568, -0.134], [0.061, 0], [1.142, -0.134], [0.304, -0.036], [0.972, -0.158], [0.243, -0.036], [0.049, -0.012], [1.106, -0.219], [0.073, -0.024], [0.413, -0.085], [0.839, -0.194], [0.486, -0.122], [0.753, -0.219], [0.51, -0.158], [0.717, -0.231], [0.51, -0.17], [0.34, -0.122], [0.279, -0.097], [0.79, -0.304], [0.535, -0.231], [0.79, -0.353], [0.498, -0.231], [0.851, -0.425], [0.413, -0.219], [1.215, -0.705], [0, 0], [0, 0], [-0.936, 0.498], [0, 0], [0, 0], [-0.997, 0.632], [-0.292, 0.194], [-0.705, 0.486], [-0.292, 0.207], [-0.802, 0.608], [-0.134, 0.097], [-0.911, 0.741], [-0.255, 0.207], [-0.656, 0.583], [-0.28, 0.255], [-0.681, 0.656], [-0.182, 0.17], [-0.839, 0.863], [-0.207, 0.219], [-0.62, 0.693], [-0.267, 0.292], [-0.595, 0.705], [-0.207, 0.255], [-0.741, 0.984], [-0.146, 0.194], [-0.583, 0.826], [-0.231, 0.328], [-0.51, 0.766], [-0.207, 0.316], [-0.085, 0.134], [-0.389, 0.644], [-0.231, 0.389], [-1.264, 2.491], [-0.195, 0.401], [-0.34, 0.717], [-0.049, 0.109], [-0.717, 1.689], [-0.146, 0.365], [-0.182, 0.462], [-0.352, 0.936], [-0.109, 0.267], [-0.109, 0.304], [-0.352, 1.033], [-0.207, 0.62], [-0.352, 1.167], [-0.122, 0.413], [-0.036, 0.097], [-0.292, 1.106], [-0.134, 0.535], [-0.389, 1.689], [-0.328, 1.653], [-0.109, 0.523], [-0.194, 1.118], [0, 0.036], [-0.061, 0.401], [-0.182, 1.264], [-0.085, 0.62], [-0.109, 0.875], [-0.024, 0.207], [-0.073, 0.644], [-0.109, 1.07], [-0.061, 0.705], [-0.049, 0.596], [-0.036, 0.474], [-0.049, 0.656], [-0.061, 1.142], [-0.024, 0.632], [-0.012, 0.255], [-0.036, 1.13], [-0.012, 0.389], [0, 1.714], [0, 0]], "o": [[-0.012, -0.535], [-0.012, -0.681], [-0.012, -0.62], [-0.012, -0.632], [-0.061, -1.385], [-0.024, -0.474], [-0.012, -0.122], [-0.109, -1.714], [-0.024, -0.425], [-0.085, -1.094], [-0.036, -0.34], [-0.061, -0.62], [-0.134, -1.276], [-0.085, -0.632], [-0.049, -0.365], [-0.134, -1.021], [-0.073, -0.51], [-0.255, -1.811], [-0.012, -0.073], [-0.061, -0.377], [-0.243, -1.519], [-0.109, -0.656], [-0.134, -0.741], [-0.097, -0.523], [-0.146, -0.729], [-0.255, -1.252], [-0.146, -0.681], [-0.061, -0.28], [-0.255, -1.179], [-0.109, -0.474], [-0.425, -1.835], [-0.024, -0.085], [-0.122, -0.498], [-0.377, -1.446], [-0.182, -0.705], [-0.219, -0.802], [-0.122, -0.462], [-0.207, -0.741], [-0.353, -1.227], [-0.219, -0.729], [-0.109, -0.352], [-0.304, -0.997], [-0.182, -0.559], [-0.595, -1.872], [-0.012, -0.024], [-1.009, -3.014], [-0.134, -0.352], [-0.109, -0.279], [-1.045, -2.844], [-0.049, -0.134], [-0.134, -0.328], [-1.009, -2.552], [-0.146, -0.365], [-0.085, -0.194], [-1.155, -2.771], [-0.097, -0.219], [-0.158, -0.365], [-1.142, -2.589], [-0.121, -0.267], [-0.049, -0.085], [-1.471, -3.123], [-0.158, -0.316], [-0.194, -0.389], [-0.51, -0.997], [-0.51, -1.009], [-0.51, -0.997], [-0.316, -0.62], [-0.146, -0.267], [-1.398, -2.601], [-0.194, -0.352], [-1.288, -2.297], [-0.207, -0.365], [-0.231, -0.389], [-0.608, -1.033], [-0.571, -0.936], [-0.62, -1.021], [-0.571, -0.924], [-0.644, -1.021], [-0.583, -0.911], [-0.656, -1.021], [-0.425, -0.644], [-0.109, -0.182], [-1.774, -2.662], [-0.377, -0.535], [-1.884, -2.686], [-0.036, -0.049], [-1.361, -1.847], [-0.474, -0.62], [-0.948, -1.227], [-0.134, -0.158], [-0.17, -0.219], [-2.346, -2.965], [-0.024, -0.012], [-0.049, -0.061], [-2.321, -2.771], [-0.182, -0.207], [-0.073, -0.097], [-2.261, -2.564], [-0.231, -0.243], [-0.316, -0.352], [-0.608, -0.656], [-0.826, -0.875], [-0.62, -0.644], [-0.34, -0.352], [-0.498, -0.498], [-0.632, -0.632], [-0.851, -0.851], [-0.62, -0.608], [-0.194, -0.194], [-0.668, -0.644], [-0.62, -0.596], [-0.936, -0.863], [-0.583, -0.547], [-0.085, -0.085], [-1.021, -0.924], [-0.413, -0.353], [-1.507, -1.325], [-0.024, -0.024], [-0.328, -0.28], [-1.227, -1.021], [-0.559, -0.462], [-0.826, -0.668], [-0.182, -0.146], [-0.583, -0.462], [-0.972, -0.754], [-0.632, -0.486], [-0.547, -0.401], [-0.413, -0.304], [-0.62, -0.45], [-0.96, -0.705], [-0.62, -0.438], [-0.279, -0.194], [-0.802, -0.547], [-0.486, -0.316], [-1.543, -1.021], [-0.024, -0.024], [-0.182, -0.122], [-1.385, -0.887], [-0.535, -0.316], [-0.693, -0.413], [-0.656, -0.389], [-1.957, -1.13], [-0.17, -0.097], [-1.215, -0.656], [-0.134, -0.061], [-1.385, -0.717], [-0.182, -0.085], [-0.462, -0.231], [-1.106, -0.535], [-0.535, -0.243], [-0.413, -0.182], [-0.668, -0.292], [-0.474, -0.207], [-1.519, -0.656], [-1.519, -0.608], [-0.486, -0.182], [-0.681, -0.255], [-0.365, -0.134], [-0.535, -0.194], [-1.009, -0.34], [-0.523, -0.17], [-0.243, -0.085], [-0.814, -0.255], [-0.45, -0.121], [-1.483, -0.413], [-0.012, 0], [-0.182, -0.049], [-1.3, -0.316], [-0.51, -0.109], [-0.717, -0.158], [-0.243, -0.049], [-0.681, -0.122], [-0.839, -0.146], [-0.681, -0.109], [-0.498, -0.073], [-0.316, -0.036], [-0.729, -0.097], [-0.778, -0.085], [-0.729, -0.073], [-0.413, -0.036], [-0.316, -0.024], [-0.839, -0.061], [-0.693, -0.024], [-0.839, -0.024], [-0.45, 0], [-0.207, 0], [-1.215, 0.012], [-0.401, 0.012], [-1.592, 0.085], [-0.061, 0.012], [-1.154, 0.109], [-0.304, 0.036], [-0.984, 0.134], [-0.255, 0.036], [-0.061, 0.012], [-1.118, 0.182], [-0.073, 0.012], [-0.425, 0.085], [-0.851, 0.182], [-0.486, 0.122], [-0.753, 0.194], [-0.51, 0.146], [-0.717, 0.207], [-0.51, 0.158], [-0.34, 0.122], [-0.28, 0.097], [-0.79, 0.292], [-0.535, 0.219], [-0.79, 0.328], [-0.498, 0.219], [-0.851, 0.401], [-0.413, 0.207], [-1.24, 0.656], [0, 0], [0, 0], [0.924, -0.535], [0, 0], [0, 0], [1.009, -0.583], [0.292, -0.182], [0.717, -0.462], [0.304, -0.207], [0.826, -0.583], [0.134, -0.097], [0.923, -0.717], [0.255, -0.219], [0.668, -0.559], [0.28, -0.243], [0.705, -0.632], [0.182, -0.182], [0.851, -0.839], [0.207, -0.219], [0.62, -0.668], [0.255, -0.292], [0.595, -0.693], [0.206, -0.255], [0.766, -0.948], [0.158, -0.194], [0.595, -0.802], [0.231, -0.328], [0.523, -0.754], [0.207, -0.304], [0.085, -0.134], [0.401, -0.644], [0.243, -0.389], [1.398, -2.333], [0.207, -0.401], [0.352, -0.717], [0.049, -0.109], [0.766, -1.629], [0.158, -0.365], [0.194, -0.45], [0.377, -0.911], [0.109, -0.267], [0.109, -0.304], [0.364, -1.009], [0.207, -0.608], [0.365, -1.142], [0.122, -0.413], [0.024, -0.109], [0.316, -1.082], [0.146, -0.535], [0.438, -1.653], [0.365, -1.604], [0.109, -0.51], [0.207, -1.106], [0, -0.036], [0.073, -0.401], [0.207, -1.24], [0.097, -0.608], [0.122, -0.863], [0.024, -0.207], [0.073, -0.644], [0.122, -1.057], [0.073, -0.693], [0.061, -0.583], [0.036, -0.474], [0.048, -0.656], [0.073, -1.13], [0.036, -0.62], [0.012, -0.255], [0.036, -1.118], [0.012, -0.401], [0.036, -1.689], [0, 0], [0.012, -1.787]], "v": [[191.927, 104.535], [191.891, 102.931], [191.842, 100.902], [191.769, 99.054], [191.708, 97.171], [191.49, 93.014], [191.417, 91.617], [191.392, 91.24], [191.016, 86.087], [190.906, 84.823], [190.627, 81.529], [190.517, 80.484], [190.323, 78.601], [189.897, 74.76], [189.667, 72.852], [189.533, 71.746], [189.12, 68.684], [188.913, 67.164], [188.111, 61.72], [188.075, 61.501], [187.88, 60.359], [187.114, 55.801], [186.762, 53.857], [186.361, 51.633], [186.057, 50.053], [185.632, 47.865], [184.854, 44.11], [184.429, 42.056], [184.246, 41.217], [183.444, 37.681], [183.128, 36.259], [181.816, 30.754], [181.743, 30.486], [181.366, 29.003], [180.236, 24.665], [179.677, 22.562], [179.021, 20.168], [178.632, 18.795], [178, 16.571], [176.918, 12.876], [176.274, 10.689], [175.946, 9.619], [175.01, 6.617], [174.475, 4.916], [172.64, -0.711], [172.604, -0.796], [169.456, -9.838], [169.091, -10.883], [168.775, -11.734], [165.567, -20.265], [165.421, -20.654], [165.032, -21.627], [161.945, -29.271], [161.52, -30.352], [161.265, -30.936], [157.716, -39.224], [157.436, -39.868], [156.938, -40.962], [153.414, -48.704], [153.049, -49.518], [152.916, -49.785], [148.407, -59.107], [147.945, -60.055], [147.361, -61.209], [145.83, -64.211], [144.287, -67.213], [142.719, -70.191], [141.759, -72.038], [141.309, -72.852], [137.068, -80.606], [136.472, -81.651], [132.535, -88.493], [131.915, -89.575], [131.234, -90.717], [129.387, -93.816], [127.686, -96.611], [125.802, -99.674], [124.076, -102.433], [122.132, -105.483], [120.369, -108.206], [118.376, -111.244], [117.1, -113.176], [116.748, -113.699], [111.339, -121.598], [110.209, -123.19], [104.461, -131.187], [104.364, -131.321], [100.256, -136.814], [98.822, -138.661], [95.966, -142.356], [95.589, -142.842], [95.054, -143.498], [87.945, -152.297], [87.884, -152.358], [87.726, -152.552], [80.701, -160.768], [80.167, -161.388], [79.924, -161.667], [73.081, -169.251], [72.401, -169.992], [71.429, -171.025], [69.593, -172.982], [67.114, -175.583], [65.255, -177.503], [64.234, -178.56], [62.739, -180.055], [60.843, -181.951], [58.267, -184.491], [56.407, -186.302], [55.812, -186.885], [53.77, -188.805], [51.886, -190.592], [49.079, -193.168], [47.317, -194.785], [47.037, -195.04], [43.95, -197.786], [42.723, -198.868], [38.165, -202.781], [38.092, -202.842], [37.12, -203.656], [33.438, -206.695], [31.749, -208.056], [29.281, -210.037], [28.735, -210.462], [26.96, -211.836], [24.056, -214.072], [22.148, -215.506], [20.519, -216.721], [19.292, -217.608], [17.42, -218.957], [14.515, -221.023], [12.668, -222.312], [11.829, -222.895], [9.423, -224.523], [7.989, -225.484], [3.347, -228.498], [3.274, -228.546], [2.727, -228.886], [-1.442, -231.487], [-3.022, -232.435], [-5.1, -233.675], [-7.056, -234.817], [-12.902, -238.086], [-13.413, -238.378], [-17.059, -240.31], [-17.447, -240.517], [-21.592, -242.607], [-22.126, -242.863], [-23.524, -243.543], [-26.83, -245.111], [-28.434, -245.852], [-29.673, -246.423], [-31.667, -247.286], [-33.101, -247.906], [-37.67, -249.814], [-42.228, -251.564], [-43.674, -252.087], [-45.728, -252.84], [-46.809, -253.205], [-48.438, -253.764], [-51.44, -254.736], [-52.995, -255.222], [-53.725, -255.453], [-56.18, -256.17], [-57.553, -256.559], [-61.977, -257.738], [-62.037, -257.75], [-62.596, -257.884], [-66.485, -258.783], [-68.005, -259.099], [-70.156, -259.549], [-70.873, -259.682], [-72.902, -260.047], [-75.394, -260.472], [-77.423, -260.776], [-78.894, -260.995], [-79.842, -261.104], [-82.029, -261.36], [-84.338, -261.603], [-86.514, -261.785], [-87.778, -261.894], [-88.726, -261.943], [-91.241, -262.077], [-93.307, -262.15], [-95.811, -262.186], [-97.16, -262.21], [-97.768, -262.198], [-101.389, -262.101], [-102.592, -262.065], [-107.332, -261.736], [-107.514, -261.724], [-110.954, -261.347], [-111.865, -261.226], [-114.794, -260.801], [-115.536, -260.679], [-115.694, -260.655], [-119.023, -260.047], [-119.23, -259.998], [-120.482, -259.731], [-123.022, -259.16], [-124.48, -258.783], [-126.753, -258.175], [-128.272, -257.726], [-130.423, -257.057], [-131.942, -256.559], [-132.975, -256.207], [-133.814, -255.891], [-136.184, -254.991], [-137.8, -254.335], [-140.17, -253.302], [-141.677, -252.634], [-144.217, -251.382], [-145.457, -250.762], [-149.139, -248.732], [-189.22, -225.435], [-191.967, -223.843], [-189.184, -225.386], [92.004, 262.21], [133.726, 237.965], [136.727, 236.142], [137.602, 235.571], [139.741, 234.149], [140.641, 233.529], [143.096, 231.742], [143.509, 231.451], [146.268, 229.263], [147.033, 228.619], [149.026, 226.905], [149.877, 226.152], [151.955, 224.22], [152.514, 223.697], [155.042, 221.145], [155.662, 220.489], [157.534, 218.447], [158.311, 217.572], [160.098, 215.469], [160.706, 214.728], [162.978, 211.836], [163.428, 211.24], [165.202, 208.797], [165.895, 207.813], [167.439, 205.516], [168.046, 204.592], [168.314, 204.191], [169.492, 202.259], [170.197, 201.116], [174.184, 193.885], [174.791, 192.658], [175.848, 190.531], [175.994, 190.203], [178.218, 185.232], [178.668, 184.126], [179.227, 182.753], [180.309, 179.982], [180.637, 179.18], [180.953, 178.256], [182.034, 175.194], [182.654, 173.371], [183.736, 169.895], [184.125, 168.667], [184.21, 168.352], [185.109, 165.07], [185.547, 163.478], [186.774, 158.471], [187.819, 153.585], [188.123, 152.018], [188.743, 148.7], [188.755, 148.591], [188.95, 147.375], [189.545, 143.62], [189.8, 141.76], [190.165, 139.16], [190.238, 138.54], [190.457, 136.595], [190.797, 133.423], [190.991, 131.309], [191.161, 129.547], [191.259, 128.125], [191.405, 126.168], [191.599, 122.753], [191.696, 120.881], [191.745, 120.128], [191.854, 116.749], [191.891, 115.57], [191.964, 110.478], [191.964, 109.907]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 9, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}], "markers": [], "props": {}}