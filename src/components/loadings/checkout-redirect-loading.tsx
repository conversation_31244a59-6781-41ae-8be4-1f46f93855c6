"use client"
import { useCheckoutStore } from "@/store/checkout-store"
import { Player } from "@lottiefiles/react-lottie-player"
import { AnimatePresence, motion } from "framer-motion"
import { IconLogoLeft, IconLogoRight, IconWalletCash } from "../Icons"
import animationData from "./loader.json"

const CheckoutRedirectLoading = () => {
  const { payment_option } = useCheckoutStore()
  return (
    <div className='flex h-screen w-full items-center justify-center'>
      <AnimatePresence mode='wait'>
        <motion.div
          initial={{}}
          animate='center'
          exit='exit'
          className={"w-[352px] rounded-xl bg-gray-50 p-6 shadow-sm"}
        >
          <div className='flex h-full flex-col space-y-8'>
            <span className='flex w-full justify-center font-bold'>
              <span className='[&>svg>path]:fill-primary-500'>
                <IconLogoLeft />
              </span>
              <IconLogoRight color='#81CD05' />
            </span>
          </div>
          <div>
            <Player
              src={animationData}
              style={{ height: "300px", width: "100%" }}
              loop
              autoplay
            />
          </div>
          {payment_option?.payment_type == 2 ? (
            <div className='flex flex-col items-center justify-center gap-6'>
              <p className='text-center font-semibold'>
                Please wait! We are verifying your Payment with our Partner
              </p>
              <p className='flex items-center gap-5 text-center font-semibold'>
                <IconWalletCash /> Your payments are secure
              </p>
            </div>
          ) : (
            <div className='flex flex-col items-center justify-center gap-6'>
              <p className='text-center font-semibold'>
                Please wait! We are processing your Order.
              </p>
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}

export default CheckoutRedirectLoading
