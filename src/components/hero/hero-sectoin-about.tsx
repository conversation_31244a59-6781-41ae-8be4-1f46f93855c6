"use client"

import { Share<PERSON>alLogo } from "@/components/main/logo"
import SpImage from "@/shared/SpImage/sp-image"
import {
  sectionVariants,
  staggerItemVariants,
} from "@/utils/animation-variants"
import { motion } from "framer-motion"
import { ArrowRight } from "lucide-react"
import Link from "next/link"
import { Button } from "../ui/button"

export function HeroSectionAbout() {
  return (
    <section className='relative mb-20 md:mb-32'>
      {/* Background Pattern */}
      <div className='absolute inset-0 -z-10 opacity-[0.02]'>
        <div className='absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]' />
      </div>

      <div className='container'>
        <motion.div
          variants={sectionVariants}
          initial='hidden'
          animate='visible'
          className='grid items-center gap-12 lg:grid-cols-2'
        >
          {/* Left Column - Text Content */}
          <div className='space-y-8'>
            <motion.div
              variants={staggerItemVariants}
              className='inline-flex items-center gap-2 rounded-full bg-primary/5 px-4 py-2'
            >
              <span className='h-2 w-2 rounded-full bg-primary'></span>
              <span className='text-sm font-medium text-primary'>
                About SharePal
              </span>
            </motion.div>

            <motion.div variants={staggerItemVariants} className='space-y-4'>
              <h1 className='text-balance text-4xl font-bold tracking-tight md:text-5xl lg:text-6xl'>
                Making Premium Products
                <span className='relative whitespace-nowrap'>
                  <span className='relative z-10'>Accessible</span>
                  <svg
                    aria-hidden='true'
                    viewBox='0 0 418 42'
                    className='absolute left-0 top-3/4 h-[0.4em] w-full fill-primary-500'
                    preserveAspectRatio='none'
                  >
                    <path d='M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z' />
                  </svg>
                </span>
                <br />
                for Everyone
              </h1>

              <p className='text-balance text-lg leading-relaxed text-muted-foreground'>
                SharePal is revolutionizing access to premium products through
                our innovative rental and refurbished marketplace. We&apos;re
                building a future where everyone can experience quality without
                compromise.
              </p>
            </motion.div>

            <motion.div
              variants={staggerItemVariants}
              className='flex flex-col gap-4 sm:flex-row'
            >
              <Button size={"lg"} asChild variant={"primary"}>
                <Link href='/careers'>
                  Join Our Team
                  <ArrowRight className='h-4 w-4' />
                </Link>
              </Button>

              <Button size={"lg"} asChild variant={"outline-primary"}>
                <Link href='/contact'>
                  Contact Us
                  <ArrowRight className='h-4 w-4' />
                </Link>
              </Button>
            </motion.div>

            <motion.div
              variants={staggerItemVariants}
              className='flex items-center gap-8 border-t border-border pt-8'
            >
              <div>
                <div className='text-3xl font-bold'>50K+</div>
                <div className='text-sm text-muted-foreground'>
                  Happy Customers
                </div>
              </div>
              <div>
                <div className='text-3xl font-bold'>4.8/5</div>
                <div className='text-sm text-muted-foreground'>
                  Customer Rating
                </div>
              </div>
            </motion.div>
          </div>

          {/* Right Column - Image Grid */}
          <motion.div
            variants={staggerItemVariants}
            className='relative grid grid-cols-2 gap-4 sm:gap-6'
          >
            <div className='grid gap-4 sm:gap-6'>
              <SpImage
                src='https://images.sharepal.in/super-categories/explore-right.webp'
                alt='Premium Product'
                width={400}
                height={600}
                className='aspect-[3/4] rounded-xl object-cover'
                containerClassName='overflow-hidden rounded-2xl bg-gradient-to-br from-primary-100 to-primary-200 p-1'
              />
              <SpImage
                src='https://images.sharepal.in/super-categories/explore-left.webp'
                alt='Premium Product'
                width={400}
                height={600}
                className='aspect-[3/4] rounded-xl object-cover'
                containerClassName='overflow-hidden rounded-2xl bg-gradient-to-br from-primary-100 to-primary-200 p-1'
              />
            </div>
            <div className='grid gap-4 pt-8 sm:gap-6'>
              <SpImage
                src='https://images.sharepal.in/super-categories/outdoor-right.webp'
                alt='Premium Product'
                width={400}
                height={600}
                className='aspect-[3/4] rounded-xl object-cover'
                containerClassName='overflow-hidden rounded-2xl bg-gradient-to-br from-primary-100 to-primary-200 p-1'
              />
              <SpImage
                src='https://images.sharepal.in/super-categories/Left Side Image.webp'
                alt='Premium Product'
                width={400}
                height={600}
                className='aspect-[3/4] rounded-xl object-cover'
                containerClassName='overflow-hidden rounded-2xl bg-gradient-to-br from-primary-100 to-primary-200 p-1'
              />
            </div>

            {/* Floating Logo */}
            <div className='absolute -right-4 -top-4 z-10 flex items-center justify-center rounded-2xl bg-primary-500 p-4 shadow-lg'>
              <SharePalLogo />
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
