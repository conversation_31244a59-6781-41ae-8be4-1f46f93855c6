"use client"

import React from "react"
import { IconWallet, IconWorld } from "../Icons/design-icons"
import { Typography } from "../ui/typography"

interface TextItemProps {
  icon: React.ReactNode
  text: string
  subtext: string
}

const TextItem: React.FC<TextItemProps> = ({ icon, text, subtext }) => (
  <div>
    <Typography
      as={"p"}
      className={`flex items-center justify-center gap-[2.5px] text-sh6 text-neutral-900 md:gap-1.5 md:text-h6`}
    >
      {icon}
      {text}
      <span className={`text-primary-700`}>{subtext}</span>{" "}
    </Typography>
  </div>
)

const Separator: React.FC = () => (
  <span
    className={`block h-4 w-[2px] rounded-full bg-neutral-200 text-[#00000000] md:h-6 md:w-[3px]`}
  >
    .
  </span>
)

const DesktopStaticText: React.FC = () => (
  <div className='flex w-full items-center justify-center gap-2 whitespace-nowrap text-center font-bold md:gap-4 md:px-2'>
    <TextItem
      icon={<IconWorld className='mr-1 size-4 w-auto md:mr-0.5 md:size-5' />}
      text='Good for our'
      subtext='Planet'
    />
    <Separator />
    <TextItem
      icon={<IconWallet className='mr-1 size-4 w-auto md:mr-0.5 md:size-5' />}
      text='Good for your'
      subtext='Pocket'
    />
  </div>
)

const StaticText: React.FC = () => (
  <div className='relative z-[1] mx-auto flex w-max items-center justify-center overflow-hidden rounded-full bg-neutral-150 px-2 py-1 md:mt-5 md:max-w-4xl md:px-5 md:py-2'>
    <DesktopStaticText />
  </div>
)

export default StaticText
