"use client"

import {
  capitalizeFirstLetter,
  formatUrlName,
} from "@/functions/small-functions"
import useSurgeProduct from "@/hooks/use-prodcut-surge"
import {
  trackSubCategoryViewed,
  trackSuperCategoryViewed,
} from "@/lib/gtag-event"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import type { SubCategory } from "@/types"
import type { SuperCategory } from "@/types/super-category"
import { customFetchPost } from "@/utils/customFetch"
import {
  getSuperCategoryBgColor,
  getSuperCategoryGradientBgColor,
} from "@/utils/get-bg-color"
import {
  getSuperCategoryDescription,
  getSuperCategoryImages,
} from "@/utils/super-category-images"
import { useQuery } from "@tanstack/react-query"
import Link from "next/link"
import { useParams } from "next/navigation"
import React, { useEffect, useMemo } from "react"
import StickySubcategoryNav from "../custom/sticky-subcategory-nav"
import { IconLogoLeft, IconLogoRight } from "../Icons"
import { ProductSubcategorySlider } from "../sliders/product-category-slider"
import TopCategoryNav from "../sliders/top-category-nav"
import { Typography } from "../ui/typography"

type Props = {
  categories: SuperCategory[]
  subCategories: SubCategory[]
  allSubCategories: SubCategory[]
  city: string
  category: string
}

const ListingHome = ({
  categories,
  subCategories,
  city,
  category,
  allSubCategories,
}: Props) => {
  const activeCategory = useMemo(
    () => categories?.find((item) => item.url.includes(category)),
    [categories, category],
  )
  // const heading = useMemo(() => formatUrlName(category), [category]);
  const { cat, subcat } = useParams<{ subcat: string; cat: string }>()
  useEffect(() => {
    if (typeof window !== "undefined") {
      const viewedFn = subcat
        ? trackSubCategoryViewed
        : trackSuperCategoryViewed

      const value = subcat ? subcat : cat
      viewedFn(
        capitalizeFirstLetter(value.split("-").join(" "))?.replace(
          "on rent",
          "",
        ),
        city,
        cat,
        "RENT",
      )
    }
  }, [city, cat, subcat])

  useSurgeProduct({
    type: "category",
    super_category: activeCategory?.super_category_short_name,
    sub_category: formatUrlName(subcat ?? ""),
  })

  const { data: bannerData } = useQuery({
    queryKey: ["banner-fetch", city, subcat, cat],
    queryFn: () =>
      customFetchPost<{
        show: boolean
        link: string
        image: string
      }>(`https://api.sharepal.in/api:qsuyzexA/promotiional-banner`, {
        city: city,
        super_cat: cat,
        sub_cat: subcat,
      }),
    enabled: city ? true : false,
    staleTime: 5 * 60 * 1000,
  })

  return (
    <>
      <div className='relative z-[1] w-full md:h-[525px]'>
        <div
          className={cn(
            "absolute inset-0 z-[-1] h-[750px] w-full bg-blue-category-gradient md:h-[870px]",
            getSuperCategoryGradientBgColor(category),
          )}
        ></div>
        {/* Don't Remove the below commented code its solve tailwind custom color not loading problem */}
        {/* <span className='bg-green-category-gradient bg-red-category-gradient bg-blue-category-gradient bg-purple-category-gradient bg-orange-category-gradient'></span> */}

        <div className='relative z-10'>
          <div className='h-20'></div>
          <div className='w-full'>
            <TopCategoryNav
              data={categories}
              city={city}
              allSubCategories={allSubCategories}
            />
          </div>

          <div className='relative mx-auto h-full min-h-[300px] w-full px-4 py-8 md:min-h-[350px] md:px-8 lg:py-16'>
            {/* Left illustration image */}
            <SpImage
              src={activeCategory?.left_image ?? ""}
              width={600}
              height={600}
              // className="absolute bottom-0 left-0 w-44 object-contain sm:w-60 md:h-auto md:w-96 lg:w-[400px]"
              className='w-48 object-contain sm:w-60 md:h-auto md:w-96 xl:w-[500px]'
              containerClassName='absolute overflow-hidden -left-0 -bottom-11 sm:-bottom-16 md:-bottom-32 md:-left-0 z-0'
            />
            {/* Right illustration image */}
            <SpImage
              src={activeCategory?.right_image ?? ""}
              width={600}
              height={600}
              // className="absolute bottom-0 right-0 w-44 object-contain sm:w-60 md:h-auto md:w-96 lg:w-[400px]"
              className='w-48 object-contain sm:w-60 md:h-auto md:w-96 xl:w-[500px]'
              containerClassName='absolute  overflow-hidden -right-0 -bottom-11 sm:-bottom-16 md:-bottom-32 md:-right-0 z-0'
            />
            {/* Center text */}
            <div className='relative z-10 flex flex-col items-center justify-center gap-2 px-0 py-2 text-center text-gray-100 md:gap-5 md:p-5'>
              <Typography
                as={"h2"}
                className='font-ubuntu text-d6 font-bold capitalize leading-10 -tracking-heading sm:text-d4 md:text-d3 lg:text-d2'
              >
                {activeCategory?.super_category_title}
              </Typography>
              <div className='mb-4 flex h-7 w-full max-w-[500px] flex-wrap items-center justify-center gap-1 md:mb-6'>
                <Typography
                  as='h3'
                  className='line-clamp-2 flex max-h-[40px] w-full flex-wrap items-center justify-center overflow-hidden text-b6 sm:max-h-max md:text-b4 lg:text-sh1 xl:text-h7'
                >
                  {getSuperCategoryDescription(category)
                    ?.split("SharePal")
                    .map((text, index, array) => (
                      <React.Fragment key={index}>
                        {text}
                        {index !== array.length - 1 && (
                          <span className='mx-1 inline-flex w-14 items-center justify-center md:w-20'>
                            <IconLogoLeft />
                            <IconLogoRight color='#fff' />
                          </span>
                        )}
                      </React.Fragment>
                    ))}
                </Typography>
              </div>
              <div className='flex w-[80%] flex-wrap items-center justify-center gap-0 md:mt-3 md:max-w-md md:gap-2'>
                {getSuperCategoryImages(category).map(
                  (imageUrl, index, arr) => (
                    <React.Fragment key={imageUrl}>
                      <SpImage
                        src={imageUrl}
                        width={300}
                        height={100}
                        className='h-auto w-16 object-contain md:w-24'
                      />
                      {index < arr.length - 1 && (
                        <span
                          className={cn(
                            "h-4 w-[2px] rounded-full opacity-50 md:h-6 md:w-[3px] md:opacity-70",
                            getSuperCategoryBgColor(category),
                          )}
                        ></span>
                      )}
                    </React.Fragment>
                  ),
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <StickySubcategoryNav
        subCategories={subCategories}
        city={city}
        cat={cat}
        subcat={subcat}
      />
      <div className='container'>
        <ProductSubcategorySlider data={subCategories} />
        {bannerData && bannerData.show && bannerData.image && (
          <Link
            id='web-engage-banner-container'
            target='_blank'
            href={bannerData?.link ?? ""}
          >
            <SpImage
              alt='Banner Image'
              width={2000}
              height={2000}
              quality={100}
              className='relative z-[9] my-5 min-h-full min-w-full rounded-xl transition-all hover:shadow-soft-bottom'
              src={bannerData.image}
            />
          </Link>
        )}
      </div>
    </>
  )
}

export default React.memo(ListingHome)
