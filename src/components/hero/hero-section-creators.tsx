"use client"
import React from "react"
import { Typography } from "../ui/typography"
import { IconLogoLeft, IconLogoRight } from "../Icons"
import SpImage from "@/shared/SpImage/sp-image"
import { SimpleInstagramEmbed } from "../custom/instagram-player"
import {
  IconOneStopText,
  IconRentalNeedsText,
  IconSolutionText,
} from "../Icons/design-icons"
import { motion } from "framer-motion"

const HeroSectionCreator = ({}) => (
  <section
    style={{
      backgroundImage:
        "linear-gradient(to bottom, #BAC7F8 0%, #1945E8 20%, #FFFFFF 100%)",
    }}
    // className="relative h-[140vh] max-h-[1300px] w-full overflow-hidden overflow-x-hidden py-32 md:py-40"
    className='relative flex h-full min-h-[650px] w-full flex-col justify-between overflow-hidden pt-28 md:min-h-[1140px] md:pt-40'
  >
    {/* text */}
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className='relative flex h-max w-full flex-col items-center justify-center gap-1 md:gap-2'
    >
      {/* title */}
      <div className='flex max-h-10 items-center justify-center gap-1 text-gray-100 md:max-h-20 md:gap-4'>
        <div className='flex h-full w-max items-center'>
          <IconLogoLeft className='!size-20 md:!size-48' />
          <IconLogoRight className='!size-12 md:!size-28' />
        </div>
        <Typography
          as={"h1"}
          className='flex items-center justify-center font-ubuntu text-d7 md:text-d3'
        >
          <span className='sr-only hidden'>Sharepal</span>
          for Creators
        </Typography>
      </div>

      {/* description */}
      <Typography
        as={"p"}
        className='max-w-xl px-4 text-center text-sh4 text-gray-100 md:text-h2'
      >
        If you are a creator , let&apos;s collaborate and make content that
        stand out!
      </Typography>
    </motion.div>

    {/* bottom cards sections  */}
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className='container absolute bottom-10 left-0 right-0 h-max rounded-xl border-input bg-gray-100 md:rounded-3xl'
    >
      <div className='h-max min-h-40 w-full overflow-hidden md:min-h-96'>
        <div>
          {/* left */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className='absolute bottom-1/2 left-1 !translate-y-1/2 p-2 text-end text-h7 text-neutral-900 md:left-4 md:p-6 md:text-d4'
          >
            You
            <span className='relative px-1 md:mx-2'>
              Focus
              <IconOneStopText
                className={"absolute -left-1 bottom-0 h-full w-full md:h-auto"}
              />
            </span>
            on <br /> 🎯
            <span className='relative md:mx-2'>
              Creation
              <IconSolutionText
                className={
                  "absolute -bottom-3 left-1 h-full w-full md:bottom-0 md:h-auto"
                }
              />
            </span>
          </motion.div>

          {/* right */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className='absolute bottom-1/2 right-1 !translate-y-1/2 p-3 text-start text-h7 text-neutral-900 md:right-4 md:p-6 md:text-d4'
          >
            We Bring the <br />
            <span className='relative'>
              Gear
              <IconRentalNeedsText
                className={"absolute -bottom-6 left-0 h-full w-full"}
              />
            </span>{" "}
            🎥
          </motion.div>

          {/* bottom */}
          {/* <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="absolute -bottom-10 left-0 right-0 flex w-full items-center justify-center text-center text-d7 text-neutral-900 md:text-d4"
            >
              <div className="flex h-max w-max items-center">
                <IconLogoLeftBlue className="!size-24 md:!size-48" />
                <IconLogoRight className="!size-16 md:!size-28" />
              </div>
            </motion.div> */}
        </div>
      </div>

      {/* background visuals */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        <SpImage
          src={
            "https://images.sharepal.in/sharepal-for-creators/original/Visuals.png"
          }
          alt='visuals'
          width={1600}
          height={900}
          className='h-full w-full object-cover'
          containerClassName='absolute mx-auto left-0 right-0 bottom-24 w-full md:bottom-32'
        />
      </motion.div>

      {/* mobile phone */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.75 }}
        className='absolute bottom-0 left-0 right-0 flex w-full items-center justify-center'
      >
        {/* content screen custom no image  */}
        <div className='absolute bottom-[112px] left-0 right-0 z-[1] flex w-full items-center justify-center overflow-hidden md:bottom-[164px]'>
          <div className='flex h-full max-h-[250px] min-h-[250px] w-full max-w-[115px] items-center justify-center overflow-hidden rounded-xl bg-primary-500 md:max-h-[576px] md:min-h-[576px] md:max-w-[271px] md:rounded-[36px]'>
            {/* <SimpleInstagramEmbed
                className="h-full w-full object-cover"
                url="https://www.instagram.com/reel/DC4Xd0_TAsi"
              /> */}
            <SimpleInstagramEmbed url='https://www.instagram.com/reel/DC4Xd0_TAsi' />
          </div>
        </div>

        {/*  phone frame */}
        <SpImage
          src={
            "https://images.sharepal.in/sharepal-for-creators/original/PhoneFrame.png"
          }
          alt='mobile phone'
          width={1200}
          height={800}
          className='h-full w-full max-w-40 object-cover md:max-w-96'
          containerClassName='absolute z-0 bottom-20 right-0 left-0 flex items-center justify-center w-full'
        />
      </motion.div>
    </motion.div>
  </section>
)

export default HeroSectionCreator
