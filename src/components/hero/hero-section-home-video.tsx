"use client"

import type { SuperCategory } from "@/types/super-category"
import { motion } from "framer-motion"
import type React from "react"
import { useEffect, useRef, useState } from "react"
import HeroCategoryCard from "../cards/hero-category-card"

const HeroSectionHomeVideo: React.FC<{
  super_categories: SuperCategory[]
  city: string
}> = ({ super_categories, city }) => {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false)
  const [isVideoError, setIsVideoError] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    const videoElement = videoRef.current
    if (videoElement) {
      videoElement.addEventListener("loadeddata", () => {
        setIsVideoLoaded(true)
      })
      videoElement.addEventListener("error", (e) => {
        console.error("Error loading video:", e)
        setIsVideoError(true)
      })
    }

    return () => {
      if (videoElement) {
        videoElement.removeEventListener("loadeddata", () =>
          setIsVideoLoaded(true),
        )
        videoElement.removeEventListener("error", () => setIsVideoError(true))
      }
    }
  }, [])

  return (
    <section className='relative h-full w-full overflow-hidden bg-gradient-to-b from-gray-100 to-neutral-150 md:h-screen md:max-h-[900px] 2xl:max-h-[850px]'>
      {/* Video container */}
      <div className='absolute inset-0 z-0'>
        {!isVideoLoaded && !isVideoError && (
          <div className='flex h-full w-full items-center justify-center'>
            <p className='text-lg font-semibold text-neutral-600'>
              Loading video...
            </p>
          </div>
        )}
        {isVideoError && (
          <div className='flex h-full w-full items-center justify-center'>
            <p className='text-lg font-semibold text-red-600'>
              Error loading video. Please try again.
            </p>
          </div>
        )}
        <video
          ref={videoRef}
          width={1920}
          height={1080}
          loop
          autoPlay
          muted
          playsInline
          preload='auto'
          className={`h-full w-full object-cover ${isVideoLoaded ? "opacity-100" : "opacity-0"}`}
          onLoadedData={() => setIsVideoLoaded(true)}
          onError={() => setIsVideoError(true)}
        >
          <source src='/homepage-final-video.mp4' type='video/mp4' />
          Your browser does not support the video tag.
        </video>
      </div>

      {/* Bottom cards section */}
      <div className='absolute bottom-0 !hidden h-max w-full bg-gradient-to-b from-transparent to-neutral-150 pt-4 md:block'>
        <div className='container h-full'>
          <div className='grid grid-cols-2 gap-4 sm:grid-cols-4 md:gap-6'>
            {super_categories?.map((data, index) => (
              <HeroCategoryCard key={index} {...data} city={city} />
            ))}
          </div>

          <div className='flex w-full items-center justify-center p-3 md:py-6 md:pb-10'>
            <div className='flex items-center justify-center gap-3 text-xs font-semibold text-neutral-300 md:text-base'>
              Scroll down
              {/* Mouse icon with animation */}
              <div className='relative flex h-7 w-5 items-center justify-center rounded-full border-2 border-neutral-300 bg-gray-100 md:h-9 md:w-6'>
                <motion.div
                  className='absolute top-2 h-2 w-[3px] rounded-full bg-neutral-300 md:h-3'
                  animate={{
                    y: [0, 8, 0],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: "easeInOut",
                  }}
                />
              </div>
              to view more
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HeroSectionHomeVideo
