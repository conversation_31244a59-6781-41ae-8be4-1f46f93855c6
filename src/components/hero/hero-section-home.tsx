"use client"
import { SuperCategory } from "@/types/super-category"
// import { Player } from "@lottiefiles/react-lottie-player"
import { CityHeader } from "@/actions/city"
// import { Player } from "@lottiefiles/react-lottie-player"
import { motion } from "framer-motion"
import React from "react"
import HeroCategoryCard from "../cards/hero-category-card"
import IconHeroBg from "../Icons/icon-hero-background"
import FloatingImages from "./floating-images"
import { OwnTheExperience } from "./hero-texts"
// import { Typography } from '../ui/typography'
// import { Player } from '@lottiefiles/react-lottie-player'

interface HeroSectionHomeProps {
  super_categories: SuperCategory[]
  cityHeader: CityHeader | null
  city: string
}

const HeroSectionHome: React.FC<HeroSectionHomeProps> = ({
  super_categories,
  cityHeader,
  city,
}) => (
  <section className='relative flex h-full w-full flex-col overflow-hidden bg-gradient-to-b from-gray-100 to-neutral-150 md:h-screen md:max-h-[900px] md:min-h-[820px] 2xl:max-h-[850px]'>
    <div className='absolute -z-0 h-full w-full md:inset-0'>
      <IconHeroBg />
    </div>

    <FloatingImages />
    {/* <Player
      src={"lottie-animations/hero-background-lottie-new.json"}
      style={{ height: "100%", width: "100%" }}
      className='absolute inset-0 z-0 max-md:hidden'
      loop
      autoplay
    />

    <div className='absolute h-screen w-screen overflow-hidden md:hidden'>
      <Player
        src={"lottie-animations/hero-mobile.json"}
        style={{ height: "100vh", width: "100vw" }}
        className='overflow-hidden'
        loop
        autoplay
      />
    </div> */}

    {/* text */}
    {/* top-[calc(100vh-75%)] */}
    {/* <div className='inset-0 top-[200px] flex w-full justify-center pt-28 md:absolute md:h-full md:min-h-[400px] md:pt-0'>
      <div className='relative z-0 h-max w-full rounded-full py-10 text-center font-bold md:max-w-[75%]'>
        <div className='absolute inset-0 -z-0 rounded-full border border-solid bg-gray-100 opacity-100 blur-2xl md:p-6'></div>
        <SwapAnimatedText
          texts={[
            cityHeader?.line_1 ?? "Save Money",
            cityHeader?.line_2 ?? "Save Planet",
          ]}
        />
      </div>
    </div> */}

    <OwnTheExperience cityHeader={cityHeader} />
    {/* bottom cards sections  */}
    <div className='h-max w-full bg-gradient-to-b from-transparent to-neutral-150 pt-4'>
      <div className='container h-full'>
        <div className='grid grid-cols-2 gap-2 sm:grid-cols-4 md:gap-6'>
          {super_categories?.map((data, index) => (
            <HeroCategoryCard key={index} {...data} city={city} />
          ))}
        </div>

        <div className='relative z-[1] flex w-full items-center justify-center p-3 md:py-6 md:pb-10'>
          <div className='flex items-center justify-center gap-3 text-xs font-semibold text-neutral-300 md:text-base'>
            Scroll down
            {/* Mouse icon with animation */}
            <div className='relative flex h-7 w-5 items-center justify-center rounded-full border-2 border-neutral-300 bg-gray-100 md:h-9 md:w-6'>
              {/* Moving line inside the mouse */}
              <motion.div
                className='absolute top-2 h-2 w-[3px] rounded-full bg-neutral-300 md:h-3'
                animate={{
                  y: [0, 8, 0], // Moves from top (0) to down (+8px) and back
                }}
                transition={{
                  duration: 1.5, // Time for one cycle
                  repeat: Infinity, // Infinite loop
                  ease: "easeInOut",
                }}
              />
            </div>
            to view more
          </div>
        </div>
      </div>
    </div>
  </section>
)

export default HeroSectionHome
