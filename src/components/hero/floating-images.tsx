"use client"

import { HARD_CODED_IMAGE_URL } from "@/constants"
import SpImage from "@/shared/SpImage/sp-image"
import { motion } from "framer-motion"
import React from "react"
// import { Player } from '@lottiefiles/react-lottie-player'
// import animationData from './hero-animation.json'

interface FloatingImageProps {
  src: string
  containerClassName: string
  imageClassName?: string
  direction?: "clockwise" | "counterclockwise"
}

const FloatingImage: React.FC<FloatingImageProps> = ({
  src,
  containerClassName,
  imageClassName = "h-60 w-60",
  direction = "clockwise",
}) => {
  const radius = 50
  const duration = 20

  // old one

  // const circularMotion = {
  //   x:
  //     direction === 'clockwise'
  //       ? [0, radius, 0, -radius, 0]
  //       : [0, -radius, 0, radius, 0],
  //   y: [radius, 0, -radius, 0, radius],
  //   scale: [1, 1.05, 1, 0.95, 1],
  //   transition: {
  //     duration: duration,
  //     ease: 'easeInOut',
  //     times: [0, 0.25, 0.5, 0.75, 1],
  //     repeat: Infinity,
  //   },
  // }
  const circularMotion = {
    x:
      direction === "clockwise"
        ? [0, radius, 0, -radius, 0]
        : [0, -radius, 0, radius, 0],
    y: [radius, 0, -radius, 0, radius],
    transition: {
      duration: duration,
      ease: "linear",
      times: [0, 0.25, 0.5, 0.75, 1],
      repeat: Infinity,
      repeatType: "loop" as const,
    },
  }

  return (
    <div className={`absolute ${containerClassName}`}>
      <motion.div className='relative' animate={circularMotion}>
        <SpImage
          src={src.trim()}
          className={`object-contain max-lg:w-[150px] max-md:hidden ${imageClassName}`}
          width={300}
          height={300}
          alt='Floating product image'
        />
      </motion.div>
    </div>
  )
}

const FloatingImages: React.FC = () => {
  const images = [
    {
      key: "bag-tilt",
      src: "/bag-tilt.webp",
      className: "left-0 top-1/4",
      direction: "clockwise" as const,
      imageClassName: "scale-130",
    },
    {
      key: "vr-glasses",
      src: "/vr-glasses.webp",
      className: "right-[30%] top-[10%]",
      direction: "counterclockwise" as const,
    },
    {
      key: "dslr-tilt",
      src: "https://images.sharepal.in/misc/hard-coded/gopro13.webp",
      className: "left-[30%] top-[7%]",
      direction: "clockwise" as const,
      fullLink: true,
    },
    {
      key: "dslr-title2",
      src: "/dslr-title2.webp",
      className: "right-[10%] top-[15%]",
      direction: "counterclockwise" as const,
    },
    {
      key: "camp-tilt",
      src: "/camp-tilt.webp",
      className: "right-0 bottom-[10%]",
      direction: "clockwise" as const,
      imageClassName: "h-96 w-96",
    },
    {
      key: "riding-jacket",
      src: "/riding-jacket.webp",
      className: "right-1/3 bottom-[23%]",
      direction: "counterclockwise" as const,
    },
    {
      key: "ps5",
      src: "/ps5.webp",
      className: "left-[20%] bottom-[23%]",
      direction: "clockwise" as const,
    },
  ]

  return (
    <div className='images inset-0 mx-auto h-full w-full max-w-screen-2xl'>
      {images.map((image) => (
        <FloatingImage
          key={image.key}
          src={
            image?.fullLink ? image.src : `${HARD_CODED_IMAGE_URL}${image.src}`
          }
          containerClassName={image.className}
          direction={image.direction}
          imageClassName={image.imageClassName}
        />
      ))}
      {/* <Player
        src={animationData}
        className='absolute '
        loop
        autoplay
      /> */}
    </div>
  )
}

export default FloatingImages
