"use client"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import CoverageCards from "../carepal/coverage-cards"
import { Typography } from "../ui/typography"

const HeroSectionCarepal = () => (
  <section className='relative z-[1] w-full md:min-h-[870px]'>
    {/* Main hero section with gradient background */}
    <div
      className={cn(
        "absolute inset-0 z-[-1] h-[750px] w-full bg-carepal-gradient md:h-[880px]",
      )}
    ></div>

    <div className='relative z-10'>
      <div className='h-20'></div>

      <div className='container relative mx-auto h-full min-h-[300px] w-full max-w-[1600px] py-8 md:min-h-[400px]'>
        {/* Carepal - left */}
        <SpImage
          src='https://images.sharepal.in/carepal/hero-left.webp'
          width={600}
          height={600}
          className='w-48 object-contain sm:w-60 md:h-auto md:w-[500px]'
          containerClassName='absolute hidden xl:block overflow-hidden -left-0 -bottom-11 sm:-bottom-16  md:-left-0 z-0'
        />

        {/* Carepal - right */}
        <SpImage
          src='https://images.sharepal.in/carepal/hero-right.webp'
          width={600}
          height={600}
          className='w-48 object-contain sm:w-60 md:h-auto md:w-[500px]'
          containerClassName='absolute hidden xl:block  overflow-hidden -right-0 -bottom-11 sm:-bottom-16  md:-right-0 z-0'
        />

        {/* Center text content */}
        <div className='relative z-10 mt-6 flex flex-col items-center justify-center gap-2 px-0 py-2 text-center text-gray-100 md:gap-5 md:p-5'>
          <SpImage
            src='https://images.sharepal.in/carepal/carepal-logo-white.svg'
            width={800}
            height={100}
            alt='CarePal Assure'
            className='h-auto w-[200px] md:h-auto md:w-[335px]'
          />
          <Typography
            as='h1'
            className='hidden font-ubuntu text-d7 font-bold tracking-tight md:text-d5 lg:text-d3'
          >
            CarePal
          </Typography>

          {/* Title */}
          <div className='flex items-center justify-center gap-1 font-ubuntu text-d7 font-bold tracking-tight md:text-d5'>
            <div className='relative flex items-center justify-center'>
              Damage Waiver
              <SpImage
                src='https://images.sharepal.in/carepal/waiver-line.svg'
                width={250}
                height={80}
                alt='Waiver Line'
                className='inline-block h-auto w-[150px] md:w-[250px]'
                containerClassName='absolute bottom-[-22px]'
              />
            </div>
            Plan
          </div>

          <Typography
            as='p'
            className='mx-auto max-w-xl px-2 text-b3 text-white md:text-b2 lg:text-h5'
          >
            Enjoy a worry-free rental experience with our damage waiver
            coverage. Get protection for accidental damage up to ₹25,000 and
            rent with complete peace of mind.
          </Typography>
        </div>
      </div>
    </div>

    {/* Coverage cards section - using the new component */}
    <div className='container px-2'>
      <CoverageCards />
    </div>
  </section>
)

export default HeroSectionCarepal
