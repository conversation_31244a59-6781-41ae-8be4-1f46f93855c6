"use client"

import { AnimatePresence, motion } from "framer-motion"
import { useEffect, useState } from "react"

interface SwapAnimatedTextProps {
  texts: string[]
}

export default function SwapAnimatedText({ texts }: SwapAnimatedTextProps) {
  const [index, setIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prevIndex) => (prevIndex + 1) % texts.length)
    }, 2000)

    return () => clearInterval(interval)
  }, [texts.length])

  const splitText = (text: string) => {
    const words = text.split(" ")
    const firstWord = words[0]
    const restWords = words.slice(1).join(" ")
    return { firstWord, restWords }
  }

  const { firstWord, restWords } = splitText(texts[index])

  return (
    <div className='relative z-[1] h-24 overflow-hidden'>
      <AnimatePresence mode='popLayout'>
        <motion.div
          key={index}
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -40, opacity: 0 }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
          className='absolute inset-0 flex items-center justify-center gap-3'
        >
          <span className='font-ubuntu text-d5 text-primary-500 md:text-d1'>
            {firstWord}
          </span>
          <span className='font-ubuntu text-d5 text-neutral-900 md:text-d1'>
            {restWords}
          </span>
        </motion.div>
      </AnimatePresence>
    </div>
  )
}

export function SwapAnimatedText2({ texts }: SwapAnimatedTextProps) {
  const [index, setIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prevIndex) => (prevIndex + 1) % texts.length)
    }, 2000)

    return () => clearInterval(interval)
  }, [texts.length])

  const splitText = (text: string) => {
    const words = text.split(" ")
    const firstWord = words[0]
    const restWords = words.slice(1).join(" ")
    return { firstWord, restWords }
  }

  const { firstWord, restWords } = splitText(texts[index])

  const animationDirection = index % 3 === 2 ? "bottom-to-top" : "top-to-bottom"

  return (
    <div className='relative z-[1] h-16 overflow-hidden md:h-20'>
      <AnimatePresence mode='popLayout'>
        <motion.div
          key={index}
          initial={{
            y: animationDirection === "top-to-bottom" ? -40 : 40,
            opacity: 0,
          }}
          animate={{ y: 0, opacity: 1 }}
          exit={{
            y: animationDirection === "top-to-bottom" ? 40 : -40,
            opacity: 0,
          }}
          transition={{
            duration: 0.4,
            ease: "easeInOut",
            staggerChildren: 0.4,
          }}
          className='absolute inset-0 flex items-center justify-center gap-2'
        >
          <motion.span
            className='text-h6 text-primary-500 md:text-h2'
            transition={{ delay: 0 }}
          >
            {firstWord}
          </motion.span>
          <motion.span
            className='text-h6 text-neutral-900 md:text-h2'
            transition={{ delay: 0.1 }}
          >
            {restWords}
          </motion.span>
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
