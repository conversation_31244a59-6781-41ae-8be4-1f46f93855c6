"use client"

import { motion } from "framer-motion"
import React from "react"
import {
  IconHeroDeposit,
  IconHeroExhaustive,
  IconHeroFlexible,
} from "../Icons/design-icons"
import { Typography } from "../ui/typography"

interface ScrollingTextItemProps {
  icon: React.ReactNode
  text: string
  subtext: string
  isDesktop?: boolean
}

const ScrollingTextItem: React.FC<ScrollingTextItemProps> = ({
  icon,
  text,
  subtext,
  isDesktop = false,
}) => (
  <div>
    <Typography
      as={"p"}
      className={`flex items-center justify-center gap-2 ${isDesktop ? "text-h6" : "text-sh6"} text-primary-700`}
    >
      {icon}
      {text}
      <span className={`text-neutral-900`}>{subtext}</span>{" "}
    </Typography>
  </div>
)

interface SeparatorProps {
  isDesktop?: boolean
}

const Separator: React.FC<SeparatorProps> = ({ isDesktop = false }) => (
  <span
    className={`block ${isDesktop ? "h-6 w-[3px]" : "h-4 w-[2px]"} rounded-full bg-neutral-200 text-[#00000000]`}
  >
    .
  </span>
)

const MobileScrollingText: React.FC = () => (
  <motion.div
    className='flex w-full items-center justify-start gap-2.5 whitespace-nowrap px-2 text-center font-bold md:hidden'
    initial={{ x: "0%" }}
    animate={{ x: "-100%" }}
    transition={{
      duration: 15,
      repeat: Infinity,
      ease: "linear",
    }}
  >
    <ScrollingTextItem
      icon={<IconHeroDeposit className='w-5' />}
      text='Zero'
      subtext='Deposit Policy'
    />
    <Separator />
    <ScrollingTextItem
      icon={<IconHeroFlexible className='w-5' />}
      text='Flexible'
      subtext='Delivery Option'
    />
    <Separator />
    <ScrollingTextItem
      icon={<IconHeroExhaustive className='w-5' />}
      text='Exhaustive'
      subtext='Product Catalogue'
    />
  </motion.div>
)

const DesktopStaticText: React.FC = () => (
  <div className='hidden w-full items-center justify-center gap-4 whitespace-nowrap px-2 text-center font-bold md:flex'>
    <ScrollingTextItem
      icon={<IconHeroDeposit className='w-auto' />}
      text='Zero'
      subtext='Deposit Policy'
      isDesktop
    />
    <Separator isDesktop />
    <ScrollingTextItem
      icon={<IconHeroFlexible className='w-auto' />}
      text='Flexible'
      subtext='Delivery Option'
      isDesktop
    />
    <Separator isDesktop />
    <ScrollingTextItem
      icon={<IconHeroExhaustive className='w-auto' />}
      text='Exhaustive'
      subtext='Product Catalogue'
      isDesktop
    />
  </div>
)

const ScrollingText: React.FC = () => (
  <div className='relative z-[1] mx-auto flex w-full max-w-[70%] items-center justify-center overflow-hidden rounded-full bg-neutral-150 px-2 py-1 md:mt-5 md:max-w-4xl md:px-5 md:py-2'>
    <MobileScrollingText />
    <DesktopStaticText />

    {/* <div className="flex h-8 w-full items-center justify-center">
        <Image
          src="/images/sequence2.gif"
          alt="sequence2"
          width={500}
          height={100}
          className="object-contain"
        />
      </div> */}
  </div>
)

export default ScrollingText
