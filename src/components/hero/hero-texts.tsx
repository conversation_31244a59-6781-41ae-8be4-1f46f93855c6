import { CityHeader } from "@/actions/city"
import { IconExperience, IconRent } from "../Icons/design-icons"
import { Typography } from "../ui/typography"
import StaticText from "./static-text"
import { SwapAnimatedText2 } from "./swap-animated-text"

interface OneStopSolutionProps {
  cityHeader: CityHeader | null
}
export const OwnTheExperience = ({ cityHeader }: OneStopSolutionProps) => (
  <div className='inset-0 flex w-full justify-center pt-20 md:absolute md:top-[30px] md:h-full md:min-h-[400px] md:pt-12'>
    <div className='relative z-0 h-max w-full rounded-full py-2 text-center font-bold md:max-w-[75%]'>
      {/* background */}
      <div className='absolute inset-0 -z-0 rounded-full border border-solid bg-gray-100 opacity-95 blur-3xl md:p-6'></div>
      <SwapAnimatedText2
        texts={[
          cityHeader?.line_0 ?? "Hello Pal!",
          cityHeader?.line_1 ?? "Save Money",
          cityHeader?.line_2 ?? "Save Planet",
        ]}
      />

      <Typography
        as={"h1"}
        className='relative z-10 mx-auto w-full font-ubuntu text-d7 tracking-[0.249px] md:text-d3'
      >
        &quot;Own
        <span className='relative px-1 md:mx-2'>the</span>
        <span className='relative md:mx-2'>
          Experience
          <IconExperience
            className={"absolute inset-0 -left-1 h-full w-full md:h-auto"}
          />
        </span>
        <br />
        <span className='relative mx-2 md:inline-block'>
          Rent
          <IconRent
            className={
              "absolute -bottom-2 left-0 h-full w-full md:-bottom-2 md:h-auto"
            }
          />
        </span>
        the Gear&quot;
      </Typography>

      <div className='flex w-full items-center justify-center pt-4 md:py-5'>
        <StaticText />
      </div>
    </div>
  </div>
)

// export const OneStopSolution = ({ cityHeader }: OneStopSolutionProps) => (
//   <div className='inset-0 flex h-max w-full items-start justify-center pt-28 md:absolute md:pt-0'>
//     <div className='top-[100px] z-0 h-max rounded-full text-center font-bold md:relative md:top-[50px] md:w-[75%] md:py-16'>
//       <div className='relative mx-auto w-10/12 p-5 py-4 md:py-10'>
//         <Typography
//           as={"h1"}
//           className='relative z-10 mx-auto w-full font-ubuntu text-d7 tracking-[0.249px] md:text-d3'
//         >
//           &quot;The
//           <span className='relative px-1 md:mx-2'>
//             one stop
//             <IconOneStopText
//               className={"absolute inset-0 -left-1 h-full w-full md:h-auto"}
//             />
//           </span>
//           <span className='relative md:mx-2'>
//             solution
//             <IconSolutionText
//               className={
//                 "absolute -bottom-3 left-1 h-full w-full md:bottom-0 md:h-auto"
//               }
//             />
//           </span>
//           <br /> for all your
//           <span className='relative mx-2 md:inline-block'>
//             rental needs&quot;
//             <IconRentalNeedsText
//               className={
//                 "absolute -bottom-4 left-0 h-full w-full md:-bottom-2 md:h-auto"
//               }
//             />
//           </span>
//         </Typography>
//         <div className='absolute inset-0 -z-0 rounded-full border border-solid bg-gray-100 opacity-100 blur-2xl md:p-6'></div>

//         <div className='w-full pt-4 md:py-5'>
//           <ScrollingText />
//         </div>
//       </div>
//     </div>
//   </div>
// )
