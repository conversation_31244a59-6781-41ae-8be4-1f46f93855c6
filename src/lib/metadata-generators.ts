import { capitalizeFirstLetter } from "@/functions/small-functions"
import { Metadata, ResolvingMetadata } from "next"

interface GenerateCityMetadataProps {
  city?: string
  parent: ResolvingMetadata
}

export async function generateCityMetadata({
  city = "",
  parent,
}: GenerateCityMetadataProps): Promise<Metadata> {
  const previousImages = (await parent).openGraph?.images || []
  const formattedCity = city ? capitalizeFirstLetter(city) : "India"

  const title = `Rent Lifestyle Gear & Gadget in ${formattedCity}`
  const description =
    "Rent Cameras, Gaming Consoles, Projectors/Speakers, Travel Gear & more. Excellent Quality & Free Delivery"

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      images: [
        "https://images.sharepal.in/misc/hard-coded/sharepal-logo-icon.webp",
        ...previousImages,
      ],
    },
  }
}
