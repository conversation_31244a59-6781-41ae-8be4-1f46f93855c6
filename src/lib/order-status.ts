// import { OrderSummary } from '@/types/order'
// import { formatShortDate } from '@/utils/date-logics'

// export const ORDER_STAGES = {
//   RECEIVED: 'Order Received',
//   CONFIRMED: 'Order Confirmed',
//   DELIVERED: 'Order Delivered',
//   RENTAL_STARTED: 'Rental Period Started',
//   RETURN_DUE: 'Order Return Due',
//   RETURN_RECEIVED: 'Return Received',
//   UNDER_INSPECTION: 'Order Under Inspection',
//   COMPLETED: 'Order Complete',
//   CANCELLED: 'Order Cancelled',
//   DAMAGE_IDENTIFIED: 'Order Damage Identified',
//   RENTAL_EXTENDED: 'Rental Period Extended',
// } as const

// export type OrderStage = (typeof ORDER_STAGES)[keyof typeof ORDER_STAGES]

// export interface StageInfo {
//   title: string
//   message: string
//   actions: { text: string; variant: 'default' | 'outline' }[]
//   warning?: boolean
//   error?: boolean
// }

// export const finalOrderStages = [
//   ORDER_STAGES.RECEIVED,
//   ORDER_STAGES.CONFIRMED,
//   ORDER_STAGES.DELIVERED,
//   ORDER_STAGES.RENTAL_STARTED,
//   ORDER_STAGES.RETURN_DUE,
//   ORDER_STAGES.RETURN_RECEIVED,
//   ORDER_STAGES.UNDER_INSPECTION,
//   ORDER_STAGES.COMPLETED,
// ]

// export const stageMapping = {
//   // Order Received Stage
//   'Order Received': ORDER_STAGES.RECEIVED,
//   'KYC Received': ORDER_STAGES.RECEIVED,
//   'Advance Received': ORDER_STAGES.RECEIVED,

//   // Order Confirmed Stage
//   'Order Confirmed': ORDER_STAGES.CONFIRMED,
//   'Order Packed': ORDER_STAGES.CONFIRMED,
//   'Delivery Scheduled': ORDER_STAGES.CONFIRMED,
//   'Delivery Partner Booked': ORDER_STAGES.CONFIRMED,
//   'Order Shipped': ORDER_STAGES.CONFIRMED,

//   // Order Delivered Stage
//   'Order Delivered': ORDER_STAGES.DELIVERED,

//   // Rental Period Stage
//   'Rental Period Started': ORDER_STAGES.RENTAL_STARTED,
//   'Order Extended': ORDER_STAGES.RENTAL_EXTENDED,
//   'Extension Request': ORDER_STAGES.RENTAL_STARTED,

//   // Return Stage
//   'Pickup Due': ORDER_STAGES.RETURN_DUE,
//   'Pickup Scheduled': ORDER_STAGES.RETURN_DUE,
//   'Pickup Partner Booked': ORDER_STAGES.RETURN_DUE,

//   // Return Processing
//   'Return Received': ORDER_STAGES.RETURN_RECEIVED,
//   'Quality Checked': ORDER_STAGES.UNDER_INSPECTION,
//   'Any Deduction': ORDER_STAGES.DAMAGE_IDENTIFIED,

//   // Completion
//   'Refund Processed': ORDER_STAGES.COMPLETED,
//   'Order Completed': ORDER_STAGES.COMPLETED,

//   // Special Cases
//   'Order Cancelled': ORDER_STAGES.CANCELLED,
//   'Order Damage Identified': ORDER_STAGES.DAMAGE_IDENTIFIED,
// } as const

// export const getStageInfo = (
//   stage: OrderStage,
//   order: OrderSummary,
// ): StageInfo => {
//   const stageMessages: Record<OrderStage, (order: OrderSummary) => StageInfo> =
//     {
//       [ORDER_STAGES.RECEIVED]: () => ({
//         title: 'Order Received!',
//         message: 'Your order is under review and awaiting order confirmation.',
//         actions: [
//           { text: 'Verify My Profile', variant: 'default' },
//           { text: 'Pay Rental Charges Online', variant: 'outline' },
//         ],
//       }),

//       [ORDER_STAGES.CONFIRMED]: () => ({
//         title: 'Order Confirmed!',
//         message:
//           'Order packing in progress. Changes allowed only before completion.',
//         actions: [
//           { text: 'Add/Modify Items', variant: 'default' },
//           { text: 'Pay Rental Charges Online', variant: 'outline' },
//         ],
//       }),

//       [ORDER_STAGES.DELIVERED]: (order: OrderSummary) => ({
//         title: 'Order Delivered!',
//         message: `Your order was delivered on ${formatShortDate(order.deal_cf_delivery_date)}`,
//         actions: [
//           { text: 'Pay Rental Charges Now', variant: 'default' },
//           { text: 'Get Product Support', variant: 'outline' },
//         ],
//       }),

//       [ORDER_STAGES.RENTAL_STARTED]: (order: OrderSummary) => ({
//         title: 'Rental Period Started!',
//         message: `Keep a track on your pickup date - ${formatShortDate(order.deal_cf_pickup_date)} or extend your order if needed.`,
//         actions: [
//           { text: 'Extend Rental Period', variant: 'default' },
//           { text: 'Get Product Support', variant: 'outline' },
//         ],
//       }),

//       [ORDER_STAGES.RETURN_DUE]: () => ({
//         title: 'Order Return Due!',
//         message: `Your rental period has ended. Schedule a return pickup to avoid a ₹100/day penalty or extend your rental period.`,
//         actions: [
//           { text: 'Extend Rental Period', variant: 'default' },
//           { text: 'Schedule Return Pickup', variant: 'outline' },
//         ],
//         warning: true,
//       }),

//       [ORDER_STAGES.RETURN_RECEIVED]: () => ({
//         title: 'Return Received!',
//         message:
//           "We've received your order and will begin the inspection soon.",
//         actions: [{ text: 'Contact Support', variant: 'outline' }],
//       }),

//       [ORDER_STAGES.UNDER_INSPECTION]: () => ({
//         title: 'Order Under Inspection!',
//         message:
//           "Your order is currently under inspection. We'll notify you once it's complete.",
//         actions: [{ text: 'Contact Support', variant: 'outline' }],
//       }),

//       [ORDER_STAGES.COMPLETED]: () => ({
//         title: 'Order Complete!',
//         message:
//           'Your rental order is complete. Thank you for choosing us. We look forward to serving you again!',
//         actions: [
//           { text: 'Order Again', variant: 'default' },
//           { text: 'Provide Your Feedback', variant: 'outline' },
//         ],
//       }),

//       [ORDER_STAGES.CANCELLED]: () => ({
//         title: 'Order Cancelled!',
//         message:
//           'Order packing in progress. Changes allowed only before completion.',
//         actions: [
//           { text: 'Order Again', variant: 'default' },
//           { text: 'Provide Your Feedback', variant: 'outline' },
//         ],
//         error: true,
//       }),

//       [ORDER_STAGES.DAMAGE_IDENTIFIED]: () => ({
//         title: 'Order Damage Identified!',
//         message:
//           'Damage has been identified in your order. Please settle the damage charges to proceed.',
//         actions: [
//           { text: 'Settle Damage Charges', variant: 'default' },
//           { text: 'Contact Support', variant: 'outline' },
//         ],
//         error: true,
//       }),

//       [ORDER_STAGES.RENTAL_EXTENDED]: (order: OrderSummary) => ({
//         title: 'Rental Period Extended!',
//         message: `Keep a track on your pickup date - ${formatShortDate(order.deal_cf_pickup_date)} or extend your order if needed.`,
//         actions: [
//           { text: 'Extend Rental Period', variant: 'default' },
//           { text: 'Get Product Support', variant: 'outline' },
//         ],
//       }),
//     }

//   return stageMessages[stage](order)
// }

// export function formatDate(date: number | string) {
//   return new Date(date).toLocaleDateString('en-GB', {
//     day: 'numeric',
//     month: 'short',
//     year: 'numeric',
//   })
// }

// export function getFinalOrderStages(order: OrderSummary) {
//   const currentStage = order.deal_deal_stage_name
//   const mappedStage = stageMapping[currentStage as keyof typeof stageMapping]

//   // Handle special cases first
//   if (currentStage === 'Order Cancelled') {
//     return {
//       stages: [ORDER_STAGES.RECEIVED, ORDER_STAGES.CANCELLED],
//       activeStage: ORDER_STAGES.CANCELLED,
//       stageInfo: getStageInfo(ORDER_STAGES.CANCELLED, order),
//     }
//   }

//   if (
//     currentStage === 'Order Damage Identified' ||
//     currentStage === 'Any Deduction'
//   ) {
//     const stages = [...finalOrderStages]
//     const damageIndex = stages.indexOf(ORDER_STAGES.UNDER_INSPECTION)
//     if (damageIndex !== -1) {
//       ;(stages as (typeof ORDER_STAGES)[keyof typeof ORDER_STAGES][])[
//         damageIndex
//       ] = ORDER_STAGES.DAMAGE_IDENTIFIED
//     }
//     return {
//       stages,
//       activeStage: ORDER_STAGES.DAMAGE_IDENTIFIED,
//       stageInfo: getStageInfo(ORDER_STAGES.DAMAGE_IDENTIFIED, order),
//     }
//   }

//   // Handle rental extension
//   if (
//     currentStage === 'Order Extended' ||
//     currentStage === 'Extension Request'
//   ) {
//     const stages = [...finalOrderStages]
//     const rentalIndex = stages.indexOf(ORDER_STAGES.RENTAL_STARTED)
//     if (rentalIndex !== -1) {
//       ;(stages as (typeof ORDER_STAGES)[keyof typeof ORDER_STAGES][]).splice(
//         rentalIndex + 1,
//         0,
//         ORDER_STAGES.RENTAL_EXTENDED,
//       )
//     }
//     return {
//       stages,
//       activeStage: ORDER_STAGES.RENTAL_EXTENDED,
//       stageInfo: getStageInfo(ORDER_STAGES.RENTAL_EXTENDED, order),
//     }
//   }

//   // Normal flow
//   return {
//     stages: finalOrderStages,
//     activeStage: mappedStage,
//     stageInfo: getStageInfo(mappedStage, order),
//   }
// }

// ! We are handling this in the backend now

export const getActionButtons = (stage: string) => {
  const buttonMap: Record<
    string,
    {
      text: string
      variant: "primary" | "outline-primary"
      type?: "link" | "button"
      func?: () => void
    }[]
  > = {
    "Order Received": [
      { text: "Verify My Profile", variant: "primary", type: "link" },
      { text: "Pay Rental Charges Online", variant: "outline-primary" },
    ],
    "Order Confirmed": [
      { text: "Add/Modify Items", variant: "primary" },
      { text: "Pay Rental Charges Online", variant: "outline-primary" },
    ],
    "Order Delivered": [
      { text: "Pay Rental Charges Now", variant: "primary" },
      { text: "Get Product Support", variant: "outline-primary" },
    ],
    "Delivery Scheduled": [
      { text: "Pay Rental Charges Now", variant: "primary" },
      { text: "Contact Support For Help", variant: "outline-primary" },
    ],
    "Return Due": [
      { text: "Extend Rental Period", variant: "primary" },
      { text: "Schedule Return Pickup", variant: "outline-primary" },
    ],
    "Return Received": [
      {
        text: "Contact Support",
        variant: "outline-primary",
        type: "button",
      },
    ],
    "Order Completed": [
      { text: "Order Again", variant: "primary" },
      { text: "Provide Your Feedback", variant: "outline-primary" },
    ],
    "Order Cancelled": [
      { text: "Order Again", variant: "primary" },
      { text: "Provide Your Feedback", variant: "outline-primary" },
    ],
    "Order Damage Identified": [
      { text: "Settle Damage Charges", variant: "primary" },
      { text: "Contact Support", variant: "outline-primary" },
    ],
  }
  return buttonMap[stage] || []
}

export const showModification = (stage: string) =>
  stage === "Advance Received" ||
  stage === "Order Received" ||
  stage === "KYC Received" ||
  stage === "Order Confirmed"
