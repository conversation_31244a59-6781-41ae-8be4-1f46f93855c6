export function registerServiceWorker(): void {
  if (typeof window !== "undefined" && "serviceWorker" in navigator) {
    window.addEventListener("load", () => {
      navigator.serviceWorker.register("/sw.js").then(
        (registration) => {
          console.info(
            "Service Worker registration successful with scope: ",
            registration.scope,
          )
        },
        () => {
          console.info("Service Worker registration failed")
        },
      )
    })
  }
}
