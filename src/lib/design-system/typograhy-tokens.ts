interface TypographyStyle {
  fontSize: string
  lineHeight: string
  letterSpacing: string
  fontWeight: string
}

interface TypographyCategory {
  [key: string]: TypographyStyle
}

interface Typography {
  display: TypographyCategory
  heading: TypographyCategory
  subheading: TypographyCategory
  body: TypographyCategory
  button: TypographyCategory
  outline: TypographyCategory
}

interface FontSizes {
  [key: string]: [
    string,
    {
      lineHeight: string
      letterSpacing: string
      fontWeight: string
    },
  ]
}

const categoryPrefixes: { [key: string]: string } = {
  display: "d",
  heading: "h",
  subheading: "sh",
  body: "b",
  button: "bt",
  outline: "o",
}

const typography: Typography = {
  display: {
    1: {
      fontSize: "4.5rem",
      lineHeight: "5.125rem",
      letterSpacing: "-0.045rem",
      fontWeight: "700",
    },
    2: {
      fontSize: "4rem",
      lineHeight: "4.5rem",
      letterSpacing: "-0.02em",
      fontWeight: "700",
    },
    3: {
      fontSize: "3.5rem",
      lineHeight: "4rem",
      letterSpacing: "-0.035rem",
      fontWeight: "700",
    },
    4: {
      fontSize: "3rem",
      lineHeight: "3.5rem",
      letterSpacing: "-0.03rem",
      fontWeight: "700",
    },
    5: {
      fontSize: "2.5rem",
      lineHeight: "3rem",
      letterSpacing: "-0.025rem",
      fontWeight: "700",
    },
    6: {
      fontSize: "2rem",
      lineHeight: "2.5rem",
      letterSpacing: "-0.02rem",
      fontWeight: "700",
    },
    7: {
      fontSize: "1.5rem",
      lineHeight: "1.75rem",
      letterSpacing: "-0.02em",
      fontWeight: "700",
    },
  },
  heading: {
    1: {
      fontSize: "2rem",
      lineHeight: "2.5rem",
      letterSpacing: "0rem",
      fontWeight: "700",
    },
    2: {
      fontSize: "1.5rem",
      lineHeight: "2rem",
      letterSpacing: "0rem",
      fontWeight: "700",
    },
    3: {
      fontSize: "1.5rem",
      lineHeight: "2rem",
      letterSpacing: "0rem",
      fontWeight: "500",
    },
    4: {
      fontSize: "1.25rem",
      lineHeight: "1.75rem",
      letterSpacing: "0rem",
      fontWeight: "700",
    },
    5: {
      fontSize: "1.25rem",
      lineHeight: "1.75rem",
      letterSpacing: "0rem",
      fontWeight: "500",
    },
    6: {
      fontSize: "1.125rem",
      lineHeight: "1.5rem",
      letterSpacing: "0rem",
      fontWeight: "700",
    },
    7: {
      fontSize: "1.125rem",
      lineHeight: "1.5rem",
      letterSpacing: "0rem",
      fontWeight: "600",
    },
  },
  subheading: {
    1: {
      fontSize: "1.125rem",
      lineHeight: "1.5rem",
      letterSpacing: "0rem",
      fontWeight: "500",
    },
    2: {
      fontSize: "1rem",
      lineHeight: "1.5rem",
      letterSpacing: "0rem",
      fontWeight: "700",
    },
    3: {
      fontSize: "1rem",
      lineHeight: "1.5rem",
      letterSpacing: "0rem",
      fontWeight: "600",
    },
    4: {
      fontSize: "0.875rem",
      lineHeight: "1.125rem",
      letterSpacing: "0rem",
      fontWeight: "700",
    },
    5: {
      fontSize: "0.875rem",
      lineHeight: "1.125rem",
      letterSpacing: "0rem",
      fontWeight: "600",
    },
    6: {
      fontSize: "0.75rem",
      lineHeight: "1rem",
      letterSpacing: "0rem",
      fontWeight: "700",
    },
    7: {
      fontSize: "0.75rem",
      lineHeight: "1rem",
      letterSpacing: "0rem",
      fontWeight: "600",
    },
  },
  body: {
    1: {
      fontSize: "1.125rem",
      lineHeight: "1.5rem",
      letterSpacing: "0rem",
      fontWeight: "400",
    },
    2: {
      fontSize: "1rem",
      lineHeight: "1.5rem",
      letterSpacing: "0rem",
      fontWeight: "500",
    },
    3: {
      fontSize: "1rem",
      lineHeight: "1.5rem",
      letterSpacing: "0rem",
      fontWeight: "400",
    },
    4: {
      fontSize: "0.875rem",
      lineHeight: "1.125rem",
      letterSpacing: "0rem",
      fontWeight: "500",
    },
    "4-700": {
      fontSize: "0.875rem",
      lineHeight: "1.125rem",
      letterSpacing: "0rem",
      fontWeight: "700",
    },
    5: {
      fontSize: "0.875rem",
      lineHeight: "1.125rem",
      letterSpacing: "0rem",
      fontWeight: "400",
    },
    6: {
      fontSize: "0.75rem",
      lineHeight: "1rem",
      letterSpacing: "0rem",
      fontWeight: "500",
    },
    7: {
      fontSize: "0.75rem",
      lineHeight: "1rem",
      letterSpacing: "0rem",
      fontWeight: "400",
    },
  },
  button: {
    1: {
      fontSize: "1.125rem",
      lineHeight: "1.5rem",
      letterSpacing: "0rem",
      fontWeight: "600",
    },
    2: {
      fontSize: "1rem",
      lineHeight: "1.25rem",
      letterSpacing: "0rem",
      fontWeight: "600",
    },
    3: {
      fontSize: "0.875rem",
      lineHeight: "1rem",
      letterSpacing: "0rem",
      fontWeight: "600",
    },
    4: {
      fontSize: "0.75rem",
      lineHeight: "1rem",
      letterSpacing: "0rem",
      fontWeight: "600",
    },
  },
  outline: {
    1: {
      fontSize: "0.875rem",
      lineHeight: "1.125rem",
      letterSpacing: "0rem",
      fontWeight: "600",
    },
    2: {
      fontSize: "0.75rem",
      lineHeight: "1rem",
      letterSpacing: "0rem",
      fontWeight: "600",
    },
    3: {
      fontSize: "0.625rem",
      lineHeight: "0.875rem",
      letterSpacing: "0rem",
      fontWeight: "700",
    },
    4: {
      fontSize: "0.625rem",
      lineHeight: "0.875rem",
      letterSpacing: "0rem",
      fontWeight: "500",
    },
  },
}

export const fontSizes: FontSizes = Object.fromEntries(
  Object.entries(typography).flatMap(([category, sizes]) =>
    Object.entries(sizes as TypographyCategory).map(([size, style]) => {
      const { fontSize, lineHeight, letterSpacing, fontWeight } = style
      return [
        `${categoryPrefixes[category]}${size}`,
        [fontSize, { lineHeight, letterSpacing, fontWeight }],
      ] as const
    }),
  ),
)
