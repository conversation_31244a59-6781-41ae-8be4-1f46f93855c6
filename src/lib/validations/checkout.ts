import * as z from "zod"

export const mapLocationSchema = z.object({
  latitude: z.number(),
  longitude: z.number(),
  formattedAddress: z.string(),
  city: z.string(),
  state: z.string(),
  pincode: z.union([z.string(), z.number()]),
})

export const deliveryAddressSchema = z.object({
  user_email: z
    .string()
    .email("Invalid email address")
    .optional()
    .or(z.literal("")),
  full_address: z.string().min(1, "Full address is required"),
  house_details: z.string().min(1, "This Field is required"),
  road: z.string().optional().or(z.literal("")),
  landmark: z.string().optional().or(z.literal("")),
  area_details: z.string().optional().or(z.literal("")),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  pincode: z.union([
    z.string().regex(/^\d{6}$/, "Pincode must be a 6-digit number"),
    z
      .number()
      .int()
      .positive()
      .refine(
        (val) => val.toString().length === 6,
        "Pincode must be a 6-digit number",
      )
      .transform(String),
  ]),
  latitude: z.string().min(1, "Latitude is required"),
  longitude: z.string().min(1, "Longitude is required"),
  nickname: z.string().optional().or(z.literal("")),

  username: z.string().optional().or(z.literal("")),

  shipping_number: z
    .string()
    .regex(/^\d{10}$/, "Phone number must be exactly 10 digits")
    .optional()
    .or(z.literal("")),

  delivery_instructions: z.string().optional(),
})

export const contactDetailsSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  // country_code_calling: z.string(),
  country_code_calling: z.number(),
  calling_number: z.string().min(10, "Phone number must be at least 10 digits"),
  country_code_whatsapp: z.number(),
  whatsapp_number: z
    .string()
    .min(10, "WhatsApp number must be at least 10 digits"),
  isWhatsappSame: z.boolean(),
})

export const paymentMethodSchema = z.object({
  type: z.enum(["full", "cod"]),
})

export type ContactDetailsFormData = z.infer<typeof contactDetailsSchema>
export type DeliveryAddressFormData = z.infer<typeof deliveryAddressSchema>
export type PaymentMethodFormData = z.infer<typeof paymentMethodSchema>
export type MapLocation = z.infer<typeof mapLocationSchema>
