import * as z from "zod"

export const personalInformationSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string(),
  email: z.union([
    z.string().email("Invalid email address"),
    z.literal(""),
    z.undefined(),
  ]),
  country_code_calling: z.number(),
  calling_number: z.string().min(10, "Phone number must be at least 10 digits"),
  country_code_whatsapp: z.number(),
  whatsapp_number: z
    .string()
    .min(10, "WhatsApp number must be at least 10 digits"),
  isWhatsappSame: z.boolean(),
  // gender: z.enum(['male', 'female', 'other']),
  date_of_birth: z.union([
    z.string().min(1, "Date of birth is required"),
    z.literal(""),
    z.undefined(),
  ]),
})

export type PersonalInformationFormData = z.infer<
  typeof personalInformationSchema
>
