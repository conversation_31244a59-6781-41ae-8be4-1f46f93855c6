import * as z from "zod"

export const creatorSchema = z.object({
  first_name: z.string().min(2, "First name must be at least 2 characters"),
  last_name: z.string().optional(),
  country_code: z.string().default("91"),
  phone_number: z
    .string()
    .regex(/^\d{10}$/, "Phone number must be exactly 10 digits"),
  email: z.string().email("Please enter a valid email address"),
  instagram_handle: z.string().min(1, "Instagram handle is required"),
  youtube_handle: z.string().optional(),
  niche: z.string().min(1, "Please select your niche"),
  category_interest: z.string().min(1, "Please select your interest"),
  offer_id: z.number().min(1, "Please select an offer"),
  following_range: z.string().min(1, "Following range is required"),
})

export interface CreatorOffer {
  id: number
  combined_following: string
  cashback: number
}

export type CreatorFormData = z.infer<typeof creatorSchema>
