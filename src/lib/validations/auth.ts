import * as z from "zod"

export const phoneSchema = z.object({
  calling_number: z
    .string()
    .min(10, "Phone number must be 10 digits")
    .max(10, "Phone number must be 10 digits")
    .regex(/^\d+$/, "Phone number must contain only digits"),
})

export const otpSchema = z.object({
  otp: z
    .string()
    .length(6, "OTP must be 6 digits")
    .regex(/^\d+$/, "OTP must contain only digits"),
})

const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

export const profileSchema = z.object({
  first_name: z.string().min(2, "First name must be at least 2 characters"),

  last_name: z.string().min(2, "Last name must be at least 2 characters"),

  email: z
    .string()
    .regex(emailRegex, "Please enter a valid email address")
    .email("Please enter a valid email address"),
})

export type PhoneFormData = z.infer<typeof phoneSchema>
export type OtpFormData = z.infer<typeof otpSchema>
export type ProfileFormData = z.infer<typeof profileSchema>
