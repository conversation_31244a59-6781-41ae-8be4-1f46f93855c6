import { capitalizeFirstLetter } from "@/functions/small-functions"

export function generateBreadcrumbs(path: string) {
  const parts = path.split("/").filter(Boolean)
  const breadcrumbs = parts.map((part, index) => {
    const href = `/${parts.slice(0, index + 1).join("/")}`
    const label = capitalizeFirstLetter(part.replace(/-/g, " "))
    return { href, label }
  })

  return [{ href: "/", label: "Home" }, ...breadcrumbs]
}
