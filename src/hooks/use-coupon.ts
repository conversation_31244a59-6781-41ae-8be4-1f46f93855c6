import { verifyCoupon } from "@/actions/checkout"
import { check_coupon_validity } from "@/services/coupon"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import * as Sentry from "@sentry/nextjs"
import { addDays } from "date-fns"
import { useEffect, useState } from "react"
import { toast } from "sonner"

const useCoupon = () => {
  const {
    total_rent,
    applied_coupon_code,
    setAppliedCouponCode,
    setDeliveryCharges,
    finalAmount,
  } = useCheckoutStore()
  const [couponCode, setCouponCode] = useState(applied_coupon_code || "")
  const [isLoading, setIsLoading] = useState(false)
  const [showViewCoupons, setShowViewCoupons] = useState(false)

  const { delivery_date, total_days } = useRentalStore()

  const handleRemoveCoupon = () => {
    setAppliedCouponCode("", 0)
  }
  const [adminOnly, setAdminOnly] = useState(false)
  useEffect(() => {
    setAdminOnly(
      ((window &&
        window.sessionStorage &&
        window.sessionStorage.getItem("backend_order")) ??
        "") == "true",
    )
  }, [])

  const handleApplyCoupon = async (
    code: string,
    total_rental_amount?: number,
    removeCoupon?: true,
  ) => {
    try {
      Sentry.captureMessage("ApplyCoupon", {
        level: "warning",
        extra: {
          user_name: "John Doe",
          email_id: "<EMAIL>",
        },
      })

      if (couponCode == code && removeCoupon) {
        handleRemoveCoupon()
        return
      }

      setIsLoading(true)

      if (!code || code === "") {
        toast.info("Please Enter the Coupon Code")
        return
      }
      if (!delivery_date) {
        toast.error("Select your rental period again!")
        return
      }

      const verified = await verifyCoupon({
        applied_coupon: code,
        delivery_date: addDays(delivery_date, 1),
        coupon_type: "rent",
        total_days: total_days,
      })

      if (!verified) {
        toast.error("Coupon is Not Verified")
        return
      }

      const {
        days_to_deliver,
        days_to_deliver_active,
        coupon_active,
        show_on_website,
        cart_active,
        cart_minimum_value,
        delivery_discount,
        cart_discount,
        cart_max_discount,
        coupon_code,
        admin,
      } = verified

      if (admin && !adminOnly) {
        toast.error("Coupon is not applicable for this order")
        return
      }

      if (coupon_code === "PAIDREPLACEMENT") {
        toast.info("Applying paid replacement coupon")
        //logic for paid replacement
        if (coupon_active) {
          setAppliedCouponCode(coupon_code, 0)
          setDeliveryCharges(299)
        }
        // toast.success("Coupon Applied Successfully")
        return
      } else if (
        coupon_code === "ZEROORDER" ||
        coupon_code === "SPDAMAGEMIG" ||
        coupon_code === "SNBPZ"
      ) {
        toast.info("Applying special coupon")
        //logic
        if (coupon_active) {
          setAppliedCouponCode(coupon_code, finalAmount())
        }
        toast.success("Coupon Applied Successfully")
        return
      }

      const couponIsValid = check_coupon_validity({
        delivery_date,
        total_rent: total_rent || total_rental_amount || 0,
        delivery_discount,
        days_to_deliver,
        days_to_deliver_active,
        coupon_active,
        show_on_website,
        cart_active,
        cart_minimum_value,
      })

      if (!couponIsValid) {
        toast.error("Coupon is Not Applicable")
        return
      }

      let couponDiscount = 0
      if (delivery_discount) {
        couponDiscount = 299
      } else {
        const partner_discount = 0 // handle partner discount
        const discount = Math.min(
          ((total_rent || total_rental_amount || 0) - (partner_discount ?? 0)) *
            cart_discount,
          cart_max_discount,
        )
        couponDiscount = discount
      }
      // toast.success("Coupon Applied Successfully")
      setAppliedCouponCode(coupon_code, couponDiscount)
      return true
    } catch (error) {
      console.error("Error applying coupon:", error)
      toast.error(
        error instanceof Error ? error.message : "Failed to apply coupon",
      )
    } finally {
      setIsLoading(false)
    }
  }
  useEffect(() => {
    setCouponCode(applied_coupon_code)
  }, [applied_coupon_code])

  return {
    couponCode,
    isLoading,
    showViewCoupons,
    setShowViewCoupons,
    handleApplyCoupon,
    setCouponCode,
    handleRemoveCoupon,
  }
}

export default useCoupon
