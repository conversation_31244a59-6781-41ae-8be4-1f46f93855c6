// useHeaderLogic.ts
import { fetchAllCartItems } from "@/actions/cart"
import { fetchSameDaySurge, fetchSurgeFactor } from "@/actions/surge"
import { getUserWallet, updateUserDates } from "@/actions/user"
import { roundValue } from "@/functions/business-logics"
import { getCookie, setCookie } from "@/functions/cookies"
import generateUniqueId from "@/functions/generate-uuid"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { customFetchPost } from "@/utils/customFetch"
import { areDatesEqual, isDateBeforeToday } from "@/utils/date-logics"
import { useQuery } from "@tanstack/react-query"
import { addDays } from "date-fns"
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"

export const useHeaderLogic = () => {
  const [isWhatsappSupportModalOpen, setIsWhatsappSupportModalOpen] =
    useState(false)
  const [isCityModalOpen, setCityModalOpen] = useState(false)
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  const {
    delivery_date,
    pickup_date,
    total_days,
    selectedCity,
    setDeliveryDate,
    setPickupDate,
    setSameDaySurge,
    setSameDaySurgeLoading,
    setSurgeFactor,
    isCartOpen,
    setCartOpen,
    setCityMinthMap,
    same_day_surge,
  } = useRentalStore()

  const { user, fetchUser, setIsLoggedIn, isLoggedIn, setWallet } =
    useUserStore()
  const { items_count, setCartItems, setWalletBalance } = useCheckoutStore()

  const { city, cat, subcat, product } = useParams<{
    cat: string
    city: string
    subcat: string
    product: string
  }>()

  // Fetch user data
  const { isLoading: isUserLoading } = useQuery({
    queryKey: ["user"],
    queryFn: () => fetchUser(selectedCity.city_url),
    enabled: !user,
    refetchInterval: 60 * 60 * 1000, // 1 hour
  })

  const { data: surgeFactorData } = useQuery({
    queryKey: ["surgeFactor", delivery_date],
    queryFn: async () => {
      if (!delivery_date) return { surge_factor: 1 }
      const response = await fetchSurgeFactor({
        delivery_date: addDays(delivery_date, 1),
      })
      return response?.surge_date ? response : { surge_factor: 1 }
    },
    enabled: !!delivery_date, // Only fetch when delivery_date exists
    staleTime: 1000 * 60 * 5, // Cache results for 5 minutes
  })

  const { data: sameDaySurgeData, isLoading: isSameDaySurgeLoading } = useQuery(
    {
      queryKey: ["sameDaySurge", selectedCity?.city_url, delivery_date],
      queryFn: async () => {
        if (!selectedCity?.city_url) return { same_day_surge: 1 }
        const response = await fetchSameDaySurge(selectedCity.city_url)
        if (
          response &&
          delivery_date &&
          areDatesEqual(delivery_date, new Date())
        ) {
          return response
        }
        return { same_day_surge: 1 }
      },
      enabled: !!selectedCity?.city_url, // Only fetch when city is selected
    },
  )

  const {} = useQuery({
    queryKey: ["user_wallet"], // Unique query key
    queryFn: async () => {
      const wallet = await getUserWallet()
      setWalletBalance(roundValue(wallet.amount))
      setWallet(wallet)
      return wallet
    },

    refetchOnWindowFocus: false, // Prevent refetch on window focus
    enabled: !isUserLoading, // Enable the query when user data is loaded
  })

  const { data: cityMinth } = useQuery({
    queryKey: ["fetch/city_minth", selectedCity.city_url], // Unique query key
    queryFn: async () =>
      customFetchPost<{
        subcategory_id: { sc_name: string }[][]
        subcategory_city_th: number[]
      }>("https://api.sharepal.in/api:EhQQ9F-L/city/minth", {
        city_url: selectedCity.city_url,
      }),

    refetchOnWindowFocus: false, // Prevent refetch on window focus
    enabled: !isUserLoading, // Enable the query when user data is loaded
  })

  useEffect(() => {
    if (cityMinth?.subcategory_city_th && cityMinth?.subcategory_city_th) {
      const cityMinthMapData: Record<string, number> = {}

      cityMinth.subcategory_id.forEach((data, index) => {
        cityMinthMapData[data[0].sc_name?.toLowerCase()] =
          cityMinth?.subcategory_city_th[index] // assuming 'id' is the number
      })

      setCityMinthMap(cityMinthMapData)
    }
  }, [cityMinth])

  //calling fetch cart items
  useEffect(() => {
    const FetchCartItems = async () => {
      try {
        const localUid = getCookie("uid")
        if (total_days > 0 && localUid) {
          const cartResult = await fetchAllCartItems({
            num_days: total_days,
            user_uid: localUid,
          })
          if (!cartResult) return []
          setCartItems(cartResult, same_day_surge)
          return cartResult
        }
      } catch (error) {
        console.error("Error fetching cart items:", error)
      }
    }

    FetchCartItems()
  }, [total_days, same_day_surge, setCartItems])

  // Set surge values when queries complete
  useEffect(() => {
    setSameDaySurgeLoading(isSameDaySurgeLoading)
    setSameDaySurge(sameDaySurgeData?.same_day_surge ?? 1)
    setSurgeFactor(surgeFactorData?.surge_factor ?? 1)
  }, [
    setSameDaySurgeLoading,
    sameDaySurgeData,
    surgeFactorData,
    setSameDaySurge,
    setSurgeFactor,
    isSameDaySurgeLoading,
  ])

  //handling uuid
  useEffect(() => {
    const localUid = getCookie("uid")
    if (!localUid) {
      setCookie("uid", generateUniqueId(), { expires: 365 })
    }
  }, [])

  // handle no city
  useEffect(() => {
    if (!city && !selectedCity.city_url) {
      setCityModalOpen(true)
    } else if (city === "india") {
      setCityModalOpen(true)
    } else {
      setCityModalOpen(false)
    }
  }, [city, selectedCity])

  // testing to reduce unncessary re renders
  // const setCityModaCallback = useCallback(
  //   (value: boolean) => setCityModalOpen(value),
  //   [setCityModalOpen]
  // )

  //if home page no city and we have city then redirec to city page
  // useEffect(() => {
  //   if (pathname === '/' && selectedCity.city_url) {
  //     router.push(`/${selectedCity.city_url}`)
  //   }
  // }, [pathname, router, selectedCity])

  // Update user dates
  useEffect(() => {
    if (delivery_date && pickup_date && total_days > 0) {
      updateUserDates({
        delivery_date: delivery_date,
        return_date: pickup_date,
        number_of_days: total_days,
      })
    }
  }, [delivery_date, pickup_date, setDeliveryDate, setPickupDate, total_days])

  // Only need to run onLoad for date check
  useEffect(() => {
    if (delivery_date && pickup_date) {
      if (isDateBeforeToday(delivery_date)) {
        setDeliveryDate(null)
        setPickupDate(null)
      }
    }
  }, [delivery_date, pickup_date, setDeliveryDate, setPickupDate])

  return {
    isWhatsappSupportModalOpen,
    setIsWhatsappSupportModalOpen,
    isCityModalOpen,
    setCityModalOpen,
    isProfileOpen,
    setIsProfileOpen,
    delivery_date,
    pickup_date,
    total_days,
    selectedCity,
    isCartOpen,
    setCartOpen,
    user,
    isLoggedIn,
    setIsLoggedIn,
    items_count,
    cat,
    subcat,
    product,
    isSearchOpen,
    setIsSearchOpen,
    specialRoute: Boolean((cat || subcat) && !product),
  }
}
