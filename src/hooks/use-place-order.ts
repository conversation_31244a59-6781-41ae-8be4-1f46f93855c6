/* eslint-disable @typescript-eslint/no-explicit-any */

import { clearCart } from "@/actions/cart"
import { createOrder, processOrder } from "@/actions/checkout"
import { getCookie } from "@/functions/cookies"
import {
  trackCheckoutCompleted,
  trackCheckoutStartedorPaymentInitiated,
  trackExtension,
  trackPaymentFailed,
} from "@/lib/gtag-event"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { useUserStore } from "@/store/user-store"
import { OrderData } from "@/types"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { handleRazorpay } from "@/utils/razorpay" // Import Razorpay handler
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { toast } from "sonner"

interface ProccesOrderType extends OrderData {
  razorpay_payment_id?: string
  razorpay_order_id?: string
  razorpay_signature?: string
  payment_made?: true
}

type ErrorMetadata = {
  order_id?: string
  payment_id?: string
}

type ErrorResponse = {
  code: string
  description: string
  source: string
  step: string
  reason: string
  metadata: ErrorMetadata
}

const usePlaceOrder = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [isExtensionPaymentLoading, setIsExtensionPaymentLoading] =
    useState(false)
  const [isReturnPaymentLoading, setIsReturnPaymentLoading] = useState(false)
  const queryClient = useQueryClient()
  const { user } = useUserStore()
  const {
    total_rent,
    finalAmount,
    applied_coupon_code,
    delivery_address,
    payment_type,
    handling_charges,
    wallet_used,
    seon_fingerprint,
    adjusted_amount,
    delivery_charges,
    total_deposit,
    total_discount,
    wallet_balance_used,
    cart_items,
    contact_details,
    coupon_discount,
    setCheckoutRedirecting,
    setActiveSection,
    carepal_fee,
    carepal_selected,
    setOrderData,
    gst_number,
  } = useCheckoutStore()

  const router = useRouter()

  const { total_days, city_surge, same_day_surge } = useRentalStore()

  // Validate checkout data
  const validateCheckout = () => {
    const hasCategory6000Item = cart_items?.some(
      (item: any) => item?.category_id === 6000,
    )

    if (!payment_type) {
      toast.error("Please select a payment Option")
      return false
    }

    const hasValidCoupon =
      applied_coupon_code === "ZEROORDER" ||
      applied_coupon_code === "PAIDREPLACEMENT"

    if (hasCategory6000Item && !hasValidCoupon) {
      toast.error(
        "Invalid Order Items in Cart. Please remove the item and try again.",
      )
      toast.info("Contact support if you're facing issues.")
      return false
    }

    if (cart_items.length !== 0 && (!total_days || total_days < 1)) {
      toast.error("Please select the rental days")
      return false
    }

    if (
      !contact_details ||
      !contact_details?.first_name ||
      !contact_details?.last_name ||
      !contact_details?.email ||
      !contact_details?.calling_number ||
      !contact_details?.whatsapp_number
    ) {
      toast.error("Please enter and save your contact details")
      setActiveSection("contact")
      return false
    }

    if (!delivery_address) {
      toast.error("Please enter your delivery address")
      setActiveSection("delivery")
      return false
    }

    if (total_rent === 0) {
      toast.error("Empty cart. Please add items to your cart", {
        description: "Try refreshing the page if you're facing issues.",
      })
      return false
    }

    return true
  }

  // Process the order and notify the user after successful payment
  const processOrderAndNotify = async (orderData: ProccesOrderType) => {
    const processedOrder = await processOrder({
      user_uid: user?.user_uid ?? "",
      amount: orderData.payment_made ? orderData.rent_order.total_amount : 0,
      order_id: orderData?.rent_order?.id,
      wallet_balance_used: wallet_balance_used,
      promised_delivery_date: "",
      utm_source: getCookie("utm_source") ?? "",
      utm_medium: getCookie("utm_medium") ?? "",
      utm_campaign: getCookie("utm_campaign") ?? "",
    })

    if (processedOrder.success) {
      if (user)
        trackCheckoutCompleted(
          cart_items,
          user?.whatsapp_number,
          finalAmount(),
          total_deposit,
          total_discount,
          applied_coupon_code,
          delivery_charges.final,
          delivery_address?.city ?? "",
          delivery_address?.state ?? "",
          delivery_address?.pincode ?? "",
          payment_type,
          orderData.rent_order.order_id,
        )
      toast.success("Order placed successfully!")
      window.dataLayer = window.dataLayer || []
      // window.dataLayer = [];
      window?.dataLayer.push({
        event: "order_success",
        total_value: finalAmount(),
        order_id: orderData.rent_order.order_id,
        currencyCode: "INR",
        projected_value: finalAmount() * 0.1,
        emailId: user?.email,
        // countryCode: user?.country_code_calling,
        // callingNumber: user?.calling_number,
      })
      clearCart()
      router.push(
        "/order/" +
          orderData?.rent_order?.order_id +
          "?status=process_confrimed",
      )
    } else {
      router.push(
        "/order/" + orderData?.rent_order?.order_id + "?status=process_failed",
      )
      throw new Error("Order processing failed")
    }
  }

  //
  // Handle Razorpay payment
  const handleRazorpayPayment = async (orderData: OrderData) => {
    if (user) {
      trackCheckoutStartedorPaymentInitiated(
        "Payment Initiated",
        cart_items,
        user?.whatsapp_number,
        finalAmount(),
        total_deposit,
        total_discount,
        applied_coupon_code,
        delivery_charges.final,
        delivery_address?.city ?? "",
        delivery_address?.state ?? "",
        delivery_address?.pincode ?? "",
        payment_type,
      )

      await handleRazorpay(
        orderData.rzp_order_id,
        user,
        // On Success
        async (response) => {
          const verifyPayment = await fetchWithAuthPost(
            "https://api.sharepal.in/api:AIoqxnqr/payments/verify-razorypay-signature",
            {
              order_id: orderData?.rent_order?.order_id,
              razorpay_order_id: orderData.rzp_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
            },
          )
          if (verifyPayment) {
            await processOrderAndNotify({
              ...orderData,
              payment_made: true,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_order_id: response.razorpay_order_id,
              razorpay_signature: response.razorpay_signature,
            })
            toast.success("Payment successful!")
            router.push(
              "/order/" +
                orderData?.rent_order?.order_id +
                "?status=payment_confrimed",
            )
            setTimeout(() => {
              setCheckoutRedirecting(false)
            })
          } else {
            toast.error("Payment verification failed")
            router.push(
              "/order/" +
                orderData?.rent_order?.order_id +
                "?status=payment_failed",
            )
            setTimeout(() => {
              setCheckoutRedirecting(false)
            })
          }
        },
        // On Failure
        async (error?: ErrorResponse) => {
          console.error("Payment failed:", error)
          toast.error(
            `Payment failed: ${error?.description || "Please try again"}`,
          )
          trackPaymentFailed(
            finalAmount(),
            "Payment Modal Closed",
            orderData?.rent_order?.order_id,
          )
          await fetchWithAuthPost(
            "https://api.sharepal.in/api:AIoqxnqr/order/payment-failed",
            {
              rzp_id: orderData?.rzp_order_id,
              order_id: orderData?.rent_order?.order_id,
              amount: orderData?.rent_order?.total_amount,
              error_desc: error?.description,
              error_code: error?.code,
              error_reason: error?.reason,
              payment_id: error?.metadata.payment_id,
            },
          )
          router.push(
            "/order/" + orderData?.rent_order?.order_id + "?status=cancelled",
          )
          setTimeout(() => {
            setCheckoutRedirecting(false)
          })
        },
        // OnDismiss
        () => {
          setCheckoutRedirecting(false)
        },
      )
    } else {
      throw new Error("User not found")
    }
  }

  // Main function to handle payment
  const handlePayment = async () => {
    setIsLoading(true)

    // Return early if checkout validation fails
    if (!validateCheckout()) {
      setIsLoading(false)
      return
    }

    try {
      if (!user) {
        throw new Error("User not found")
      }
      trackCheckoutStartedorPaymentInitiated(
        "Checkout Started",
        cart_items,
        user?.whatsapp_number,
        finalAmount(),
        total_deposit,
        total_discount,
        applied_coupon_code,
        delivery_charges.final,
        delivery_address?.city ?? "",
        delivery_address?.state ?? "",
        delivery_address?.pincode ?? "",
        payment_type,
      )
      setCheckoutRedirecting(true)
      const orderData = await createOrder({
        cod_handling_charge: handling_charges,
        coupon_code: applied_coupon_code,
        address_id: delivery_address?.id ?? 0,
        wallet_applied: wallet_used,
        total_amount: finalAmount(),
        total_amount_rent: finalAmount(),
        time_slot: "",
        payment_option: payment_type,
        // partner_name: user.user_partner, // Use the user's partner name also get partner discount using getPartnerDiscount
        partner_name: "sharepal",
        seon_fingerprint: seon_fingerprint,
        same_day_surge: same_day_surge,
        coupon_discount: coupon_discount,
        city_surge: city_surge,
        adjusted_amount: adjusted_amount,
        carepal_applied: carepal_selected,
        wallet_balance_used,
        total_rent: total_rent,
        carepal_amount: carepal_fee,
        gstin: gst_number,
      })
      setOrderData(orderData)

      if (
        payment_type === 3 || // Payment option 3 is for COD (Cash on Delivery)
        payment_type === 5 || // Payment option 5 could represent a free order
        (finalAmount() === 0 && // Zero amount order
          orderData.rent_order)
      ) {
        await processOrderAndNotify(orderData)
      } else {
        await handleRazorpayPayment(orderData)
      }
    } catch (error) {
      console.error("Payment error:", error)
      toast.error(
        error instanceof Error
          ? error.message
          : "Unable to create order. Please try again. refresh the page if the issue persists.",
      )
      setCheckoutRedirecting(false)
    } finally {
      setIsLoading(false)
    }
  }

  const { mutate: hanldePaymentForOrder } = useMutation({
    mutationFn: async ({
      order_id,
      amount,
      type = "BP",
    }: {
      order_id: string
      amount: number
      type?: "BP" | "EX"
    }) => {
      setIsLoading(true)
      try {
        const rzp_id = await fetchWithAuthPost<string>(
          "https://api.sharepal.in/api:AIoqxnqr/payments/create",
          {
            order_id: order_id,
            amount: amount,
            type,
          },
        )
        if (user)
          await handleRazorpay(
            rzp_id,
            user,
            // On Success
            async (response) => {
              const verifyPayment = await fetchWithAuthPost(
                "https://api.sharepal.in/api:AIoqxnqr/payments/verify-razorypay-signature",
                {
                  order_id: order_id,
                  razorpay_order_id: rzp_id,
                  razorpay_payment_id: response.razorpay_payment_id,
                  razorpay_signature: response.razorpay_signature,
                },
              )
              if (verifyPayment) {
                const data = await fetchWithAuthPost<{ status: string }>(
                  "https://api.sharepal.in/api:AIoqxnqr/return/process-payment",
                  {
                    rzp_id: rzp_id,
                    order_id: order_id,
                  },
                )
                if (data.status == "paid") {
                  toast.success("Payment successful!")
                  queryClient.invalidateQueries({
                    queryKey: ["order-details-fetch"],
                  })
                }
              } else {
                toast.error("Payment verification failed")
              }
            },
            // On Failure
            (error) => {
              console.error("Payment failed:", error)
              toast.error(
                `Payment failed: ${error?.message || "Please try again"}`,
              )
            },
            // OnDismiss
            () => {
              toast.error("Payment Cancelled")
            },
          )
        return {}
      } catch (error) {
        console.error(error)
      } finally {
        setIsLoading(false)
      }
    },
  })

  const { mutate: hanldePaymentForExtensionOrder } = useMutation({
    mutationFn: async ({
      order_id,
      amount,
      type = "OEX",
      days,
      pickup_date,
      new_pickup_date,
    }: {
      order_id: string
      amount: number
      type?: "OEX"
      days: number
      pickup_date?: Date
      new_pickup_date: Date
    }) => {
      setIsExtensionPaymentLoading(true)
      try {
        const rzp_id = await fetchWithAuthPost<string>(
          "https://api.sharepal.in/api:AIoqxnqr/payments/create",
          {
            order_id: order_id,
            amount: amount,
            type,
          },
        )

        try {
          await fetchWithAuthPost<string>(
            "https://api.sharepal.in/api:qsuyzexA/order/extension-request",
            {
              order_id,
              amount,
              days,
              rzp_id,
            },
          )
          trackExtension({
            order_id,
            amount,
            pickup_date: pickup_date || new Date(),
            user_id: user?.id || 0,
            new_pickup_date,
            days,
            eventName: "Extension Requested",
          })
        } catch {
          toast.error("Failed to create extension request. Please try again.")
          return
        }

        if (user)
          await handleRazorpay(
            rzp_id,
            user,
            // On Success
            async (response) => {
              const verifyPayment = await fetchWithAuthPost(
                "https://api.sharepal.in/api:AIoqxnqr/payments/verify-razorypay-signature",
                {
                  order_id: order_id,
                  razorpay_order_id: rzp_id,
                  razorpay_payment_id: response.razorpay_payment_id,
                  razorpay_signature: response.razorpay_signature,
                },
              )
              if (verifyPayment) {
                const data = await fetchWithAuthPost<{ status: string }>(
                  "https://api.sharepal.in/api:qsuyzexA/extension/payment-process",
                  {
                    rzp_id,
                  },
                )
                if (data.status == "paid") {
                  toast.success("Payment successful!")
                  queryClient.invalidateQueries({
                    queryKey: ["order-details-fetch"],
                  })
                  router.push(
                    "/extend-order/" +
                      order_id +
                      "?status=payment_confirmed&days=" +
                      days,
                  )
                  trackExtension({
                    order_id,
                    amount,
                    pickup_date: pickup_date || new Date(),
                    user_id: user?.id || 0,
                    new_pickup_date,
                    days,
                    eventName: "Extension Payment Success",
                  })
                }
              } else {
                toast.error("Payment verification failed")
                router.push(
                  "/extend-order/" + order_id + "?status=payment_failed",
                )
                trackExtension({
                  order_id,
                  amount,
                  pickup_date: pickup_date || new Date(),
                  user_id: user?.id || 0,
                  new_pickup_date,
                  days,
                  eventName: "Extension Payment Failed",
                })
              }
            },
            // On Failure
            async (error: ErrorResponse) => {
              console.error("Payment failed:", error)
              toast.error(
                `Payment failed: ${error?.reason || "Please try again"}`,
              )
              trackPaymentFailed(
                finalAmount(),
                "Payment Modal Closed",
                order_id,
              )
              trackExtension({
                order_id,
                amount,
                pickup_date: pickup_date || new Date(),
                user_id: user?.id || 0,
                new_pickup_date,
                days,
                eventName: "Extension Payment Failed",
              })
              await fetchWithAuthPost(
                "https://api.sharepal.in/api:AIoqxnqr/order/payment-failed",
                {
                  rzp_id,
                  order_id,
                  amount,
                  error_desc: error?.description,
                  error_code: error?.code,
                  error_reason: error?.reason,
                  payment_id: error?.metadata.payment_id,
                },
              )
              router.push(
                "/extend-order/" + order_id + "?status=payment_failed",
              )
            },
            // OnDismiss
            () => {
              toast.error("Payment Cancelled")
              trackExtension({
                order_id,
                amount,
                pickup_date: pickup_date || new Date(),
                user_id: user?.id || 0,
                new_pickup_date,
                days,
                eventName: "Extension Payment Failed",
              })
              setIsExtensionPaymentLoading(false)
            },
          )
        return {}
      } catch (error) {
        console.error(error)
        setIsExtensionPaymentLoading(false)
      }
    },
  })

  const { mutate: hanldePaymentForReturnOrder } = useMutation({
    mutationFn: async ({
      order_id,
      amount,
      onSuccess,
    }: {
      order_id: string
      amount: number
      onSuccess?: () => void
    }) => {
      setIsReturnPaymentLoading(true)
      try {
        const rzp_id = await fetchWithAuthPost<string>(
          "https://api.sharepal.in/api:AIoqxnqr/payments/create",
          {
            order_id: order_id,
            amount: amount,
            type: "ORT",
          },
        )
        if (user)
          await handleRazorpay(
            rzp_id,
            user,
            // On Success
            async (response) => {
              const verifyPayment = await fetchWithAuthPost(
                "https://api.sharepal.in/api:AIoqxnqr/payments/verify-razorypay-signature",
                {
                  order_id: order_id,
                  razorpay_order_id: rzp_id,
                  razorpay_payment_id: response.razorpay_payment_id,
                  razorpay_signature: response.razorpay_signature,
                },
              )
              if (verifyPayment) {
                const data = await fetchWithAuthPost<{ status: string }>(
                  "https://api.sharepal.in/api:AIoqxnqr/return/process-payment",
                  {
                    rzp_id,
                    order_id,
                    amount,
                  },
                )
                if (data.status == "paid") {
                  toast.success("Payment successful!")
                  queryClient.invalidateQueries({
                    queryKey: ["order-details-fetch"],
                  })
                  if (onSuccess) onSuccess()
                  router.push(
                    "/return-payment/" + order_id + "?status=payment_confirmed",
                  )
                  setIsReturnPaymentLoading(false)
                }
              } else {
                toast.error("Payment verification failed")
                router.push(
                  "/return-payment/" + order_id + "?status=payment_failed",
                )
                setIsReturnPaymentLoading(false)
              }
            },
            // On Failure
            async (error: ErrorResponse) => {
              console.error("Payment failed:", error)
              toast.error(
                `Payment failed: ${error?.reason || "Please try again"}`,
              )
              trackPaymentFailed(amount, "Payment Modal Closed", order_id)
              await fetchWithAuthPost(
                "https://api.sharepal.in/api:AIoqxnqr/order/payment-failed",
                {
                  rzp_id,
                  order_id,
                  amount,
                  error_desc: error?.description,
                  error_code: error?.code,
                  error_reason: error?.reason,
                  payment_id: error?.metadata.payment_id,
                },
              )
              router.push(
                "/return-payment/" + order_id + "?status=payment_failed",
              )
              setIsReturnPaymentLoading(false)
            },
            // OnDismiss
            () => {
              toast.error("Payment Dismissed")
              setIsReturnPaymentLoading(false)
            },
          )
        return {}
      } catch (error) {
        console.error(error)
        setIsReturnPaymentLoading(false)
      }
    },
  })

  // const { mutate: hanldePaymentForCarepal } = useMutation({
  //   mutationFn: async ({
  //     order_id,
  //     amount,
  //   }: {
  //     order_id: string
  //     amount: number
  //   }) => {
  //     setIsLoading(true)
  //     try {
  //       const rzp_id = await fetchWithAuthPost<string>(
  //         "https://api.sharepal.in/api:AIoqxnqr/payments/create",
  //         {
  //           order_id: order_id,
  //           amount: amount,
  //           type: "CP",
  //         },
  //       )

  //       if (user)
  //         await handleRazorpay(
  //           rzp_id,
  //           user,
  //           // On Success
  //           async (response) => {
  //             const verifyPayment = await fetchWithAuthPost(
  //               "https://api.sharepal.in/api:AIoqxnqr/payments/verify-razorypay-signature",
  //               {
  //                 order_id: order_id,
  //                 razorpay_order_id: rzp_id,
  //                 razorpay_payment_id: response.razorpay_payment_id,
  //                 razorpay_signature: response.razorpay_signature,
  //               },
  //             )
  //             if (verifyPayment) {
  //               const data = await fetchWithAuthPost<{ status: string }>(
  //                 "https://api.sharepal.in/api:BV_IWA_a/carepal/payment-process",
  //                 {
  //                   rzp_id,
  //                 },
  //               )
  //               if (data.status == "paid") {
  //                 toast.success("Payment successful!")
  //                 router.push(
  //                   "/carepal/" + order_id + "?status=payment_confirmed",
  //                 )
  //               }
  //             } else {
  //               toast.error("Payment verification failed")
  //               router.push("/carepal/" + order_id + "?status=payment_failed")
  //             }
  //           },
  //           // On Failure
  //           async (error: ErrorResponse) => {
  //             console.error("Payment failed:", error)
  //             toast.error(
  //               `Payment failed: ${error?.reason || "Please try again"}`,
  //             )
  //             trackPaymentFailed(
  //               finalAmount(),
  //               "Payment Modal Closed",
  //               order_id,
  //             )
  //             await fetchWithAuthPost(
  //               "https://api.sharepal.in/api:AIoqxnqr/order/payment-failed",
  //               {
  //                 rzp_id,
  //                 order_id,
  //                 amount,
  //                 error_desc: error?.description,
  //                 error_code: error?.code,
  //                 error_reason: error?.reason,
  //                 payment_id: error?.metadata.payment_id,
  //               },
  //             )
  //             router.push("/carepal/" + order_id + "?status=payment_failed")
  //           },
  //           // OnDismiss
  //           () => {
  //             toast.error("Payment Cancelled")
  //             setIsLoading(false)
  //           },
  //         )
  //       return {}
  //     } catch (error) {
  //       console.log(error)
  //       setIsLoading(false)
  //     }
  //   },
  // })

  return {
    handlePayment,
    isLoading,
    isExtensionPaymentLoading,
    processOrderAndNotify,
    handleRazorpayPayment,
    validateCheckout,
    hanldePaymentForOrder,
    hanldePaymentForExtensionOrder,
    hanldePaymentForReturnOrder,
    isReturnPaymentLoading,
    // hanldePaymentForCarepal,
  }
}

export default usePlaceOrder
