import { calculateRent } from "@/functions/business-logics"
import { useCheckoutStore } from "@/store/checkout-store"
import { useRentalStore } from "@/store/rental-store"
import { CartItem, RentalItem } from "@/types"

interface CalculateRentProps {
  product?: RentalItem
  cart?: CartItem
  type: "product" | "cart"
  custom_total_days?: number
}

const useCalculateRent = (props: CalculateRentProps) => {
  const {
    city_surge,
    same_day_surge,
    surge_factor,
    total_days,
    surgeProductMap,
    partner,
  } = useRentalStore()

  const { cart_items } = useCheckoutStore()

  // Extract shared calculation parameters
  const getCalculationParams = ({
    product,
    cart,
    type,
    custom_total_days,
  }: {
    product?: RentalItem
    cart?: CartItem
    type: "product" | "cart"
    custom_total_days?: number
  }) => {
    if (type === "cart" && cart) {
      return {
        total_days: custom_total_days || cart.num_days,
        base_rent: cart.base_rent,
        pd_rent: cart.per_day_rent,
        pt_rent: cart.rent_per_trip,
        n_factor: cart.normalisation_factor,
        surge_factor: cart.surge_factor,
        city_surge: cart.city_surge,
        quantity: cart.quantity,
        product_surge: surgeProductMap[cart.item_name] || 1,
        same_day_surge: same_day_surge,
        partner_surge: partner?.surge_factor || 1,
        remove_surge: partner?.remove_surge || false,
      }
    }

    if (type === "product" && product) {
      return {
        total_days: custom_total_days || total_days,
        base_rent: product.base_rent,
        pd_rent: product.per_day_rent,
        pt_rent: product.per_trip_rent,
        n_factor: product.normalisation_factor,
        surge_factor,
        city_surge,
        product_surge: surgeProductMap[product.ri_short_name] || 1,
        same_day_surge: same_day_surge,
        partner_surge: partner?.surge_factor || 1,
        remove_surge: partner?.remove_surge || false,
      }
    }
    return null
  }

  // Calculate total rent
  const getRent = ({
    product,
    cart,
    type,
    total_days,
  }: {
    product?: RentalItem
    cart?: CartItem
    type: "product" | "cart"
    total_days?: number
  }) => {
    const params = getCalculationParams({
      product,
      cart,
      type,
      custom_total_days: total_days,
    })
    if (!params) return { rent: 0, surge: 0 }
    const calulation = calculateRent(params)
    return calulation
  }

  // Calculate per-day rent for one extra day
  const extraDayRent = ({
    product,
    cart,
    type,
    total_days,
  }: {
    product?: RentalItem
    cart?: CartItem
    type: "product" | "cart"
    total_days?: number
  }) => {
    const params = getCalculationParams({
      product,
      cart,
      type,
      custom_total_days: total_days,
    })
    if (!params) return 0
    const calulation = calculateRent({
      ...params,
      total_days: params.total_days + 1,
    })
    const calulation2 = calculateRent({
      ...params,
      total_days: params.total_days,
    })
    return (
      ((calulation.rent || 0) - (calulation2.rent || 0)) *
      (params?.quantity ?? 1)
    )
  }

  const getTotalCartExtraDayRent = () => {
    const totalCartItemExtraRent = cart_items.reduce(
      (acc, item) =>
        extraDayRent({ type: "cart", cart: item, total_days }) + acc,
      0,
    )

    return totalCartItemExtraRent
  }

  return {
    getRent,
    extraDayRentFn: extraDayRent,
    getTotalCartExtraDayRent,
    extraDayRent: props.type
      ? extraDayRent({
          product: props.product,
          cart: props.cart,
          type: props.type,
          total_days: props.custom_total_days,
        })
      : 0,
    rent: props.type
      ? getRent({
          product: props.product,
          cart: props.cart,
          type: props.type,
          total_days: props.custom_total_days,
        }).rent
      : 0,
    mainSurge: getRent({
      product: props.product,
      cart: props.cart,
      type: props.type,
      total_days: props.custom_total_days,
    }).surge,
  }
}

export default useCalculateRent
