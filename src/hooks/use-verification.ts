// import { useUserStore } from '@/store/use-user-store'
// import { useMutation } from '@tanstack/react-query'
// import axios, { AxiosProgressEvent } from 'axios'
// import { useState } from 'react'

// const useVerification = ({ form }) => {
//   const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
//     {},
//   )
//   const [uploadError, setUploadError] = useState<Record<string, string>>({})
//   const [isUploading, setIsUploading] = useState<Record<string, boolean>>({})
//   const { user, setUser } = useUserStore()
//   // this is to handle upload_file for Pan and occupation both
//   const { mutate: handleFileUpload } = useMutation({
//     mutationFn: async (fileType: 'frontFile' | 'backFile') => {
//       setIsUploading({ [fileType]: true })
//       const file = form.getValues(fileType)

//       const token = localStorage.getItem('token')

//       console.log('file', file)

//       const formData = new FormData()
//       formData.append('file', file)
//       formData.append('file_type', 'pan')

//       const config = {
//         headers: {
//           'Content-Type': 'multipart/form-data',
//           Authorization: `Bearer ${token}`,
//         },
//         method: 'POST',
//         body: formData,
//       }

//       const res = await axios.post(
//         'https://api.sharepal.in/api:YeisLDqw:v1/upload_file',
//         formData,
//         {
//           ...config,
//           onUploadProgress: (progressEvent: AxiosProgressEvent) => {
//             const { loaded, total } = progressEvent
//             if (total) {
//               const percentCompleted = Math.round((loaded * 100) / total)
//               console.log('Upload Progress:', percentCompleted + '%')
//               setUploadProgress({ [fileType]: percentCompleted })
//             }
//           },
//         },
//       )
//       setIsUploading({ [fileType]: false })
//       return res
//     },
//     onSuccess: (res, fileType) => {
//       console.log('file-upload-res', res)
//       if (res.status == 200) {
//         const data = res.data
//         if (user)
//           setUser({
//             ...user,
//             credit_report: data?.credit_report,
//             credit_verification_type: data?.credit_verification_type,
//           })
//         console.log(data)
//       } else {
//         setUploadError((prev) => ({
//           ...prev,
//           [fileType]: 'Upload failed. Please try again.',
//         }))
//       }
//     },
//     onError: (error, fileType) => {
//       setUploadError((prev) => ({
//         ...prev,
//         [fileType]: 'Upload failed. Please try again.',
//       }))
//     },
//   })

//   return {
//     uploadProgress,
//     handleFileUpload,
//     uploadError,
//     isUploading,
//   }
// }
// export default useVerification
