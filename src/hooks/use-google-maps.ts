import { useCallback, useEffect, useRef, useState } from "react"

import type { GoogleMapLocation, MapState } from "@/types/google-maps"

const BANGALORE_COORDINATES = { lat: 12.9716, lng: 77.5946 }
const DEFAULT_ZOOM = 15

export function useGoogleMaps() {
  const [mapState, setMapState] = useState<MapState>({
    map: null,
    marker: null,
    searchBox: null,
  })
  const [address, setAddress] = useState<string>("")
  const [addressDetails, setAddressDetails] =
    useState<GoogleMapLocation | null>(null)
  const mapRef = useRef<HTMLDivElement | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const searchInputRef = useRef<HTMLInputElement | null>(null)

  const initMap = useCallback(() => {
    if (!mapRef.current || typeof window === "undefined" || !window.google)
      return

    const map = new window.google.maps.Map(mapRef.current, {
      center: { lat: 0, lng: 0 },
      zoom: DEFAULT_ZOOM,
      disableDefaultUI: true,
      zoomControl: true,
      streetViewControl: false,
      fullscreenControl: false,
    })

    map.setCenter(BANGALORE_COORDINATES)

    const marker = new window.google.maps.Marker({
      map,
      position: BANGALORE_COORDINATES,
      draggable: true,
    })

    const searchInput = searchInputRef.current
    const searchBox = searchInput
      ? new window.google.maps.places.SearchBox(searchInput)
      : null

    setMapState({ map, marker, searchBox })
  }, [])

  useEffect(() => {
    if (isLoaded && !mapState.map) {
      initMap()
    }
  }, [isLoaded, initMap, mapState.map])

  const handleLocationChange = useCallback(
    async (position: google.maps.LatLng) => {
      const { map, marker } = mapState
      if (!map || !marker) return

      const newPosition = { lat: position.lat(), lng: position.lng() }
      marker.setPosition(newPosition)
      map.panTo(newPosition)

      try {
        const geocoder = new window.google.maps.Geocoder()
        const results = await new Promise<google.maps.GeocoderResult[]>(
          (resolve, reject) => {
            geocoder.geocode(
              { location: newPosition },
              (
                results: google.maps.GeocoderResult[] | null,
                status: google.maps.GeocoderStatus,
              ) => {
                if (status === "OK" && results?.length) {
                  resolve(results)
                } else {
                  reject(new Error("Geocoding failed"))
                }
              },
            )
          },
        )

        const primaryResult = results[0]
        interface AddressComponent {
          long_name: string
          short_name: string
          types: string[]
        }

        interface AddressDetails {
          lat: number
          lng: number
          formatted_address: string
          city?: string
          state?: string
          pincode?: string
          area_details?: string
        }

        const details: AddressDetails = {
          lat: newPosition.lat,
          lng: newPosition.lng,
          formatted_address: primaryResult.formatted_address,
          city: primaryResult.address_components.find((c: AddressComponent) =>
            c.types.includes("locality"),
          )?.long_name,
          state: primaryResult.address_components.find((c: AddressComponent) =>
            c.types.includes("administrative_area_level_1"),
          )?.long_name,
          pincode: primaryResult.address_components.find(
            (c: AddressComponent) => c.types.includes("postal_code"),
          )?.long_name,
          area_details: primaryResult.address_components.find(
            (c: AddressComponent) => c.types.includes("sublocality"),
          )?.long_name,
        }

        setAddress(primaryResult.formatted_address)
        setAddressDetails(details)
      } catch (error) {
        console.error("Error getting address:", error)
      }
    },
    [mapState],
  )

  useEffect(() => {
    const { map, marker, searchBox } = mapState
    if (!map || !marker || !searchBox) return

    const markerDragListener = marker.addListener("dragend", () => {
      const position = marker.getPosition()
      if (position) handleLocationChange(position)
    })

    const searchBoxListener = searchBox.addListener("places_changed", () => {
      const places = searchBox.getPlaces()
      if (places && places.length > 0) {
        const place = places[0]
        if (place.geometry && place.geometry.location) {
          handleLocationChange(place.geometry.location)
        }
      }
    })

    return () => {
      markerDragListener.remove()
      searchBoxListener.remove()
    }
  }, [mapState, handleLocationChange])

  const getCurrentLocation = useCallback(async () => {
    if (
      typeof window === "undefined" ||
      !navigator.geolocation ||
      !mapState.map ||
      !mapState.marker
    )
      return

    try {
      const position = await new Promise<GeolocationPosition>(
        (resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject)
        },
      )
      const { latitude, longitude } = position.coords
      const newPosition = new window.google.maps.LatLng(latitude, longitude)
      handleLocationChange(newPosition)
    } catch (error) {
      console.error("Error getting current location:", error)
    }
  }, [mapState, handleLocationChange])

  return {
    mapRef,
    address,
    addressDetails,
    isLoaded,
    getCurrentLocation,
    handleLocationChange,
    setIsLoaded,
    searchInputRef,
  }
}
