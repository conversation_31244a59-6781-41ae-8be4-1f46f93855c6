import { useScroll } from "framer-motion"
import { useEffect, useState } from "react"

const useScrollState = (threshold: number) => {
  const { scrollY } = useScroll()
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const unsubscribe = scrollY.on("change", (latest) => {
      setIsScrolled(latest > threshold)
    })

    return () => unsubscribe()
  }, [scrollY, threshold])

  return isScrolled
}

export default useScrollState
