import { useCallback } from "react"
import { addHours, isAfter, isBefore, isToday } from "date-fns"

const useDisabledDate = (minTh: number) =>
  useCallback(
    (date: Date): boolean => {
      // Get current date and time
      const currentDate = new Date()

      // Calculate the minimum allowed date based on minTh (hours)
      const minAllowedDate = addHours(currentDate, minTh)

      // If the date is today and after 6 PM, allow it for selection
      if (
        isToday(date) &&
        isAfter(currentDate, new Date().setHours(18, 0, 0, 0))
      ) {
        // Allow the current day if it’s after 6 PM
        if (isBefore(date, currentDate)) {
          return false
        }
      }

      // Disable dates before the minAllowedDate
      if (isBefore(date, minAllowedDate)) return true

      return false // Otherwise, the date is selectable
    },
    [minTh], // Dependency on minTh to re-calculate when it changes
  )

export default useDisabledDate
