import { useCallback, useRef } from "react"

type AnyFunction = (...args: never[]) => unknown

export function useThrottle<T extends AnyFunction>(
  func: T,
  delay: number = 3000,
): (...args: Parameters<T>) => void {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastCalledRef = useRef<number>(0)

  return useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now()

      if (now - lastCalledRef.current >= delay) {
        func(...args)
        lastCalledRef.current = now
      } else {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }

        timeoutRef.current = setTimeout(
          () => {
            func(...args)
            lastCalledRef.current = Date.now()
          },
          delay - (now - lastCalledRef.current),
        )
      }
    },
    [func, delay],
  )
}
