import {
  fetchProductSurgeByCategory,
  fetchProductSurgeByRINames,
} from "@/actions/surge"
import { useRentalStore } from "@/store/rental-store"
import { ProductSurge } from "@/types"
import { useQuery } from "@tanstack/react-query"
import { addDays } from "date-fns"
import { useEffect, useMemo } from "react"

interface useSurgeProductProps {
  ri_names?: string[]
  type: "ri_names" | "category"
  super_category?: string
  sub_category?: string
}

const useSurgeProduct = ({
  sub_category = "",
  super_category = "",
  ri_names = [],
  type,
}: useSurgeProductProps) => {
  const { delivery_date, setSurgeProducts, selectedCity, setSurgeProductMap } =
    useRentalStore()

  // Memoized Query Key to Prevent Unnecessary Refetches
  const queryKey = useMemo(
    () => [
      "product_surge/fetch",
      selectedCity.city_name,
      delivery_date,
      ri_names,
      super_category,
      sub_category,
    ],
    [
      selectedCity.city_name,
      delivery_date,
      ri_names,
      super_category,
      sub_category,
    ],
  )

  // **Fetch Function to Prevent Unnecessary Execution**
  const fetchSurgeData = async () => {
    if (delivery_date) {
      if (type === "ri_names" && ri_names.length > 0) {
        return fetchProductSurgeByRINames({
          city: selectedCity.city_name,
          delivery_date: addDays(delivery_date, 1),
          ri_name: ri_names,
        })
      } else if (type === "category" && super_category && sub_category) {
        return fetchProductSurgeByCategory({
          city: selectedCity.city_name,
          delivery_date: addDays(delivery_date, 1),
          super_category,
          sub_category,
        })
      }
    }
    return []
  }

  // Use Query with Memoized Query Key
  const { data: surge_product } = useQuery({
    queryKey,
    queryFn: fetchSurgeData,
    enabled: !!delivery_date, // Ensures query runs only when delivery_date exists
    staleTime: 1000 * 60 * 10, // Cache results for 10 minutes
  })

  // Update Store Only When Data Changes
  useEffect(() => {
    if (!surge_product || surge_product.length === 0) return

    setSurgeProducts(surge_product)

    // Map Surge Factor Only If Data Changes
    const newSurgeMap: { [key: string]: number } = surge_product.reduce(
      (acc, item: ProductSurge) => {
        acc[item.ri_name] = item.surge_factor
        return acc
      },
      {} as { [key: string]: number },
    )

    setSurgeProductMap(newSurgeMap)
  }, [surge_product, setSurgeProductMap, setSurgeProducts])
}

export default useSurgeProduct
