import { useReturnOrderStore } from "@/store/return-order-store"
import { authHeader } from "@/utils/fetchWithAuth"
import axios, { AxiosProgressEvent } from "axios"
import { useCallback, useEffect, useState } from "react"
import {
  DropzoneInputProps,
  DropzoneRootProps,
  FileRejection,
  useDropzone,
} from "react-dropzone"
import { toast } from "sonner"

export interface FileWithPreview extends File {
  preview: string
  uploaded?: boolean // Track if the file has been uploaded
}

export interface ImageUploadType {
  files: FileWithPreview[]
  uploadError: string
  uploadProgress: number
  imageLoadErrors: Set<number>
  getRootProps: <T extends DropzoneRootProps>(props?: T) => T
  getInputProps: <T extends DropzoneInputProps>(props?: T) => T
  isDragActive: boolean
  isUploading: boolean
  removeFile: (indexToRemove: number) => void
  handleUpload: () => void
  setImageLoadErrors: React.Dispatch<React.SetStateAction<Set<number>>>
}

export function useImageUpload() {
  const { images, setImages, orderId, goToNextStep } = useReturnOrderStore()
  const [files, setFiles] = useState<FileWithPreview[]>([])
  const [uploadError, setUploadError] = useState<string>("")
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [imageLoadErrors, setImageLoadErrors] = useState<Set<number>>(new Set())

  const handleFileChange = useCallback(
    (newFiles: FileWithPreview[]) => {
      const imageUrls = newFiles.map((file) => file.preview)
      setImages(imageUrls)
    },
    [setImages],
  )

  const initializeFilesFromImages = useCallback(() => {
    if (images.length > 0 && files.length === 0) {
      const fileList = images.map((url, index) => {
        const file = new File([], `image-${index}.jpg`, { type: "image/jpeg" })
        return Object.assign(file, { preview: url, uploaded: true })
      })
      setFiles(fileList)
    }
  }, [images, files.length])

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      setUploadError("")
      const newFiles = acceptedFiles.map((file) =>
        Object.assign(file, {
          preview: URL.createObjectURL(file),
          uploaded: false,
        }),
      )
      const updatedFiles = [...files, ...newFiles]
      setFiles(updatedFiles)
      handleFileChange(updatedFiles)
      setImageLoadErrors(new Set())
    },
    [files, handleFileChange],
  )

  const onDropRejected = useCallback((fileRejections: FileRejection[]) => {
    if (fileRejections.length > 0) {
      setUploadError("Please upload only PNG, JPEG, or JPG files")
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".webp", ".heic", ".heif"],
    },
    maxSize: 5 * 1024 * 1024, // 5MB
    onDrop,
    onDropRejected,
  })

  const removeFile = useCallback(
    (indexToRemove: number) => {
      setFiles((prevFiles) => {
        const fileToRemove = prevFiles[indexToRemove]
        if (fileToRemove?.preview?.startsWith("blob:")) {
          URL.revokeObjectURL(fileToRemove.preview)
        }

        const newFiles = prevFiles.filter((_, index) => index !== indexToRemove)
        handleFileChange(newFiles)

        setImageLoadErrors((prevErrors) => {
          const newErrors = new Set<number>()
          prevErrors.forEach((i) => {
            if (i < indexToRemove) newErrors.add(i)
            else if (i > indexToRemove) newErrors.add(i - 1)
          })
          return newErrors
        })

        return newFiles
      })
    },
    [handleFileChange],
  )

  const handleUpload = async () => {
    if (files.length === 0) {
      setUploadError("Please upload at least one image.")
      return
    }

    const token = localStorage.getItem("token")
    if (!token) {
      toast.error("Authentication required")
      return
    }

    const formData = new FormData()
    const filesToUpload = files.filter((file) => !file.uploaded)

    if (filesToUpload.length === 0) {
      // toast.info("All files have already been uploaded.")
      goToNextStep()
      return
    }

    filesToUpload.forEach((file) => formData.append("images[]", file))
    if (orderId) formData.set("order_id", orderId)

    try {
      setIsUploading(true)

      goToNextStep()

      const response = await axios.post(
        "https://api.sharepal.in/api:qsuyzexA/return/upload-images",
        formData,
        {
          headers: authHeader(token, "multipart/form-data"),
          onUploadProgress: (progressEvent: AxiosProgressEvent) => {
            if (progressEvent.total) {
              const percent = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total,
              )
              setUploadProgress(percent)
              if (percent === 100) {
                setTimeout(() => setUploadProgress(0), 1000)
              }
            }
          },
        },
      )

      // Mark files as uploaded
      setFiles((prevFiles) =>
        prevFiles.map((file) => ({
          ...file,
          uploaded: true,
        })),
      )

      return response
    } catch (error) {
      toast.error("Error uploading images")
      console.error(error)
    } finally {
      setIsUploading(false)
    }
  }

  useEffect(() => {
    initializeFilesFromImages()
  }, [initializeFilesFromImages])

  // useEffect(
  //   () => () => {
  //     files.forEach((file) => {
  //       if (file.preview?.startsWith("blob:")) {
  //         URL.revokeObjectURL(file.preview)
  //       }
  //     })
  //   },
  //   [files],
  // )

  return {
    files,
    uploadError,
    uploadProgress,
    imageLoadErrors,
    getRootProps,
    getInputProps,
    isDragActive,
    isUploading,
    removeFile,
    setImageLoadErrors,
    handleUpload,
  }
}
