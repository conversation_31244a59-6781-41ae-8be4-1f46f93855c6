import * as z from "zod"
import { OrderSummary } from "./order"
import { AdditionalOrderSummary } from "./return-order"

export const scheduleFormSchema = z.object({
  type: z.enum(["pickup", "delivery"]),
  date: z.date(),
  timeSlot: z.string(),
  address: z.object({
    name: z.string(),
    fullAddress: z.string(),
  }),
})

export type ScheduleFormData = z.infer<typeof scheduleFormSchema>

export interface AlreadyBookedResponse {
  id: number
  schedule_timestamp: number
  active: boolean
  order_id: string
  status: string
  calling_number: string
  whatsapp_number: string

  crn_number: string
  schedule_date: string
  schedule_time: string
  stage: string
  requested_by: string
  partner_type: string
}

export interface ScheduleProps {
  type: "pickup" | "delivery"
  isShipmentBooking: boolean
  order_id: string
  order: OrderSummary

  address: {
    name: string
    fullAddress: string
  }
  alreadyBooked: AlreadyBookedResponse | null

  onSchedule: (data: ScheduleFormData) => void
  onClose: () => void

  extendRentalPeriod?: () => void
  isRental?: boolean
  defaultDate?: number
  className?: string
}

export interface ScheduleAgainProps {
  type: "pickup" | "delivery"
  isShipmentBooking: boolean
  order_id: string
  order: OrderSummary

  address: {
    name: string
    fullAddress: string
  }
  alreadyBooked: AlreadyBookedResponse | null

  handleScheduleAgain: (data: ScheduleFormData) => void
  onClose: () => void

  defaultDate?: number
  className?: string
  additionalOrderSummary?: AdditionalOrderSummary | null
}
