// Types and Interfaces
export type ChecklistOption = "return" | "damaged" | "buy_lost" | null
export type ReturnStep = "checklist" | "packing" | "pickup"
export type PaymentOption = "pay_now" | "pay_later" | "without_payment"

export interface ChecklistItem {
  replacement_cost?: number | null
  product_image?: string
  product_name: string
  product_code: string
  status: string
  type: string
  order_id: string
  product_id: string
  product_value: number
}

export interface AdditionalOrderSummary {
  id: number
  deal_name: string
  delay_rental_charge: number
  order_timeline: string[]
  return_charge_received: number
  return_images: string[]
  return_payment_option: string
  timeslot_charges: number
  total_lost_charges: number
  total_return_charges: number
  tsa_applied: boolean
  ziplock_applied: boolean
}

export interface PickupData {
  pickup_time: string
  contact_person: string
  phone_number: string
  country_code: number
  address_id?: string
}

export interface PackingInstructions {
  steps: Array<{
    title: string
    description: string
    image?: string
  }>
}

export interface ReturnOrderState {
  // Navigation
  activeStep: ReturnStep
  showReviewModal: boolean

  // Data
  orderId: string | null
  products: ChecklistItem[]
  packaging: ChecklistItem[]

  // Flattened checklist data fields
  productSelections: Record<string, ChecklistOption>
  packagingSelections: Record<string, ChecklistOption>
  totalCharges: number
  buyLostItemsCount: number
  buyLostTotal: number
  delayRentalCharges: number
  totalDelayDays: number
  timeSlotCharges: number
  previousPayment: number

  //packaging
  tsaLockApplied: boolean
  zipLockApplied: boolean
  images: string[]

  // Payment
  paymentOption: PaymentOption

  // Pickup data
  pickupData: PickupData | null
  packingInstructions: PackingInstructions | null
  isSubmitting: boolean
  tsaPresent: boolean

  // Completion flags
  isChecklistCompleted: boolean
  isPackingCompleted: boolean
  isPickupCompleted: boolean

  // Methods
  setOrderId: (id: string) => void
  setActiveStep: (step: ReturnStep) => void
  toggleReviewModal: (show: boolean) => void
  goToNextStep: () => void
  goToPrevStep: () => void
  setProducts: (products: ChecklistItem[]) => void
  setPackaging: (packaging: ChecklistItem[]) => void
  updateProductSelection: (id: string, option: ChecklistOption) => void
  updatePackagingSelection: (id: string, option: ChecklistOption) => void
  setPickupData: (data: PickupData) => void
  setPackingInstructions: (instructions: PackingInstructions) => void
  setIsSubmitting: (isSubmitting: boolean) => void
  setTsaPresent: (tsaPresent: boolean) => void
  resetStore: () => void
  calculateTotals: () => void
  setTsaLockApplied: (applied: boolean) => void
  setZipLockApplied: (applied: boolean) => void
  setImages: (images: string[]) => void
  addImage: (image: string) => void
  removeImage: (image: string) => void
  clearImages: () => void
  setPaymentOption: (option: PaymentOption) => void
  setDelayRentalCharges: (amount: number) => void
  setPreviousPayment: (amount: number) => void
  seTotalDelayDays: (days: number) => void
  setTimeSlotCharges: (amount: number) => void
}
