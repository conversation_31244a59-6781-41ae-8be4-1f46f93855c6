// Basic user information
interface UserBasicInfo {
  id: number
  created_at: number
  first_name: string
  last_name: string
  email: string
  date_of_birth: number
  user_uid: string
}

// Contact information
interface UserContactInfo {
  whatsapp_number: string
  calling_number: string
  country_code_whatsapp: number
  country_code_calling: number
  user_city: string
  user_zipcode: string
}

// Occupation and verification
interface UserOccupationAndVerification {
  occupation_type: string
  occupation_email: string
  aadhar_details_id: number
  identity_kyc_received: boolean
  occupation_kyc_received: boolean
  occupation_verified_type: string
  identity_verified_type: string
  credit_report: boolean
  credit_verification_type: string
}

// User preferences and activity
interface UserPreferencesAndActivity {
  user_category: string
  source: string
  bubble_id: string
  favourite_items: number[]
  buy_favorite_items: number[]
  estimate_rental_value: number
  estimate_delivery_date: string | null
  cart_item_id: number | null
}

// Rental information
interface UserRentalInfo {
  wallet_id: number
  delivery_date: string
  return_date: string
  number_of_days: number
}

// User role and partner information
interface UserRoleAndPartner {
  user_role: string
  role: string
  user_partner: string
  city_type: number
}

export interface Wallet {
  id: number
  active: boolean
  amount: number
  user_id: number
  calling_number: string
  country_code_calling: number
}
// Main User interface
export interface User
  extends UserBasicInfo,
    UserContactInfo,
    UserOccupationAndVerification,
    UserPreferencesAndActivity,
    UserRentalInfo,
    UserRoleAndPartner {}
