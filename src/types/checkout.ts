export type CheckoutSection = "contact" | "delivery" | "review" | "carepal"

export interface ContactDetails {
  first_name: string
  last_name: string
  email: string
  country_code_calling: number
  calling_number: string
  country_code_whatsapp: number
  whatsapp_number: string
}

export interface PaymentOption {
  id: number
  payment_active: boolean
  payment_type: number
  payment_priority: number
  visible_text: string
  backend_order: boolean
}
export interface DeliveryCharges {
  original: number
  final: number
}

export interface CheckoutParams {
  cod_handling_charge?: number
  coupon_code?: string
  address_id: number
  wallet_applied: boolean
  total_amount: number
  total_amount_rent: number
  time_slot: string
  payment_option: number
  partner_name: string
  seon_fingerprint: string
  same_day_surge?: number
  city_surge?: number
  adjusted_amount?: number
  coupon_discount?: number
  carepal_applied?: boolean
  wallet_balance_used?: number
  total_rent?: number
  carepal_amount?: number
  gstin?: string
}

export interface ProcessOrderParams {
  user_uid: string
  amount: number
  wallet_balance_used?: number
  order_id: number
  promised_delivery_date?: string
  utm_source?: string
  utm_medium?: string
  utm_campaign?: string
}
