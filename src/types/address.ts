// Basic user information
export interface UserInfo {
  user_email: string
  user_id: string
  username?: string
}

// Geographical coordinates
export interface Coordinates {
  latitude: string
  longitude: string
}

// Basic location information
export interface LocationInfo {
  city: string
  state: string
  pincode: number
  location: string
}

// Detailed address information
export interface AddressDetails {
  full_address: string
  house_details: string
  road: string
  landmark: string
  area_details: string
}

// Additional address properties
export interface AddressProperties {
  nickname?: string
  transit_address: boolean
  special_type?: string
}

// Main Address type combining all the above
export interface Address
  extends UserInfo,
    Coordinates,
    LocationInfo,
    AddressDetails,
    AddressProperties {
  id: number
  created_at: number
  active: boolean
  update_ts: number
  shipment_number?: string
}

// Form-specific type with optional fields
export interface AddressFormData {
  user_email: string
  full_address: string
  house_details: string
  road: string
  landmark: string
  area_details: string
  city: string
  state: string
  pincode: number
  latitude: string
  longitude: string
  nickname?: string
  delivery_instructions?: string
}

export interface City {
  id: number
  created_at: number
  city_name: string
  city_sequence: number
  city_type: number
  city_url: string
  category_id: number[]
  subcategory_id: number[]
  city_th: number[]
  subcategory_city_th: number[]
  update_ts: number
  active: boolean
  city_surge: number
  same_day_surge: number
}
