export interface Coupon {
  id: number
  coupon_code: string
  coupon_description: string
  coupon_header: string
  coupon_type: string
  coupon_cat: string
  coupon_active: boolean
  coupon_owner_email: string
  created_at: number // Timestamp in milliseconds
  show_on_website: boolean
  cart_active: boolean
  cart_discount: number // Percentage discount (e.g., 0.1 for 10%)
  cart_max_discount: number // Maximum discount value
  cart_minimum_value: number // Minimum order value for the coupon
  category: string // Optional, could be a specific category or empty
  city_type: string // Optional, could represent a city type or empty
  days_to_deliver: number
  days_to_deliver_active: boolean
  delivery_discount: boolean
  delivery_discount_value: number // Delivery discount value
}
