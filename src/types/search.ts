import { RentalItem } from "./index"

export interface SearchResponse {
  items: RentalItem[]
  total?: number
  page?: number
  per_page?: number
}

export interface SearchParams {
  search_text: string
  city: string
  paging: {
    page: number
    per_page: number
  }
}

export interface SearchContainerProps {
  searchText: string
  setSearchText: (text: string) => void
  searchResults: SearchResponse
  isLoading: boolean
  isFetching: boolean
  onClearSearch: () => void
  onSearch?: () => void
}

export interface SearchDropdownProps {
  data: SearchResponse
  searchText: string
  setSearchText: (text: string) => void
  selectedCity: {
    city_name: string
  }
  isLoading: boolean
}

export interface SearchResultItemProps {
  item: RentalItem
  selectedCity: {
    city_name: string
  }
}
