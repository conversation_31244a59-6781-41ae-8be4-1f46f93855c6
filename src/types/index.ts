export interface RentalItem {
  id: number
  category_name: string
  sc_name: string
  category_id: number
  sc_id: number
  ri_name: string
  ri_image: string
  size_specific: boolean
  ri_code: string
  per_day_rent: number
  base_rent: number
  ri_city: string
  normalisation_factor: number
  per_trip_rent: number
  category_short_name: string
  ri_id: string
  ri_short_name: string
  product_qualities: string
  ri_image_alt_text: string
  out_of_stock: boolean
  rf_price: number
  rf_out_of_stock: boolean
  rating: number
  decoration_text: string
  booked_count: number
  features_specs?: string
  super_category_url: string
  admin_only: boolean
}

export interface RentalItemProduct extends RentalItem {
  ri_deposit: number
  features_specs: string
  goods_value: number
  shipping_cost: number
  sub_product_codes: string
  customizable: boolean
  seo_desc: string
  product_video?: string
  update_ts: number
  buy_price: number
  buy_images: string[]
  buy_out_of_stock: boolean
  buy_warranty?: string
  is_electronic: boolean
  ri_rf_images: string
  has_combo_products: boolean
  is_combo_product: boolean
  has_inclusions: boolean
  super_category_url: string
}

export interface Category {
  id: number
  category_id: number
  category_image: string
  category_name: string
  category_short_name: string
  url: string
  category_card_image: string
  on_rent: boolean
  on_buy: boolean
  on_rf: boolean
  category_icon: string
  super_category_url: string
}

export interface ProductVideo {
  id: number
  created_at: number
  ri_name: string
  video_id: string
  title_text: string
  sequence: number
  thumbnail_url: string
}
export interface SubCategory {
  id: number
  sc_name: string
  sc_image: string
  url: string
  sc_name_ri: string
  options?: number
  sc_order: number
  category_url: string
  seo_desc: string
  sc_image_alt_text: string
  super_category_short_name: string
  super_category_url: string
  admin_only: boolean
}

export interface RentalItemVarient {
  id: number
  ri_code: string
  product_short_name: string
  size: string
  size_in_cms: string
  size_tip: string
  sort_code: number
  size_type: string
}

export interface CartItem {
  id: number
  created_at: number
  rent: number
  product_code: string
  quantity: number
  rentalitem_id: number
  size: string
  deposit: number
  goods_value: number
  shipping_cost: number
  update_ts: number
  cart_item_active: boolean
  size_specific: boolean
  rent_per_day: number
  user_uid: string
  cart_image: string
  user_new_id: number
  num_days: number
  base_rent: number
  per_day_rent: number
  normalisation_factor: number
  rent_per_trip: number
  cat_sname: string
  sub_product_code: string
  item_name: string
  cart_type: string
  special_type: string
  surge_factor: number
  city_surge: number
  subcat_sname: string
}

export interface Category {
  id: number
  category_id: number
  category_image: string
  category_name: string
  category_short_name: string
  url: string
  category_card_image: string
  on_rent: boolean
  on_buy: boolean
  on_rf: boolean
  category_icon: string
}

export interface SubCategory {
  id: number
  sc_name: string
  sc_image: string
  url: string
  sc_name_ri: string
  options?: number
  sc_order: number
  category_url: string
  seo_desc: string
  sc_image_alt_text: string
}

export interface SideViewProps {
  openSideView: boolean
  setOpenSideView: React.Dispatch<React.SetStateAction<boolean>>
}

export interface DiscountCoupons {
  id: number
  created_at: number
  cart_active: boolean
  cart_discount: number
  cart_max_discount: number
  cart_minimum_value: number
  category: string
  city_type: string
  coupon_active: boolean
  coupon_code: string
  coupon_description: string
  coupon_header: string
  coupon_owner_email: string
  coupon_type: string
  days_to_deliver: number
  days_to_deliver_active: boolean
  delivery_discount: boolean
  delivery_discount_value: number
  show_on_website: boolean
  coupon_cat: string
}

export interface Inclusions {
  id: number
  created_at: number
  active: boolean
  product_short_name: string
  sub_product_code: string
  sub_product_image: string
  sub_product_name: string
}

export interface SideViewProps {
  openSideView: boolean
  setOpenSideView: React.Dispatch<React.SetStateAction<boolean>>
}

export interface RentOrder {
  id: number
  email: string
  user_id: number
  category: string
  crm_rent: number
  order_id: string
  city_type: number
  last_name: string
  order_pdf: string
  processed: boolean
  time_slot: string
  user_city: string
  address_id: number
  created_at: number
  first_name: string
  order_date: string
  order_type: string
  surge_city: number
  coupon_code: string
  goods_value: number
  order_items: number[]
  order_stage: string
  pickup_date: string
  user_new_id: number
  zoho_crm_id: string
  partner_name: string
  rzp_order_id: string
  surge_factor: number
  total_amount: number
  user_partner: string
  delivery_city: string
  delivery_date: string
  rental_amount: number
  advance_amount: number
  calling_number: string
  crm_net_rental: number
  deposit_amount: number
  same_day_surge: number
  adjusted_amount: number
  rental_end_date: string
  shipping_amount: number
  whatsapp_number: string
  crm_total_amount: number
  delivery_address: string
  delivery_pincode: string
  partner_discount: number
  payment_discount: number
  seon_fingerprint: string
  delivery_landmark: string
  delivery_latitude: number
  rental_start_date: string
  delivery_longitude: number
  total_rent_per_day: number
  calling_number_code: string
  wallet_balance_used: number
  cod_handling_charges: number
  crm_net_total_amount: number
  whatsapp_number_code: string
  coupon_discount_amount: number
  payment_option: number
  carepal_amount: number
  carepal_applied: boolean
}

export interface OrderData {
  rent_order: RentOrder
  rzp_order_id: string
}

export interface ProductSurge {
  id: number
  created_at: number
  delivery_date: string
  ri_name: string
  city: string
  surge_factor: number
  out_of_stock: boolean
  booked_count: string
  rating: number
  decoration_text: string
  super_category_short_name: string
  sub_category_short_name: string
}

export interface Partner {
  id: number
  created_at: number
  partner_name: string
  discount: number
  partner_logo: string
  surge_factor: number
  remove_surge: boolean
  partner_coupons: boolean
}

export type FaqType = {
  id: number
  question: string
  answer: string
  category: string
  faq_type: string
}

export type DealAdditionalDetails = {
  id: number
  created_at: number
  deal_name: string
  delay_rental_charge: number
  timeslot_charges: number
  total_return_charges: number
  tsa_applied: boolean
  ziplock_applied: boolean
  return_images: string[]
  return_payment_option: string
  return_charge_received: number
}
