export type OrderStatus =
  | "verification-required"
  | "received"
  | "confirmed"
  | "cancelled"
  | "not-ready"
  | "return-completed"
  | "Order Cancelled"

export type PaymentStatus = "pre-paid" | "pending"

export type FilterValue = "cancelled" | "active" | "completed"

export interface FilterPayload {
  filters: FilterValue[]
  order_type: "rent"
  paging: {
    page: number
    per_page: number
  }
}

export interface APIOrder {
  id: number
  deal_name: string
  deal_cf_order_pdf: string
  deal_cf_net_amount: number
  deal_cf_pickup_date: Date
  deal_deal_stage_name: string
  deal_cf_delivery_date: Date
  deal_cf_rental_end_date: Date
  calling_number: string
  order_fulfillment_type: string
  order_date: Date
  same_day_surge: number
  city_surge: number
}

export interface APIOrderResponse {
  itemsReceived: number
  curPage: number
  nextPage: number | null
  prevPage: number | null
  offset: number
  items: APIOrder[]
}

export interface OrderItem {
  rent: number
  quantity: number
  size: string
  cart_image: string
  cat_sname: string
  item_name: string
  per_day_rent: number
}

export interface OrderItemsResponse {
  itemsReceived: number
  curPage: number
  nextPage: number | null
  prevPage: number | null
  offset: number
  items: OrderItem[]
}

export interface ActionButton {
  active: boolean
  button_text: string
}

// New
// Define the type for a single action
export interface Action {
  text: string // Text displayed on the button
  variant:
    | "default"
    | "outline"
    | "outline-primary"
    | "primary"
    | "outline-destructive" // Variant of the button (e.g., default, outline)
  action_type: string // Variant of the button (e.g., default, outline)
  disabled: true
  message: string
}

// Define the type for stage information
interface StageInfo {
  title: string // Title of the stage
  message: string // Message describing the stage
  actions: Action[] // List of actions available for this stage
  warning?: boolean
  error?: boolean
  specialMessage?: {
    title: string
    description: string
    type: "warning" | "error" | "success" | "info" | "neutral-info"
    show: boolean
  }
}

interface AdditionalStageInfo {
  warning?: boolean
  error?: boolean
  specialMessage?: {
    title: string
    description: string
    type: "warning" | "error" | "success" | "info" | "neutral-info"
    show: boolean
  }
}

// Define the main type for the JSON structure
export interface OrderStatusResponse {
  stages: string[] // List of all possible stages
  activeStage: string // The currently active stage
  stageInfo: StageInfo // Information about the active stage
  additionalStages: string[]
  additionalActiveStage: string
  additionalStageInfo: AdditionalStageInfo
  showAdditionalStages: boolean
  order_timeline: { [key: string]: string }
}

export interface OrderTimelineResponse {
  order_timeline: { [key: string]: string }
}

// export interface OrderStatusResponse {
//   stages: string[]
//   messages: {
//     [key: string]: string[]
//   }
//   activeStage: string
// }

export interface OrderSummary {
  id: number
  deal_name: string
  deal_cf_order_pdf: string
  deal_cf_due_amount: number
  deal_cf_net_amount: number
  deal_cf_total_rent: number
  deal_cf_pickup_date: Date
  deal_deal_stage_name: string
  deal_cf_contact_email: string
  deal_cf_delivery_date: Date
  deal_cf_deposit_amount: number
  deal_cf_wallet_payment: number
  deal_cf_advance_payment: number
  deal_cf_package_remarks: string
  deal_cf_delivery_address: string
  deal_cf_total_order_amount: number
  deal_cf_balance_payment_link: string | null
  deal_cf_coupon_discount_payment: number
  calling_number: string
  country_code_calling: number
  first_name: string
  order_date: Date
  total_price: number
  last_name: string
  shipping_charge: number
  pay_on_delivery_fees: number
  adjusted_amount: number
  same_day_surge: number
  city_surge: number
  main_order_id: number
  deal_cf_total_per_day_rent: number
  carepal_amount: number
  shipment_calling_number: string
}

export interface CancellationReason {
  reason: string
  id: number
}

export interface ShipmentData {
  id: number
  deal_name: string
  deal_cf_order_pdf: string
  deal_cf_net_amount: number
  deal_cf_pickup_date: Date
  deal_deal_stage_name: string
  deal_cf_delivery_date: Date
  deal_cf_pickup_partner: string
  deal_cf_shipping_method: string
  deal_cf_delivery_partner: string
  order_fulfillment_type: string
  order_date: Date
}

export interface CancellationReasons {
  id: number
  created_at: number
  reason: string
  auto_cancel: boolean
  reason_type: string
}
