/// <reference types="@types/googlemaps" />

declare global {
  interface Window {
    google: typeof google
    initMap: () => void
  }
}

export interface GoogleMapLocation {
  lat: number
  lng: number
  user_email?: string
  full_address?: string
  house_details?: string
  road?: string
  landmark?: string
  area_details?: string
  city?: string
  state?: string
  pincode?: string
  latitude?: string
  longitude?: string
  address?: string
  nickname?: string
  delivery_instructions?: string
  formatted_address?: string
}

export interface MapViewProps {
  onLocationSelect: (location: GoogleMapLocation) => void
  defaultLocation?: {
    lat: number
    lng: number
  }
  isScriptLoaded: boolean
  compact?: boolean
  fullAddress?: string
  setIsLocationSelected?: React.Dispatch<React.SetStateAction<boolean>>
}

export interface MapState {
  map: google.maps.Map | null
  marker: google.maps.Marker | null
  searchBox: google.maps.places.SearchBox | null
}

export interface AddressComponents {
  buildingName?: string
  streetAddress?: string
  city?: string
  state?: string
  pincode?: string
}
