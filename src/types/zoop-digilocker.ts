// Simplified types for Aadhaar verification only

// Core Aadhaar data structure
export interface AadhaarData {
  name: string
  dob: string
  gender: string
  address: string
  pin_code: string
  aadhaar_number: string
}

// API Request types
export interface AadhaarVerificationRequest {
  purpose: string
  webhook_url: string
}

export interface ZoopInitRequest {
  docs: ["ADHAR"]
  purpose: string
  response_url: string
  redirect_url?: string
  fast_track: "Y" | "N"
  pinless: boolean
}

// API Response types
export interface ZoopInitResponse {
  request_id: string
  success: boolean
  webhook_security_key: string
  request_timestamp: string
  expires_at: string
  sdk_url: string
}

export interface ZoopFailureResponse {
  success: false
  response_code: string
  response_message: string
}

// Document types
export interface AadhaarDocument {
  doctype: "ADHAR"
  status: "FETCHED" | "SKIPPED"
  fetched_at?: string
  data_xml?: string
  data_pdf?: string
  data_json?: string
}

// Webhook types
export interface ZoopWebhookResponse {
  request_id: string
  success: boolean
  response_code: string
  response_message: string
  metadata: {
    billable: string
  }
  result: Array<{
    issued?: {
      name: string
      type: string
      size: string
      date: string
      parent: string
      mime: string[]
      uri: string
      doctype: string
      description: string
      issuerid: string
      issuer: string
    }
    doctype: string
    status: string
    fetched_at?: string
    data_xml?: string
    data_pdf?: string
    data_json?: string
  }>
  request_timestamp: string
  webhook_security_key?: string
}

// Status check types
export interface ZoopStatusResponse {
  id: string
  response_url: string
  env: string
  purpose: string
  transaction_status: string
  webhook_sent: string
  request_timestamp: string
  issued_docs: string[]
  pending_docs: string[]
}

// SDK Event types for frontend
export type DigilockerEventType =
  | "close"
  | "consent-denied"
  | "gateway-error"
  | "digilocker-success"
  | "digilocker-error"

export interface DigilockerEventPayload {
  request_id?: string
  success: boolean
  response_code?: string
  response_message?: string
  metadata?: {
    billable: "Y" | "N"
    reason_code?: string
    reason_message?: string
  }
  result?: AadhaarDocument[] | null
  request_timestamp?: string | null
  response_timestamp?: string | null
}

// Store state interface
export interface DigilockerVerificationState {
  isLoading: boolean
  isVerified: boolean
  requestId: string | null
  webhookSecurityKey: string | null
  sdkUrl: string | null
  aadhaarData: AadhaarData | null
  error: string | null
}
