export enum VERIFICATION_STATUS {
  Requested = "requested",
  Received = "received",
  Verified = "verified",
}

export enum ORDER_TYPES {
  LRLV = "LRLV",
  LRHV = "LRHV",
  HRLV = "HRLV",
  HRHV = "HRHV",
}

export enum OCCUPATION_TYPE {
  Salaried = "salaried",
  Student = "student",
  DefensePersonnel = "defense",
  SocialMediaInfluencer = "smi",
  Freelancer = "freelancer",
  MedicalPersonnel = "medical",
  Advocate = "advocate",
  Business = "business",
  CharteredAccountant = "ca",
}

export enum VERIFICATION_CASE {
  IdentityOnly = "identity_only",
  AllRequired = "all_required",
  IdentityAndChoice = "identity_and_choice",
}

export interface USER_VERIFICATION {
  id: number
  created_at: number
  user_id: number
  identity_verified_type: "otp" | "document"
  occupation_verified_type: "gst" | "document" | "otp"
  credit_verified_type: "auto_fetched" | "fetched_with_pan"
  identity_status: VERIFICATION_STATUS
  occupation_status: VERIFICATION_STATUS
  credit_status: VERIFICATION_STATUS
  occupation_value: string
  occupation_type: OCCUPATION_TYPE
  verification_case: VERIFICATION_CASE
  last_order_processed: boolean
  last_seon_result: "APPROVE" | "DECLINE" | null
  last_order_type: ORDER_TYPES
  last_order_processed_ts: string
}
