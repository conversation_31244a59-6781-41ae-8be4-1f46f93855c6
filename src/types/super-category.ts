export interface SuperCategory {
  id: number
  super_category_id: number
  super_category_name: string
  super_category_short_name: string
  super_category_desc: string
  url: string
  super_category_image: string
  super_category_card_image: string
  left_image: string
  right_image: string
  // subcategory_ids: string[]
  super_category_title: string
}

export interface SeoDetails {
  id: number
  created_at: Date
  url: string
  first_block: string
  second_block: string
  third_block: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  products: any // Assuming 'json' should be 'any', as 'json' is not a valid type
  seo_image: string
  cat_desc: string
}
