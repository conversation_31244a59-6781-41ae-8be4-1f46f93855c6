"use client"
import React, { FC, useState } from "react"
import Image, { ImageProps } from "next/image"
import { cn } from "@/lib/utils"

export interface NcImageProps extends Omit<ImageProps, "alt"> {
  containerClassName?: string
  alt?: string
  src: string
}

const SpImage: FC<NcImageProps> = (props) => {
  const {
    containerClassName = "",
    alt = "sp-image",
    className = "object-cover w-full h-full",
    src,
    loading,
    priority,
    ...args
  } = props
  const [isLoading, setLoading] = useState(true)

  return (
    <div className={containerClassName}>
      <Image
        className={cn(
          "duration-700 ease-in-out group-hover:opacity-75",
          isLoading
            ? "scale-110 blur-2xl grayscale"
            : "scale-100 blur-0 grayscale-0",
          className,
        )}
        src={src?.trim()}
        alt={alt}
        loading={loading}
        // priority={loading === 'eager'}
        priority={priority}
        quality={100}
        // onLoad={() => setLoaded(true)}
        onLoadingComplete={() => setLoading(false)}
        {...args}
      />
    </div>
  )
}

export default SpImage
