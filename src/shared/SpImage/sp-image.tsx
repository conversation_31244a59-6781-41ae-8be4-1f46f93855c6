"use client"
import Image, { ImageProps } from "next/image"
import { FC, useState } from "react"

export interface NcImageProps extends Omit<ImageProps, "alt"> {
  containerClassName?: string
  alt?: string
  src: string
  fallbackSrc?: string
}

// https://images.sharepal.in/misc/hard-coded/sharepal/category-image-placeholder.svg

const SpImage: FC<NcImageProps> = ({
  containerClassName = "",
  alt = "sp-image",
  className = "object-cover w-full h-full",
  fallbackSrc = "https://images.sharepal.in/misc/hard-coded/sharepal/product-image-placeholder.svg",
  src,
  ...args
}) => {
  const [imgSrc, setImgSrc] = useState(src?.trim())

  return (
    <div className={containerClassName}>
      <Image
        className={className}
        src={imgSrc !== "" ? imgSrc : fallbackSrc}
        alt={alt}
        {...args}
        onError={() => setImgSrc(fallbackSrc)}
      />
      {/* Place holder image for now loading images */}
      {/* <Image className={className} src={src?.trim()} alt={alt} {...args}
        onError={e => { e.currentTarget.src = 'https://images.sharepal.in/misc/hard-coded/sharepal/product-image-placeholder.svg'; e.currentTarget.onerror = null }}
      /> */}
    </div>
  )
}

export default SpImage
