import { Inter, Ubuntu } from "next/font/google"
import NextTopLoader from "nextjs-toploader"

import Providers from "@/providers"
import "./globals.css"
// import Footer from '@/components/layout/footer'
// import Header from '@/components/layout/header'
import { RentalPeriodSelector } from "@/components/custom/rental-dates-select"
import { generateStaticMetadata } from "@/lib/metadata"
import { GoogleTagManager } from "@next/third-parties/google"
import Script from "next/script"
// Import Swiper styles
import "swiper/css"
import "swiper/css/free-mode"
import "swiper/css/navigation"
import "swiper/css/pagination"

// import { Suspense } from 'react'
// import { Skeleton } from '@/components/ui/skeleton'

// Import the WelcomeDialog component
import OnLoad from "@/components/custom/on-load"

export const metadata = generateStaticMetadata()

const ubuntu = Ubuntu({
  weight: ["300", "400", "500", "700"],
  style: ["normal", "italic"],
  variable: "--font-ubuntu",
  subsets: ["latin"],
})
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
})

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang='en'>
      <head>
        {/* react scan for development only */}
        {/* {process.env.NEXT_PUBLIC_APP_ENV === "development" && (
          <Script src='//unpkg.com/react-scan/dist/auto.global.js' />
        )} */}
        {/* <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/icon?family=Material+Icons"
        /> */}
        <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID ?? ""} />

        <Script
          id='gtag1'
          strategy='beforeInteractive'
          dangerouslySetInnerHTML={{
            __html: `
               (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','${process.env.NEXT_PUBLIC_GTM_ID ?? ""}');
              `,
          }}
        />
        <Script
          id='clarity-data'
          strategy='beforeInteractive'
          dangerouslySetInnerHTML={{
            __html: `(function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "r4yyoglgwr");`,
          }}
        />
        {/* Facebook Pixel Event */}
        {/* <Script
          id='fb-events'
          strategy='beforeInteractive'
          dangerouslySetInnerHTML={{
            __html: `
                !function(f,b,e,v,n,t,s)
                {if(f.fbq)return;n=f.fbq=function(){
                n.callMethod?n.callMethod.apply(n,arguments):n.queue.push(arguments)
                };
                if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                n.queue=[];t=b.createElement(e);t.async=!0;
                t.src=v;s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)}(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');
                fbq('init', '2535592586831623');
                fbq('track', 'PageView');
                `,
          }}
        /> */}
      </head>
      <body className={`${inter.className} ${ubuntu.variable} antialiased`}>
        {/* <noscript>
          <Image
            height='1'
            width='1'
            style={{ display: "none", visibility: "hidden" }}
            src='https://www.facebook.com/tr?id=2535592586831623&ev=PageView&noscript=1'
            alt={"facebook"}
          />
        </noscript> */}

        <noscript>
          <iframe
            src={
              "https://www.googletagmanager.com/ns.html?id=" +
              (process.env.NEXT_PUBLIC_GTM_ID ?? "GTM-NWGKTNLP")
            }
            height='0'
            width='0'
            style={{ display: "none", visibility: "hidden" }}
          ></iframe>
        </noscript>
        <NextTopLoader
          showSpinner={false}
          height={3}
          color='#1945E8'
          initialPosition={0.95}
        />

        <Providers>
          <RentalPeriodSelector />
          <OnLoad />
          {/* Welcome dialog that shows only once */}
          {/* <WelcomeDialog /> */}

          {children}
          {/* <Suspense fallback={<Skeleton className="h-56 w-full" />}>
            <Header />
          </Suspense>
          <main className="min-h-svh">{children}</main>
          <Footer /> */}
        </Providers>
      </body>
    </html>
  )
}

// force the build to render the dynamic pages
// export const dynamic = 'force-dynamic';
// export const dynamic = 'force-static';
