import { generateLinkPath } from "@/functions/generate-link-path"
import { formatProductUrl } from "@/functions/small-functions"
import { revalidatePath } from "next/cache"
import { NextRequest, NextResponse } from "next/server"

// API route handler for POST requests
export async function POST(request: NextRequest) {
  try {
    // Parse the request body to get product details

    const {
      ri_name,
      ri_city,
      category_short_name,
      sc_name,
      super_category_url,
    } = await request.json()

    const city = ri_city.toLowerCase()

    // Validate required parameters
    if (!ri_name || !city || !sc_name) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Required parameters missing: ri_name, city, category_short_name, and sc_name are required",
        },
        { status: 400 },
      )
    }

    // Track all paths that were revalidated
    const revalidatedPaths: string[] = []

    // Generate product URL using the same function used in the app
    const productUrlObj = generateLinkPath(
      ri_name,
      city,
      category_short_name,
      sc_name,
    )
    const productPath = productUrlObj.pathname as string

    // Revalidate product page
    revalidatePath(productPath)
    revalidatedPaths.push(productPath)

    // Extract category and subcategory paths
    // const cat = formatProductUrl(category_short_name)
    const subcat = formatProductUrl(sc_name)

    // Revalidate subcategory page
    const subcategoryPath = `/${city}/${super_category_url}/${subcat}`
    revalidatePath(subcategoryPath)
    revalidatedPaths.push(subcategoryPath)

    // Revalidate supercategory page
    const superCategoryPath = `/${city}/${super_category_url}`
    revalidatePath(superCategoryPath)
    revalidatedPaths.push(superCategoryPath)

    // Revalidate city homepage
    const cityPath = `/${city}`
    revalidatePath(cityPath)
    revalidatedPaths.push(cityPath)

    return NextResponse.json({
      success: true,
      message: `Successfully revalidated ${revalidatedPaths.length} paths`,
      paths: revalidatedPaths,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Revalidation error:", error)
    return NextResponse.json(
      { success: false, message: `Failed to revalidate: ${error}` },
      { status: 500 },
    )
  }
}
