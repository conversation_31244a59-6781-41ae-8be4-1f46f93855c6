@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    /* --background: 0 0% 100%; */
    --background: 200, 16%, 96%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 28px;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 150px; /* Adjust based on your header height */
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* In your global CSS file (e.g., styles.css) */
.hide-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

@layer utilities {
  .custom-scrollbar::-webkit-scrollbar {
    width: 10px;
    height: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: theme("colors.primary.600");
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: theme("colors.primary.200");
    border-radius: 10px;
  }

  .custom-scrollbar-black::-webkit-scrollbar {
    width: 10px;
    height: 3px;
    border-radius: 100%;
    overflow: hidden;
  }

  .custom-scrollbar-black::-webkit-scrollbar-thumb {
    background-color: theme("colors.neutral.900");
    border-radius: 10px;
  }

  .custom-scrollbar-black::-webkit-scrollbar-track {
    background: theme("colors.gray.200");
    border-radius: 10px;
  }
}

/* Global scrollbar styles */
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-thumb {
  background-color: theme("colors.neutral.250");
  border-radius: var(--radius);
}

*::-webkit-scrollbar-track {
  background: theme("colors.neutral.200");
  border-radius: var(--radius);
}

/* Firefox */
/* * {
  scrollbar-width: thin;
  scrollbar-color: theme("colors.gray.400") theme("colors.gray.200");
} */

@keyframes marquee-horizontal {
  0% {
    transform: translateX(0%);
  }

  100% {
    transform: translateX(-50%);
  }
}

@keyframes marquee-vertical {
  0% {
    transform: translateY(0%);
  }

  100% {
    transform: translateY(-50%);
  }
}

.animate-marquee-horizontal {
  animation: marquee-horizontal var(--duration, 40s) linear infinite;
}

.animate-marquee-vertical {
  animation: marquee-vertical var(--duration, 40s) linear infinite;
}

/* Style the autocomplete dropdown container */
/* .pac-container {
  z-index: 1000;
  border: 1px solid #ddd;
  background-color: #fff;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: none;
  overflow: hidden;
}

.google-maps-autocomplete-dropdown {
  z-index: 1000;
} */

/* Style individual suggestion items */
/* .pac-item {
  z-index: 2000;
  position: relative;
  font-size: 12px;
  color: #333;
  padding: 5px 6px;
  cursor: pointer;
} */

/* Highlighted suggestion item on hover */
/* .pac-item:hover {
  background-color: #438de7;
} */

/* Style the primary and secondary text separately */
/* .pac-item .pac-icon {
  font-weight: 800;
} */

/* .pac-item .pac-item-query {
  font-weight: bold;
  color: #007bff;
} */

/* @layer components {
  .rdp {
    --rdp-cell-size: 40px;
    --rdp-accent-color: hsl(var(--primary));
    --rdp-background-color: hsl(var(--primary) / 0.1);
    --rdp-accent-color-dark: hsl(var(--primary));
    --rdp-background-color-dark: hsl(var(--primary) / 0.2);
    --rdp-outline: 2px solid var(--rdp-accent-color);
    --rdp-outline-selected: 3px solid var(--rdp-accent-color);
    margin: 0;
  }

  .rdp-months {
    justify-content: space-between;
  }

  .rdp-month {
    background-color: hsl(var(--background));
    border-radius: theme('borderRadius.lg');
    padding: theme('spacing.4');
  }

  .rdp-day_selected,
  .rdp-day_selected:focus-visible,
  .rdp-day_selected:hover {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
  }

  .rdp-day_range_start,
  .rdp-day_range_end {
    border-radius: theme('borderRadius.lg') !important;
  }

  .rdp-day_range_middle {
    background-color: hsl(var(--muted)) !important;
    color: hsl(var(--muted-foreground)) !important;
  }

  .rdp-day:hover:not(.rdp-day_selected) {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
  }
} */

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}
