import { fetchSiteMap } from "@/actions/sitemap"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"

export default async function SitemapPage() {
  const sitemap = await fetchSiteMap()
  const cities = Array.from(new Set(sitemap.map((item) => item.city)))

  return (
    <div className='overflow-hidden py-16'>
      <div className='container mx-auto px-6'>
        <div className='grid gap-6'>
          {cities.map((city) => (
            <Card
              key={city}
              className='overflow-hidden rounded-2xl border border-input bg-neutral-100 md:rounded-3xl'
            >
              <CardHeader className='rounded-t-lg bg-gray-100 p-4'>
                <CardTitle className='px-3 text-xl font-semibold text-gray-800 md:text-3xl'>
                  {city}
                </CardTitle>
              </CardHeader>
              <CardContent className='p-6'>
                <div className='grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5'>
                  {sitemap
                    ?.filter((site) => site.city === city)
                    .map((site, i) => (
                      <Link
                        href={site.url}
                        target='_blank'
                        key={i}
                        className='text-start text-primary-700 hover:underline'
                      >
                        <Button
                          variant='outline'
                          className='line-clamp-1 w-full justify-start overflow-hidden border-neutral-200 text-left text-sm font-medium md:rounded-xl'
                        >
                          {site.title}
                        </Button>
                      </Link>
                    ))}
                </div>
                <div className='mt-4 text-right'>
                  <Link
                    href={`/sitemap/${city.toLowerCase()}`}
                    className='font-medium text-blue-600 hover:underline'
                  >
                    View {city} Sitemap →
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
