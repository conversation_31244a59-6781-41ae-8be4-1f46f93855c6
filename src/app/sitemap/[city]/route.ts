import { fetchCitySiteMap, generateCitySitemap } from "@/actions/sitemap"
import { NextRequest, NextResponse } from "next/server"

export const revalidate = 86400 // 10 days

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ city: string }> },
): Promise<NextResponse> {
  const city = (await params).city // 'a', 'b', or 'c'

  if (!city) {
    return new NextResponse("City parameter is required", { status: 400 })
  }

  try {
    const urls = await fetchCitySiteMap()
    const filteredUrls = urls.filter(
      (urlObj) => urlObj.city?.toLowerCase() === city.toLowerCase(),
    )

    const sitemap: string = generateCitySitemap(filteredUrls)

    return new NextResponse(sitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, s-maxage=864000, stale-while-revalidate",
      },
    })
  } catch {
    return new NextResponse("Error generating sitemap", { status: 500 })
  }
}
