import { fetchSiteUrls } from "@/actions/sitemap"
import { NextResponse } from "next/server"

interface SiteUrl {
  id: string
  url: string
  title: string
  city: string
  type: string
  frequency?:
    | "always"
    | "hourly"
    | "daily"
    | "weekly"
    | "monthly"
    | "yearly"
    | "never"
  priority?: number
}

// async function fetchSiteUrls(type: string): Promise<SiteUrl[]> {
//   try {
//     const response = await fetch(API_URL, {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify({ type }),
//       next: { revalidate: 864000 }, // Cache for 10 days
//     })

//     if (!response.ok) {
//       throw new Error(`Failed to fetch site URLs for type ${type}: ${response.status} ${response.statusText}`)
//     }

//     return await response.json()
//   } catch (error) {
//     console.error(`Error fetching site URLs for type ${type}:`, error)
//     return []
//   }
// }

function generateSitemapIndex(urls: SiteUrl[]): string {
  let sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
`

  urls.forEach((urlObj) => {
    sitemapIndex += `  <sitemap>
    <loc>${process.env.NEXT_PUBLIC_APP_URL ?? "https://sharepal.in"}/sitemap/${urlObj.city.toLowerCase()}</loc>
  </sitemap>
`
  })

  sitemapIndex += `</sitemapindex>`

  return sitemapIndex
}

export const revalidate = 864000 // 10 days

export async function GET() {
  const allUrls = await fetchSiteUrls()

  const cityUrls = allUrls.filter((url) => url.type === "city")
  const sitemapIndex = generateSitemapIndex(cityUrls)

  return new NextResponse(sitemapIndex, {
    headers: {
      "Content-Type": "application/xml",
      "Cache-Control": "public, s-maxage=864000, stale-while-revalidate",
    },
  })
}
