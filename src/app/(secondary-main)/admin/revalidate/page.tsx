"use client"

import { fetchCities } from "@/actions/city"
import { revalidatePathAction } from "@/actions/revalidate"
import { fetchSuperCategories } from "@/actions/super-category"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useUserStore } from "@/store/user-store"
import { City } from "@/types/address"
import { SuperCategory } from "@/types/super-category"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  AlertCircle,
  CheckCircle2,
  FileText,
  Grid3X3,
  Home,
  Layers,
  Loader2,
  RefreshCw,
  XCircle,
  XIcon,
} from "lucide-react"
import { useRouter } from "next/navigation"
import React, { useEffect, useState } from "react"
import { toast } from "sonner"

const allowedRoles = ["admin", "marketing"]

function RevalidatePage() {
  const { user, userLoading } = useUserStore()
  const router = useRouter()
  const queryClient = useQueryClient()
  const [path, setPath] = useState("")
  const [selectedCities, setSelectedCities] = useState<string[]>([])
  const [selectedSuperCategories, setSelectedSuperCategories] = useState<
    string[]
  >([])
  const [activeTab, setActiveTab] = useState("path")
  const [revalidationStatus, setRevalidationStatus] = useState<{
    [key: string]: "pending" | "success" | "error"
  }>({})

  // Fetch filteredCities using React Query
  const { data: cities = [], isLoading: citiesLoading } = useQuery<City[]>({
    queryKey: ["filteredCities"],
    queryFn: fetchCities,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  })

  const filteredCities = cities.filter(
    (city) => city.city_type !== 1 && city.city_type !== 6,
  )
  // Fetch super categories using React Query
  const { data: superCategories = [], isLoading: superCategoriesLoading } =
    useQuery<SuperCategory[]>({
      queryKey: ["superCategories"],
      queryFn: fetchSuperCategories,
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    })

  // Revalidation mutation using server action
  const revalidateMutation = useMutation({
    mutationFn: async (pathToRevalidate: string) => {
      const result = await revalidatePathAction(pathToRevalidate)
      if (!result.success) throw new Error(result.message)
      return result
    },
    onSuccess: (result, path) => {
      toast.success(result.message, {
        icon: <CheckCircle2 className='h-4 w-4 text-green-500' />,
      })
      setRevalidationStatus((prev) => ({
        ...prev,
        [path]: "success",
      }))
      queryClient.invalidateQueries({ queryKey: ["filteredCities"] })
    },
    onError: (error, path) => {
      toast.error(`Failed to revalidate: ${path}`, {
        icon: <XCircle className='h-4 w-4 text-red-500' />,
      })
      setRevalidationStatus((prev) => ({
        ...prev,
        [path]: "error",
      }))
    },
  })

  useEffect(() => {
    if (!userLoading && (!user || !allowedRoles.includes(user.user_role))) {
      toast.error("Unauthorized access", {
        icon: <AlertCircle className='h-4 w-4 text-red-500' />,
      })
      router.replace("/")
    }
  }, [user, userLoading, router])

  const handleRevalidate = async (pathToRevalidate: string) => {
    setRevalidationStatus((prev) => ({
      ...prev,
      [pathToRevalidate]: "pending",
    }))
    revalidateMutation.mutate(pathToRevalidate)
  }

  const handleRevalidateForCities = async () => {
    const citiesToProcess =
      selectedCities.length > 0
        ? selectedCities
        : filteredCities.map((city) => city.city_url)

    for (const city of citiesToProcess) {
      const cityPath = path.startsWith("/")
        ? `/${city}${path}`
        : `/${city}/${path}`
      await handleRevalidate(cityPath)
    }
  }

  const handleRevalidateHomepages = async () => {
    const citiesToProcess =
      selectedCities.length > 0
        ? selectedCities
        : filteredCities.map((city) => city.city_url)

    for (const city of citiesToProcess) {
      await handleRevalidate(`/${city}`)
    }

    // Also revalidate the main homepage
    await handleRevalidate("/")
  }

  // Add handler for revalidating super category pages
  const handleRevalidateSuperCategories = async () => {
    const superCategoriesToProcess =
      selectedSuperCategories.length > 0
        ? selectedSuperCategories
        : superCategories.map((sc) => sc.url)

    const citiesToProcess =
      selectedCities.length > 0
        ? selectedCities
        : filteredCities.map((city) => city.city_url)

    for (const superCat of superCategoriesToProcess) {
      for (const city of citiesToProcess) {
        await handleRevalidate(`/${city}/${superCat}`)
      }
    }
  }

  // Select all super categories
  const selectAllSuperCategories = () => {
    if (superCategories && superCategories.length > 0) {
      // If all super categories are already selected, clear the selection
      if (selectedSuperCategories.length === superCategories.length) {
        setSelectedSuperCategories([])
      } else {
        // Otherwise select all super categories
        setSelectedSuperCategories(superCategories.map((sc) => sc.url))
      }
    }
  }

  if (userLoading || citiesLoading || superCategoriesLoading) {
    return (
      <div className='flex h-screen items-center justify-center'>
        <div className='flex flex-col items-center gap-4'>
          <Loader2 className='h-12 w-12 animate-spin text-primary-500' />
          <p className='text-lg font-medium text-gray-600'>Loading...</p>
        </div>
      </div>
    )
  }

  // Modified All Cities button functionality
  const selectAllCities = () => {
    if (filteredCities && filteredCities.length > 0) {
      // If all filteredCities are already selected, clear the selection
      if (selectedCities.length === filteredCities.length) {
        setSelectedCities([])
      } else {
        // Otherwise select all filteredCities
        setSelectedCities(filteredCities.map((city) => city.city_url))
      }
    }
  }

  // Disable all action buttons by default
  const isActionDisabled =
    revalidateMutation.isPending || (!path && activeTab === "path")

  return (
    <div className='container mx-auto space-y-8 py-24'>
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2 text-2xl'>
            <RefreshCw className='h-6 w-6 text-primary-500' />
            Revalidation Dashboard
          </CardTitle>
          <CardDescription>
            Manage and revalidate your application&apos;s pages and routes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className='space-y-6'
          >
            <TabsList className='grid w-full grid-cols-3'>
              <TabsTrigger value='path'>Custom Path</TabsTrigger>
              <TabsTrigger value='homepage'>Homepages</TabsTrigger>
              <TabsTrigger value='supercategory'>Super Categories</TabsTrigger>
            </TabsList>

            {/* City Selection (common to both tabs) */}
            <div>
              <label className='mb-2 block font-medium'>Select Cities</label>
              <ScrollArea className='h-32 rounded-md border p-2'>
                <div className='flex flex-wrap gap-2'>
                  <Button
                    variant='outline'
                    onClick={selectAllCities}
                    className={
                      selectedCities.length === filteredCities.length
                        ? "border-primary-500 bg-blue-50"
                        : ""
                    }
                  >
                    {selectedCities.length === filteredCities.length
                      ? "Deselect All Cities"
                      : "Select All Cities"}
                    {selectedCities.length === filteredCities.length && (
                      <CheckCircle2 className='ml-2 h-4 w-4 text-primary-500' />
                    )}
                  </Button>
                  {filteredCities.map((city) => (
                    <Button
                      key={city.city_url}
                      variant={
                        selectedCities.includes(city.city_url)
                          ? "default"
                          : "outline"
                      }
                      onClick={() => {
                        setSelectedCities((prev) =>
                          prev.includes(city.city_url)
                            ? prev.filter((c) => c !== city.city_url)
                            : [...prev, city.city_url],
                        )
                      }}
                    >
                      {city.city_name}
                      {selectedCities.includes(city.city_url) && (
                        <button type='button' className='ml-2'>
                          <XIcon className='h-4 w-4' />
                        </button>
                      )}
                    </Button>
                  ))}
                </div>
              </ScrollArea>
            </div>

            <TabsContent value='path' className='space-y-6'>
              <div className='space-y-4'>
                <div>
                  <label className='mb-2 block font-medium'>
                    Path to Revalidate
                  </label>
                  <Input
                    type='text'
                    value={path}
                    onChange={(e) => setPath(e.target.value)}
                    placeholder='/trekking-gear-on-rent or /category/subcategory/product'
                    className='max-w-md'
                  />
                  <p className='mt-1 text-sm text-gray-500'>
                    Example: /trekking-gear-on-rent (will be prefixed with city)
                  </p>
                </div>
              </div>

              <div className='flex gap-4'>
                <Button
                  onClick={() => handleRevalidate(path)}
                  disabled={isActionDisabled}
                  className='min-w-[200px]'
                >
                  {revalidateMutation.isPending ? (
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  ) : null}
                  Revalidate Single Path
                </Button>
                <Button
                  onClick={handleRevalidateForCities}
                  disabled={isActionDisabled}
                  variant='default'
                  className='min-w-[200px]'
                >
                  {revalidateMutation.isPending ? (
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  ) : null}
                  Revalidate for Selected Cities
                </Button>
              </div>
            </TabsContent>

            <TabsContent value='homepage' className='space-y-6'>
              <div className='rounded-md bg-blue-50 p-4'>
                <div className='flex items-center'>
                  <Home className='mr-2 h-5 w-5 text-blue-500' />
                  <h3 className='font-medium text-blue-700'>
                    Revalidate City Homepages
                  </h3>
                </div>
                <p className='mt-2 text-sm text-blue-600'>
                  This will revalidate the homepage for each selected city (/
                  {"{city}"}) and the main homepage (/).
                </p>
              </div>

              <Button
                onClick={handleRevalidateHomepages}
                disabled={revalidateMutation.isPending}
                variant='default'
                className='min-w-[200px]'
              >
                {revalidateMutation.isPending ? (
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                ) : null}
                Revalidate Homepages
              </Button>
            </TabsContent>

            <TabsContent value='supercategory' className='space-y-6'>
              <div className='rounded-md bg-blue-50 p-4'>
                <div className='flex items-center'>
                  <Layers className='mr-2 h-5 w-5 text-blue-500' />
                  <h3 className='font-medium text-blue-700'>
                    Revalidate Super Category Pages
                  </h3>
                </div>
                <p className='mt-2 text-sm text-blue-600'>
                  This will revalidate the super category pages for each
                  selected city and super category.
                </p>
              </div>

              {/* Super Category Selection */}
              <div>
                <label className='mb-2 block font-medium'>
                  Select Super Categories
                </label>
                <ScrollArea className='h-40 rounded-md border p-2'>
                  <div className='flex flex-wrap gap-2'>
                    <Button
                      variant='outline'
                      onClick={selectAllSuperCategories}
                      className={
                        selectedSuperCategories.length ===
                        superCategories.length
                          ? "border-primary-500 bg-blue-50"
                          : ""
                      }
                    >
                      {selectedSuperCategories.length === superCategories.length
                        ? "Deselect All Super Categories"
                        : "Select All Super Categories"}
                      {selectedSuperCategories.length ===
                        superCategories.length && (
                        <CheckCircle2 className='ml-2 h-4 w-4 text-primary-500' />
                      )}
                    </Button>
                    {superCategories.map((sc) => (
                      <Button
                        key={sc.url}
                        variant={
                          selectedSuperCategories.includes(sc.url)
                            ? "default"
                            : "outline"
                        }
                        onClick={() => {
                          setSelectedSuperCategories((prev) =>
                            prev.includes(sc.url)
                              ? prev.filter((c) => c !== sc.url)
                              : [...prev, sc.url],
                          )
                        }}
                      >
                        <div className='flex items-center gap-2'>
                          <span>{sc.super_category_short_name}</span>
                          {selectedSuperCategories.includes(sc.url) && (
                            <button type='button' className='ml-2'>
                              <XIcon className='h-4 w-4' />
                            </button>
                          )}
                        </div>
                      </Button>
                    ))}
                  </div>
                </ScrollArea>
              </div>

              <Button
                onClick={handleRevalidateSuperCategories}
                disabled={revalidateMutation.isPending}
                variant='default'
                className='min-w-[200px]'
              >
                {revalidateMutation.isPending ? (
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                ) : null}
                Revalidate Super Category Pages
              </Button>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <QuickActions
        filteredCities={filteredCities}
        superCategories={superCategories}
        onRevalidate={handleRevalidate}
        disabled={revalidateMutation.isPending}
      />

      <RevalidationStatus status={revalidationStatus} />
    </div>
  )
}

function QuickActions({
  filteredCities,
  superCategories,
  onRevalidate,
  disabled,
}: {
  filteredCities: City[]
  superCategories: SuperCategory[]
  onRevalidate: (path: string) => void
  disabled: boolean
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2 text-xl'>
          <Grid3X3 className='h-5 w-5 text-primary-500' />
          Quick Actions
        </CardTitle>
        <CardDescription>Common revalidation tasks</CardDescription>
      </CardHeader>
      <CardContent>
        <div className='grid gap-3 md:grid-cols-2 lg:grid-cols-3'>
          <QuickActionButton
            label='Main Homepage'
            path='/'
            onClick={() => onRevalidate("/")}
            disabled={disabled}
            icon={<Home className='mb-2 h-5 w-5 text-primary-500' />}
          />
          <QuickActionButton
            label='Main Sitemap'
            path='/sitemap.xml'
            onClick={() => onRevalidate("/sitemap.xml")}
            disabled={disabled}
            icon={<FileText className='mb-2 h-5 w-5 text-primary-500' />}
          />
          <QuickActionButton
            label='All City Sitemaps'
            onClick={async () => {
              for (const city of filteredCities) {
                await onRevalidate(`/sitemap/${city.city_url}`)
              }
            }}
            disabled={disabled}
            icon={<FileText className='mb-2 h-5 w-5 text-primary-500' />}
          />
          {superCategories.slice(0, 3).map((sc) => (
            <QuickActionButton
              key={sc.url}
              label={`${sc.super_category_short_name} Pages`}
              path={`/city/${sc.url}`}
              onClick={async () => {
                for (const city of filteredCities.slice(0, 3)) {
                  await onRevalidate(`/${city.city_url}/${sc.url}`)
                }
              }}
              disabled={disabled}
              icon={<Layers className='mb-2 h-5 w-5 text-primary-500' />}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

function RevalidationStatus({
  status,
}: {
  status: { [key: string]: "pending" | "success" | "error" }
}) {
  if (Object.keys(status).length === 0) return null

  return (
    <Card>
      <CardHeader>
        <CardTitle className='text-xl'>Revalidation Status</CardTitle>
        <CardDescription>
          Track the status of your revalidation requests
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className='h-[300px]'>
          <div className='space-y-2'>
            {Object.entries(status).map(([path, status]) => (
              <div
                key={path}
                className='flex items-center justify-between rounded-md border p-3 transition-colors hover:bg-gray-50'
              >
                <span className='font-mono text-sm'>{path}</span>
                <StatusBadge status={status} />
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

function StatusBadge({ status }: { status: "pending" | "success" | "error" }) {
  const config = {
    pending: {
      className: "bg-yellow-100 text-yellow-800",
      icon: <Loader2 className='mr-1 h-3 w-3 animate-spin' />,
    },
    success: {
      className: "bg-green-100 text-green-800",
      icon: <CheckCircle2 className='mr-1 h-3 w-3' />,
    },
    error: {
      className: "bg-red-100 text-red-800",
      icon: <XCircle className='mr-1 h-3 w-3' />,
    },
  }

  return (
    <span
      className={`flex items-center rounded-full px-3 py-1 text-sm ${config[status].className}`}
    >
      {config[status].icon}
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  )
}

function QuickActionButton({
  label,
  path,
  onClick,
  disabled,
  icon,
}: {
  label: string
  path?: string
  onClick: () => void
  disabled: boolean
  icon?: React.ReactNode
}) {
  return (
    <Button
      variant='outline'
      onClick={onClick}
      disabled={disabled}
      className='relative h-auto min-h-[80px] w-full p-4'
    >
      <div className='flex flex-col items-center gap-2'>
        {disabled && <Loader2 className='h-4 w-4 animate-spin' />}
        {icon}
        <span className='font-medium'>{label}</span>
        {path && <span className='text-xs text-gray-500'>{path}</span>}
      </div>
    </Button>
  )
}

export default RevalidatePage
