"use client"
import { Stepper } from "@/components/checkout/checkout-stepper"
import { ContactDetailsForm } from "@/components/checkout/contact-details-form"
import { DeliveryAddressForm } from "@/components/checkout/delivery-address-form"
import { OrderSummaryNew } from "@/components/checkout/order-summary-new"
import { ReviewAndPay } from "@/components/checkout/review-and-pay"
import { SectionHeader } from "@/components/checkout/section-header"
import { useCheckoutStore } from "@/store/checkout-store"
import { useEffect } from "react"

import { fetchAllCartItems } from "@/actions/cart"
import { useRentalStore } from "@/store/rental-store"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
// import { getLoggedInUser, getUserWallet } from '@/actions/user'
import { initializeSEON } from "@/actions/tools"
import { getUserWallet, updateUserDates } from "@/actions/user"
import { CarepalForm } from "@/components/checkout/carepal/carepal-form"
import { CheckoutError } from "@/components/checkout/checkout-error"
import { CartProducts } from "@/components/checkout/product-summary"
import CheckoutSkeleton from "@/components/skeletons/checkout"
import { generateFullAddress } from "@/functions/address-utils"
import { roundValue } from "@/functions/business-logics"
import { getCookie } from "@/functions/cookies"
import { moneyFormatter } from "@/functions/small-functions"
import { trackCheckoutInitiated } from "@/lib/gtag-event"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useUserStore } from "@/store/user-store"
import { CircleXIcon, VerifiedIcon } from "lucide-react"
import dynamic from "next/dynamic"

const CheckoutRedirectLoading = dynamic(
  () => import("@/components/loadings/checkout-redirect-loading"),
  {
    ssr: false,
  },
)

// Animation variants
const sectionVariants = {
  hidden: { height: 0, opacity: 0 },
  visible: { height: "auto", opacity: 1 },
}

export default function CheckoutPage() {
  const { openModal, closeModal, setIsNonCloseable } = useOnboardingStore()
  const { isLoggedIn } = useUserStore()

  const {
    active_section,
    is_contact_completed,
    is_delivery_completed,
    is_carepal_completed,
    is_review_completed,
    goToNextSection,
    goToPrevSection,
    setCartItems,
    setWalletBalance,
    setContactDetails,
    checkout_redirect_loading,
    setSeonFingerprint,
    cart_items,
    finalAmount,
    getCartCount,
    setCouponDiscount,
    setAppliedCouponCode,
    setOrderTotal,
    setHandlingCharges,
    // setWalletBalance
    contact_details,
    delivery_address,
    carepal_coverage,
    carepal_selected,
  } = useCheckoutStore()

  const { setWallet, user, userLoading } = useUserStore()

  const {
    total_days,
    same_day_surge,
    pickup_date,
    delivery_date,
    setCartOpen,
  } = useRentalStore()

  useEffect(() => {
    if (user) {
      setContactDetails({
        calling_number: user.calling_number,
        country_code_calling: user.country_code_calling,
        country_code_whatsapp: user.country_code_whatsapp,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        whatsapp_number: user.whatsapp_number,
      })
      setCartOpen(false)
    }
  }, [user, isLoggedIn, setContactDetails, setCartOpen])

  useEffect(() => {
    if (delivery_date && pickup_date && total_days > 0) {
      updateUserDates({
        delivery_date: delivery_date,
        return_date: pickup_date,
        number_of_days: total_days,
      })
    }
  }, [delivery_date, pickup_date, total_days])

  const { isLoading: cartItemsLoading, isError: cartError } = useQuery({
    queryKey: ["cart_items", delivery_date, pickup_date],
    queryFn: async () => {
      // if (!user) return
      const cartResult = await fetchAllCartItems({
        num_days: total_days,
        user_uid: getCookie("uid") ?? "",
      })
      if (!cartResult) return []
      setCartItems(cartResult, same_day_surge)
      return cartResult
    },
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    enabled: !userLoading,
  })

  const {} = useQuery({
    queryKey: ["user_wallet"], // Unique query key
    queryFn: async () => {
      const wallet = await getUserWallet()
      setWalletBalance(roundValue(wallet.amount))
      setWallet(wallet)
      return wallet
    }, // Async function to fetch SEON session

    refetchOnWindowFocus: true, // Prevent refetch on window focus
    enabled: !userLoading || !cartItemsLoading, // Enable the query when user data is loaded
  })

  const {} = useQuery({
    queryKey: ["seon_session"], // Unique query key
    queryFn: async () => {
      const session = await initializeSEON()
      setSeonFingerprint(session)
      return session
    },
    // Async function to fetch SEON session
    // retry: false,
    // // Optionally disable retries if initialization fails
    // Prevent refetch on window focus
    refetchOnWindowFocus: false,
    // Enable the query when user data is loaded
    enabled: !userLoading || !cartItemsLoading,
  })

  useEffect(() => {
    if (total_days > 0 && cart_items.length > 0) {
      const event_name = {
        contact: "CheckoutStep1",
        delivery: "CheckoutStep2",
        carepal: "Checkout Step Carepal",
        review: "CheckoutStep3",
      }
      trackCheckoutInitiated(
        event_name[active_section],
        cart_items,
        finalAmount(),
      )
    }
  }, [cart_items, finalAmount, total_days, active_section])

  useEffect(() => {
    trackCheckoutInitiated("CheckoutInitiated", cart_items, finalAmount())
  }, [])

  useEffect(() => {
    if (!isLoggedIn && !cartItemsLoading && !userLoading) openModal(true)
    else {
      setIsNonCloseable(false)
      closeModal()
    }
    // return () => openModal(false)
    //clean up function
    return () => {
      setIsNonCloseable(false)
      closeModal()
    }
  }, [
    cartItemsLoading,
    closeModal,
    isLoggedIn,
    openModal,
    setIsNonCloseable,
    userLoading,
  ])

  useEffect(() => {
    if (getCartCount() == 0) {
      setCouponDiscount(0)
      setAppliedCouponCode("", 0)
      setOrderTotal(0)
      setHandlingCharges(0)
    }
  }, [
    cart_items,
    getCartCount,
    setAppliedCouponCode,
    setCouponDiscount,
    setHandlingCharges,
    setOrderTotal,
  ])

  if (cartItemsLoading || userLoading) {
    return <CheckoutSkeleton />
  } else if (cartError) {
    return <CheckoutError cartError={cartError} />
  } else if (checkout_redirect_loading) {
    return <CheckoutRedirectLoading />
  } else if (!isLoggedIn) {
    return <></>
  } else {
    return (
      <>
        {/* {cartItemsLoading || userLoading ? (
          <CheckoutSkeleton />
        ) : ( */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className='w-full py-16 md:py-24'
        >
          {/* <div className="container grid grid-cols-1 gap-3 lg:gap-6 lg:grid-cols-3"> */}
          <div className='container grid grid-cols-1 gap-3 lg:grid-cols-[1fr,400px] lg:gap-6'>
            <Stepper
              currentSection={active_section}
              is_contact_completed={is_contact_completed}
              is_delivery_completed={is_delivery_completed}
              is_carepal_completed={is_carepal_completed}
              is_review_completed={is_review_completed}
              onNext={goToNextSection}
              onPrev={goToPrevSection}
            />
            <div className='lg:col-span- relative rounded-2xl bg-gray-100 p-4 md:rounded-radius'>
              {/* Mobile View */}

              <div className='md:hidden'>
                {[
                  { name: "contact", fullName: "Contact Details" },
                  { name: "delivery", fullName: "Delivery Address" },
                  { name: "carepal", fullName: "Damage Waiver" },
                  { name: "review", fullName: "Review & Pay" },
                ].map((section, index) => (
                  <motion.div
                    key={section.name}
                    variants={sectionVariants}
                    initial='hidden'
                    animate={
                      active_section === section.name ? "visible" : "hidden"
                    }
                    transition={{ duration: 0.3 }}
                    className='overflow-hidden'
                  >
                    <h2 className='mb-4 text-center text-xl font-semibold'>
                      {`${index + 1}. ${section.fullName}`}
                    </h2>
                    {section.name === "contact" && <ContactDetailsForm />}
                    {section.name === "delivery" && <DeliveryAddressForm />}
                    {section.name === "carepal" && <CarepalForm />}
                    {section.name === "review" && <ReviewAndPay />}
                  </motion.div>
                ))}
              </div>

              {/* Desktop View */}
              <div className='hidden md:block'>
                {/* Contact Details Section */}
                <div className='border-b border-neutral-150'>
                  <SectionHeader
                    number={1}
                    title='Contact Details'
                    section='contact'
                    isCompleted={is_contact_completed}
                    canOpen={true}
                  >
                    {is_contact_completed && (
                      <div className='text-b2 text-neutral-500'>
                        <p>
                          {contact_details?.first_name}{" "}
                          {contact_details?.last_name}
                        </p>
                        <p>{contact_details?.calling_number}</p>
                        <p>{contact_details?.whatsapp_number} (Whatsapp)</p>
                        <p>{contact_details?.email}</p>
                      </div>
                    )}
                  </SectionHeader>
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{
                      height: active_section === "contact" ? "auto" : 0,
                      opacity: active_section === "contact" ? 1 : 0,
                    }}
                    transition={{ duration: 0.3 }}
                    className='overflow-hidden'
                  >
                    <ContactDetailsForm />
                  </motion.div>
                </div>

                {/* Delivery Address Section */}
                <div className='border-b border-neutral-150'>
                  <SectionHeader
                    number={2}
                    title='Delivery Address'
                    section='delivery'
                    isCompleted={is_delivery_completed}
                    canOpen={true}
                  >
                    {is_delivery_completed && (
                      <div className='text-b2 text-neutral-500'>
                        {delivery_address && (
                          <p>{generateFullAddress(delivery_address)}</p>
                        )}
                      </div>
                    )}
                  </SectionHeader>
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{
                      height: active_section === "delivery" ? "auto" : 0,
                      opacity: active_section === "delivery" ? 1 : 0,
                    }}
                    transition={{ duration: 0.3 }}
                    className='overflow-hidden'
                  >
                    <DeliveryAddressForm />
                  </motion.div>
                </div>

                {/* Carepal Section */}
                <div className='border-b border-neutral-150'>
                  <SectionHeader
                    number={3}
                    title={`CarePal - Get a damage waiver of ${moneyFormatter(carepal_coverage)}`}
                    section='carepal'
                    isCompleted={is_carepal_completed}
                    canOpen={true}
                  >
                    {is_carepal_completed && (
                      <div className='text-b2 text-neutral-500'>
                        {carepal_selected ? (
                          <VerifiedIcon className='size-4 text-success-500 md:size-8' />
                        ) : (
                          <CircleXIcon className='size-4 text-destructive-500 md:size-8' />
                        )}
                      </div>
                    )}
                  </SectionHeader>
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{
                      height: active_section === "carepal" ? "auto" : 0,
                      opacity: active_section === "carepal" ? 1 : 0,
                    }}
                    transition={{ duration: 0.3 }}
                    className='overflow-hidden'
                  >
                    <CarepalForm />
                  </motion.div>
                </div>

                {/* Review & Pay Section */}
                <div>
                  <SectionHeader
                    number={4}
                    title='Review & Pay'
                    section='review'
                    isCompleted={is_review_completed}
                    canOpen={true}
                    updateLabel='Review Items'
                  >
                    <CartProducts />
                  </SectionHeader>
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{
                      height: active_section === "review" ? "auto" : 0,
                      opacity: active_section === "review" ? 1 : 0,
                    }}
                    transition={{ duration: 0.3 }}
                    className='overflow-hidden'
                  >
                    <ReviewAndPay />
                  </motion.div>
                </div>
              </div>
            </div>

            <div className='hidden self-start md:block lg:sticky lg:top-5 lg:col-span-1'>
              <OrderSummaryNew
                onDateChange={(range) => {
                  console.info("Date range changed:", range)
                }}
                onApplyCoupon={async (code) => {
                  console.info("Applyng coupon:", code)
                }}
                onRemoveCoupon={() => {
                  // console.log('Removing coupon')
                }}
                onPlaceOrder={async () => {
                  // console.log('Placing order')
                }}
              />
            </div>
          </div>
        </motion.div>
        {/* )} */}
      </>
    )
  }
}
