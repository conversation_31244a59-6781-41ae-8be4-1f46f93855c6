import { PolicyLayout } from "@/components/policy/policy-layout"
import { PolicySection } from "@/components/policy/policy-section"
import shippingPolicySections from "@/constants/shipping-policy.json"

export default function PrivacyPolicyPage() {
  return (
    <PolicyLayout
      title='Shipping Policy'
      subtitle='We deliver across India!'
      sections={shippingPolicySections}
    >
      {shippingPolicySections.map((section, index) => (
        <PolicySection
          key={section.id}
          id={section.id}
          title={section.title}
          content={section.content}
          index={index}
          isLast={index === shippingPolicySections.length - 1}
        />
      ))}
    </PolicyLayout>
  )
}
