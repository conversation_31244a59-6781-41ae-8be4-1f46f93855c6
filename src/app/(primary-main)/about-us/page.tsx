import { ContactInformations, ValuesSection } from "./_components/sectionts"

export const metadata = {
  title: "About Us | SharePal",
  description:
    "Learn about SharePal, a leading rental & refurbished platform for lifestyle products.",
}

const jsonLd = {
  "@context": "http://schema.org",
  "@type": "Organization",
  name: "<PERSON>harePal",
  description:
    "SharePal is a leading rental & refurbished platform for lifestyle products such as Cameras, Gaming consoles, Travel gear, Projectors/speakers and more. Get Zero Deposit rentals and free doorstep delivery at sharepal.in",
  email: "<EMAIL>",
  telephone: "+91-76192-20543",
  address: {
    "@type": "PostalAddress",
    addressLocality: "Bengaluru",
    addressRegion: "Karnataka",
    postalCode: "560103",
    streetAddress:
      "2nd Floor Marathahalli Sarjapur ORR Next to HSR Traffic Police Station",
  },
  aggregateRating: {
    "@type": "AggregateRating",
    ratingValue: "4.7",
    bestRating: "5",
    ratingCount: "1000",
  },
  founders: [
    { "@type": "Person", name: "<PERSON><PERSON>" },
    { "@type": "Person", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>" },
    { "@type": "Person", name: "<PERSON><PERSON><PERSON><PERSON>" },
  ],
  url: "https://sharepal.in",
  sameAs: [
    "https://www.facebook.com/Sharepal.in?mibextid=ZbWKwL",
    "https://www.instagram.com/sharepal.in?igsh=cGhhM3dvbDJkemMx",
    "https://youtube.com/@sharepal?si=uieQJwcktZBrCApC",
    "https://www.linkedin.com/company/sharepal",
  ],
}

export default function page() {
  return (
    <div className='to-whitempx-6 min-h-screen bg-gradient-to-b from-gray-50 py-20 !pb-5 md:px-12 md:py-24 lg:px-24'>
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      {/* <HeroSectionAbout />
      <MeetThePrincipalsSection /> */}
      <ValuesSection />
      <ContactInformations />
    </div>
  )
}
