"use client"

import { ValuesCard } from "@/components/cards/values-card"
import SpImage from "@/shared/SpImage/sp-image"
import { sectionAnimations } from "@/utils/animation-variants"
import { motion } from "framer-motion"
import { Building, Building2, EyeIcon, Phone, TargetIcon } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { LinkedInFilledIcon, WhatsAppLogoFilledIcon } from "sharepal-icons"

export function ValuesSection() {
  return (
    <motion.section
      variants={sectionAnimations}
      initial='initial'
      animate='animate'
      className='container relative mb-20 overflow-hidden rounded-2xl bg-gray-150 py-10 marker:rounded-3xl md:p-20'
    >
      {/* Background Elements */}
      <div className='absolute inset-0 -z-10'>
        <div className='absolute inset-0 bg-gradient-to-br from-gray-50 via-gray-100 to-gray-50' />
        <div className='bg-gradient-radial absolute right-0 top-0 -z-10 h-[600px] w-[600px] rotate-12 transform from-primary/5 to-transparent blur-3xl' />
        <div className='bg-gradient-radial absolute left-0 top-1/2 -z-10 h-[600px] w-[600px] -rotate-12 transform from-primary/5 to-transparent blur-3xl' />
      </div>

      <div className='w-full'>
        {/* Section Header */}
        <div className='mx-auto mb-10 max-w-3xl text-center md:mb-16'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className='inline-flex items-center gap-2 rounded-full bg-primary/5 px-4 py-2'
          >
            <span className='h-2 w-2 rounded-full bg-primary'></span>
            <span className='text-sm font-medium text-primary'>
              Our Foundation
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className='mt-6 text-balance text-4xl font-bold tracking-tight md:text-5xl lg:text-6xl'
          >
            Vision & Mission
          </motion.h2>
        </div>

        <motion.div
          variants={sectionAnimations}
          initial='initial'
          animate='animate'
          className='grid gap-8 md:grid-cols-2'
        >
          <ValuesCard
            title='Vision'
            description='To make aspirational-products accessible & affordable for everyone.'
            icon={EyeIcon}
            direction='left'
          />

          <ValuesCard
            title='Mission'
            description='To help consumers with multiple ways of experiencing aspirational-products. So you have the power to rent or buy-used or buy-new as per their budget & use.'
            icon={TargetIcon}
            direction='right'
          />
        </motion.div>
      </div>
    </motion.section>
  )
}

export function MeetThePrincipalsSection() {
  return (
    <motion.section
      variants={sectionAnimations}
      initial='initial'
      animate='animate'
      className='container relative mb-20 overflow-hidden rounded-2xl bg-gray-150 py-10 md:mb-32 md:rounded-3xl md:p-20'
    >
      {/* Background Elements */}
      <div className='absolute inset-0 -z-10'>
        <div className='absolute inset-0 bg-gradient-to-b from-gray-50/50 to-white' />
        <div className='bg-gradient-radial absolute right-0 top-0 -z-10 h-[600px] w-[600px] rotate-12 transform from-primary/5 to-transparent blur-3xl' />
        <div className='bg-gradient-radial absolute left-0 top-1/2 -z-10 h-[600px] w-[600px] -rotate-12 transform from-primary/5 to-transparent blur-3xl' />
      </div>

      <div className='w-full'>
        {/* Section Header */}
        <div className='mx-auto mb-10 max-w-3xl text-center md:mb-16'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className='inline-flex items-center gap-2 rounded-full bg-primary/5 px-4 py-2'
          >
            <span className='h-2 w-2 rounded-full bg-primary'></span>
            <span className='text-sm font-medium text-primary'>
              Leadership Team
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className='mt-6 text-balance text-4xl font-bold tracking-tight md:text-5xl lg:text-6xl'
          >
            Meet the Founders
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className='mt-6 text-balance text-lg text-muted-foreground'
          >
            As principals and co-founders, they oversee the day-to-day
            operations of SharePal and the development of our rental and
            refurbished product offerings.
          </motion.p>
        </div>

        {/* Principals Grid */}
        <div className='grid gap-8 px-6 md:grid-cols-3 md:p-0 lg:gap-12'>
          <PrincipalCard
            name='Rushi Narang'
            role='FOUNDER AND PRINCIPAL'
            imageSrc='/images/team/rushi-narang.jpg'
            delay={0.3}
            linkedin='https://linkedin.com/in/rushi-narang'
            description='Leading product strategy and innovation at SharePal'
          />
          <PrincipalCard
            name='Sourabh Jain'
            role='FOUNDER AND PRINCIPAL'
            imageSrc='/images/team/sourabh-jain.jpg'
            delay={0.4}
            linkedin='https://linkedin.com/in/sourabh-jain'
            description='Driving operations and business development'
          />
          <PrincipalCard
            name='Akashdeep Chhabra'
            role='FOUNDER AND PRINCIPAL'
            imageSrc='/images/team/akashdeep-chhabra.jpg'
            delay={0.5}
            linkedin='https://linkedin.com/in/akashdeep-chhabra'
            description='Overseeing technology and platform scaling'
          />
        </div>
      </div>
    </motion.section>
  )
}

interface PrincipalCardProps {
  name: string
  role: string
  imageSrc: string
  delay: number
  linkedin?: string
  description?: string
}

function PrincipalCard({
  name,
  imageSrc,
  delay,
  linkedin,
  description,
}: PrincipalCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: 0.5 }}
      className='group relative overflow-hidden rounded-2xl bg-gray-100 p-1 shadow-black/[0.03] transition-shadow'
    >
      <div className='aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-gray-100 to-gray-50'>
        <div className='relative h-full w-full'>
          <Image
            src={imageSrc}
            alt={name}
            fill
            className='object-cover transition duration-500 group-hover:scale-105'
          />
          {/* Gradient Overlay */}
          <div className='absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 transition duration-300 group-hover:opacity-100' />
        </div>
      </div>

      <div className='relative space-y-1 px-6 py-4'>
        {/* Social Links - Appears on Hover */}
        {linkedin && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: delay + 0.2 }}
            className='absolute right-6 top-0 -translate-y-1/2 transform'
          >
            <Link
              href={linkedin}
              target='_blank'
              rel='noopener noreferrer'
              className='flex h-10 w-10 items-center justify-center rounded-full bg-gray-100 shadow-lg transition-transform hover:-translate-y-1 hover:shadow-xl'
            >
              <LinkedInFilledIcon className='h-5 w-5 text-primary' />
            </Link>
          </motion.div>
        )}

        <div>
          <h3 className='text-xl font-bold'>{name}</h3>
          {/* <p className='mt-1 text-sm font-medium text-muted-foreground'>
            {role}
          </p> */}
        </div>

        {description && (
          <p className='text-sm text-muted-foreground'>{description}</p>
        )}
      </div>
    </motion.div>
  )
}

export function ContactInformations() {
  return (
    <motion.section
      variants={sectionAnimations}
      initial='initial'
      animate='animate'
      className='container relative mb-20 overflow-hidden rounded-2xl bg-gray-150 py-10 marker:rounded-3xl md:p-20'
    >
      {/* Background Elements */}
      <div className='absolute inset-0 -z-10'>
        <div className='absolute inset-0 bg-gradient-to-br from-gray-50 via-gray-100 to-gray-50' />
        <div className='bg-gradient-radial absolute right-0 top-0 -z-10 h-[600px] w-[600px] rotate-12 transform from-primary/5 to-transparent blur-3xl' />
        <div className='bg-gradient-radial absolute left-0 top-1/2 -z-10 h-[600px] w-[600px] -rotate-12 transform from-primary/5 to-transparent blur-3xl' />
      </div>

      <div className='w-full'>
        {/* Section Header */}
        <div className='mx-auto mb-10 max-w-3xl text-center md:mb-16'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className='inline-flex items-center gap-2 rounded-full bg-primary/5 px-4 py-2'
          >
            <span className='h-2 w-2 rounded-full bg-primary'></span>
            <span className='text-sm font-medium text-primary'>
              Get in Touch
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className='mt-6 text-balance text-4xl font-bold tracking-tight md:text-5xl lg:text-6xl'
          >
            Contact Information
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className='mt-6 text-balance text-lg text-muted-foreground'
          >
            We&apos;re here to help! Reach out through any of these channels
          </motion.p>
        </div>

        <div className='grid gap-8 md:grid-cols-2'>
          <ValuesCard
            title='Corporate Office'
            description='SharePal, C/o Awfis, 1st Floor, Soul Space Paradigm Marathahalli Village, Marathahalli, Bengaluru, Karnataka 560037'
            icon={Building2}
            direction='left'
          />
          <ValuesCard
            title='Registered Office'
            description='Swnac E-Kiraya Services Private Limited 1003/14, Sadar Road, Ambikapur, Surguja, Chattisgarh, India, 497001'
            icon={Building}
            direction='right'
          />

          {/* <ValuesCard
            title='Email Us'
            description={`For general inquiries: <EMAIL> `}
            description1={`\n For business: <EMAIL>`}
            icon={Mail}
            direction='right'
          /> */}
        </div>

        <div className='mt-6 md:mt-10'>
          <ValuesCard
            title='Customer Support'
            description='Our customer support team is available from 9 AM to 12 AM on all days. Call or WhatsApp us at +91 76192 20543'
            icon={Phone}
            direction='left'
          />
        </div>

        {/* Additional Contact Methods */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className='mt-16 flex flex-col items-center justify-center gap-8 rounded-3xl bg-gray-100 p-6 text-center md:p-12'
        >
          <div className='space-y-4'>
            <h3 className='text-2xl font-bold md:text-3xl'>
              Need Immediate Assistance?
            </h3>
            <p className='text-lg text-muted-foreground'>
              Our team is available on WhatsApp for quick support
            </p>
          </div>

          <Link
            href='https://wa.me/917619220543'
            target='_blank'
            rel='noopener noreferrer'
            className='inline-flex items-center gap-2 rounded-full bg-primary-500 px-6 py-3 text-white transition-transform hover:-translate-y-1 hover:shadow-lg'
          >
            <WhatsAppLogoFilledIcon className='h-5 w-5' />
            Chat on WhatsApp
          </Link>
        </motion.div>
      </div>
      <div id='web-engage-banner' className='container'>
        <Link target='_blank' href={""}>
          <SpImage
            alt='Banner Image'
            width={2000}
            height={2000}
            quality={100}
            className='relative z-[9] my-5 min-h-full min-w-full rounded-xl transition-all hover:shadow-soft-bottom'
            src={
              "https://images.sharepal.in/bike-rental-banner/bike-rental-compressed1.webp"
            }
          />
        </Link>
      </div>
    </motion.section>
  )
}
