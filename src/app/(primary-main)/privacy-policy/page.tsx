import { PolicyLayout } from "@/components/policy/policy-layout"
import { PolicySection } from "@/components/policy/policy-section"
// import { privacyPolicySections } from '@/constants/privacy-policy'
import privacyPolicySections from "@/constants/privacy-policy.json"

export default function PrivacyPolicyPage() {
  return (
    <PolicyLayout
      title='Privacy Policy'
      subtitle='Because your privacy matters!'
      sections={privacyPolicySections}
    >
      {privacyPolicySections.map((section, index) => (
        <PolicySection
          key={section.id}
          id={section.id}
          title={section.title}
          content={section.content}
          index={index}
          isLast={index === privacyPolicySections.length - 1}
        />
      ))}
    </PolicyLayout>
  )
}
