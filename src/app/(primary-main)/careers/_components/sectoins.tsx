"use client"

import type React from "react"

import { ValuesCard } from "@/components/cards/values-card"
import { Button } from "@/components/ui/button"
import { Typography } from "@/components/ui/typography"
import {
  sectionVariants,
  staggerContainerVariants,
} from "@/utils/animation-variants"
import { motion } from "framer-motion"
import { ArrowRight, Coffee, Mail, Rocket, Star, Users } from "lucide-react"

export const HeroSection: React.FC = () => (
  <section className='container'>
    <motion.div
      variants={sectionVariants}
      className='relative'
      initial='hidden'
      animate='visible'
    >
      {/* Decorative background */}
      {/* <div className='absolute inset-0 -z-10'>
        <div className='absolute left-1/2 top-1/2 h-[300px] w-[300px] -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary/5 blur-3xl md:h-[600px] md:w-[600px]' />
      </div> */}

      <Typography
        as='h1'
        className='relative mb-6 text-d6 font-bold tracking-tight text-foreground md:text-d3 lg:text-d1'
      >
        Join Our Mission
        <Star className='inline-flex size-6 -translate-y-7 animate-pulse text-yellow-500 md:h-8 md:w-8' />
      </Typography>

      <div className='mx-auto space-y-6'>
        <Typography as='p' className='text-b2 text-neutral-500 md:text-h5'>
          Join us in revolutionizing the sharing economy and building a more
          more affordable, accessible & sustainable future.
          {/* Join our mission in making this world more affordable, accessible & sustainable */}
        </Typography>
        <Typography as='p' className='text-b2 text-neutral-500 md:text-b1'>
          At SharePal, we&apos;re not just renting products - we&apos;re
          creating a movement towards mindful consumption and accessibility.
        </Typography>
      </div>
    </motion.div>
  </section>
)

export function ValueProposition() {
  return (
    <section className='container py-16 md:py-24'>
      <motion.div
        initial='hidden'
        animate='visible'
        variants={sectionVariants}
        className='mx-auto max-w-7xl'
      >
        <Typography
          as='h2'
          className='relative mb-6 text-d6 font-bold tracking-tight text-foreground md:text-d3 lg:text-d1'
        >
          Our Values
        </Typography>

        <motion.div
          variants={staggerContainerVariants}
          initial='hidden'
          whileInView='visible'
          viewport={{ once: true }}
          className='grid gap-6 md:grid-cols-2'
        >
          <ValuesCard
            title='Innovation First'
            description="We're building the future of consumption, one rental at a time"
            icon={Rocket}
            direction='left'
          />
          <ValuesCard
            title='Impact Driven'
            description='Make a real difference in how people access and use products'
            icon={Star}
            direction='right'
          />
          <ValuesCard
            title='Growth Focused'
            description='Rapid scaling with endless opportunities to grow and learn'
            icon={Users}
            direction='left'
          />
          <ValuesCard
            title='Work-Life Balance'
            description='We value your wellbeing and encourage a healthy balance'
            icon={Coffee}
            direction='right'
          />
        </motion.div>
      </motion.div>
    </section>
  )
}

export const CTASection: React.FC = () => (
  <section className='container py-16'>
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
      className='relative mx-auto max-w-4xl rounded-3xl bg-gradient-to-br from-primary-100 to-primary-200 p-6 md:p-12'
    >
      <div className='absolute inset-0 -z-10'>
        <div className='absolute left-1/2 top-1/2 h-[200px] w-[200px] -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary/10 blur-3xl md:h-[300px] md:w-[300px]' />
      </div>

      <Typography
        as='h2'
        className='mb-4 text-h3 font-bold text-foreground md:mb-8 md:text-h2'
      >
        Ready to Make an Impact?
      </Typography>

      <Typography
        as='p'
        className='mb-6 text-b2 text-muted-foreground md:mb-10 md:text-b1'
      >
        If you are passionate about the sharing economy and have the desire to
        build things for the future, we&apos;d love to hear from you.
      </Typography>

      <div className='flex flex-col gap-4 sm:flex-row'>
        <Button variant='default' size='lg' asChild>
          <a
            href='mailto:<EMAIL>'
            className='flex items-center justify-center gap-3'
          >
            <Mail className='h-5 w-5' />
            <Typography as='span' className='text-bt1'>
              <EMAIL>
            </Typography>
            <ArrowRight className='h-4 w-4 transition-transform group-hover:translate-x-1' />
          </a>
        </Button>
      </div>
    </motion.div>
  </section>
)

export const AdditionalInfoSection: React.FC = () => (
  <section className='container pb-16 md:pb-24'>
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      className='mx-auto max-w-3xl text-center'
    >
      <div className='mb-6 inline-flex items-center gap-3 rounded-full bg-secondary px-4 py-2 sm:px-6 sm:py-3'>
        <span className='h-2 w-2 animate-pulse rounded-full bg-green-500' />
        <Typography
          as='span'
          className='text-b4 font-medium text-secondary-foreground md:text-b3'
        >
          We&apos;re actively hiring!
        </Typography>
      </div>

      <Typography as='p' className='text-b2 text-muted-foreground md:text-b1'>
        We&apos;re always looking for talented individuals who share our vision.
      </Typography>

      <div className='mt-8 flex flex-wrap items-center justify-center gap-4 md:gap-8'>
        <span className='flex items-center gap-2 text-b4 text-muted-foreground md:text-b3'>
          <Users className='h-4 w-4' />
          Remote-friendly
        </span>
        <span className='hidden h-1 w-1 rounded-full bg-border sm:block' />
        <span className='flex items-center gap-2 text-b4 text-muted-foreground md:text-b3'>
          <Star className='h-4 w-4' />
          Competitive Benefits
        </span>
        <span className='hidden h-1 w-1 rounded-full bg-border sm:block' />
        <span className='flex items-center gap-2 text-b4 text-muted-foreground md:text-b3'>
          <Users className='h-4 w-4' />
          Inclusive Workplace
        </span>
      </div>
    </motion.div>
  </section>
)
