"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import Image from "next/image"

type Section = {
  id: string
  title: string
}

type Step = {
  title: string
  description: string
  icon: string
}

type RefundTimeline = {
  value: string
  title: string
  locations: string[] | string
}

const steps: Step[] = [
  {
    title: "1. Pick-up",
    description:
      "On the scheduled pick-up date, we will arrange the pick-up of your order. Depending on your location, it will take 3 hours to 5 days for the product(s) to reach back to us.",
    icon: "https://sharepal.cdn.bubble.io/f1611525224528x227497696031349950/id-card.svg",
  },
  {
    title: "2. Quality Check",
    description:
      "Post receipt of the goods, a quick quality check is performed and a quality report is generated. If there is any damage/or missing items you will be notified.",
    icon: "https://sharepal.cdn.bubble.io/f1611525237265x323867553799469060/verify.svg",
  },
  {
    title: "3. Payout Link Issued",
    description:
      "The refund is issued via a payout link, where all you need to do is update your bank details and get the money deposited to your account within 2 hours.",
    icon: "https://sharepal.cdn.bubble.io/f1611525248491x536896963801899200/verified.svg",
  },
]

const refundTimelines: RefundTimeline[] = [
  {
    value: "quick",
    title: "2-3 days",
    locations: ["Mumbai", "Delhi/NCR", "Bangalore"],
  },
  {
    value: "standard",
    title: "5-6 days",
    locations: ["Pune", "Chennai", "Hyderabad", "Ahmedabad"],
  },
  {
    value: "others",
    title: "7 days",
    locations:
      "All other locations not mentioned in Quick or Standard refund categories.",
  },
]

export default function RefundProcessWrapper({
  sections,
}: {
  sections: Section[]
}) {
  return (
    <div className='w-full'>
      {sections.map((section, index) => (
        <motion.div
          key={section.id}
          id={section.id}
          className='mb-8'
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          {/* <h3 className="mb-4 text-2xl font-bold"> */}
          <h3 className='mb-2 text-sm font-semibold text-gray-900 md:text-base'>
            {" "}
            {index + 1}. {section.title}
          </h3>

          {section.id === "policy" && (
            <div className='space-y-4'>
              <p className='text-xs font-medium leading-relaxed text-neutral-700'>
                To ensure responsible usage, we take a nominal security deposit,
                which is refundable upon the safe return of the product(s). The
                refund is initiated within 2-3 days of the receipt of the
                products.
              </p>
              <p className='text-xs font-medium leading-relaxed text-neutral-700'>
                The refund is issued via a payout link which you will receive
                over SMS/WhatsApp. All you need to do is update your bank
                details and get the money directly into your bank account.
              </p>
              <p className='text-xs font-medium leading-relaxed text-neutral-700'>
                We see very few damages or lost items and hence more than 98%
                orders are issued full refund.
              </p>
            </div>
          )}

          {section.id === "steps" && (
            <div className='grid gap-4'>
              {steps.map((step, index) => (
                <Card key={index}>
                  <CardHeader className='p-4 pb-2'>
                    <CardTitle className='flex items-center gap-2'>
                      <Image
                        src={step.icon}
                        alt=''
                        className='h-8 w-8'
                        width={100}
                        height={100}
                      />
                      {step.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='p-4 pt-0'>
                    <p className='text-xs font-medium leading-relaxed text-neutral-700'>
                      {step.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {section.id === "timelines" && (
            <Tabs defaultValue='quick'>
              <TabsList className='w-full justify-start rounded-b-none border-input'>
                {refundTimelines.map((timeline) => (
                  <TabsTrigger key={timeline.value} value={timeline.value}>
                    {timeline.title}
                  </TabsTrigger>
                ))}
              </TabsList>
              {refundTimelines.map((timeline) => (
                <TabsContent
                  key={timeline.value}
                  className='mt-0'
                  value={timeline.value}
                >
                  <Card className='rounded-xl rounded-t-none p-4'>
                    <CardHeader className='p-0'>
                      <CardTitle className='pb-3'>{timeline.title}</CardTitle>
                    </CardHeader>
                    <CardContent className='p-0'>
                      {Array.isArray(timeline.locations) ? (
                        <ul className='list-disc space-y-2 pl-5'>
                          {timeline.locations.map((location, index) => (
                            <li
                              className='text-sm font-medium leading-relaxed text-neutral-700'
                              key={index}
                            >
                              {location}
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className='text-sm font-medium leading-relaxed text-neutral-700'>
                          {timeline.locations}
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              ))}
            </Tabs>
          )}

          {section.id === "payout" && (
            <p className='text-xs font-medium leading-relaxed text-neutral-700'>
              The refund is processed via a secure payout link. Once you receive
              the link via SMS or WhatsApp, enter your bank details to have the
              money deposited directly into your account within 2 hours.
            </p>
          )}
        </motion.div>
      ))}
    </div>
  )
}
