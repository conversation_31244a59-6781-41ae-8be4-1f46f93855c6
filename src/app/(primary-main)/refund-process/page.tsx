import type { Metadata } from "next"
import { PolicyLayout } from "@/components/policy/policy-layout"
import RefundProcessContent from "./_components/refund-process-content"

export const metadata: Metadata = {
  title: "Refund Process | SharePal",
  description: "Learn about SharePal's quick and easy online refund process.",
}

const sections = [
  { id: "policy", title: "Refund Policy" },
  { id: "steps", title: "How It Works" },
  { id: "timelines", title: "Refund Timelines" },
  { id: "payout", title: "Payout Process" },
]

export default function RefundProcessPage() {
  return (
    <PolicyLayout
      title='Deposit Refund Process'
      subtitle='A quick & easy completely online process.'
      sections={sections}
    >
      <RefundProcessContent sections={sections} />
    </PolicyLayout>
  )
}
