import { PolicyLayout } from "@/components/policy/policy-layout"
import { PolicySection } from "@/components/policy/policy-section"
import damagePolicySections from "@/constants/damage-policy.json"

export default function PrivacyPolicyPage() {
  return (
    <PolicyLayout
      title='Damage Policy'
      subtitle='We understand, sometimes things go wrong!'
      sections={damagePolicySections}
    >
      {damagePolicySections.map((section, index) => (
        <PolicySection
          key={section.id}
          id={section.id}
          title={section.title}
          content={section.content}
          index={index}
          isLast={index === damagePolicySections.length - 1}
        />
      ))}
    </PolicyLayout>
  )
}
