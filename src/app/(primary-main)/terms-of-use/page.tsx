import { PolicyLayout } from "@/components/policy/policy-layout"
import { PolicySection } from "@/components/policy/policy-section"
import rentalAgreementSections from "@/constants/rental-agreement.json"

export default function PrivacyPolicyPage() {
  return (
    <PolicyLayout
      title='Terms of Use'
      subtitle='Our Usage Policy'
      sections={rentalAgreementSections}
    >
      {rentalAgreementSections.map((section, index) => (
        <PolicySection
          key={section.id}
          id={section.id}
          title={section.title}
          content={section.content}
          index={index}
          isLast={index === rentalAgreementSections.length - 1}
        />
      ))}
    </PolicyLayout>
  )
}
