// import ProductCard from '@/components/cards/product-card'
import ListingHome from "@/components/hero/listing-home"
import Reviews from "@/components/reviews"
// import SectionTitle from '@/components/section-title'

// import { Button } from '@/components/ui/button'
import {
  // fetchAllCategoriesProductsSC,
  fetchCategorySeo,
  fetchSubCategoriesWithSC,
} from "@/actions/category"
// import { RentalItem } from '@/types'
import { redirectCategory } from "@/actions/redirects"
import { fetchGeneralSeoContent } from "@/actions/seo-data"
import { fetchSuperCategories } from "@/actions/super-category"
import NotFound from "@/app/not-found"
import { DynamicBreadcrumb } from "@/components/custom/breadcrumbs"
import FAQServerComponent from "@/components/custom/faqs-list"
import ShowMoreProducts from "@/components/custom/show-more-products"
import FooterSeoPortal from "@/components/layout/footer/footer-seo-portal"
import CategorySeoContent from "@/components/seocontent/cat-seo"
import SeoContent from "@/components/seocontent/seo-content"
import {
  capitalizeFirstLetter,
  formatUrlName,
} from "@/functions/small-functions"
import { SubCategory } from "@/types"
import { Metadata, ResolvingMetadata } from "next"
import { permanentRedirect } from "next/navigation"

interface ListingPageProps {
  params: Promise<{ city: string; cat: string }>
}

export async function generateMetadata(
  { params }: ListingPageProps,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  // read route params
  const { city, cat } = await params
  const seoDesc = await fetchCategorySeo(cat)
  const formattedCatName = formatUrlName(cat)
  const fomattedCity = capitalizeFirstLetter(city)
  const keywords = `${formattedCatName} on rent in ${fomattedCity}, rent ${formattedCatName} in ${fomattedCity}, ${formattedCatName} rental in ${fomattedCity}`
  // optionally access and extend (rather than replace) parent metadata
  const previousImages = (await parent).openGraph?.images || []
  const title = `Rent ${formattedCatName} in ${fomattedCity} |  Zero Deposit Rentals `
  const description = `Rent ${formattedCatName} in ${fomattedCity} from SharePal - India's most trusted lifestyle gear rental platform. Zero Deposit | Free Delivery | Excellent Quality | Pay on Delivery`
  return {
    title: title,
    description: description,
    openGraph: {
      title: title,
      description: description,
      images: [
        seoDesc?.seo_image
          ? seoDesc?.seo_image
          : `https://images.sharepal.in/misc/hard-coded/SharePal_logo.webp`,
        ...previousImages,
      ],
    },
    keywords: keywords,
  }
}

export default async function Page({ params }: ListingPageProps) {
  // const city = (await params).city
  const { city, cat } = await params

  //fetch superCategories first to redirect to supercategory url if its container category url in cat.

  const [superCategories, subCategories, seoDesc, seoContent] =
    await Promise.all([
      fetchSuperCategories().catch((error) => {
        console.error("Error fetching superCategories:", error)
        return [] // Return a fallback value (empty array)
      }),
      fetchSubCategoriesWithSC(cat).catch((error) => {
        console.error("Error fetching subcategories:", error)
        return [] // Return a fallback value (empty array)
      }),
      fetchCategorySeo(cat).catch((error) => {
        console.error("Error fetching SEO data:", error)
        return null // Return a fallback value (null)
      }),
      fetchGeneralSeoContent(city, cat).catch((error) => {
        console.error("Error fetching SEO content:", error)
        return null // Return a fallback value (null)
      }),
    ])

  // Fetch subcategories on the server using Promise.allSettled
  const allSubCategoriesResults = await Promise.allSettled(
    superCategories.map((cat) => fetchSubCategoriesWithSC(cat.url)),
  )

  // Filter out only fulfilled promises and flatten the results
  const allSubCategories = allSubCategoriesResults
    .filter((result) => result.status === "fulfilled")
    .flatMap(
      (result) => (result as PromiseFulfilledResult<SubCategory[]>).value,
    )

  if (!superCategories || !superCategories.length || !subCategories.length) {
    // here before sending not found iwill check whether its my old url based on that i will permanet rediret
    const data = await redirectCategory(cat)

    if (data) {
      permanentRedirect(`/${city}/${data.super_category_url}`)
    } else {
      return NotFound()
    }
  }

  // Need to change to the new api to get all the product from the superCategories
  // const allCategoryProducts = await fetchAllCategoriesProductsSC(
  //   city,
  //   formatUrlName(cat),
  //   1,
  // )

  const jsonLd = {
    "@context": "http://schema.org",
    "@type": "Product",
    name: `${cat}`,
    image: `${seoDesc?.seo_image}`,
    brand: {
      "@type": "Brand",
      name: "SharePal",
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.7",
      bestRating: "5",
      ratingCount: "1000",
    },
    keywords: [`rent ${cat} in ${city}`, `${cat} on rent in ${city}`],
  }
  // console.log(allCategoryProducts)
  return (
    <div className='min-h-screen w-full'>
      {/* Adding JSON-LD to the page */}
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      {/* hero section */}
      <ListingHome
        categories={superCategories}
        subCategories={subCategories}
        city={city}
        category={cat}
        allSubCategories={allSubCategories}
      />

      <ShowMoreProducts />

      <section className='container !my-10 rounded-3xl bg-gray-100 p-4 py-10'>
        <FAQServerComponent
          category={formatUrlName(cat)}
          city={city}
          type={"rent"}
        />
      </section>

      <DynamicBreadcrumb />

      <Reviews />

      {/* seo content  */}
      <FooterSeoPortal>
        {/* <h2>{seoDesc?.desc}</h2> */}
        {seoContent ? (
          <SeoContent seoContent={seoContent} />
        ) : (
          <CategorySeoContent city={city} seoDesc={seoDesc} />
        )}
      </FooterSeoPortal>
    </div>
  )
}
