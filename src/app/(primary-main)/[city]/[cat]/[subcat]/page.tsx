import {
  fetchSubCategoriesWithSC,
  fetchSubCategoryProducts,
} from "@/actions/category"
import { redirectCategory } from "@/actions/redirects"
import { fetchSuperCategories } from "@/actions/super-category"
import NotFound from "@/app/not-found"
import ProductCard from "@/components/cards/product-card"
import { DynamicBreadcrumb } from "@/components/custom/breadcrumbs"
import FAQServerComponent from "@/components/custom/faqs-list"
import EmptyListingItems from "@/components/empty-screens/empty-listing-items"
import ListingHome from "@/components/hero/listing-home"
import Reviews from "@/components/reviews"
import SectionTitle from "@/components/section-title"
import { filterAndSortRentalItems } from "@/functions/products"

import {
  addOnRent,
  capitalizeFirstLetter,
  formatUrl,
  formatUrlName,
  removeOnRent,
} from "@/functions/small-functions"
import type { RentalItem, SubCategory } from "@/types"
import type { Metadata, ResolvingMetadata } from "next"
import { permanentRedirect } from "next/navigation"

interface ListingPageProps {
  params: Promise<{ city: string; cat: string; subcat: string }>
}

export async function generateMetadata(
  { params }: ListingPageProps,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const { city, subcat } = await params
  const formattedSubCatName = formatUrlName(subcat)
  const formattedCity = capitalizeFirstLetter(city)

  const keywords = `${formattedSubCatName} on rent in ${formattedCity}, rent ${formattedSubCatName} in ${formattedCity}, ${formattedSubCatName} rental in ${formattedCity}`
  const previousImages = (await parent).openGraph?.images || []

  return {
    title: `Rent ${formattedSubCatName} in ${formattedCity} | Zero Deposit Rentals`,
    description: `Rent ${formattedSubCatName} in ${formattedCity} from SharePal - India's most loved lifestyle gear rental platform. Zero Deposit | Free Delivery | Excellent Quality | Pay on Delivery`,
    openGraph: {
      images: [...previousImages],
    },
    keywords: keywords,
  }
}

export default async function Page({ params }: ListingPageProps) {
  const { city, cat, subcat } = await params

  const [superCategories, subCategories, subCategoryProducts] =
    await Promise.all([
      fetchSuperCategories().catch((error) => {
        console.error("Error fetching superCategories:", error)
        return [] // Return a fallback value (null) in case of an error
      }),
      fetchSubCategoriesWithSC(addOnRent(cat)).catch((error) => {
        console.error("Error fetching subcategories:", error)
        return [] // Return a fallback value (empty array)
      }),
      fetchSubCategoryProducts(city, subcat).catch((error) => {
        console.error("Error fetching subcategory products:", error)
        return [] // Return a fallback value (empty array)
      }),
    ])

  // const activeCategory = useMemo(
  //   () => superCategories?.find((item) => item.url.includes(category)),
  //   [superCategories, category],
  // )

  // Fetch subcategories on the server using Promise.allSettled
  const allSubCategoriesResults = await Promise.allSettled(
    superCategories.map((cat) => fetchSubCategoriesWithSC(cat.url)),
  )

  // Filter out only fulfilled promises and flatten the results
  const allSubCategories = allSubCategoriesResults
    .filter((result) => result.status === "fulfilled")
    .flatMap(
      (result) => (result as PromiseFulfilledResult<SubCategory[]>).value,
    )

  // If essential data is missing, return NotFound
  if (
    !superCategories ||
    !superCategories.length ||
    !subCategories ||
    !subCategories.length
  ) {
    console.error("Essential data is missing. Rendering NotFound page.")
    // return NotFound()
    const data = await redirectCategory(addOnRent(cat))

    if (data) {
      permanentRedirect(
        `/${city}/${removeOnRent(data.super_category_url)}/${subcat}`,
      )
    } else {
      return NotFound()
    }
  }

  const jsonLd = {
    "@context": "http://schema.org",
    "@type": "Product",
    name: `${formatUrl(subcat)} in ${city}`,
    image: `${subCategoryProducts.length > 0 ? subCategoryProducts[0]?.ri_image.split(",")[0] : ""}`,
    brand: {
      "@type": "Brand",
      name: "SharePal",
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.7",
      bestRating: "5",
      ratingCount: "1000",
    },
    keywords: [`rent ${subcat} in ${city}`, `${subcat} on rent in ${city}`],
    review: {
      "@type": "Review",
      reviewRating: {
        "@type": "Rating",
        ratingValue: "4.7",
        bestRating: "5",
      },
      author: {
        "@type": "Organization",
        name: "SharePal",
      },
    },
  }

  return (
    <div className='min-h-screen w-full'>
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <div className='relative'>
        {/* hero section */}
        <ListingHome
          categories={superCategories}
          subCategories={subCategories}
          city={city}
          category={cat}
          allSubCategories={allSubCategories}
        />

        <section className='container relative z-[1] pb-10'>
          <SectionTitle
            nText={formatUrlName(subcat) + " on rent"}
            className='border-b-2 border-neutral-200 py-2 font-inter text-h6 capitalize md:py-5 md:text-h2'
            cText=''
          />
          {subCategoryProducts && subCategoryProducts.length > 0 ? (
            <div className='mt-3 grid grid-cols-2 gap-x-2 gap-y-5 md:mt-6 md:grid-cols-3 md:gap-8 lg:grid-cols-4'>
              {filterAndSortRentalItems(subCategoryProducts).map(
                (item: RentalItem) => (
                  <ProductCard key={item.id} data={item} city={city} />
                ),
              )}
            </div>
          ) : (
            <EmptyListingItems />
          )}
        </section>

        <section className='container !my-10 rounded-3xl bg-gray-100 p-4 py-10'>
          <FAQServerComponent
            type={"rent"}
            city={city}
            category={formatUrlName(subcat) ?? ""}
          />
        </section>

        <DynamicBreadcrumb />
      </div>
      <Reviews />
    </div>
  )
}
