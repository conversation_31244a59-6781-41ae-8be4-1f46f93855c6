import KeyBenefitCard from "@/components/cards/key-benefit-card"

import ProductInfo from "@/components/custom/product-info"
// import { RentalPeriodSelector } from '@/components/custom/rental-dates-select'
import SharePalPromise from "@/components/custom/sharepal-promise"

import SectionTitle from "@/components/section-title"
import ProductSlider from "@/components/sliders/product-slider"

import SpImage from "@/shared/SpImage/sp-image"

import { fetchCategoriesProducts } from "@/actions/category"
import { fetchComboProducts, fetchProduct } from "@/actions/product"
import { DynamicBreadcrumb } from "@/components/custom/breadcrumbs"
import ComboProducts from "@/components/custom/combo-products"
import FAQServerComponent from "@/components/custom/faqs-list"
import { ProductPageTitle } from "@/components/heading/prouduct-page"
import WhatsInBox from "@/components/product/inclusions"
import ProductDetailsWrapper from "@/components/product/product-details-wrapper"
import ProductVideos from "@/components/product/product-videos"
import { ProductReviews } from "@/components/reviews"
import { SwiperSlider } from "@/components/sliders/swiper-carousel"
import { HARD_CODED_IMAGE_URL } from "@/constants"
import { filterAndSortRentalItems } from "@/functions/products"
import {
  capitalizeFirstLetter,
  formatUrlName,
  splitString,
} from "@/functions/small-functions"
import { RentalItemProduct } from "@/types"
import { Metadata, ResolvingMetadata } from "next"
import { notFound } from "next/navigation"
import { Suspense } from "react"

export const revalidate = 3600 // revalidate at most every hour

interface ProductPageProps {
  params: Promise<{
    city: string
    cat: string
    product: string
    subcat: string
  }>
}

export async function generateMetadata(
  { params }: ProductPageProps,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  // read route params
  const { city, product, subcat } = await params

  const formattedProductName = formatUrlName(product)
  const fomattedCity = capitalizeFirstLetter(city)
  const productDetails = (await fetchProduct(
    city,
    product,
    subcat,
  )) as RentalItemProduct

  const keywords = `${formattedProductName} on rent in ${fomattedCity}, rent ${formattedProductName} in ${fomattedCity}, ${formattedProductName} rental in ${fomattedCity}`
  // optionally access and extend (rather than replace) parent metadata
  const previousImages = (await parent).openGraph?.images || []

  const productImages = splitString(productDetails?.ri_image)
  //   categoryData?.sc_image
  // get image from super category

  return {
    title: `Rent ${formattedProductName} in ${fomattedCity} |  Zero Deposit Rentals `,
    description: `Rent ${formattedProductName} in ${fomattedCity} from SharePal - India's most loved lifestyle gear rental  platform. Zero Deposit | Free Delivery | Excellent Quality | Pay on Delivery`,
    openGraph: {
      images: [
        productImages[0],

        // seoDesc?.seo_image
        //   ? seoDesc?.seo_image
        //   : `https://images.sharepal.in/misc/hard-coded/SharePal_logo.webp`,
        ...previousImages,
      ],
    },
    keywords: keywords,
  }
}

const ProductPage = async ({ params }: ProductPageProps) => {
  // const city = 'bangalore'
  const { city, cat, subcat, product } = await params
  const productDetails = (await fetchProduct(
    city,
    product,
    subcat,
  )) as RentalItemProduct

  if (!productDetails) {
    notFound()
  }

  const { ri_image, ri_short_name, ri_name, has_inclusions } = productDetails

  //fetch fetchComboProducts if productDetails?.combo_product
  let comboProducts = []
  if (productDetails?.has_combo_products) {
    comboProducts = await fetchComboProducts(ri_name, city).catch((error) => {
      console.error("Failed to fetch combo products:", error)
      return []
    })
  }

  //need to update this too
  const randomCategory = await fetchCategoriesProducts(
    city,
    productDetails?.category_id,
  )

  //old way to get product videos
  // const productVideos = splitString(videoSize?.product_short_videos, ";")

  //jsonLd schema
  const jsonLd = {
    "@context": "http://schema.org",
    "@type": "Product",
    name: `${productDetails?.ri_short_name}`,
    image: `${productDetails?.ri_image?.split(",")[0]}`,
    description: `${productDetails?.product_qualities}`,
    brand: {
      "@type": "Brand",
      name: "SharePal",
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.7",
      bestRating: "5",
      ratingCount: "1000", // example value, update as needed
    },
    keywords: [
      `rent ${productDetails?.ri_short_name} in ${city}`,
      `${productDetails?.ri_short_name} on rent ${capitalizeFirstLetter(city)}`,
    ],
    review: {
      "@type": "Review",
      reviewRating: {
        "@type": "Rating",
        ratingValue: "4.7",
        bestRating: "5",
      },
      author: {
        "@type": "Organization",
        name: "SharePal",
      },
    },
  }

  return (
    // <div className="w-full overflow-x-hidden pt-28 md:pt-24">
    <div className='w-full pt-28 md:pt-24'>
      {/* Adding JSON-LD to the page */}
      <script
        type='application/ld+json'
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      {/* the overflow-x-hidden is causing issue with the styling */}
      <div className='container relative flex flex-col items-start gap-3 md:gap-6 xl:flex-row'>
        {/* left */}
        <div className='flex w-full flex-col gap-3 md:gap-6 xl:max-w-3xl xl:flex-[2]'>
          <ProductSlider images={splitString(ri_image)} />

          {/* <RentalPeriodSelector /> */}

          <div className='flex flex-[1] flex-col items-center justify-center gap-2 sm:gap-3 md:gap-6 xl:hidden'>
            {/*mobile right */}
            <WhatsInBox
              has_inclusions={has_inclusions}
              productName={ri_short_name}
            />

            <ComboProducts data={comboProducts} city={city} />

            <Suspense fallback={<div>Loading...</div>}>
              <ProductInfo product={productDetails} />
            </Suspense>

            <Suspense fallback={<div>Loading...</div>}>
              <ProductDetailsWrapper
                productName={ri_short_name}
                featureSpecs={productDetails?.features_specs}
              />
            </Suspense>
          </div>

          <div className='hidden w-full xl:block'>
            <WhatsInBox
              has_inclusions={has_inclusions}
              productName={ri_short_name}
            />

            <ComboProducts data={comboProducts} city={city} />
          </div>

          <div className='flex flex-col items-start gap-4 space-y-5 rounded-2xl bg-gray-100 md:gap-8 md:rounded-3xl md:pt-6'>
            <SharePalPromise className='px-0 py-0' />
            <HowtoRent />

            <Suspense fallback={<div>Loading...</div>}>
              <ProductVideos productName={ri_name} />
            </Suspense>

            <KeyBenefits />
            <FAQServerComponent type='rent' city={city} category={ri_name} />
          </div>
        </div>

        {/* right */}
        <div className='sticky top-6 hidden flex-[1] flex-col items-center justify-center gap-6 md:top-24 xl:flex'>
          {/*desktop right */}
          <ProductInfo product={productDetails} />
          <Suspense fallback={<div>Loading...</div>}>
            <ProductDetailsWrapper
              productName={ri_short_name}
              featureSpecs={productDetails?.features_specs}
            />
          </Suspense>
        </div>
      </div>

      <div className='py-5 md:py-10'>
        <DynamicBreadcrumb
          isProductPage={true}
          superCategoryUrl={productDetails.super_category_url}
        />
      </div>

      {/* Similar Products */}
      <section className='relative mx-auto w-full items-center justify-center bg-neutral-150 pb-7 md:pb-14'>
        <SwiperSlider
          CustomTitle={
            <SectionTitle
              cText='people viewed'
              nTColor='text-decorative-pink'
              nText='Similar products '
              className='text-left md:!text-d5'
            />
          }
          data={filterAndSortRentalItems(randomCategory.items)}
          city={city}
        />
      </section>

      <Suspense fallback={<div>Loading...</div>}>
        <ProductReviews category={cat} />
      </Suspense>
    </div>
  )
}

const HowtoRent = () => (
  <div className='flex h-full w-full flex-col items-start gap-2 px-3 md:px-6'>
    <ProductPageTitle heading='How to Rent on SharePal?' />
    <SpImage
      src={
        // 'https://images.sharepal.in/Static+Images/1732430523698-How+to+Rent+Infographic+v2.webp'
        "https://images.sharepal.in/misc/hard-coded/sharepal/how-to-rent-desktop.svg"
      }
      width={1400}
      height={900}
      className='h-full w-full'
      containerClassName='w-full hidden md:block'
    />
    <SpImage
      src={
        "https://images.sharepal.in/misc/hard-coded/sharepal/how-to-rent-mobile.svg"
      }
      width={500}
      height={900}
      className='h-full w-full'
      containerClassName='w-full md:hidden  block'
    />
  </div>
)

const keyBenefits = [
  {
    title: "Top-Quality Products",
    desc: "Always maintained & ready for action",
    image: `${HARD_CODED_IMAGE_URL}/New+Star+Box.webp`,
  },
  {
    title: "Affordable Rates",
    desc: "Save big compared to buying",
    image: `${HARD_CODED_IMAGE_URL}/Offer+Tags.webp`,
  },
  {
    title: "Eco-Friendly",
    desc: "Reduce single-use products",

    image: `${HARD_CODED_IMAGE_URL}/Icon+(3).webp`,
  },
]

const KeyBenefits = () => (
  <div className='flex h-full w-full flex-col items-start gap-2 px-3 md:px-6'>
    <ProductPageTitle heading='Key Benefits of Renting?' />
    <div className='hide-scrollbar flex w-full items-start gap-2 overflow-x-auto py-2 md:gap-4'>
      {keyBenefits.map((item, index) => (
        <KeyBenefitCard
          key={index}
          title={item.title}
          desc={item.desc}
          image={item.image}
        />
      ))}
    </div>
  </div>
)

export default ProductPage
