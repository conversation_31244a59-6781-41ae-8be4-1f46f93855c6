import { PolicyLayout } from "@/components/policy/policy-layout"
import { PolicySection } from "@/components/policy/policy-section"
import termsAndConditionSections from "@/constants/terms-and-conditions.json"

export default function PrivacyPolicyPage() {
  return (
    <PolicyLayout
      title='Rental Terms & Conditions'
      subtitle='Everything explained in plain & simple language'
      sections={termsAndConditionSections}
    >
      <p className='mb-4 text-sm font-medium leading-relaxed text-neutral-700'>
        A customer placing an order on SharePal&apos;s website (www.sharepal.in)
        or through an online order form agrees to abide by the following terms
        and conditions:
      </p>
      {termsAndConditionSections.map((section, index) => (
        <PolicySection
          key={section.id}
          id={section.id}
          title={section.title}
          content={section.content}
          index={index}
          isLast={index === termsAndConditionSections.length - 1}
        />
      ))}
    </PolicyLayout>
  )
}
