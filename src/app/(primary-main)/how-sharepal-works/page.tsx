import { PolicyLayout } from "@/components/policy/policy-layout"
import { PolicySection } from "@/components/policy/policy-section"
import howItWorkSections from "@/constants/how-it-works.json"

export default function PrivacyPolicyPage() {
  return (
    <PolicyLayout
      title='How it Works'
      subtitle="Renting from SharePal is quick & easy. Let's get you started right away."
      sections={howItWorkSections}
    >
      {howItWorkSections.map((section, index) => (
        <PolicySection
          key={section.id}
          id={section.id}
          title={section.title}
          content={section.content}
          index={index}
          isLast={index === howItWorkSections.length - 1}
        />
      ))}
    </PolicyLayout>
  )
}
