"use client"

import React from "react"
import { motion } from "framer-motion"
import { FAQS, Faq } from "@/components/custom/faq"

type Props = {
  formatCategoryWithIdAndTitle: { id: string; title: string }[]
  groupedFaqs: Record<string, Faq[]>
}

const FaqWrapper: React.FC<Props> = ({
  formatCategoryWithIdAndTitle,
  groupedFaqs,
}) => (
  <div className='w-full'>
    {formatCategoryWithIdAndTitle.map((category, index) => (
      <motion.div
        key={category.id}
        id={category.id}
        className='mb-8'
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        viewport={{ once: true, amount: 0.2 }}
      >
        <h3 className='mb-4 text-lg font-bold md:text-xl'>
          {" "}
          {index + 1}. {category.title}
        </h3>
        {groupedFaqs[category.id] && <FAQS faqs={groupedFaqs[category.id]} />}
      </motion.div>
    ))}
  </div>
)

export default FaqWrapper
