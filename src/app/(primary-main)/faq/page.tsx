import { fetchFaqs } from "@/actions/product"
import { Faq } from "@/components/custom/faq"
import { PolicyLayout } from "@/components/policy/policy-layout"
import type { Metadata } from "next"
import FaqWrapper from "./_components/faq-wrapper"

export const metadata: Metadata = {
  title: "Frequently Asked Questions | SharePal",
  description: "Find answers to common questions about SharePal services.",
}

export default async function FAQPage() {
  const faqs: Faq[] = (await fetchFaqs("rent", "", "all")) || [] // fetch only rent faqs
  // const faqs: Faq[] = (await fetchAllFaqs()) || [] // fetch all faqs

  const rentFaqs = faqs
  // const rentFaqs = faqs.filter((faq) => faq.faq_type === 'rent')

  // Group FAQs by category
  const groupedFaqs: Record<string, Faq[]> = rentFaqs.reduce<
    Record<string, Faq[]>
  >(
    (acc, faq) => {
      const category = faq.category?.trim() || "General"
      acc[category] = acc[category] || []
      acc[category].push(faq)
      return acc
    },
    {}, // Initial empty object
  )

  // Format categories with id and title
  const formatCategoryWithIdAndTitle = Object.keys(groupedFaqs).map(
    (category) => ({
      id: category,
      title: category,
    }),
  )

  return (
    <PolicyLayout
      title='Frequently Asked Questions'
      subtitle="We're here to help. Find answers to common questions about SharePal services."
      sections={formatCategoryWithIdAndTitle}
    >
      {/* FAQ Content */}
      <FaqWrapper
        formatCategoryWithIdAndTitle={formatCategoryWithIdAndTitle}
        groupedFaqs={groupedFaqs}
      />
    </PolicyLayout>
  )
}
