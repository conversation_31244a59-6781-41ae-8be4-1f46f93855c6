import CityHomePage from "@/components/pages/city-home-page"
import { DEFAULT_CITY } from "@/constants"
import { generateCityMetadata } from "@/lib/metadata-generators"
import { Metadata, ResolvingMetadata } from "next"

interface ListingPageProps {
  searchParams?: { [key: string]: string | string[] | undefined }
}

export async function generateMetadata(
  parent: ResolvingMetadata,
): Promise<Metadata> {
  return generateCityMetadata({ city: DEFAULT_CITY, parent })
}

export default async function Page({ searchParams }: ListingPageProps) {
  return (
    <CityHomePage
      city={DEFAULT_CITY}
      searchParams={searchParams}
      showSeoContent={true}
    />
  )
}
