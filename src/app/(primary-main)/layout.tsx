import { fetchCities } from "@/actions/city"
import BottomNav from "@/components/layout/bottom-nav"
import Footer from "@/components/layout/footer"
import Header from "@/components/layout/header/primary-header"
import Search from "@/components/modals/search"

export default async function PrimaryLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const cities = await fetchCities()

  return (
    <>
      <Header cities={cities} />
      <Search />
      <main className='min-h-svh'>{children}</main>
      <Footer />
      <BottomNav />
    </>
  )
}
