import { PolicyLayout } from "@/components/policy/policy-layout"
import { PolicySection } from "@/components/policy/policy-section"

const cancellationPolicySections = [
  {
    id: "order-cancellation-before-shipping",
    title: "Order cancellation before shipping",
    content: `If you cancel the order before shipping, there is absolutely no reason to worry. We will refund your advance amount in the form of a credit note which can be used for any future transaction.`,
  },
  {
    id: "order-cancellation-after-shipping",
    title: "Order cancellation after shipping",
    content: `Once your order is shipped, we won't be able to offer a refund in any form. It is because shipping is expensive and, we will have to incur costs for both delivery and return. Also there is a loss of rental for us as the products were reserved for your order.`,
  },
]

export default function PrivacyPolicyPage() {
  return (
    <PolicyLayout
      title='Cancellation Policy'
      subtitle="Change of plans? Don't worry, we understand."
      sections={cancellationPolicySections}
    >
      {cancellationPolicySections.map((section, index) => (
        <PolicySection
          key={section.id}
          id={section.id}
          title={section.title}
          content={section.content}
          index={index}
          isLast={index === cancellationPolicySections.length - 1}
        />
      ))}
    </PolicyLayout>
  )
}
