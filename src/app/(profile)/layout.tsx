"use client"

import { DashboardSidebar } from "@/components/layout/dashboard-sidebar"
import SecondaryFooter from "@/components/layout/footer/secondary-footer"
import { SecondaryHeader } from "@/components/layout/header/secondary-header"
import { Skeleton } from "@/components/ui/skeleton"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useUserStore } from "@/store/user-store"
import { AnimatePresence, motion } from "framer-motion"
import { usePathname, useRouter } from "next/navigation"
import { Suspense, useEffect } from "react"

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user, userLoading } = useUserStore()
  const { openModal } = useOnboardingStore()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (!userLoading && !user) {
      openModal()
      router.push("/")
    }
  }, [user, userLoading, openModal, router])

  return (
    <>
      <Suspense fallback={<Skeleton className='h-56 w-full' />}>
        <SecondaryHeader />
      </Suspense>

      <div className='container flex min-h-screen flex-col gap-4 p-3 py-16 lg:flex-row lg:gap-6 lg:py-24'>
        <DashboardSidebar />
        <AnimatePresence mode='wait'>
          <motion.main
            key={pathname}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 0 }}
            transition={{
              type: "spring",
              stiffness: 260,
              damping: 20,
            }}
            className='min-h-screen flex-1 md:min-h-[calc(100vh-8rem)]'
          >
            {children}
          </motion.main>
        </AnimatePresence>
      </div>

      <SecondaryFooter />
    </>
  )
}
