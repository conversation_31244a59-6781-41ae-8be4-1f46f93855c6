import { RentalItem } from "@/types"

export function filterAndSortRentalItems(
  rentalItems: RentalItem[],
): RentalItem[] {
  if (!Array.isArray(rentalItems) || rentalItems.length === 0) return []

  const itemMap = new Map<string, RentalItem>()
  const outOfStockItems: RentalItem[] = []

  for (const item of rentalItems) {
    if (!item.ri_short_name) continue // Skip invalid items

    if (item.out_of_stock) {
      outOfStockItems.push(item)
    } else {
      // Keep only the first in-stock item per ri_short_name
      if (!itemMap.has(item.ri_short_name)) {
        itemMap.set(item.ri_short_name, item)
      }
    }
  }

  return [...itemMap.values(), ...outOfStockItems]
}

export function filterAndSortRentalItemsInPlace(
  rentalItems: RentalItem[],
): RentalItem[] {
  if (!Array.isArray(rentalItems) || rentalItems.length === 0) return []

  let writeIndex = 0
  const seen = new Set<string>()

  // Move in-stock unique items to the front
  for (let i = 0; i < rentalItems.length; i++) {
    const item = rentalItems[i]

    // if (!item.ri_short_name || seen.has(item.ri_short_name)) continue // Skip invalid & duplicates

    if (!item.out_of_stock) {
      seen.add(item.ri_short_name)
      rentalItems[writeIndex++] = item
    }
  }

  // Append out-of-stock items (without duplicate check)
  for (let i = 0; i < rentalItems.length; i++) {
    const item = rentalItems[i]

    if (item.out_of_stock) {
      rentalItems[writeIndex++] = item
    }
  }

  rentalItems.length = writeIndex // Trim array to valid items

  return rentalItems
}
