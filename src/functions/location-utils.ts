import { loadScript } from "@/functions/loadScript"
import type { City } from "@/types/address"

type CityMappings = {
  [key: string]: string
}

const CITY_MAPPINGS: CityMappings = {
  // Bangalore variations
  bengaluru: "bangalore", // Official alternate name

  // Mumbai variations
  bombay: "mumbai", // Historical official name

  // Delhi variations
  "new delhi": "delhi", // Administrative capital name
  "delhi ncr": "delhi", // Metropolitan region

  // Gurgaon variations
  gurugram: "gurgaon", // Official new name

  // Noida variations
  "greater noida": "noida", // Administrative region

  // Kolkata variations
  calcutta: "kolkata", // Historical official name

  // Transit cities
  "chandigarh ut": "transit via chandigarh", // Union Territory designation
  "dehradun uk": "transit via dehradun", // Uttarakhand designation
}

const loadGoogleMapsScript = async (): Promise<void> => {
  if (typeof window === "undefined") {
    throw new Error("Window is not defined")
  }

  // If already loaded, return immediately
  if (window.google?.maps) {
    return
  }

  await loadScript(
    `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`,
    "google-maps-script",
  )
}

export const detectUserCity = async (cities: City[]): Promise<City | null> => {
  // console.log("detecting city", cities)
  // log cities name

  if (typeof window === "undefined" || !navigator.geolocation) {
    throw new Error("Geolocation is not supported")
  }

  // Load Google Maps script
  await loadGoogleMapsScript()

  // Check if Google Maps API is loaded
  if (typeof google === "undefined" || !google.maps) {
    throw new Error("Google Maps API is not loaded")
  }

  try {
    // Get current position
    const position = await new Promise<GeolocationPosition>(
      (resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject)
      },
    )

    const { latitude, longitude } = position.coords

    // Use Google Maps Geocoding API to get address details
    const geocoder = new google.maps.Geocoder()
    const response = await new Promise<{
      results: google.maps.GeocoderResult[]
    }>((resolve, reject) => {
      geocoder.geocode(
        { location: { lat: latitude, lng: longitude } },
        (
          results: google.maps.GeocoderResult[] | null,
          status: google.maps.GeocoderStatus,
        ) => {
          if (status === "OK" && results?.length) {
            resolve({ results })
          } else {
            reject(new Error(`Geocoding failed with status: ${status}`))
          }
        },
      )
    })

    if (!response.results?.[0]) {
      throw new Error("No results found")
    }

    // Extract city from geocoding result
    const detectedCity = extractCityFromGeocodeResult(response.results[0])
    if (!detectedCity) {
      throw new Error("City not found in geocoding result")
    }

    // Find matching city in our cities list
    const matchedCity = findMatchingCity(detectedCity, cities)
    return matchedCity
  } catch (error) {
    console.error("Error detecting city:", error)
    throw error
  }
}

const extractCityFromGeocodeResult = (
  result: google.maps.GeocoderResult,
): string | null => {
  // First try to find locality
  let cityComponent = result.address_components.find((component) =>
    component.types.includes("locality"),
  )

  // If no locality, try administrative_area_level_2
  if (!cityComponent) {
    cityComponent = result.address_components.find((component) =>
      component.types.includes("administrative_area_level_2"),
    )
  }

  return cityComponent ? cityComponent.long_name.toLowerCase() : null
}

const findMatchingCity = (
  detectedCity: string,
  cities: City[],
): City | null => {
  const normalizedCity = detectedCity.toLowerCase()

  // First check direct match
  let matchedCity = cities.find(
    (city) => city.city_name.toLowerCase() === normalizedCity,
  )

  // If no direct match, check mappings
  if (!matchedCity && normalizedCity in CITY_MAPPINGS) {
    matchedCity = cities.find(
      (city) => city.city_name.toLowerCase() === CITY_MAPPINGS[normalizedCity],
    )
  }

  return matchedCity || null
}

export const handleLocationError = (
  error: GeolocationPositionError,
): string => {
  switch (error.code) {
    case GeolocationPositionError.PERMISSION_DENIED:
      return "Location permission denied. Please enable location access."
    case GeolocationPositionError.POSITION_UNAVAILABLE:
      return "Location information unavailable."
    case GeolocationPositionError.TIMEOUT:
      return "Location request timed out."
    default:
      return "An unknown error occurred."
  }
}

// const getCurrentLocation = useCallback(async () => {
//   if (typeof window === "undefined" || !navigator.geolocation) {
//     toast.info("Enable Location Services", {
//       description: "Please enable location services in your browser settings",
//     })
//     return
//   }

//   try {
//     const matchedCity = await detectUserCity(cities)

//     if (matchedCity) {
//       handleCitySelect(matchedCity)
//       toast.success("Perfect! Location Found", {
//         description: `Welcome to Sharepal ${matchedCity.city_name}`,
//       })
//     } else {
//       toast.info("Coming Soon!", {
//         description:
//           "We're expanding to your city soon. Meanwhile, please select a nearby city",
//       })
//     }
//   } catch (error) {
//     console.error("Error getting current location:", error)

//     if (error instanceof GeolocationPositionError) {
//       toast.info("Location Access Required", {
//         description: handleLocationError(error),
//       })
//     } else {
//       toast.info("Location Not Found", {
//         description: "Please select your city from the list above",
//       })
//     }
//   }
// }, [cities, handleCitySelect])
