import { UrlObject } from "url"
import { formatProductUrl } from "./small-functions"

export function generateLinkPath(
  ri_name: string,
  city: string,
  category: string,
  SubCategory: string,
): UrlObject {
  const cat = formatProductUrl(category)
  const subcat = formatProductUrl(SubCategory)
  const product = formatProductUrl(ri_name)

  return {
    pathname: `/${city}/${cat}/${subcat}/${product}`,
    // pathname: `/${city}/${cat}/${product}`,
  }
}
