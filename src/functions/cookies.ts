// Define cookie options interface
interface CookieOptions {
  expires?: Date | number // Date object or days (number)
  path?: string // Path of the cookie
  domain?: string // Domain of the cookie
  secure?: boolean // Secure flag
  sameSite?: "Strict" | "Lax" | "None" // SameSite attribute
}

/**
 * Get a specific cookie by name.
 * Example:
 * const token = getCookie('userToken') // Returns the value of 'userToken' cookie
 */
export const getCookie = (key: string): string | undefined => {
  const cookies = document?.cookie
    .split("; ")
    .reduce<Record<string, string>>((acc, cookie) => {
      const [name, value] = cookie.split("=")
      acc[decodeURIComponent(name)] = decodeURIComponent(value)
      return acc
    }, {})
  return cookies[key]
}

/**
 * Get all cookies as an object.
 * Example:
 * const allCookies = getCookies() // Returns { userToken: 'abc123', sessionId: 'xyz456' }
 */
export const getCookies = (): Record<string, string> =>
  document.cookie.split("; ").reduce<Record<string, string>>((acc, cookie) => {
    const [name, value] = cookie.split("=")
    acc[decodeURIComponent(name)] = decodeURIComponent(value)
    return acc
  }, {})

/**
 * Set a cookie with a name, value, and optional settings.
 * Example:
 * setCookie('userToken', 'abc123', { expires: 7, path: '/' })
 */
export const setCookie = (
  key: string,
  value: string,
  options: CookieOptions = {},
): void => {
  let cookieString = `${encodeURIComponent(key)}=${encodeURIComponent(value)}`

  if (options.expires) {
    const expires =
      options.expires instanceof Date
        ? options.expires.toUTCString()
        : new Date(
            Date.now() + options.expires * 24 * 60 * 60 * 1000,
          ).toUTCString()
    cookieString += `; expires=${expires}`
  }

  if (options.path) {
    cookieString += `; path=${options.path}`
  }

  if (options.domain) {
    cookieString += `; domain=${options.domain}`
  }

  if (options.secure) {
    cookieString += `; secure`
  }

  if (options.sameSite) {
    cookieString += `; samesite=${options.sameSite}`
  }

  document.cookie = cookieString
}

/**
 * Delete a cookie by name.
 * Example:
 * deleteCookie('userToken', { path: '/' })
 */
export const deleteCookie = (
  key: string,
  options: CookieOptions = {},
): void => {
  setCookie(key, "", { ...options, expires: -1 })
}

/**
 * Check if a cookie exists.
 * Example:
 * const exists = hasCookie('userToken') // Returns true or false
 */
export const hasCookie = (key: string): boolean => getCookie(key) !== undefined
