import { Address } from "@/types/address"

interface OrganizeAddressesOptions {
  addresses: Address[] | undefined
  selectedAddress: number | null
}

export const organizeAddresses = ({
  addresses,
  selectedAddress,
}: OrganizeAddressesOptions) => {
  if (!addresses) return []

  // First sort by ID in descending order (newest first)
  const sortedAddresses = [...addresses].sort((a, b) => b.id - a.id)

  // If there's a selected address, move it to the top only if it's not in first two positions
  if (selectedAddress) {
    const selectedIndex = sortedAddresses.findIndex(
      (addr) => addr.id === selectedAddress,
    )
    if (selectedIndex > 1) {
      // Only move if index is greater than 1 (not in first two)
      const [selected] = sortedAddresses.splice(selectedIndex, 1)
      sortedAddresses.unshift(selected)
    }
  }

  return sortedAddresses
}

// Helper function to generate full address string
export const generateFullAddress = (address: Address): string => {
  const parts = [
    address.house_details,
    address.road,
    address.landmark,
    address.area_details,
    address.city,
    address.state,
    address.pincode.toString(),
  ]
  return parts.filter(Boolean).join(", ")
}

// Helper function to generate user label
export const generateUserLabel = (
  firstName: string | undefined,
  shipmentNumber: string | undefined,
  callingNumber: string | undefined,
): string => `${firstName || ""} | ${shipmentNumber || callingNumber || ""}`
