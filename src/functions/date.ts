import { getCookie, setCookie } from "./cookies"

/**
 * Function to get date data from localStorage or cookies.
 */
export const getDateData = (): Record<string, unknown> | null => {
  if (typeof window === "undefined") return null

  try {
    if (typeof window == "undefined") return null
    const localData = localStorage?.getItem("dateData")
    if (localData) return JSON.parse(localData)

    const cookieData = getCookie("dateData")
    if (cookieData) return JSON.parse(cookieData)
  } catch (error) {
    console.error("Error parsing dateData", error)
  }
  return null
}

/**
 * Function to get one day after a given date.
 */
export const getOneDayAfter = (date: string | number | Date): string => {
  const myDate = new Date(date)
  if (!isNaN(myDate.getTime())) {
    return new Date(myDate.getTime() + 86400000).toDateString()
  }
  return ""
}

/**
 * Function to get one day before a given date.
 */
export const getOneDayBefore = (date: string | number | Date): string => {
  const myDate = new Date(date)
  if (!isNaN(myDate.getTime())) {
    return new Date(myDate.getTime() - 86400000).toDateString()
  }
  return ""
}

/**
 * Function to calculate the transit date after a minimum threshold of hours.
 */
export const calculateTransitDate = (minTh: number): Date => {
  const now = new Date()
  now.setTime(now.getTime() + minTh * 3600000) // Add threshold hours in milliseconds

  if (now.getHours() >= 24) {
    now.setDate(now.getDate() + 1)
    now.setHours(0, 0, 0, 0) // Reset to midnight
  }

  return now
}

/**
 * Function to update local date data in cookies and localStorage.
 */
export const updateLocalDateData = (
  newDateData: Record<string, unknown>,
  dispatch: (action: {
    type: string
    payload: Record<string, unknown>
  }) => void,
): void => {
  const serializedData = JSON.stringify(newDateData)
  setCookie("dateData", serializedData, {
    expires: 60 * 60 * 24 * 365,
  }) // 1 year max-age
  localStorage?.setItem("dateData", serializedData)
  dispatch({ type: "dateUpdate", payload: newDateData })
}

/**
 * Function to convert Unix timestamp to a human-readable date format.
 */
export const convertTimeIntoDate = (time: number): string => {
  const dateObject = new Date(time)
  return dateObject.toLocaleDateString("en-US", {
    day: "numeric",
    month: "long",
    year: "numeric",
  })
}

/**
 * Function to format a date into a specific string format (e.g., DD-MMM-YYYY).
 */
export const formattedDate = (date: string | number | Date): string =>
  new Date(date)
    .toLocaleDateString("en-GB", {
      day: "numeric",
      month: "short",
      year: "numeric",
    })
    .replace(/ /g, "-")

/**
 * Function to calculate the difference in days between two dates.
 */
export const getDateDifference = (
  delivery_date: string | Date,
  pickup_date: string | Date,
): number | null => {
  const start = new Date(delivery_date)
  const end = new Date(pickup_date)

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return null
  }

  return Math.floor((end.getTime() - start.getTime()) / 86400000) // Difference in days
}
