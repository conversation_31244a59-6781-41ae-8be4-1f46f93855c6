type RentCalculationParams = {
  total_days: number
  base_rent: number
  pd_rent: number
  pt_rent: number
  n_factor: number
  surge_factor?: number
  city_surge?: number
  same_day_surge?: number
  product_surge?: number
  partner_surge?: number
  remove_surge: boolean
  partner_discount?: number
  discount?: number
}

export const calculateRent = ({
  total_days,
  base_rent,
  pd_rent,
  pt_rent,
  n_factor,
  surge_factor = 1,
  city_surge = 1,
  same_day_surge = 1,
  product_surge = 1,
  partner_surge = 1,
  remove_surge,
  partner_discount = 0,
  discount = 0,
}: RentCalculationParams): { rent: number; surge: number } => {
  if (!total_days) return { rent: 0, surge: 1 }

  const num_days = Math.min(total_days, 15)
  const final_rent =
    base_rent +
    pd_rent * num_days +
    pt_rent +
    Math.max(total_days - 15, 0) * n_factor -
    discount -
    partner_discount

  let main_surge =
    surge_factor * city_surge * same_day_surge * product_surge * partner_surge

  if (main_surge < 0.75) main_surge = 0.75
  if (main_surge > 1.25) main_surge = 1.25
  if (remove_surge) main_surge = 1

  return { rent: roundValue(final_rent * main_surge), surge: main_surge }
}

//functoin to round values either floor or ceil or round , so that we can just change here whether we want to round up or down
export const roundValue = (value: number) => Math.round(value)

// export const calculateRent = (
//   totalDays: number,
//   baseRent: number,
//   pdRent: number,
//   ptRent: number,
//   nFactor: number,
//   surge_factor: number = 1,
//   city_surge: number = 1,
//   same_day_surge: number = 1,
//   prodcut_surge: number = 1,
//   partner_surge: number = 1,
//   remove_surge: boolean,
//   partner_discount: number = 0,
//   discount: number = 0,

//   // ): number | undefined => {
// ): number => {
//   // if (!totalDays) {
//   //   return undefined;
//   // }
//   if (!totalDays) {
//     return 0
//   }

//   // const { delivery_date } = useRentalStore()

//   const num_days = Math.min(totalDays, 15)
//   const finalRent =
//     baseRent +
//     pdRent * num_days +
//     ptRent +
//     Math.max(totalDays - 15, 0) * nFactor -
//     discount -
//     partner_discount

//   // if (delivery_date != new Date()) same_day_surge = 1

//   let mainSurge =
//     surge_factor * city_surge * same_day_surge * prodcut_surge * partner_surge

//   if (mainSurge < 0.75) {
//     mainSurge = 0.75
//   }
//   if (mainSurge > 1.25) {
//     mainSurge = 1.25
//   }

//   if (remove_surge) {
//     mainSurge = 1
//   }

//   return roundValue(finalRent * mainSurge)
// }
