import { roundValue } from "./business-logics"

export const capitalizeFirstLetter = (str: string): string =>
  str ? str.charAt(0).toUpperCase() + str.slice(1) : ""

export const getImage = (url: string, imgNumber: number = 0): string | "" =>
  url ? url.split(",")[imgNumber].trim() || "" : ""

export const splitString = (str: string, delimiter: string = ","): string[] =>
  str ? str.split(delimiter) : []

export const getRandomColor = (): string => {
  const colors = [
    "bg-yellow-50",
    "bg-red-50",
    "bg-blue-50",
    "bg-green-50",
    "bg-purple-50",
    "bg-pink-50",
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

// functio to formate the url name by removing also '-on-rent '-' and  replacing with ' '
export const formatUrlName = (url: string): string =>
  url.replace(/-on-rent/g, "").replace(/-/g, " ")

export const formatProductUrl = (pName: string): string =>
  pName ? pName.split(" ").join("-").replaceAll("/", "%2F").toLowerCase() : ""

export const formatUrl = (url: string): string =>
  capitalizeFirstLetter(url?.split("-").join(" ").replaceAll("%2F", "/"))

export const removeOnRent = (url: string): string =>
  url?.replace("-on-rent", "") || ""

export const addOnRent = (url: string): string =>
  url?.endsWith("-on-rent") ? url : url + "-on-rent"

// Function to get the total quantity of items in a cart
export function getCartQuantity(fullCart: { quantity: number }[] = []): number {
  return fullCart.reduce((sum, item) => sum + (item?.quantity || 0), 0)
}

// Formatter for currency in INR
export const moneyFormatter = (amount: number): string => {
  const formatter = new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: 0,
  })
  return formatter.format(roundValue(amount || 0))
}
