import { getDateDifference } from "@/functions/date"

type CouponBase = {
  days_to_deliver: number
  days_to_deliver_active: boolean
  coupon_active: boolean
  show_on_website: boolean
  cart_active: boolean
  cart_minimum_value: number
}

type CouponValidityParams = CouponBase & {
  delivery_date: Date
  total_rent: number
  delivery_discount: boolean
}

export const check_coupon_validity = ({
  delivery_date,
  total_rent,
  delivery_discount,
  days_to_deliver,
  days_to_deliver_active,
  coupon_active,
  show_on_website,
  cart_active,
  cart_minimum_value,
}: CouponValidityParams): boolean => {
  const days_since_start = getDateDifference(new Date(), delivery_date)

  if (days_since_start === undefined || days_since_start === null) return false

  const is_delivery_coupon_valid =
    days_to_deliver_active &&
    coupon_active &&
    show_on_website &&
    days_since_start > days_to_deliver &&
    total_rent > cart_minimum_value

  const is_cart_coupon_valid = cart_active && total_rent > cart_minimum_value

  const is_delivery_discount_valid =
    delivery_discount && total_rent > cart_minimum_value

  return (
    is_delivery_coupon_valid ||
    is_cart_coupon_valid ||
    is_delivery_discount_valid
  )
}

// export const applyCoupon = async (
//   applied_coupon: string,
//   delivery_date: Date,
//   total_rent: number,
//   partner_discount?: number,
//   coupon_type?: string,
// ) => {
//   try {
//     const verified = await verifyCoupon(applied_coupon)
//     if (!verified) throw new Error('Coupon not Verified')

//     const {
//       days_to_deliver,
//       days_to_deliver_active,
//       coupon_active,
//       show_on_website,
//       cart_active,
//       cart_minimum_value,
//       delivery_discount,
//       cart_discount,
//       cart_max_discount,
//     } = verified

//     const couponIsValid = check_coupon_validity({
//       delivery_date,
//       total_rent,
//       delivery_discount,
//       days_to_deliver,
//       days_to_deliver_active,
//       coupon_active,
//       show_on_website,
//       cart_active,
//       cart_minimum_value,
//     })

//     if (!couponIsValid) throw new Error('Coupon is not Valid')

//     if (delivery_discount) {
//       // return delivery_discount
//       //do delivery discount
//     } else {
//       const discount = Math.min(
//         (total_rent - (partner_discount ?? 0)) * cart_discount,
//         cart_max_discount,
//       )

//       // return
//     }
//   } catch (error) {}
// }
