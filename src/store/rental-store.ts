import { trackCalendarDateSelect } from "@/lib/gtag-event"
import { Partner, ProductSurge } from "@/types"
import { City } from "@/types/address"
import {
  addDays,
  differenceInDays,
  isAfter,
  isBefore,
  isValid,
  startOfDay,
} from "date-fns"
import { create } from "zustand"
import { persist } from "zustand/middleware"

interface RentalState {
  // Dates
  delivery_date: Date | null
  pickup_date: Date | null
  total_days: number

  isCartOpen: boolean
  // surge
  city_surge: number
  surge_factor: number
  same_day_surge: number
  same_day_surge_loading: boolean

  min_th: number

  // city
  selectedCity: City
  // cities: City[]
  partner: Partner

  surgeProducts: ProductSurge[]
  surgeProductMap: Record<string, number>
  cityMinthMap: Record<string, number>

  setDeliveryDate: (date: Date | null) => void
  setPickupDate: (date: Date | null) => void
  reset: () => void
  isDateValid: (date: Date | null) => boolean
  isDeliveryDateValid: (date: Date) => boolean
  isPickupDateValid: (date: Date) => boolean

  setCartOpen: (isOpen: boolean) => void

  setSelectedCity: (selectedCity: City) => void
  // setCities: (cities: City[]) => void

  setCitySurge: (city_surge: number) => void
  setSurgeFactor: (surge_factor: number) => void
  setMinTh: (min_th: number) => void
  setSameDaySurge: (same_day_surge: number) => void
  setSameDaySurgeLoading: (same_day_surge_loading: boolean) => void
  setSurgeProducts: (surgeProducts: ProductSurge[]) => void

  setPartner: (partner: Partner) => void
  setSurgeProductMap: (surge_product_map: Record<string, number>) => void
  setCityMinthMap: (data: Record<string, number>) => void
}

const calculateTotalDays = (start: Date | null, end: Date | null): number => {
  if (!start || !end || !isValid(start) || !isValid(end)) return 0
  return Math.max(0, differenceInDays(end, start) - 1)
}

export const useRentalStore = create<RentalState>()(
  persist(
    (set, get) => ({
      delivery_date: null,
      pickup_date: null,
      total_days: 0,
      cart: [],
      isCartOpen: false,
      selectedCity: {} as City,
      // cities: [],
      partner: {} as Partner,

      city_surge: 1,
      surge_factor: 1,
      same_day_surge: 1,
      same_day_surge_loading: false,

      min_th: 0,
      surgeProducts: [],
      surgeProductMap: {},
      cityMinthMap: {},
      setSurgeProductMap: (surge_product_map) => {
        set({ surgeProductMap: surge_product_map })
      },
      setCityMinthMap: (data) => {
        set({ cityMinthMap: data })
      },
      setSurgeProducts: (surgeProducts) => {
        set({ surgeProducts })
      },

      setPartner: (partner) => set({ partner }),
      setCitySurge: (city_surge) => set({ city_surge }),
      setSameDaySurge: (same_day_surge) => set({ same_day_surge }),
      setSameDaySurgeLoading: (same_day_surge_loading) => {
        set({ same_day_surge_loading })
      },
      setSurgeFactor: (surge_factor) => set({ surge_factor }),
      setMinTh: (min_th) => set({ min_th }),

      setSelectedCity: (selectedCity: City) => set({ selectedCity }),
      // setCities: (cities: City[]) => set({ cities }),

      setCartOpen: (isOpen) => set({ isCartOpen: isOpen }),

      setDeliveryDate: (date) => {
        if (date && isValid(date)) {
          const normalizedDate = startOfDay(date)
          set({ delivery_date: normalizedDate })
          const currentPickupDate = get().pickup_date
          if (
            currentPickupDate &&
            isBefore(currentPickupDate, addDays(normalizedDate, 2))
          ) {
            set({ pickup_date: addDays(normalizedDate, 2) })
          }

          set((state) => ({
            total_days: calculateTotalDays(
              new Date(normalizedDate),
              new Date(state.pickup_date || new Date()),
            ),
          }))
          trackCalendarDateSelect()
        } else {
          set({ delivery_date: null, total_days: 0 })
        }
      },

      setPickupDate: (date) => {
        if (date && isValid(date)) {
          const normalizedDate = startOfDay(date)
          set({ pickup_date: normalizedDate })
          const currentDeliveryDate = get().delivery_date
          if (
            currentDeliveryDate &&
            isAfter(currentDeliveryDate, addDays(normalizedDate, -2))
          ) {
            set({ delivery_date: addDays(normalizedDate, -2) })
          }

          set((state) => ({
            total_days: calculateTotalDays(
              new Date(state.delivery_date || new Date()),
              new Date(normalizedDate),
            ),
          }))
          trackCalendarDateSelect()
        } else {
          set({ pickup_date: null, total_days: 0 })
        }
      },

      reset: () =>
        set({ delivery_date: null, pickup_date: null, total_days: 0 }),

      isDateValid: (date) => {
        if (!date || !isValid(date)) return false
        const today = startOfDay(new Date())
        return !isBefore(date, today)
      },

      isDeliveryDateValid: (date) => {
        const { isDateValid } = get()
        return isDateValid(date)
      },

      isPickupDateValid: (date) => {
        const { delivery_date, isDateValid } = get()
        if (!isDateValid(date)) return false
        if (!delivery_date) return true
        return isAfter(date, delivery_date)
      },
    }),
    {
      name: "rental-storage",
      version: 1,
    },
  ),
)

interface calendarStore {
  isOpen: boolean
  openCalendar: () => void
  closeCalendar: () => void
}

export const useCalendarStore = create<calendarStore>((set) => ({
  isOpen: false,
  openCalendar: () => set({ isOpen: true }),
  closeCalendar: () => set({ isOpen: false }),
}))
