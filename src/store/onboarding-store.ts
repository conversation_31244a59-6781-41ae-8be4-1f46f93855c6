import { create } from "zustand"
import { devtools } from "zustand/middleware"

type OnboardingStep = "phone" | "otp" | "profile"

interface OnboardingState {
  isOpen: boolean
  currentStep: OnboardingStep
  phoneNumber: string
  openModal: (isNonClosable?: boolean) => void
  isOtpRequested: boolean
  isNonClosable: boolean

  closeModal: (isNonClosable?: boolean) => void
  setStep: (step: OnboardingStep) => void
  setPhoneNumber: (phone: string) => void
  setIsNonCloseable: (isNonClosable: boolean) => void
}

export const useOnboardingStore = create<OnboardingState>()(
  devtools((set, get) => ({
    isOpen: false,
    currentStep: "phone",
    phoneNumber: "",
    isNonClosable: false,
    isOtpRequested: false,
    openModal: (isNonClosable = false) => set({ isOpen: true, isNonClosable }),
    closeModal: () => {
      if (!get().isNonClosable)
        set({
          isOpen: false,
          currentStep: "phone",
          phoneNumber: "",
        })
    },
    setIsNonCloseable: (isNonClosable) => set({ isNonClosable }),
    setStep: (step) => set({ currentStep: step }),
    setPhoneNumber: (phone) => set({ phoneNumber: phone }),
  })),
)
