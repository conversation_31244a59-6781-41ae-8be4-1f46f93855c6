import {
  ChecklistItem,
  ChecklistOption,
  ReturnOrderState,
} from "@/types/return-order"
import { create } from "zustand"
import { devtools } from "zustand/middleware"

// Helper functions
// const getDefaultSelections = (
//   items: ChecklistItem[],
// ): Record<string, ChecklistOption> =>
//   Object.fromEntries(items.map((item) => [item.product_id, null]))

const calculateBuyLostTotal = (
  products: ChecklistItem[],
  selections: Record<string, ChecklistOption>,
): number =>
  products.reduce(
    (sum, item) =>
      selections[item.product_id] === "buy_lost"
        ? sum + (item.replacement_cost ? item.replacement_cost : 0)
        : sum,
    0,
  )

const countSelections = (
  selections: Record<string, ChecklistOption>,
  option: ChecklistOption,
): number => Object.values(selections).filter((val) => val === option).length

// Create the store
export const useReturnOrderStore = create<ReturnOrderState>()(
  devtools((set, get) => ({
    activeStep: "checklist",
    showReviewModal: false,
    orderId: null,
    products: [],
    packaging: [],

    // Flattened checklist data fields
    productSelections: {},
    packagingSelections: {},
    totalCharges: 0,
    buyLostItemsCount: 0,
    buyLostTotal: 0,
    delayRentalCharges: 0,
    totalDelayDays: 0,
    tsaPresent: false,
    timeSlotCharges: 0,
    previousPayment: 0,

    // Packaging
    tsaLockApplied: false,
    zipLockApplied: false,
    images: [],

    // Payment
    paymentOption: "without_payment",

    pickupData: null,
    packingInstructions: null,
    isSubmitting: false,
    isChecklistCompleted: false,
    isPackingCompleted: false,
    isPickupCompleted: false,

    // Methods
    setOrderId: (id) => set({ orderId: id }),

    setActiveStep: (step) => {
      set({
        activeStep: step,
        isChecklistCompleted: step === "checklist",
        isPackingCompleted: step === "packing",
        isPickupCompleted: step === "pickup",
      })
    },

    toggleReviewModal: (show) => set({ showReviewModal: show }),

    goToNextStep: () => {
      const { activeStep } = get()
      if (activeStep === "checklist") {
        set({ activeStep: "packing", isChecklistCompleted: true })
      } else if (activeStep === "packing") {
        set({ activeStep: "pickup", isPackingCompleted: true })
      }
    },

    goToPrevStep: () => {
      const { activeStep } = get()
      if (activeStep === "pickup") {
        set({ activeStep: "packing" })
      } else if (activeStep === "packing") {
        set({ activeStep: "checklist" })
      }
    },

    setProducts: (products) => {
      set({ products })
      // const productSelections = getDefaultSelections(products)
      // set({ productSelections })
      get().calculateTotals()
    },

    setPackaging: (packaging) => {
      set({ packaging })
      // const packagingSelections = getDefaultSelections(packaging)
      // set({ packagingSelections })
    },

    updateProductSelection: (id, option) => {
      set((state) => ({
        productSelections: {
          ...state.productSelections,
          [id]: option,
        },
      }))
      get().calculateTotals()
    },

    updatePackagingSelection: (id, option) => {
      set((state) => ({
        packagingSelections: {
          ...state.packagingSelections,
          [id]: option,
        },
      }))
      get().calculateTotals()
    },

    setPickupData: (data) => set({ pickupData: data }),

    setPackingInstructions: (instructions) =>
      set({ packingInstructions: instructions }),

    setIsSubmitting: (isSubmitting) => set({ isSubmitting }),
    setPreviousPayment: (previousPayment) => set({ previousPayment }),
    setTsaPresent: (tsaPresent) => set({ tsaPresent }),

    resetStore: () =>
      set({
        activeStep: "checklist",
        showReviewModal: false,
        productSelections: {},
        packagingSelections: {},
        totalCharges: 0,
        buyLostItemsCount: 0,
        buyLostTotal: 0,
        tsaLockApplied: false,
        zipLockApplied: false,
        images: [],
        paymentOption: "without_payment",
        pickupData: null,
        isSubmitting: false,
        isChecklistCompleted: false,
        isPackingCompleted: false,
        isPickupCompleted: false,
        delayRentalCharges: 0,
        totalDelayDays: 0,
        timeSlotCharges: 0,
      }),

    calculateTotals: () => {
      const {
        products,
        productSelections,
        packagingSelections,
        delayRentalCharges,
        timeSlotCharges,
        packaging,
        previousPayment,
      } = get()

      const buyLostTotalProducts = calculateBuyLostTotal(
        products,
        productSelections,
      )
      const buyLostTotalPackaging = calculateBuyLostTotal(
        packaging,
        packagingSelections,
      )
      // const buyLostItemsCount = countSelections(productSelections, "buy_lost")
      const buyLostProductItemsCount = countSelections(
        productSelections,
        "buy_lost",
      )
      const buyLostPackingItemsCount = countSelections(
        packagingSelections,
        "buy_lost",
      )

      set({
        buyLostTotal: buyLostTotalProducts + buyLostTotalPackaging,
        buyLostItemsCount: buyLostProductItemsCount + buyLostPackingItemsCount,
        totalCharges: Math.max(
          buyLostTotalProducts +
            buyLostTotalPackaging +
            delayRentalCharges +
            timeSlotCharges -
            previousPayment,
          0,
        ),
      })
    },

    //packaging
    setImages: (images) => set({ images }),
    addImage: (image) =>
      set((state) => ({
        images: [...state.images, image],
      })),
    removeImage: (image) =>
      set((state) => ({
        images: state.images.filter((img) => img !== image),
      })),
    clearImages: () => set({ images: [] }),
    setDelayRentalCharges: (amount) => set({ delayRentalCharges: amount }),
    seTotalDelayDays: (days) => set({ totalDelayDays: days }),
    setTimeSlotCharges: (amount) => set({ timeSlotCharges: amount }),
    setTsaLockApplied: (applied) => set({ tsaLockApplied: applied }),
    setZipLockApplied: (applied) => set({ zipLockApplied: applied }),
    setPaymentOption: (option) => set({ paymentOption: option }),
  })),
)
