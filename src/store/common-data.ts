import { create } from "zustand"
import { devtools } from "zustand/middleware"

interface FaqItem {
  type: string
  question: string
  answer: string
}

export interface SeoItem {
  id: number
  city: string
  desc: string
}

interface CommonDataState {
  seo: SeoItem
  faq: Array<FaqItem>
  setSeo: (seo: SeoItem) => void
  setFaq: (faq: Array<FaqItem>) => void
}

const useCommonDataStore = create<CommonDataState>()(
  devtools(
    (set) => ({
      seo: {
        id: 0,
        city: "",
        desc: "",
      },
      faq: [],
      setSeo: (seo) => set({ seo }, false, "setSeo"),
      setFaq: (faq) => set({ faq }, false, "setFaq"),
    }),
    {
      name: "CommonDataStore", // Name the store for better identification in devtools
    },
  ),
)

export default useCommonDataStore
