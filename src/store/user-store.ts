import { getLoggedInUser, getUserWallet } from "@/actions/user"
import { setCookie } from "@/functions/cookies"
import generateUniqueId from "@/functions/generate-uuid"
import { trackOTPVerification } from "@/lib/gtag-event"
import { User, Wallet } from "@/types/user"
import { USER_VERIFICATION } from "@/types/verification"
import { create } from "zustand"
import { devtools } from "zustand/middleware"

interface UserStore {
  user: User | null
  wallet: Wallet | null
  userLoading: boolean
  userError: string | null
  isLoggedIn: boolean
  userVerification: USER_VERIFICATION | null
  fetchUser: (city: string) => Promise<void>
  clearUser: () => void
  setUser: (user: User) => void
  setIsLoggedIn: (isLoggedIn: boolean) => void
  setWallet: (wallet: Wallet) => void
  updateWishlist: (newWishList: number[]) => void // New action to update wishlist

  setUserVerification: (verification: USER_VERIFICATION) => void

  logout: () => void
}

export const useUserStore = create<UserStore>()(
  devtools((set) => ({
    user: null,
    wallet: null,
    userLoading: true,
    userError: null,
    isLoggedIn: false,
    userVerification: null,

    // Fetching user and wallet data together
    fetchUser: async (city: string) => {
      set({ userLoading: true, userError: null })
      try {
        const user = await getLoggedInUser(city)
        if (user) {
          set({ user })
          if (user?.whatsapp_number && user?.whatsapp_number !== "") {
            trackOTPVerification("user_login_with_whatsapp", user)
          } else {
            trackOTPVerification("user_login_without_whatsapp", user)
          }
          set({ isLoggedIn: true })
          const wallet = await getUserWallet() // Fetch wallet once user is available
          set({ wallet })
        } else {
          set({ isLoggedIn: false })
        }
        return user
      } catch (error) {
        set({ userError: (error as Error).message, userLoading: false })
      } finally {
        set({ userLoading: false })
      }
    },

    setUser: (user) => set({ user }),
    setIsLoggedIn: (isLoggedIn) => set({ isLoggedIn }),
    setWallet: (wallet) => set({ wallet }),
    clearUser: () => set({ user: null, wallet: null, isLoggedIn: false }),
    // New action to update wishlist
    updateWishlist: (newWishList) =>
      set((state) => ({
        user: state.user
          ? { ...state.user, favourite_items: newWishList }
          : null,
      })),

    setUserVerification: (verification) =>
      set({ userVerification: verification }),

    // Implementing logout function
    logout: () => {
      localStorage.removeItem("token")
      setCookie("uid", generateUniqueId(), { expires: 365 })
      set({ user: null, wallet: null, isLoggedIn: false })
    },
  })),
)
