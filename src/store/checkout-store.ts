import { roundValue } from "@/functions/business-logics"
import { CartItem, OrderData } from "@/types"
import { Address } from "@/types/address"
import type {
  CheckoutSection,
  ContactDetails,
  DeliveryCharges,
  PaymentOption,
} from "@/types/checkout"
import { create } from "zustand"
import { devtools } from "zustand/middleware"

// Utility Functions
export const calculateTotalRent = (
  cartItems: CartItem[],
  same_day_surge: number,
): number =>
  cartItems.reduce(
    (total, item) =>
      total +
      roundValue(
        item.rent *
          item.surge_factor *
          item.city_surge *
          item.quantity *
          same_day_surge,
      ),
    0,
  )
const addCartProductsLS = (cart_items: CartItem[]) => {
  if (typeof window == "undefined") return
  localStorage.setItem(
    "cartProducts",
    JSON.stringify(cart_items.map((data) => data.product_code)),
  )
}

export const calculateTotalDeposit = (cartItems: CartItem[]): number =>
  cartItems.reduce((total, item) => total + item.deposit, 0)

export const getTotalItemsCount = (cartItems: CartItem[]): number =>
  cartItems.reduce((total, item) => total + item.quantity, 0)

export const calculateTotalGoodsValue = (cartItems: CartItem[]): number =>
  cartItems.reduce((total, item) => total + item.goods_value * item.quantity, 0)

export const calculateDeliveryCharges = (
  totalRent: number,
  cart_count: number,
): DeliveryCharges =>
  cart_count > 0
    ? {
        original: 299,
        final: totalRent < 1000 ? 299 : 0,
      }
    : {
        original: 0,
        final: 0,
      }

export const calculateCarepalCoverage = (totalGoodsValue: number): number => {
  const coverage = Math.round(totalGoodsValue * 0.5)
  // Cap the coverage at 25000
  return Math.min(coverage, 25000)
}

export const calculateCarepalFee = (totalGoodsValue: number): number => {
  // Calculate coverage first (with cap)
  const coverage = calculateCarepalCoverage(totalGoodsValue)
  // Fee is 1% of coverage amount
  return Math.round(coverage * 0.01)
}
// Store Interface
interface CheckoutState {
  // Section navigation
  active_section: CheckoutSection
  seon_fingerprint: string

  // Section completion flags
  is_contact_completed: boolean
  is_delivery_completed: boolean
  is_carepal_completed: boolean
  is_review_completed: boolean

  // Customer and shipping details
  contact_details: ContactDetails | null
  delivery_address: Address | null

  // Payment details
  payment_option: PaymentOption | null
  payment_type: number

  partner_discount: number
  payment_discount: number

  // Cart and charges
  cart_items: CartItem[]
  total_rent: number
  total_deposit: number
  total_goods_value: number
  delivery_charges: DeliveryCharges
  pickup_charges: DeliveryCharges
  carepal_fee: number
  carepal_coverage: number
  handling_charges: number
  items_count: number
  total_discount: number
  applied_coupon_code: string
  coupon_discount: number
  wallet_balance: number
  // total_amount: number
  total_amount: number // Unadjusted total
  // finalAmount: number // Adjusted total after discounts, wallet, etc.
  adjusted_amount: number

  // Additional options
  // discount_amount: number
  wallet_used: boolean
  wallet_balance_used: number
  gst_claimed: boolean
  gst_number: string
  payment_mode: "full" | "partial"
  carepal_selected: boolean

  checkout_redirect_loading: boolean
  orderData: OrderData

  setOrderData: (order_data: OrderData) => void

  // Methods
  finalAmount: () => number
  setSeonFingerprint: (fingerprint: string) => void
  setActiveSection: (section: CheckoutSection) => void
  setStepCompleted: (step: CheckoutSection, completed: boolean) => void
  goToNextSection: () => void
  goToPrevSection: () => void

  setContactDetails: (details: ContactDetails) => void
  setDeliveryAddress: (address: Address) => void

  setPaymentType: (type: number) => void
  setPaymentOption: (paymentOption: PaymentOption) => void
  setHandlingCharges: (charges: number) => void
  setDeliveryCharges: (charges: number) => void
  setCartItems: (cartItems: CartItem[], same_day_surge: number) => void
  updateCartItem: (
    itemId: number,
    same_day_surge: number,
    data: Partial<CartItem>,
  ) => void
  setItemCount: (count: number) => void
  // setDiscountAmount: (amount: number) => void
  setAppliedCouponCode: (code: string, discount: number) => void
  setWalletBalance: (balance: number) => void
  setOrderTotal: (total: number) => void
  setCouponDiscount: (discount: number) => void
  setUseWallet: (use: boolean) => void
  setWalletBalanceUsed: (amount: number) => void
  setUseGST: (use: boolean) => void
  setGSTNumber: (number: string) => void
  setCarepalSelected: (selected: boolean) => void
  setCarepalFee: (fee: number) => void
  setAdjustedAmount: (amount: number) => void
  setPaymentMode: (mode: "full" | "partial") => void
  applyGst: (apply: boolean, gstRate: number) => void

  addToCart: (cart_item: CartItem, same_day_surge: number) => void
  removeFromCart: (cart_item: CartItem, same_day_surge: number) => void
  updateCartItemQuantity: (cart_item: CartItem, same_day_surge: number) => void

  setCheckoutRedirecting: (checkout_redirect_loading: boolean) => void

  getCartCount: () => number
  resetCheckout: () => void
}

// Zustand Store
export const useCheckoutStore = create<CheckoutState>()(
  devtools((set, get) => ({
    // Initial State
    active_section: "contact",
    seon_fingerprint: "",
    is_contact_completed: false,
    is_delivery_completed: false,
    is_carepal_completed: false,
    is_review_completed: false,

    contact_details: null,
    delivery_address: null,

    payment_type: null,
    handling_charges: 0,
    payment_option: null,
    checkout_redirect_loading: false,
    cart_items: [],
    total_rent: 0,
    total_deposit: 0,
    total_goods_value: 0,
    delivery_charges: { original: 0, final: 0 },
    pickup_charges: { original: 299, final: 0 },
    carepal_fee: 0,
    carepal_coverage: 0,

    // New Fields
    wallet_used: false,
    gst_claimed: false,
    gst_amount: 0,
    applied_coupon_code: "",
    // discount_amount: 0,
    total_amount: 0,
    wallet_balance: 0,
    wallet_balance_used: 0,

    // other amounts and sicounts
    partner_discount: 0,
    payment_discount: 0,
    adjusted_amount: 0,
    // Additional Fields
    items_count: 0,
    total_discount: 0,
    coupon_discount: 0,
    carepal_selected: false,

    orderData: {},
    setOrderData: (order_data: OrderData) => set({ orderData: order_data }),

    finalAmount: () => {
      // finalAmount() {
      const {
        total_rent,
        total_deposit,
        delivery_charges,
        wallet_balance_used,
        total_discount,
        partner_discount,
        payment_discount,
        adjusted_amount,
        coupon_discount,
        handling_charges,
        carepal_fee,
        carepal_selected,
      } = get()

      return roundValue(
        total_rent +
          total_deposit +
          handling_charges +
          delivery_charges.final +
          (carepal_selected ? carepal_fee : 0) -
          wallet_balance_used -
          total_discount -
          coupon_discount -
          partner_discount -
          payment_discount +
          adjusted_amount,
      )
    },

    // Methods
    setSeonFingerprint: (fingerprint) => set({ seon_fingerprint: fingerprint }),
    setActiveSection: (section) => set({ active_section: section }),
    setStepCompleted: (step, completed) =>
      set(() => ({
        ...(step === "contact" && { is_contact_completed: completed }),
        ...(step === "delivery" && { is_delivery_completed: completed }),
        ...(step === "carepal" && { is_carepal_completed: completed }),
        ...(step === "review" && { is_review_completed: completed }),
      })),
    goToNextSection: () =>
      set((state) => {
        const current = state.active_section
        if (current === "contact") return { active_section: "delivery" }
        if (current === "delivery") return { active_section: "carepal" }
        if (current === "carepal") return { active_section: "review" }
        return state
      }),
    goToPrevSection: () =>
      set((state) => {
        const current = state.active_section
        if (current === "review") return { active_section: "carepal" }
        if (current === "carepal") return { active_section: "delivery" }
        if (current === "delivery") return { active_section: "contact" }
        return state
      }),

    setContactDetails: (details) => set({ contact_details: details }),
    setDeliveryAddress: (address) => set({ delivery_address: address }),

    setPaymentType: (type) => set({ payment_type: type }),
    setPaymentOption: (paymentOption) => set({ payment_option: paymentOption }),
    setCartItems: (cartItems, same_day_surge) =>
      set(() => {
        const totalRent = calculateTotalRent(cartItems, same_day_surge)
        const totalDeposit = calculateTotalDeposit(cartItems)
        const totalGoodsValue = calculateTotalGoodsValue(cartItems)
        const itemsCount = getTotalItemsCount(cartItems)

        const deliveryCharges = calculateDeliveryCharges(totalRent, itemsCount)
        const carepalFee = calculateCarepalFee(totalGoodsValue)
        const carepalCoverage = calculateCarepalCoverage(totalGoodsValue)
        const totalAmount = totalRent + totalDeposit + deliveryCharges.final
        addCartProductsLS(cartItems)
        return {
          cart_items: cartItems,
          total_rent: totalRent,
          total_deposit: totalDeposit,
          total_goods_value: totalGoodsValue,
          delivery_charges: deliveryCharges,
          carepal_fee: carepalFee,
          carepal_coverage: carepalCoverage,
          items_count: itemsCount,
          total_amount: totalAmount,
        }
      }),

    updateCartItem: (itemId, same_day_surge, data) =>
      set((state) => {
        const updatedItems = state.cart_items.map((item) =>
          item.id === itemId ? { ...item, ...data } : item,
        )
        addCartProductsLS(updatedItems)
        const totalRent = calculateTotalRent(updatedItems, same_day_surge)
        const totalDeposit = calculateTotalDeposit(updatedItems)
        const totalGoodsValue = calculateTotalGoodsValue(updatedItems)
        const itemsCount = getTotalItemsCount(updatedItems)

        const deliveryCharges = calculateDeliveryCharges(totalRent, itemsCount)
        const carepalFee = calculateCarepalFee(totalGoodsValue)
        const carepalCoverage = calculateCarepalCoverage(totalGoodsValue)
        const totalAmount = totalRent + totalDeposit + deliveryCharges.final

        return {
          cart_items: updatedItems,
          total_rent: totalRent,
          total_deposit: totalDeposit,
          total_goods_value: totalGoodsValue,
          delivery_charges: deliveryCharges,
          carepal_fee: carepalFee,
          carepal_coverage: carepalCoverage,
          items_count: itemsCount,
          total_amount: totalAmount,
        }
      }),

    getCartCount: () => get().cart_items.length,

    setItemCount: (count) => set({ items_count: count }),
    // setDiscountAmount: (amount) => set({ discount_amount: amount }),
    setAppliedCouponCode: (code, discount) => {
      set((state) => ({
        applied_coupon_code: code,
        coupon_discount: discount,
        wallet_used: false,
        wallet_balance_used: 0,
        wallet_balance: state.wallet_balance + state.wallet_balance_used,
        total_amount: state.total_amount + state.wallet_balance_used,
      }))
    },
    setWalletBalance: (balance) => set({ wallet_balance: balance }),
    setOrderTotal: (total) => set({ total_amount: total }),
    setCouponDiscount: (discount) => set({ coupon_discount: discount }),

    // user Wallet
    setUseWallet: (use) =>
      set((state) => {
        if (use) {
          //calculation
          const wallet_balance_used = Math.min(
            state.wallet_balance ?? 0,
            Math.min(
              state.total_amount,
              state.total_rent -
                state?.coupon_discount -
                state?.partner_discount -
                state?.payment_discount,
            ),
          )
          return {
            wallet_used: true,
            wallet_balance_used,
            wallet_balance: state.wallet_balance - wallet_balance_used,
            total_amount: state.total_amount - wallet_balance_used,
          }
        } else {
          // reset calculation
          return {
            wallet_used: false,
            wallet_balance_used: 0,
            wallet_balance: state.wallet_balance + state.wallet_balance_used,
            total_amount: state.total_amount + state.wallet_balance_used,
          }
        }
      }),

    addToCart: (cart_item: CartItem, same_day_surge: number) => {
      set(({ cart_items }) => {
        const updatedCartItems = cart_items.concat(cart_item)
        const totalRent = calculateTotalRent(updatedCartItems, same_day_surge)
        const totalDeposit = calculateTotalDeposit(updatedCartItems)
        const totalGoodsValue = calculateTotalGoodsValue(updatedCartItems)
        const itemsCount = getTotalItemsCount(updatedCartItems)

        const deliveryCharges = calculateDeliveryCharges(totalRent, itemsCount)
        const carepalFee = calculateCarepalFee(totalGoodsValue)
        const carepalCoverage = calculateCarepalCoverage(totalGoodsValue)
        addCartProductsLS(updatedCartItems)
        const totalAmount = totalRent + totalDeposit + deliveryCharges.final
        return {
          cart_items: updatedCartItems,
          total_rent: totalRent,
          total_deposit: totalDeposit,
          total_goods_value: totalGoodsValue,
          delivery_charges: deliveryCharges,
          carepal_fee: carepalFee,
          carepal_coverage: carepalCoverage,
          items_count: itemsCount,
          total_amount: totalAmount,
        }
      })
    },
    removeFromCart: (cart_item: CartItem, same_day_surge: number) => {
      set(({ cart_items }) => {
        const updatedCartItems = cart_items.filter(
          (item) => item.id !== cart_item.id,
        )
        addCartProductsLS(updatedCartItems)
        const totalRent = calculateTotalRent(updatedCartItems, same_day_surge)
        const totalDeposit = calculateTotalDeposit(updatedCartItems)
        const totalGoodsValue = calculateTotalGoodsValue(updatedCartItems)
        const itemsCount = getTotalItemsCount(updatedCartItems)

        const deliveryCharges = calculateDeliveryCharges(totalRent, itemsCount)
        const carepalFee = calculateCarepalFee(totalGoodsValue)
        const carepalCoverage = calculateCarepalCoverage(totalGoodsValue)
        const totalAmount = totalRent + totalDeposit + deliveryCharges.final
        return {
          cart_items: updatedCartItems,
          total_rent: totalRent,
          total_deposit: totalDeposit,
          total_goods_value: totalGoodsValue,
          delivery_charges: deliveryCharges,
          carepal_fee: carepalFee,
          carepal_coverage: carepalCoverage,
          items_count: itemsCount,
          total_amount: totalAmount,
        }
      })
    },
    updateCartItemQuantity: (cart_item: CartItem, same_day_surge: number) => {
      set(({ cart_items }) => {
        const updatedCartItems = cart_items
        const index = cart_items.findIndex((item) => item.id === cart_item.id)
        if (index !== -1) {
          cart_items[index] = cart_item
          set({ cart_items })
        }
        addCartProductsLS(updatedCartItems)
        const totalRent = calculateTotalRent(updatedCartItems, same_day_surge)
        const totalDeposit = calculateTotalDeposit(updatedCartItems)
        const totalGoodsValue = calculateTotalGoodsValue(updatedCartItems)
        const itemsCount = getTotalItemsCount(updatedCartItems)
        const deliveryCharges = calculateDeliveryCharges(totalRent, itemsCount)
        const carepalFee = calculateCarepalFee(totalGoodsValue)
        const carepalCoverage = calculateCarepalCoverage(totalGoodsValue)
        const totalAmount = totalRent + totalDeposit + deliveryCharges.final
        return {
          cart_items: updatedCartItems,
          total_rent: totalRent,
          total_deposit: totalDeposit,
          total_goods_value: totalGoodsValue,
          delivery_charges: deliveryCharges,
          carepal_fee: carepalFee,
          carepal_coverage: carepalCoverage,
          items_count: itemsCount,
          total_amount: totalAmount,
          applied_coupon_code: "",
        }
      })
    },

    setCheckoutRedirecting: (checkout_redirect_loading) =>
      set({ checkout_redirect_loading }),

    setUseGST: (use) => set({ gst_claimed: use }),
    setGSTNumber: (number) => set({ gst_number: number }),
    setCarepalSelected: (selected) => set({ carepal_selected: selected }),
    setCarepalFee: (fee) => set({ carepal_fee: fee }),
    setPaymentMode: (mode) => set({ payment_mode: mode }),
    setAdjustedAmount: (amount) => set({ adjusted_amount: amount }),
    setHandlingCharges: (charges) => set({ handling_charges: charges }),
    setDeliveryCharges: (charges) =>
      set({
        delivery_charges: {
          original: 299,
          final: charges,
        },
      }),

    setWalletBalanceUsed: (amount) => set({ wallet_balance_used: amount }),
    applyGst: (apply, gstRate) =>
      set((state) => ({
        gst_claimed: apply,
        gst_amount: apply ? state.total_rent * (gstRate / 100) : 0,
      })),

    resetCheckout: () => {
      set({
        active_section: "contact",
        is_contact_completed: false,
        is_delivery_completed: false,
        is_carepal_completed: false,
        is_review_completed: false,
        contact_details: null,
        delivery_address: null,
        payment_type: undefined,
        payment_option: undefined,
        cart_items: [],
        total_rent: 0,
        total_deposit: 0,
        total_goods_value: 0,
        delivery_charges: { original: 0, final: 0 },
        pickup_charges: { original: 299, final: 0 },
        handling_charges: 0,
        items_count: 0,
        total_discount: 0,
        applied_coupon_code: "",
        coupon_discount: 0,
        wallet_used: false,
        gst_claimed: false,
        // discount_amount: 0,
        total_amount: 0,
        wallet_balance: 0,
        wallet_balance_used: 0,
        partner_discount: 0,
        payment_discount: 0,
        adjusted_amount: 0,
        carepal_selected: false,
        carepal_fee: 0,
        carepal_coverage: 0,
      })
    },
  })),
)
