import { BASE_URL } from "@/constants"

// Helper function to parse error message
const parseErrorMessage = (error: unknown): string => {
  try {
    if (typeof error === "string") {
      // Try to parse if it's a JSON string
      const parsedError = JSON.parse(error)
      return parsedError.message || parsedError.error || error
    }

    if (error && typeof error === "object") {
      // If it's already an object, look for message or error properties
      const errorObj = error as Record<string, unknown>
      return (
        (errorObj.message as string) ||
        (errorObj.error as string) ||
        "An unknown error occurred"
      )
    }

    return String(error)
  } catch {
    // If <PERSON>SO<PERSON> parsing fails, return the original string
    return typeof error === "string" ? error : "An unknown error occurred"
  }
}

export async function fetchWithAuth(url: string, options: RequestInit = {}) {
  // Retrieve the auth token
  const token = localStorage.getItem("token")

  if (!token) {
    return null
  }

  const fullUrl = url.startsWith("https") ? url : `${BASE_URL}${url}`

  try {
    const response = await fetch(fullUrl, {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "X-Internal-App": "Sharepal",
      },
      cache: "no-store",
    })

    if (response.status === 401) {
      localStorage.removeItem("token")
      throw new Error("Token expired. Please login again.")
    }

    if (!response.ok) {
      let errorData: unknown

      // Try to parse error response as JSON
      try {
        errorData = await response.json()
      } catch {
        // If JSON parsing fails, get text response
        errorData = await response.text()
      }

      console.error(`Fetch error: ${response.status} - ${response.statusText}`)
      console.error("Error details:", errorData)

      const errorMessage = parseErrorMessage(errorData)
      throw new Error(errorMessage)
    }

    return await response.json()
  } catch (error) {
    console.error("An error occurred during fetchWithAuth:", error)

    // If it's already an Error instance with a parsed message, throw it directly
    if (error instanceof Error) {
      throw error
    }

    // Otherwise, parse the error
    throw new Error(parseErrorMessage(error))
  }
}

export const fetchWithAuthPost = async <T>(
  url: string,
  body: unknown,
  options: RequestInit = {},
): Promise<T> =>
  fetchWithAuth(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(body),
    ...options,
  }) as Promise<T>

export const fetchWithAuthGet = async <T>(
  url: string,
  options: RequestInit = {},
): Promise<T> =>
  fetchWithAuth(url, {
    method: "GET",
    ...options,
  }) as Promise<T>

export const authHeader = (
  token: string,
  type: string = "application/json",
) => ({
  Authorization: `Bearer ${token}`,

  "Content-Type": type,
})
