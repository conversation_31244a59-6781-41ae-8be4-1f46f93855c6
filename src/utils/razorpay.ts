/* eslint-disable @typescript-eslint/no-explicit-any */

// Define Razorpay response structure
interface RazorpayResponse {
  razorpay_order_id: string
  razorpay_payment_id: string
  razorpay_signature: string
}

// RazorpayOptions type definition for better typings
interface RazorpayOptions {
  key: string
  amount: number
  currency: string
  name: string
  image: string
  description: string
  order_id: string
  handler: (response: RazorpayResponse) => Promise<void>
  prefill: {
    name: string
    email: string
    contact: string
  }
  notes: {
    order_id: string
  }
  theme: {
    color: string
  }
  modal?: {
    ondismiss?: () => void
  }
}

interface User {
  first_name: string
  last_name: string
  email: string
  whatsapp_number: string
}

// Function to load Ra<PERSON>pay script dynamically
const loadRazorpayScript = (): Promise<boolean> =>
  new Promise((resolve) => {
    const script = document.createElement("script")
    script.src = "https://checkout.razorpay.com/v1/checkout.js"
    script.onload = () => resolve(true)
    script.onerror = () => resolve(false)
    document.body.appendChild(script)
  })

// Main function to handle Razorpay payment
export const handleRazorpay = async (
  rzp_order_id: string,
  user: User,
  onSuccess: (response: RazorpayResponse) => void,
  onFailure: (error: any) => void,
  onDismiss: () => void,
): Promise<void> => {
  // Load Razorpay script
  const razorpayLoaded = await loadRazorpayScript()
  if (!razorpayLoaded) {
    alert("Failed to load Razorpay. Please refresh the page and try again.")
    return
  }
  // Razorpay options configuration
  const options: RazorpayOptions = {
    key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID!, // Your Razorpay API key
    amount: 0, // Amount in paise
    currency: "INR", // Currency code
    name: "Sharepal",
    order_id: rzp_order_id, // Razorpay Order ID
    image:
      "https://images.sharepal.in/misc/hard-coded/sharepal/sharepal-logo-new.jpg", // Replace with your logo URL
    prefill: {
      name: `${user.first_name} ${user.last_name}`,
      email: user.email,
      contact: user.whatsapp_number,
    },
    notes: {
      order_id: rzp_order_id,
    },
    theme: {
      color: "#0a59e1",
    },
    handler: async (response: RazorpayResponse) => {
      // console.log("Razorpay response in handler:", response)
      try {
        if (response.razorpay_payment_id) {
          // Handle successful payment
          onSuccess(response)
        } else {
          throw new Error("Payment verification failed.")
        }
      } catch (error) {
        // Handle failure
        onFailure(error)
      }
    },
    modal: {
      ondismiss: onDismiss,
    },
    description: "",
  }

  try {
    const rzp = new (window as any).Razorpay(options)

    // Payment failed event listener
    rzp.on("payment.failed", (response: any) => {
      // Handle failed payment
      console.error("Payment failed:", response.error)
      // alert(`Payment failed: ${response.error.reason}. Please try again.`)
      // toast.error(`Payment failed: ${response.error.reason}. Please try again.`)
      onFailure(response.error)
    })

    // Payment success event listener
    rzp.on("payment.success", (response: RazorpayResponse) => {
      // Handle successful payment
      // console.log('Payment successful:', response)
      onSuccess(response)
    })

    // Open Razorpay payment modal
    rzp.open()
  } catch (error) {
    console.error("Failed to initialize Razorpay:", error)
    alert("An error occurred during the payment process.")
    onFailure(error)
  }
}
