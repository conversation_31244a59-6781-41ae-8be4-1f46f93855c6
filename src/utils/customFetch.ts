import { BASE_URL } from "@/constants"

export async function customFetch(
  url: string,
  options: RequestInit = {},
  // cache: RequestCache = 'force-cache',
) {
  try {
    // Use the default base URL if the passed URL does not start with 'http' or '/'
    const fullUrl = url.startsWith("https") ? url : `${BASE_URL}${url}`

    const response = await fetch(fullUrl, {
      ...options,
      headers: {
        ...options.headers,
        "Content-Type": "application/json", // Default content type
        "X-Internal-App": "Sharepal",
      },
      next: options.next ?? { revalidate: 900 }, // Default revalidation
      cache: options.cache ?? "force-cache", // Avoid caching for fresh data for development
    })

    if (!response.ok) {
      const errorMessage = await response.text() // Read error response for details
      console.error(`Fetch error: ${response.status} - ${response.statusText}`)
      console.error(`Error details: ${errorMessage}`)
      throw new Error(`Failed to fetch: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    console.error("An error occurred during customFetch:", error)
    throw error
  }
}

export const customFetchPost = async <T>(
  url: string,
  body: unknown,
  options: RequestInit = {},
) =>
  (await customFetch(url, {
    method: "POST",
    body: JSON.stringify(body),
    ...options,
  })) as T

export const customFetchGet = async <T>(
  url: string,
  options: RequestInit = {},
) =>
  (await customFetch(url, {
    method: "GET",
    ...options,
  })) as T
