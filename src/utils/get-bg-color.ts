const categoryColors = {
  photography: {
    bg: "bg-category-orange",
    gradient: "bg-orange-category-gradient",
    border: "border-category-orange",
  },
  gaming: {
    bg: "bg-category-purple",
    gradient: "bg-purple-category-gradient",
    border: "border-category-purple",
  },
  outdoor: {
    bg: "bg-category-green",
    gradient: "bg-green-category-gradient",
    border: "border-category-green",
  },
  entertainment: {
    bg: "bg-category-red",
    gradient: "bg-red-category-gradient",
    border: "border-category-red",
  },
  carepal: {
    bg: "bg-carepal-dark",
    gradient: "bg-carepal-gradient",
    border: "border-carepal-dark",
  },
  default: {
    bg: "bg-category-purple",
    gradient: "bg-blue-category-gradient",
    border: "border-neutral-200",
  },
}

// Helper function to find matching category
const getCategoryKey = (category?: string) => {
  if (!category) return "default"
  if (/photography/i.test(category)) return "photography"
  if (/gaming/i.test(category)) return "gaming"
  if (/outdoor/i.test(category)) return "outdoor"
  if (/entertainment/i.test(category)) return "entertainment"
  if (/carepal/i.test(category)) return "carepal"
  return "default"
}

export const getSuperCategoryBgColor = (name?: string) =>
  categoryColors[getCategoryKey(name)].bg

export const getSuperCategoryGradientBgColor = (category?: string) =>
  categoryColors[getCategoryKey(category)].gradient

export const getHeaderBorderColor = (category?: string) =>
  categoryColors[getCategoryKey(category)].border

// export const getHeaderBorderColorBasedOnPage = (pathname: string) => {
//   if (pathname.includes("/carepal")) {
//     return "border-carepal-light"
//   }
//   return "border-neutral-200"
// }
