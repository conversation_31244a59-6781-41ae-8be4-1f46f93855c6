import type { MapState, GoogleMapLocation } from "../types/google-maps"

export class MapManager {
  private state: MapState = {
    map: null,
    marker: null,
    searchBox: null,
  }

  constructor(private mapRef: React.RefObject<HTMLDivElement>) {}

  initMap(center: GoogleMapLocation): void {
    if (!this.mapRef.current || typeof window === "undefined" || !window.google)
      return

    const map = new window.google.maps.Map(this.mapRef.current, {
      center,
      zoom: 15,
      disableDefaultUI: true,
      zoomControl: true,
      streetViewControl: false,
      fullscreenControl: false,
    })

    const searchInput = document.getElementById("pac-input") as HTMLInputElement
    const searchBox = searchInput
      ? new window.google.maps.places.SearchBox(searchInput)
      : null

    this.state = { ...this.state, map, searchBox }
  }

  setMarker(position: GoogleMapLocation): void {
    if (!this.state.map) return

    if (this.state.marker) {
      this.state.marker.setPosition(position)
    } else {
      const marker = new window.google.maps.Marker({
        map: this.state.map,
        position,
        draggable: true,
      })
      this.state = { ...this.state, marker }
    }
  }

  removeMarker(): void {
    if (this.state.marker) {
      this.state.marker.setMap(null)
      this.state = { ...this.state, marker: null }
    }
  }

  panTo(position: GoogleMapLocation): void {
    this.state.map?.panTo(position)
  }

  addMarkerDragListener(
    callback: (position: google.maps.LatLng) => void,
  ): void {
    this.state.marker?.addListener("dragend", () => {
      const position = this.state.marker?.getPosition()
      if (position) callback(position)
    })
  }

  addSearchBoxListener(
    callback: (places: google.maps.places.PlaceResult[]) => void,
  ): void {
    this.state.searchBox?.addListener("places_changed", () => {
      const places = this.state.searchBox?.getPlaces()
      if (places) callback(places)
    })
  }
}
