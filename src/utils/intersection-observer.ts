export interface InviewPortType {
  callback: () => void
  target: HTMLElement
  options: IntersectionObserverInit
  freezeOnceVisible: boolean
}

const checkInViewIntersectionObserver = ({
  target,
  options = { root: null, rootMargin: `0%`, threshold: 0 },
  callback,
  freezeOnceVisible = false,
}: InviewPortType) => {
  const _funCallback: IntersectionObserverCallback = (
    entries: IntersectionObserverEntry[],
    observer: IntersectionObserver,
  ) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        callback()
        if (freezeOnceVisible) {
          observer.unobserve(entry.target)
        }
      }
    })
  }

  // _checkBrowserSupport-----
  if (typeof window.IntersectionObserver === "undefined") {
    console.error(
      "window.IntersectionObserver === undefined! => Your Browser does not support IntersectionObserver",
    )
    return
  }

  const observer = new IntersectionObserver(_funCallback, options)
  if (target) {
    observer.observe(target)
  }
}

export default checkInViewIntersectionObserver
