import { BASE_URL } from "@/constants"

interface CustomFetchOptions extends RequestInit {
  cache?: RequestCache
  revalidate?: number
}

interface FetchErrorOptions {
  message: string
  status?: number
  statusText?: string
  details?: string
}

export class FetchError extends Error {
  status?: number
  statusText?: string
  details?: string

  constructor({ message, status, statusText, details }: FetchErrorOptions) {
    super(message)
    this.status = status
    this.statusText = statusText
    this.details = details
  }
}

export async function customFetch<T>(
  url: string,
  options: CustomFetchOptions = {},
): Promise<T> {
  try {
    // Ensure absolute URL
    const fullUrl = url.startsWith("https") ? url : `${BASE_URL}${url}`

    const response = await fetch(fullUrl, {
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      next: {
        revalidate: options.revalidate ?? 900, // Default: 1-hour revalidation
      },
      cache: options.cache ?? "force-cache", // Default cache behavior
    })

    if (!response.ok) {
      let errorMessage = `Failed to fetch: ${response.statusText}`

      try {
        const errorBody = await response.json()
        errorMessage = errorBody?.message || errorMessage
      } catch {
        // Fallback to response text if JSON parsing fails
        errorMessage = await response.text()
      }

      throw new FetchError({
        message: errorMessage,
        status: response.status,
        statusText: response.statusText,
        details: errorMessage,
      })
    }

    return (await response.json()) as T
  } catch (error) {
    console.error("customFetch error:", error)

    if (error instanceof FetchError) {
      throw error // Re-throw custom FetchError
    }

    throw new FetchError({
      message: "An unexpected error occurred during fetch",
      details: error instanceof Error ? error.message : "Unknown error",
    })
  }
}
