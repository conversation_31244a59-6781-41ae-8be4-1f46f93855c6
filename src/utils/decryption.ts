import { getKeys } from "./encryption"

export const decryptData = async (encryptedData: string) => {
  // Prepare the decryption key
  const { encryption<PERSON>ey, initVector } = await getKeys()

  const cryptoKey = await crypto.subtle.importKey(
    "raw",
    encryptionKey,
    {
      name: "AES-GCM",
      length: 256,
    },
    true,
    ["encrypt", "decrypt"],
  )

  try {
    // Decrypt the encrypted data using the key and IV
    const decodedData = await crypto.subtle.decrypt(
      {
        name: "AES-GCM",
        iv: initVector,
      },
      cryptoKey,
      Buffer.from(encryptedData, "base64"),
    )

    // console.log("Decrypted Data:", decodedData)

    // Decode and return the decrypted data
    return new TextDecoder().decode(decodedData)
  } catch (error) {
    console.error("Decryption failed:", error)
    return JSON.stringify({ payload: null })
  }
}
