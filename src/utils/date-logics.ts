import {
  addDays,
  differenceInDays,
  format,
  isAfter,
  isBefore,
  isToday,
  setHours,
  setMinutes,
  setSeconds,
  startOfDay,
} from "date-fns"

export const formatShortDate = (date: Date) => {
  if (!date) return

  const actualDate = new Date(date)

  const day = actualDate.getDate()
  const suffix = getDaySuffix(day)
  const month = actualDate.toLocaleString("en-US", { month: "short" })

  return `${day}${suffix} ${month}`
}

const getDaySuffix = (day: number) => {
  if (day >= 11 && day <= 13) return "th"
  switch (day % 10) {
    case 1:
      return "st"
    case 2:
      return "nd"
    case 3:
      return "rd"
    default:
      return "th"
  }
}

export const areDatesEqual = (date1: Date, date2: Date) => {
  // console.log(date1)
  // console.log(date2)
  if (date1 && date2) {
    date1 = startOfDay(date1)
    date2 = startOfDay(date2)
    return date1.getTime() === date2.getTime()
  }
}

export const formatDate = (date: Date) =>
  format(date ?? new Date(), "MMM dd, yyyy")

export const formatTransactionDate = (date: Date) =>
  format(date, "MMM dd, yyyy hh:mm a")

//format date in this format 25th Aug
export const formatDateWithOrdinal = (date: Date) =>
  format(date || new Date(), "do MMM")

export const getDay = (date: Date) =>
  new Date(date).toLocaleDateString("en-US", { weekday: "long" })

export const isValidDateRange = (start: Date, end: Date) => {
  const startDay = startOfDay(start)
  const endDay = startOfDay(end)
  return !isBefore(endDay, startDay)
}

export const calculateDaysBetween = (start: Date, end: Date) => {
  const startDay = startOfDay(start)
  const endDay = startOfDay(end)
  const days = Math.round(
    (endDay.getTime() - startDay.getTime()) / (1000 * 60 * 60 * 24),
  )
  return Math.max(0, days)
}

export const adjustDate = (days: number, date: Date | undefined): Date => {
  // Default to today's date if the passed date is undefined or null
  const currentDate = new Date()
  date = date ? new Date(date) : currentDate

  // Prevent selection of dates before today or more than 90 days in the future
  const minDate = currentDate
  const maxDate = addDays(currentDate, 90)

  // If the adjusted date is outside the valid range, return the boundary value (either today or 90 days from now)
  const newDate = new Date(date)
  newDate.setDate(newDate.getDate() + days)

  if (newDate < minDate) {
    return minDate // Ensure the date isn't before today
  }

  if (newDate > maxDate) {
    return maxDate // Ensure the date isn't more than 90 days from today
  }

  return newDate
}

export const updateRentalDates = (
  deliveryDate: Date,
  pickupDate: Date,
  type: "delivery" | "pickup",
  days_type: "add" | "minus",
): {
  newDeliveryDate: Date
  newPickupDate: Date
  newTotalDays: number
} => {
  const today = startOfDay(new Date())
  const maxDate = addDays(today, 90)

  let newDeliveryDate = new Date(deliveryDate)
  let newPickupDate = new Date(pickupDate)

  if (type === "delivery") {
    if (days_type === "add") {
      const potentialNewDeliveryDate = addDays(newDeliveryDate, 1)
      if (differenceInDays(newPickupDate, potentialNewDeliveryDate) >= 2) {
        newDeliveryDate = potentialNewDeliveryDate
      } else {
        newDeliveryDate = potentialNewDeliveryDate
        newPickupDate = addDays(newDeliveryDate, 2)
      }
    } else if (days_type === "minus") {
      const potentialNewDeliveryDate = addDays(newDeliveryDate, -1)
      if (potentialNewDeliveryDate >= today) {
        newDeliveryDate = potentialNewDeliveryDate
        // Ensure pickup date maintains 2-day gap
        if (differenceInDays(newPickupDate, newDeliveryDate) < 2) {
          newPickupDate = addDays(newDeliveryDate, 2)
        }
      }
    }
  }

  if (type === "pickup") {
    if (days_type === "add") {
      const potentialNewPickupDate = addDays(newPickupDate, 1)
      if (potentialNewPickupDate <= maxDate) {
        newPickupDate = potentialNewPickupDate
      }
    } else if (days_type === "minus") {
      const potentialNewPickupDate = addDays(newPickupDate, -1)
      if (differenceInDays(newPickupDate, newDeliveryDate) > 2) {
        newPickupDate = potentialNewPickupDate
      } else if (differenceInDays(newPickupDate, newDeliveryDate) === 2) {
        // If the gap is exactly 2 days, move both dates back by 1 day if possible
        const potentialNewDeliveryDate = addDays(newDeliveryDate, -1)
        if (potentialNewDeliveryDate >= today) {
          newDeliveryDate = potentialNewDeliveryDate
          newPickupDate = potentialNewPickupDate
        }
      }
    }
  }

  // Calculate total rental days (excluding delivery & pickup days)
  const newTotalDays = Math.max(
    differenceInDays(newPickupDate, newDeliveryDate) - 1,
    1,
  )

  return { newDeliveryDate, newPickupDate, newTotalDays }
}

// Determines if a date should be disabled based on various criteria
export const disableDates = (
  date: Date,
  min_th: number = 0,
  type: "delivery" | "pickup" = "delivery",
  deliveryDate?: Date | null,
) => {
  // Calculate the earliest possible date based on min_th
  const transitDate = calculateTransitDate(min_th)

  // Normalize the date to start of day for comparison
  const dateToCheck = startOfDay(new Date(date))

  // Check if the date is before the earliest possible date
  const isBeforeTransitDate = isDateBefore(dateToCheck, transitDate)

  // Maximum allowed date (90 days from today)
  const today = new Date()
  const maxDate = addDays(today, 90)

  // Handle delivery dates
  if (type === "delivery") {
    return isBeforeTransitDate || isAfter(dateToCheck, maxDate)
  }

  // Handle pickup dates
  if (type === "pickup" && deliveryDate) {
    // Pickup must be at least 1 day after delivery
    const minPickupDate = addDays(deliveryDate, 1)

    // Maximum pickup date is 90 days after delivery
    const maxPickupDate = addDays(deliveryDate, 90)

    // Use the later of transit date or day after delivery
    const effectiveMinPickupDate = isAfter(minPickupDate, transitDate)
      ? minPickupDate
      : transitDate

    return (
      dateToCheck < effectiveMinPickupDate ||
      isAfter(dateToCheck, maxPickupDate)
    )
  }

  if (isToday(date)) {
    const eightPM = setSeconds(setMinutes(setHours(new Date(), 20), 0), 0)
    // console.log("todayCheck", eightPM, new Date(), isAfter(new Date(), eightPM))
    return isAfter(new Date(), eightPM)
  }

  // Default case
  return isBeforeTransitDate || isAfter(dateToCheck, maxDate)
}

const isDateBefore = (date: Date, compareDate: Date): boolean =>
  date.getFullYear() < compareDate.getFullYear() ||
  (date.getFullYear() === compareDate.getFullYear() &&
    date.getMonth() < compareDate.getMonth()) ||
  (date.getFullYear() === compareDate.getFullYear() &&
    date.getMonth() === compareDate.getMonth() &&
    date.getDate() < compareDate.getDate())

//
export const calculateTransitDate = (minTh: number): Date => {
  const today = new Date()
  const currentHour = today.getHours()

  // Create a new date object to avoid modifying the original
  const transitDate = new Date(today)

  // If adding minTh hours would push us to the next day, set to start of next day
  if (currentHour + minTh >= 24) {
    transitDate.setDate(transitDate.getDate() + 1) // Move to the next day
    transitDate.setHours(0, 0, 0, 0) // Set the time to 00:00:00
  }

  // Add the threshold hours
  transitDate.setTime(transitDate.getTime() + minTh * 60 * 60 * 1000)

  return transitDate
}

export const isDateBeforeToday = (date: Date | null | undefined): boolean => {
  if (!date) {
    return false
  }

  const today = startOfDay(new Date())
  const normalizedDate = startOfDay(date)

  return normalizedDate.getTime() < today.getTime()
}
