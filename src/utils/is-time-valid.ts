import { isBefore, setHours, startOfHour } from "date-fns"

export const isTimeValid = (
  shipmentDetail: {
    order_stage: string
    pickup_date: number
    delivery_date: number
  },
  selectedValue: string,
): boolean => {
  const currentHour = new Date().getHours()
  const currentTime = Date.now()
  const selectHour = Number.parseInt(selectedValue, 10)
  let isWithinTimeRange = false

  if (isPickup(shipmentDetail)) {
    if (
      isBefore(new Date(currentTime), new Date(shipmentDetail.pickup_date)) &&
      isBefore(new Date(), new Date(shipmentDetail.pickup_date))
    ) {
      isWithinTimeRange = true
    } else {
      isWithinTimeRange = currentHour < 18 && selectHour >= currentHour
    }
  } else {
    isWithinTimeRange = currentHour < 22 && selectHour >= currentHour
  }

  return isWithinTimeRange
}

export const isPickup = (shipmentDetail: { order_stage: string }): boolean =>
  shipmentDetail.order_stage === "Pickup Due"

export const getCombinedTimestamp = (
  dateTimestamp: number,
  timeString: string,
): number => {
  const hour = Number.parseInt(timeString, 10)
  return setHours(startOfHour(new Date(dateTimestamp)), hour).getTime()
}

// export const getExpectedDeliveryDate = (deliveryDate?: number): number => {
//   if (!deliveryDate) return Date.now()
//   const today = Date.now()
//   return isAfter(new Date(deliveryDate), new Date(today)) ? deliveryDate : today
// }

// export const getExpectedPickupDate = (pickup?: number): number => {
//   if (!pickup) return Date.now()
//   const today = Date.now()
//   return isBefore(new Date(today), new Date(pickup)) ? pickup : today
// }

// to get the expected date for delivery and pickup in schedule modal
export const getExpectedDateScheduleDate = (
  date: number,
  type: "delivery" | "pickup",
): number => {
  const today = new Date().getTime()

  if (type === "delivery") {
    // If the date is before today, set it to today
    return date >= today ? date : today
  } else if (type === "pickup") {
    return today <= date ? date : today
  }

  return today // Default: return today if type is not recognized.
}
