function combineDateWithCurrentTime(passedDate: Date) {
  const datePart = new Date(passedDate)
  const now = new Date()

  datePart.setHours(now.getHours())
  datePart.setMinutes(now.getMinutes())
  datePart.setSeconds(now.getSeconds())
  datePart.setMilliseconds(now.getMilliseconds())

  // datePart.setMinutes(0)
  // datePart.setSeconds(0)
  // datePart.setMilliseconds(0)

  return datePart
}

import {
  addHours,
  format,
  isBefore,
  isToday,
  setHours,
  setMinutes,
} from "date-fns"

export function generateTimeSlots(
  isPickup: boolean,
  checkingDate: Date,
  newPickup: boolean = false,
  cost?: number,
) {
  const slots: {
    value: string
    label: string
    disabled: boolean
    cost: number
  }[] = []

  const checkingDateWithCurrentTime = isToday(checkingDate)
    ? combineDateWithCurrentTime(checkingDate)
    : checkingDate
  // Default slots from 9:00 AM to 3:00 PM

  for (let hour = 9; hour <= 14; hour++) {
    const startDate = setMinutes(setHours(checkingDateWithCurrentTime, hour), 0)
    const endDate = addHours(startDate, 1)
    // console.log(startDate, new Date(checkingDateWithCurrentTime))

    slots.push({
      value: format(startDate, "HH"),
      label: `${format(startDate, "hh:00 aa")} - ${format(endDate, "hh:00 aa")}`,
      disabled: isBefore(startDate, new Date(checkingDateWithCurrentTime)),
      cost: 0,
    })
  }

  if (isPickup) {
    // Add 3:00 PM - 4:00 PM slot if it's a pickup
    const pickupStart = setMinutes(setHours(checkingDateWithCurrentTime, 15), 0)
    // console.log(
    //   "pickup-check",
    //   pickupStart,
    //   checkingDateWithCurrentTime,
    //   isBefore(pickupStart, new Date(checkingDateWithCurrentTime)),
    // )
    const pickupEnd = addHours(pickupStart, 1)

    slots.push({
      value: format(pickupStart, "HH"),
      label: `${format(pickupStart, "hh:00 aa")} - ${format(pickupEnd, "hh:00 aa")}`,
      disabled: isBefore(pickupStart, new Date(checkingDateWithCurrentTime)),
      cost: cost || 0,
    })

    if (newPickup) {
      for (let hour = 16; hour <= 21; hour++) {
        const startDate = setMinutes(
          setHours(checkingDateWithCurrentTime, hour),
          0,
        )
        const endDate = addHours(startDate, 1)

        slots.push({
          value: format(startDate, "HH"),
          label: `${format(startDate, "hh:00 aa")} - ${format(endDate, "hh:00 aa")}`,
          disabled: isBefore(startDate, new Date(checkingDateWithCurrentTime)),
          cost: cost || 0,
        })
      }
    }
  } else {
    // Add delivery slots from 4:00 PM to 9:00 PM
    for (let hour = 16; hour <= 21; hour++) {
      const startDate = setMinutes(
        setHours(checkingDateWithCurrentTime, hour),
        0,
      )
      const endDate = addHours(startDate, 1)

      slots.push({
        value: format(startDate, "HH"),
        label: `${format(startDate, "hh:00 aa")} - ${format(endDate, "hh:00 aa")}`,
        disabled: isBefore(startDate, new Date(checkingDateWithCurrentTime)),
        cost: 0,
      })
    }
  }

  return slots
}
