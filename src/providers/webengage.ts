"use client"

import { loadScript } from "@/functions/loadScript"
import { useEffect } from "react"

const WEBENGAGE_LICENSE_CODE = process.env.NEXT_PUBLIC_WEBENGAGE_LICENSE_CODE

export default function WebEngageProvider() {
  useEffect(() => {
    if (typeof window === "undefined" || !WEBENGAGE_LICENSE_CODE) {
      console.error(
        "WebEngage license code is missing or running on the server",
      )
      return
    }

    // Dynamically load WebEngage script
    loadScript(
      "https://widgets.in.webengage.com/js/webengage-min-v-6.0.js",
      "_webengage_script_tag",
    )
      .then(() => {
        // Initialize WebEngage
        const webengage = window.webengage || {}
        window.webengage = webengage

        if (!webengage.init) {
          console.error("Failed to initialize WebEngage")
          return
        }

        webengage.init(WEBENGAGE_LICENSE_CODE)
        // console.log('WebEngage initialized successfully')
      })
      .catch((err) => {
        console.error("Failed to load WebEngage script:", err)
      })
  }, [])

  return null // This component doesn't render anything
}
