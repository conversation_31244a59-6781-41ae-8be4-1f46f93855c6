"use client"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { useState } from "react"

const ReactQueryProvider = ({ children }: { children: React.ReactNode }) => {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // During development, set a longer staleTime to reduce unnecessary refetches
            staleTime:
              process.env.NODE_ENV === "development"
                ? 1000 * 60 * 5 // 5 minutes
                : 1000 * 60 * 1, // 1 minute in production
            retry: process.env.NODE_ENV === "development" ? false : 3,
            refetchOnWindowFocus: process.env.NODE_ENV !== "development",
            // Other default options you might want to set
            gcTime: 1000 * 60 * 10, // 10 minutes
          },
          mutations: {
            retry: process.env.NODE_ENV === "development" ? false : 3,
          },
        },
      }),
  )

  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

export default ReactQueryProvider
