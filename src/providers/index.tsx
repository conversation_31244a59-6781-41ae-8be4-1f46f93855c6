"use client"
import React from "react"

import { Toaster } from "@/components/ui/sonner"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { Analytics } from "@vercel/analytics/react"
import { PHProvider } from "./posthog"
import ReactQueryProvider from "./react-query-provider"

// import { SessionProvider, SessionProviderProps } from 'next-auth/react';
export default function Providers({
  // session,
  children,
}: {
  // session: SessionProviderProps['session'];
  children: React.ReactNode
}) {
  return (
    <>
      <ReactQueryProvider>
        {/* <SessionProvider session={session}> */}
        <PHProvider>{children}</PHProvider>

        <Toaster position='top-center' richColors duration={2000} closeButton />
        <Analytics />
        <ReactQueryDevtools
          buttonPosition='bottom-left'
          initialIsOpen={false}
        />
        {/* </SessionProvider> */}
      </ReactQueryProvider>
    </>
  )
}
