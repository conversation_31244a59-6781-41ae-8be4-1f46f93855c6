import type { Config } from "tailwindcss"
import { fontSizes } from "./src/lib/design-system/typograhy-tokens"

export default {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    // container: {
    //   center: true,
    //   padding: '0rem',
    //   screens: {
    //     '2xl': '1440px',
    //   },
    // },
    extend: {
      fontFamily: {
        inter: ["var(--font-inter)"],
        ubuntu: ["var(--font-ubuntu)"],
      },
      // ... other theme extensions
      fontSize: fontSizes,
      container: {
        center: true,
        padding: {
          DEFAULT: "1rem",
          md: "2rem",
        },
        screens: {
          "2xl": "1216px",
        },
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          "100": "#E8ECFD",
          "150": "#D1DAFA",
          "200": "#BAC7F8",
          "250": "#A3B5F6",
          "300": "#758FF1",
          "400": "#476AED",
          "500": "#1945E8",
          "600": "#1437BA",
          "700": "#0F298B",
          "800": "#0A1C5D",
          "850": "#081546",
          "900": "#030D31",
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          "100": "#F5FFE5",
          "150": "#ECFFCC",
          "200": "#E2FFB3",
          "250": "#D8FF99",
          "300": "#C5FF66",
          "400": "#B1FF33",
          "500": "#9EFF00",
          "600": "#91EA00",
          "700": "#7EC904",
          "800": "#6EB102",
          "850": "#2F4D00",
          "900": "#243A01",
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          "100": "#FEECEB",
          "150": "#FCDAD7",
          "200": "#FBC7C3",
          "250": "#F9B4AF",
          "300": "#F68F88",
          "400": "#F36960",
          "500": "#F04438",
          "600": "#C0362D",
          "700": "#902922",
          "800": "#601B16",
          "850": "#481411",
          "900": "#2A0806",
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        warning: {
          "100": "#FEF4E6",
          "150": "#FDE9CE",
          "200": "#FDDEB5",
          "250": "#FCD39D",
          "300": "#FABC6B",
          "400": "#F9A63A",
          "500": "#F79009",
          "600": "#C67307",
          "700": "#945605",
          "800": "#633A04",
          "850": "#4A2B03",
          "900": "#2A1902",
        },
        success: {
          "100": "#E0F8EA",
          "150": "#C9F1D9",
          "200": "#B2EBC9",
          "250": "#9BE4B8",
          "300": "#6CD898",
          "400": "#3ECB77",
          "500": "#10BE56",
          "600": "#0D9845",
          "700": "#0A7234",
          "800": "#064C22",
          "850": "#05391A",
          "900": "#032210",
        },
        gray: {
          "100": "#FFFFFF",
          "150": "#F2F2F2",
          "200": "#E3E3E3",
          "250": "#C4C4C4",
          "300": "#A6A6A6",
          "400": "#979797",
          "500": "#898989",
          "600": "#7A7A7A",
          "700": "#6B6B6B",
          "800": "#595959",
          "850": "#3B3B3B",
          "900": "#101010",
        },
        neutral: {
          "100": "#FBFBFB",
          "150": "#F4F6F7",
          "200": "#E2E7EA",
          "250": "#CCD2D5",
          "300": "#9C9FA8",
          "400": "#7D808D",
          "500": "#5D6171",
          "600": "#4A4E5A",
          "700": "#383A44",
          "800": "#1C1D22",
          "850": "#131317",
          "900": "#090A0B",
        },

        decorative: {
          orange: "#E86419",
          teal: "#0FC08B",
          pink: "#E819AE",
          blue: "#0079BC",
        },
        category: {
          orange: "#FF5733",
          green: "#228B22",
          purple: "#8A2BE2",
          red: "#FF4465",
        },
        carepal: {
          lighter: "#FFD9E6",
          light: "#FF9DBF",
          dark: "#FF7BAC",
          darker: "#73324A",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
      borderRadius: {
        radius: "var(--radius)",
        "4xl": "1.75rem",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        marquee: {
          from: {
            transform: "translateX(0)",
          },
          to: {
            transform: "translateX(calc(-100% - var(--gap)))",
          },
        },
        orbit: {
          "0%": {
            transform:
              "rotate(calc(var(--angle) * 1deg)) translateY(calc(var(--radius) * 1px)) rotate(calc(var(--angle) * -1deg))",
          },
          "100%": {
            transform:
              "rotate(calc(var(--angle) * 1deg + 360deg)) translateY(calc(var(--radius) * 1px)) rotate(calc((var(--angle) * -1deg) - 360deg))",
          },
        },
        ripple: {
          "0%, 100%": {
            transform: "translate(-50%, -50%) scale(1)",
          },
          "50%": {
            transform: "translate(-50%, -50%) scale(0.9)",
          },
        },

        marquee2: {
          "0%": { transform: "translateX(0%)" },
          "100%": { transform: "translateX(-30%)" },
        },
      },

      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        marquee: "marquee var(--duration) linear infinite",
        orbit: "orbit calc(var(--duration)*1s) linear infinite",
        ripple: "ripple var(--duration,2s) ease calc(var(--i, 0)*.2s) infinite",
        "marquee-mobile": "marquee2 5s linear infinite",
      },
      backgroundImage: {
        bento:
          "linear-gradient(169.85deg, rgba(248, 248, 248, 0) 23.89%, #EFF3FF 97.94%)",
        "zero-policy-title-card":
          "linear-gradient(90deg, #9771FF 0%, #160766 100%)",
        "zero-policy-title-card-text":
          "linear-gradient(90deg, #F3FF6A 0%, #B5DCB7 100%)",
        "zero-policy-card": "linear-gradient(180deg, #FFF 55.5%, #F1ECFF 100%)",
        "carepal-card": "linear-gradient(180deg, #FFF 55.5%, #FFEFF5 100%)",
        "zero-policy-card-text":
          "linear-gradient(360deg, #FBFAFF 4.82%, #9F90DD 100%)",
        "sharepal-promise-card":
          "linear-gradient(90deg, #0B6E80 0%, #081851 100%)",
        "review-text":
          "linear-gradient(90deg, #002CD1 27.44%, #1945E8 48.86%, #7EC904 70.28%)",
        "top-categoies-gradient":
          " linear-gradient(90deg, #F4F6F7 0%, #E8ECFD 20%, #E8ECFD 80%, #F4F6F7 100%)",
        "footer-logo": "linear-gradient(90deg, #030D31 0%, #03134F 100%)",

        "blue-category-gradient":
          "linear-gradient(0deg, #F4F6F7 30%, #1945E8 50%, #0A1C5D 100%)",
        "purple-category-gradient":
          " linear-gradient(0deg, #F4F6F7 30%, #8A2BE2 50%, #200539 100%)",
        "red-category-gradient":
          " linear-gradient(0deg, #F4F6F7 30%, #FF4465 50%, #2E050C 100%)",
        "green-category-gradient":
          " linear-gradient(0deg, #F4F6F7 30%, #228B22 50%, #031F03 100%)",
        "orange-category-gradient":
          " linear-gradient(0deg, #F4F6F7 30%, #FF5733 50%, #200803 100%)",
        "bento-section":
          " linear-gradient(180deg, #F4F6F7 0%, #FFFFFF 10%, #FFFFFF 90%, #FFFFFF 100%)",
        "review-gradient":
          "linear-gradient(92deg, #002CD1 27.44%, #1945E8 48.86%, #7EC904 70.28%)",
        "carepal-gradient":
          "linear-gradient(0deg, #F4F6F7 30%, #FF7BAC 50%, #73324A 100%)",
      },
      letterSpacing: {
        heading: "1%",
      },
    },
    boxShadow: {
      sm: "0px 0px 8px 0px rgba(0, 0, 0, 0.05), 0px 0px 16px 1px rgba(0, 0, 0, 0.10)",
      xs: "0px 0px 4px 0px rgba(0, 0, 0, 0.05), 0px 0px 8px 1px rgba(0, 0, 0, 0.10)",
      md: " 0px 0px 10px 0px rgba(0, 0, 0, 0.10), 0px 0px 20px 1px rgba(0, 0, 0, 0.15)",
      lg: "0px 0px 14px 0px rgba(0, 0, 0, 0.12), 0px 0px 16px 1px rgba(0, 0, 0, 0.16)",
      xl: "0px 0px 16px 0px rgba(0, 0, 0, 0.15), 0px 0px 16px 1px rgba(0, 0, 0, 0.20)",
      "2xl":
        "0px 0px 20px 0px rgba(0, 0, 0, 0.20), 0px 0px 24px 1px rgba(0, 0, 0, 0.24)",
      "soft-bottom": "0px 12px 24px 6px rgba(0, 0, 0, 0.15)",
    },
  },
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  plugins: [require("tailwindcss-animate"), require("tailwind-scrollbar")],
} satisfies Config
