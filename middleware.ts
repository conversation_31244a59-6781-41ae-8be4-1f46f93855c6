import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"

const EXCLUDED_PATHS = ["/_next/", "/static/"]
const EXCLUDED_EXTENSIONS = [
  ".svg",
  ".js",
  ".css",
  ".ico",
  ".png",
  ".jpg",
  ".jpeg",
  ".gif",
  ".webp",
  ".woff2",
]
const BOT_PATTERNS = [/bot/i, /crawler/i, /spider/i, /curl/i, /wget/i]

// Extend NextRequest to include geo type
interface GeoNextRequest extends NextRequest {
  geo?: {
    country?: string
    region?: string
    city?: string
  }
}

export function middleware(request: GeoNextRequest) {
  const url = new URL(request.url)
  const userAgent = request.headers.get("user-agent") || ""

  const country =
    request.geo?.country ||
    request.headers.get("x-vercel-ip-country") ||
    "unknown"

  if (
    EXCLUDED_PATHS.some((path) => url.pathname.startsWith(path)) ||
    EXCLUDED_EXTENSIONS.some((ext) => url.pathname.endsWith(ext))
  ) {
    return NextResponse.next()
  }

  if (BOT_PATTERNS.some((pattern) => pattern.test(userAgent))) {
    console.log(`Bot detected! (${userAgent}) - Redirecting to /bot-detected.`)
    return NextResponse.redirect(new URL("/bot-detected", request.url))
  }

  if (country !== "IN") {
    console.log(`Access denied for country: ${country}`)
    return NextResponse.redirect(new URL("/access-denied", request.url))
  }

  console.log(`Request received: ${request.url} | User-Agent: ${userAgent}`)

  // Check if the request is a POST request to the specific page
  // if (
  //   request.method === "POST" &&
  //   request.nextUrl.pathname === "/complete-verification"
  // ) {
  //   // Convert the POST request to a GET request
  //   const url = new URL(request.nextUrl)
  //   url.search = new URLSearchParams(request.url).toString()

  //   // Redirect to the same path with query parameters
  //   return NextResponse.redirect(new URL(url.toString()))
  // }

  // For all other requests, proceed without modification

  return NextResponse.next()
}

export const config = {
  matcher: "/:path*",
}
