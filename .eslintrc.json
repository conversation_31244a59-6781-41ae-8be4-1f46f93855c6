{"extends": ["next/core-web-vitals", "next/typescript", "prettier"], "plugins": ["prettier"], "rules": {"prettier/prettier": ["error"], "arrow-body-style": ["error", "as-needed"], "prefer-arrow-callback": ["error"], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "no-console": ["warn", {"allow": ["warn", "error", "info"]}]}, "ignorePatterns": ["node_modules/**/*", ".next/**/*", "dist/**/*", "build/**/*"]}